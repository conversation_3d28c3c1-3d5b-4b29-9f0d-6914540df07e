{"cells": [{"cell_type": "markdown", "metadata": {"id": "IjtpAnHBRP53"}, "source": ["# Velocity and Acceleration of a point of a rigid body\n", "\n", "> <PERSON><PERSON>, <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"id": "TMTmKj33RP59"}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Frame-of-reference-attached-to-a-body\" data-toc-modified-id=\"Frame-of-reference-attached-to-a-body-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Frame of reference attached to a body</a></span></li><li><span><a href=\"#Position-of-a-point-on-a-rigid-body\" data-toc-modified-id=\"Position-of-a-point-on-a-rigid-body-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Position of a point on a rigid body</a></span></li><li><span><a href=\"#Translation-of-a-rigid-body\" data-toc-modified-id=\"Translation-of-a-rigid-body-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Translation of a rigid body</a></span></li><li><span><a href=\"#Angular-velocity-of-a-body\" data-toc-modified-id=\"Angular-velocity-of-a-body-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Angular velocity of a body</a></span></li><li><span><a href=\"#Velocity-of-a-point-with-no-translation\" data-toc-modified-id=\"Velocity-of-a-point-with-no-translation-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Velocity of a point with no translation</a></span></li><li><span><a href=\"#Relative-velocity-of-a-point-on-a-rigid-body-to-another-point\" data-toc-modified-id=\"Relative-velocity-of-a-point-on-a-rigid-body-to-another-point-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Relative velocity of a point on a rigid body to another point</a></span></li><li><span><a href=\"#Velocity-of-a-point-on-rigid-body-translating\" data-toc-modified-id=\"Velocity-of-a-point-on-rigid-body-translating-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Velocity of a point on rigid body translating</a></span></li><li><span><a href=\"#Acceleration-of-a-point-on-a-rigid-body\" data-toc-modified-id=\"Acceleration-of-a-point-on-a-rigid-body-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Acceleration of a point on a rigid body</a></span></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-12\"><span class=\"toc-item-num\">12&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-13\"><span class=\"toc-item-num\">13&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "qzFiBLkIRP5-"}, "source": ["This notebook shows the expressions of  the velocity and acceleration of a point on rigid body, given the angular velocity of the body."]}, {"cell_type": "markdown", "metadata": {"id": "wTfQ-6HGRP5_"}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_0kcdc_TRP5_", "outputId": "fa623339-dfce-4f19-cb66-7ccd2e33b15e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: ipympl in /usr/local/lib/python3.10/dist-packages (0.9.4)\n", "Requirement already satisfied: ipython-genutils in /usr/local/lib/python3.10/dist-packages (from ipympl) (0.2.0)\n", "Requirement already satisfied: ipython<9 in /usr/local/lib/python3.10/dist-packages (from ipympl) (7.34.0)\n", "Requirement already satisfied: ipywidgets<9,>=7.6.0 in /usr/local/lib/python3.10/dist-packages (from ipympl) (7.7.1)\n", "Requirement already satisfied: matplotlib<4,>=3.4.0 in /usr/local/lib/python3.10/dist-packages (from ipympl) (3.8.0)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from ipympl) (1.26.4)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.10/dist-packages (from ipympl) (11.0.0)\n", "Requirement already satisfied: traitlets<6 in /usr/local/lib/python3.10/dist-packages (from ipympl) (5.7.1)\n", "Requirement already satisfied: setuptools>=18.5 in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (75.1.0)\n", "Requirement already satisfied: jedi>=0.16 in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (0.19.2)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (4.4.2)\n", "Requirement already satisfied: pickleshare in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (0.7.5)\n", "Requirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (3.0.48)\n", "Requirement already satisfied: pygments in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (2.18.0)\n", "Requirement already satisfied: backcall in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (0.2.0)\n", "Requirement already satisfied: matplotlib-inline in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (0.1.7)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.10/dist-packages (from ipython<9->ipympl) (4.9.0)\n", "Requirement already satisfied: ipykernel>=4.5.1 in /usr/local/lib/python3.10/dist-packages (from ipywidgets<9,>=7.6.0->ipympl) (5.5.6)\n", "Requirement already satisfied: widgetsnbextension~=3.6.0 in /usr/local/lib/python3.10/dist-packages (from ipywidgets<9,>=7.6.0->ipympl) (3.6.10)\n", "Requirement already satisfied: jupyterlab-widgets>=1.0.0 in /usr/local/lib/python3.10/dist-packages (from ipywidgets<9,>=7.6.0->ipympl) (3.0.13)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (4.54.1)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (1.4.7)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (3.2.0)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.10/dist-packages (from matplotlib<4,>=3.4.0->ipympl) (2.8.2)\n", "Requirement already satisfied: jupyter-client in /usr/local/lib/python3.10/dist-packages (from ipykernel>=4.5.1->ipywidgets<9,>=7.6.0->ipympl) (6.1.12)\n", "Requirement already satisfied: tornado>=4.2 in /usr/local/lib/python3.10/dist-packages (from ipykernel>=4.5.1->ipywidgets<9,>=7.6.0->ipympl) (6.3.3)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.10/dist-packages (from jedi>=0.16->ipython<9->ipympl) (0.8.4)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.10/dist-packages (from pexpect>4.3->ipython<9->ipympl) (0.7.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.10/dist-packages (from prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0->ipython<9->ipympl) (0.2.13)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.7->matplotlib<4,>=3.4.0->ipympl) (1.16.0)\n", "Requirement already satisfied: notebook>=4.4.1 in /usr/local/lib/python3.10/dist-packages (from widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (6.5.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (3.1.4)\n", "Requirement already satisfied: pyzmq<25,>=17 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (24.0.1)\n", "Requirement already satisfied: argon2-cffi in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (23.1.0)\n", "Requirement already satisfied: jupyter-core>=4.6.1 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (5.7.2)\n", "Requirement already satisfied: nbformat in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (5.10.4)\n", "Requirement already satisfied: nbconvert>=5 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (7.16.4)\n", "Requirement already satisfied: nest-asyncio>=1.5 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.6.0)\n", "Requirement already satisfied: Send2Trash>=1.8.0 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.8.3)\n", "Requirement already satisfied: terminado>=0.8.3 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.18.1)\n", "Requirement already satisfied: prometheus-client in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.21.0)\n", "Requirement already satisfied: nbclassic>=0.4.7 in /usr/local/lib/python3.10/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.1.0)\n", "Requirement already satisfied: platformdirs>=2.5 in /usr/local/lib/python3.10/dist-packages (from jupyter-core>=4.6.1->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (4.3.6)\n", "Requirement already satisfied: notebook-shim>=0.2.3 in /usr/local/lib/python3.10/dist-packages (from nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.2.4)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (4.12.3)\n", "Requirement already satisfied: bleach!=5.0.0 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (6.2.0)\n", "Requirement already satisfied: defusedxml in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.7.1)\n", "Requirement already satisfied: jupyterlab-pygments in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.3.0)\n", "Requirement already satisfied: markupsafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (3.0.2)\n", "Requirement already satisfied: mistune<4,>=2.0.3 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (3.0.2)\n", "Requirement already satisfied: nbclient>=0.5.0 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.10.0)\n", "Requirement already satisfied: pandocfilters>=1.4.1 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.5.1)\n", "Requirement already satisfied: tinycss2 in /usr/local/lib/python3.10/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.4.0)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /usr/local/lib/python3.10/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (2.20.0)\n", "Requirement already satisfied: jsonschema>=2.6 in /usr/local/lib/python3.10/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (4.23.0)\n", "Requirement already satisfied: argon2-cffi-bindings in /usr/local/lib/python3.10/dist-packages (from argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (21.2.0)\n", "Requirement already satisfied: webencodings in /usr/local/lib/python3.10/dist-packages (from bleach!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.5.1)\n", "Requirement already satisfied: attrs>=22.2.0 in /usr/local/lib/python3.10/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (24.2.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.10/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.10/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.35.1)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.10/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (0.21.0)\n", "Requirement already satisfied: jupyter-server<3,>=1.8 in /usr/local/lib/python3.10/dist-packages (from notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.24.0)\n", "Requirement already satisfied: cffi>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.17.1)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (2.6)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (2.22)\n", "Requirement already satisfied: anyio<4,>=3.1.0 in /usr/local/lib/python3.10/dist-packages (from jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (3.7.1)\n", "Requirement already satisfied: websocket-client in /usr/local/lib/python3.10/dist-packages (from jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.8.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<4,>=3.1.0->jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.10/dist-packages (from anyio<4,>=3.1.0->jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.3.1)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<4,>=3.1.0->jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets<9,>=7.6.0->ipympl) (1.2.2)\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "!pip install ipympl\n", "%matplotlib widget\n", "from matplotlib.animation import FuncAnimation\n", "from matplotlib.patches import FancyArrowPatch"]}, {"cell_type": "code", "source": ["from google.colab import output\n", "output.enable_custom_widget_manager()"], "metadata": {"id": "Ivvq4x0hgaW9"}, "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "source": ["Support for third party widgets will remain active for the duration of the session. To disable support:"], "metadata": {"id": "bJ6tewBigaW-"}}, {"cell_type": "markdown", "metadata": {"id": "Bm6QoJLhRP6B"}, "source": ["## Frame of reference attached to a body\n", "\n", "The concept of reference frame in Biomechanics and motor control is very important and central to the understanding of human motion. For example, do we see, plan and control the movement of our hand with respect to reference frames within our body or in the environment we move? Or a combination of both?  \n", "The figure below, although derived for a robotic system, illustrates well the concept that we might have to deal with multiple coordinate systems.  \n", "\n", "<div class='center-align'><figure><img src=\"https://raw.githubusercontent.com/demotu/BMC/master/images/coordinatesystems.png\" width=450/><figcaption><center><i>Figure. Multiple coordinate systems for use in robots (figure from <PERSON><PERSON> (2017)).</i></center></figcaption></figure></div>\n", "\n", "For three-dimensional motion analysis in Biomechanics, we may use several different references frames for convenience and refer to them as global, laboratory, local, anatomical, or technical reference frames or coordinate systems (we will study this later).  \n", "There has been proposed different standardizations on how to define frame of references for the main segments and joints of the human body. For instance, the International Society of Biomechanics has a [page listing standardization proposals](https://isbweb.org/activities/standards) by its standardization committee and subcommittees:"]}, {"cell_type": "markdown", "metadata": {"id": "WgKKwHplRP6B"}, "source": ["## Position of a point on a rigid body\n", "\n", "The description of the position of a point P of a rotating rigid body is given by:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{r}_{P/O}} = x_{P/O}^*{\\bf\\hat{i}'} + y_{P/O}^*{\\bf\\hat{j}'}\n", "\\end{equation}\n", "</span>\n", "\n", "where $x_{P/O}^*$ and $y_{P/O}^*$ are the coordinates of the point P position at a reference state with the versors described as:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\hat{i}'} = \\cos(\\theta){\\bf\\hat{i}}+\\sin(\\theta){\\bf\\hat{j}}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\hat{j}'} = -\\sin(\\theta){\\bf\\hat{i}}+\\cos(\\theta){\\bf\\hat{j}}\n", "\\end{equation}\n", "</span>\n", "\n", "\n", "<img src=\"https://github.com/BMClab/BMC/blob/master/images/rotBody.png?raw=1\" style=\"width: 1000000px;\">\n", "\n", "Note that the vector ${\\bf\\vec{r}_{P/O}}$ has always the same description for any point P of the rigid body when described as a linear combination of <span class=\"notranslate\">${\\bf\\hat{i}'}$</span> and <span class=\"notranslate\">${\\bf\\hat{j}'}$</span>.\n"]}, {"cell_type": "markdown", "metadata": {"id": "knN5lKegRP6B"}, "source": ["## Translation of a rigid body\n", "\n", " Let's consider now the case in which, besides a rotation, a translation of the body happens. This situation is represented in the figure below. In this case, the position of the point P is given by:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{r}_{P/O}} = {\\bf\\vec{r}_{A/O}}+{\\bf\\vec{r}_{P/A}}= {\\bf\\vec{r}_{A/O}}+x_{P/A}^*{\\bf\\hat{i}'} + y_{P/A}^*{\\bf\\hat{j}'}\n", "\\end{equation}\n", "</span>\n", "\n", "<img src=\"https://github.com/BMClab/BMC/blob/master/images/rotTrBody.png?raw=1\" width=1000/>"]}, {"cell_type": "markdown", "metadata": {"id": "RhkZkVoKRP6C"}, "source": ["## Angular velocity of a body\n", "\n", "The magnitude of the angular velocity of a rigid  body rotating on a plane is defined as:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\omega = \\frac{d\\theta}{dt}\n", "\\end{equation}\n", "</span>\n", "\n", "Usually, it is defined an angular velocity vector perpendicular to the plane where the rotation occurs (in this case the x-y plane) and with magnitude $\\omega$:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{\\omega}} = \\omega\\hat{\\bf{k}}\n", "\\end{equation}\n", "</span>\n", "\n", "\n", "<img src=\"https://github.com/BMClab/BMC/blob/master/images/angvel.png?raw=1\" width=600/>"]}, {"cell_type": "markdown", "metadata": {"id": "FbL9CFO0RP6C"}, "source": ["## Velocity of a point with no translation"]}, {"cell_type": "markdown", "metadata": {"id": "VUFePBaPRP6D"}, "source": ["First we will consider the situation with no translation. The velocity of the point P is given by:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{v}_{P/O}} = \\frac{d{\\bf\\vec{r}_{P/O}}}{dt} = \\frac{d(x_{P/O}^*{\\bf\\hat{i}'} + y_{P/O}^*{\\bf\\hat{j}'})}{dt}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "YWrdEHg3RP6D"}, "source": ["To continue this deduction, we have to find the expression of the derivatives of <span class=\"notranslate\">\n", "${\\bf\\hat{i}'}$</span> and <span class=\"notranslate\">${\\bf\\hat{j}'}$</span>. This is very similar to the derivative expressions of ${\\bf\\hat{e_R}}$ and  ${\\bf\\hat{e_\\theta}}$ of [polar basis](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/PolarBasis.ipynb).\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{d{\\bf\\hat{i}'}}{dt} = -\\dot{\\theta}\\sin(\\theta){\\bf\\hat{i}}+\\dot{\\theta}\\cos(\\theta){\\bf\\hat{j}} = \\dot{\\theta}{\\bf\\hat{j}'}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{d{\\bf\\hat{j}'}}{dt} = -\\dot{\\theta}\\cos(\\theta){\\bf\\hat{i}}-\\dot{\\theta}\\sin(\\theta){\\bf\\hat{j}} = -\\dot{\\theta}{\\bf\\hat{i}'}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "AOjP3BfnRP6D"}, "source": ["Another way to represent the expressions above is by using the vector form to express the angular velocity $\\dot{\\theta}$. It is usual to represent the angular velocity as a vector in the direction ${\\bf\\hat{k}}$: ${\\bf\\vec{\\omega}} = \\dot{\\theta}{\\bf\\hat{k}} = \\omega{\\bf\\hat{k}}$. Using this definition of the angular velocity, we can write the above expressions as:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{d{\\bf\\hat{i}'}}{dt} = \\dot{\\theta}{\\bf\\hat{j}'} = \\dot{\\theta} {\\bf\\hat{k}}\\times {\\bf\\hat{i}'} = {\\bf\\vec{\\omega}} \\times {\\bf\\hat{i}'}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{d{\\bf\\hat{j}'}}{dt} = -\\dot{\\theta}{\\bf\\hat{i}'} = \\dot{\\theta} {\\bf\\hat{k}}\\times {\\bf\\hat{j}'} ={\\bf\\vec{\\omega}} \\times {\\bf\\hat{j}'}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "snpy5Zd_RP6D"}, "source": ["So, the velocity of the point P in the situation of no translation is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{v}_{P/O}} = \\frac{d(x_{P/O}^*{\\bf\\hat{i}'} + y_{P/O}^*{\\bf\\hat{j}'})}{dt} = x_{P/O}^*\\frac{d{\\bf\\hat{i}'}}{dt} + y_{P/O}^*\\frac{d{\\bf\\hat{j}'}}{dt}=x_{P/O}^*{\\bf\\vec{\\omega}} \\times {\\bf\\hat{i}'} + y_{P/O}^*{\\bf\\vec{\\omega}} \\times {\\bf\\hat{j}'} = {\\bf\\vec{\\omega}} \\times \\left(x_{P/O}^*{\\bf\\hat{i}'}\\right) + {\\bf\\vec{\\omega}} \\times \\left(y_{P/O}^*{\\bf\\hat{j}'}\\right) ={\\bf\\vec{\\omega}} \\times \\left(x_{P/O}^*{\\bf\\hat{i}'}+y_{P/O}^*{\\bf\\hat{j}'}\\right)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{v}_{P/O}} = {\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/O}}}\n", "\\end{equation}\n", "</span>\n", "\n", "This expression shows that the velocity vector of any point of a rigid body is orthogonal to the vector linking the point O and the point P.\n", "\n", "It is worth to note that despite the above expression was deduced for a planar movement, the expression above is general, including three dimensional movements."]}, {"cell_type": "markdown", "metadata": {"id": "gViBgq6KRP6D"}, "source": ["## Relative velocity of a point on a rigid body to another point\n", "\n", "To compute the velocity of a point on a rigid body that is translating, we need to find the expression of the velocity of a point (P) in relation to another point on the body (A). So:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{v}_{P/A}} = {\\bf\\vec{v}_{P/O}}-{\\bf\\vec{v}_{A/O}} = {\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/O}}} - {\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{A/O}}} = {\\bf\\vec{\\omega}} \\times ({\\bf{\\vec{r}_{P/O}}}-{\\bf{\\vec{r}_{A/O}}}) =  {\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/A}}}\n", "\\end{equation}\n", "</span>\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "X41E_PSkRP6E"}, "source": ["## Velocity of a point on rigid body translating\n", "\n", "The velocity of a point on a rigid body that is translating is given by:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "{\\bf\\vec{v}_{P/O}}  = \\frac{d{\\bf\\vec{r}_{P/O}}}{dt} = \\frac{d({\\bf\\vec{r}_{A/O}}+x_{P/A}^*{\\bf\\hat{i}'} + y_{P/A}^*{\\bf\\hat{j}'})}{dt} = \\frac{d{\\bf\\vec{r}_{A/O}}}{dt}+\\frac{d(x_{P/A}^*{\\bf\\hat{i}'} + y_{P/A}^*{\\bf\\hat{j}'})}{dt} = {\\bf\\vec{v}_{A/O}} + {\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/A}}}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "S3wlOwYZRP6E"}, "source": ["Below is an example of a body rotating with the angular velocity of $\\omega = \\pi/10$ rad/s and translating at the velocity of <span class=\"notranslate\">\n", "${\\bf\\vec{v}} = 0.7 {\\bf\\hat{i}} + 0.5 {\\bf\\hat{j}}$ m/s</span>. The red arrow indicates the velocity of the geometric center of the body and the blue arrow indicates the velocity of the lower point of the body"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 589, "referenced_widgets": ["522a49cb61454a1d9b908fcb9bd36d6a", "7dc985aada224956b412cd53fa8425cd", "d17beec8dcce46b1b2443b4b8902c4d8", "df649aca8630420ab9fa551389bba4ca"]}, "id": "tY58VFIyRP6E", "outputId": "9c19b3da-1ab6-459d-d2bb-0b894f2a5b4a"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAfQAAAH0CAYAAADL1t+KAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy81sbWrAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAIEElEQVR4nO3VsW3DQBAAwbPB+Pvvjx1cA1LgDuSA0GKmgs32Z2ZeAwB8tWtm5r7vOec83QIAfGB3/4Z+zjF0APhiv08HAAD/Z+gAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQYOgAEGDoABBg6AAQcM3M7O7THQDAh3Z33k/VEJa+s6etAAAAAElFTkSuQmCC", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=500.0/>\n", "            </div>\n", "        "], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "522a49cb61454a1d9b908fcb9bd36d6a"}}, "metadata": {"application/vnd.jupyter.widget-view+json": {"colab": {"custom_widget_manager": {"url": "https://ssl.gstatic.com/colaboratory-static/widgets/colab-cdn-widget-manager/2b70e893a8ba7c0f/manager.min.js"}}}}}], "source": ["t = np.linspace(0,13,40)\n", "omega = np.pi/10 #[rad/s]\n", "voa = np.array([[0.7],[0.5]]) # velocity of center of mass\n", "fig = plt.figure()\n", "plt.grid()\n", "ax = fig.add_axes([0, 0, 1, 1])\n", "ax.axis(\"on\")\n", "plt.rcParams['figure.figsize']=5,5\n", "def run(i):\n", "    ax.clear()\n", "    theta = omega * t[i]\n", "    phi = np.linspace(0,2*np.pi,100)\n", "    B = np.squeeze(np.array([[2*np.cos(phi)],[6*np.sin(phi)]]))\n", "    Baum = np.vstack((B,np.ones((1,np.shape(B)[1]))))\n", "    roa = voa * t[i]\n", "    R = np.array([[np.cos(theta), -np.sin(theta)],[np.sin(theta), np.cos(theta)]])\n", "    T = np.vstack((np.hstack((R,roa)), np.array([0,0,1])))\n", "    BRot = R@B\n", "    BRotTr = T@Baum\n", "\n", "\n", "\n", "    plt.plot(BRotTr[0,:],BRotTr[1,:], roa[0], roa[1],'.')\n", "    plt.fill(BRotTr[0,:],BRotTr[1,:], 'g')\n", "\n", "    vVoa = FancyArrowPatch(roa.squeeze(), roa.squeeze()+5*voa.squeeze(), mutation_scale=20,\n", "                           lw=2, arrowstyle=\"->\", color=\"r\", alpha=1)\n", "    ax.add_artist(vVoa)\n", "\n", "    element = 75\n", "\n", "    # cross product between omega and r\n", "    Vp = voa + np.cross(np.array([0,0,omega]), BRot[:,[element]].T)[:,0:2].T\n", "\n", "    vVP = FancyArrowPatch(BRotTr[0:2,element], BRotTr[0:2,element] + 5*Vp.squeeze(),\n", "                          mutation_scale=20,\n", "                          lw=2, arrowstyle=\"->\", color=\"b\", alpha=1)\n", "    ax.add_artist(vVP)\n", "\n", "    plt.xlim((-10, 20))\n", "    plt.ylim((-10, 20))\n", "\n", "ani = FuncAnimation(fig, run, frames = 50, repeat=False,  interval =500)\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {"id": "YsYCkyoqRP6F"}, "source": ["## Acceleration of  a point on a rigid body\n", "\n", "The acceleration of a point on a rigid body is obtained by deriving the previous expression:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "{\\bf\\vec{a}_{P/O}} =& {\\bf\\vec{a}_{A/O}} + \\dot{\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/A}}} + {\\bf\\vec{\\omega}} \\times {\\bf{\\vec{v}_{P/A}}} =\\\\\n", "    =&{\\bf\\vec{a}_{A/O}} + \\dot{\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/A}}} + {\\bf\\vec{\\omega}} \\times ({\\bf\\vec{\\omega}} \\times {\\bf{\\vec{r}_{P/A}}}) =\\\\\n", "    =&{\\bf\\vec{a}_{A/O}} + \\ddot{\\theta}\\bf\\hat{k} \\times {\\bf{\\vec{r}_{P/A}}} - \\dot{\\theta}^2{\\bf{\\vec{r}_{P/A}}}\n", "\\end{align}\n", "</span>\n", "\n", "The acceleration has three terms:\n", "\n", "- <span class=\"notranslate\">${\\bf\\vec{a}_{A/O}}$</span> -- the acceleration of the point O.\n", "- <span class=\"notranslate\">$\\ddot{\\theta}\\bf\\hat{k} \\times {\\bf{\\vec{r}_{P/A}}}$</span> -- the acceleration of the point P due to the angular acceleration of the body.\n", "- <span class=\"notranslate\">$- \\dot{\\theta}^2{\\bf{\\vec{r}_{P/A}}}$</span> -- the acceleration of the point P due to the angular velocity of the body. It is known as centripetal acceleration.\n", "\n", "\n", "\n", "Below is an example of a rigid  body with an angular acceleration of <span class=\"notranslate\"> $\\alpha = \\pi/150$ rad/s$^2$ </span> and initial angular velocity of <span class=\"notranslate\">$\\omega_0 = \\pi/100$ rad/s</span>. Consider also that the center of the body accelerates with <span class=\"notranslate\">${\\bf\\vec{a}} = 0.01{\\bf\\hat{i}} + 0.05{\\bf\\hat{j}}$</span>, starting from rest.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/", "height": 72, "referenced_widgets": ["63464999d97f4d75841657c57dd6eebf"]}, "id": "CdfPaeo2RP6F", "outputId": "dac2cb85-4fc5-473c-a978-1c0ebe4b6a28"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/matplotlib/animation.py:892: UserWarning: Animation was deleted without rendering anything. This is most likely not intended. To prevent deletion, assign the Animation to a variable, e.g. `anim`, that exists until you output the Animation using `plt.show()` or `anim.save()`.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"], "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=500.0/>\n", "            </div>\n", "        "], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "63464999d97f4d75841657c57dd6eebf"}}, "metadata": {}}], "source": ["t = np.linspace(0, 20, 40)\n", "\n", "alpha = np.pi/150 #[rad/s^2] angular acceleration\n", "\n", "omega0 = np.pi/100 #[rad/s] angular velocity\n", "\n", "aoa = np.array([[0.01],[0.05]]) # linear acceleration\n", "\n", "fig = plt.figure()\n", "plt.grid()\n", "ax = fig.add_axes([0, 0, 1, 1])\n", "ax.axis(\"on\")\n", "plt.rcParams['figure.figsize']=5,5\n", "theta = 0\n", "omega = 0\n", "def run(i):\n", "    ax.clear()\n", "    phi = np.linspace(0,2*np.pi,100)\n", "    B = np.squeeze(np.array([[2*np.cos(phi)],[6*np.sin(phi)]]))\n", "    Baum = np.vstack((B,np.ones((1,np.shape(B)[1]))))\n", "\n", "    omega = alpha*t[i]+omega0 #[rad/s] angular velocity\n", "\n", "    theta = alpha/2*t[i]**2 + omega0*t[i] # [rad] angle\n", "\n", "    voa = aoa*t[i] # linear velocity\n", "\n", "    roa = aoa/2*t[i]**2 # position of the center of the body\n", "\n", "    R = np.array([[np.cos(theta), -np.sin(theta)],[np.sin(theta), np.cos(theta)]])\n", "    T = np.vstack((np.hstack((R,roa)), np.array([0,0,1])))\n", "    BRot = R@B\n", "    BRotTr = T@Baum\n", "\n", "    plt.plot(BRotTr[0,:],BRotTr[1,:], roa[0], roa[1],'.')\n", "    plt.fill(BRotTr[0,:],BRotTr[1,:],'g')\n", "\n", "    element = 75\n", "\n", "    ap = (aoa + np.cross(np.array([0,0,alpha]), BRot[:,[element]].T)[:,0:2].T\n", "          - omega**2*BRot[:,[element]])\n", "\n", "    vVP = FancyArrowPatch(BRotTr[0:2,element], BRotTr[0:2,element] + 5*ap.squeeze(),\n", "                          mutation_scale=20,\n", "                          lw=2, arrowstyle=\"->\", color=\"b\", alpha=1)\n", "    ax.add_artist(vVP)\n", "\n", "    plt.xlim((-10, 20))\n", "    plt.ylim((-10, 20))\n", "\n", "\n", "\n", "ani = FuncAnimation(fig, run, frames=50, repeat=False,  interval=500)\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {"id": "qMnvz_0pRP6G"}, "source": ["## Further reading\n", "\n", "- Read pages 958-971 of the 18th chapter of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book] (http://ruina.tam.cornell.edu/Book/index.html) about circular motion of particle."]}, {"cell_type": "markdown", "metadata": {"id": "UIeZ923eRP6G"}, "source": ["## Video lectures on the Internet\n", "\n", "- [Kinematics Of Rigid Bodies - General Plane Motion - Solved Problems](https://www.youtube.com/watch?v=4LsLy9iJKFA)\n", "- [Kinematics of Rigid Bodies -Translation And Rotation About Fixed Axis - Rectilinear and Rotational](https://www.youtube.com/watch?v=VnzsQmP6eMQ)  "]}, {"cell_type": "markdown", "metadata": {"id": "u6qXelKaRP6G"}, "source": ["## Problems\n", "\n", "- 1. Solve the problems 16.2.5, 16.2.10, 16.2.11 and 16.2.20 from [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html).\n", "- 2. Solve the problems 17.1.2, 17.1.8, 17.1.9, 17.1.10, 17.1.11 and 17.1.12 from [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html).\n", "- 3. Solve the problems 2.1, 2.2, 2.7, 2.8, 2.17"]}, {"cell_type": "markdown", "metadata": {"id": "ZwcTdIt2RP6H"}, "source": ["## Reference\n", "\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.\n", "- <PERSON><PERSON> (2017) [Robotics, Vision and Control: Fundamental Algorithms in MATLAB](http://www.petercorke.com/RVC/). 2nd ed. Springer-Verlag Berlin."]}], "metadata": {"celltoolbar": "Slideshow", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "pt", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": []}, "widgets": {"application/vnd.jupyter.widget-state+json": {"522a49cb61454a1d9b908fcb9bd36d6a": {"model_module": "jupyter-mat<PERSON><PERSON>lib", "model_name": "MPLCanvasModel", "model_module_version": "^0.11", "state": {"_cursor": "default", "_data_url": "data:image/png;base64,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", "_dom_classes": [], "_figure_label": "Figure 3", "_image_mode": "diff", "_message": "", "_model_module": "jupyter-mat<PERSON><PERSON>lib", "_model_module_version": "^0.11", "_model_name": "MPLCanvasModel", "_rubberband_height": 0, "_rubberband_width": 0, "_rubberband_x": 0, "_rubberband_y": 0, "_size": [500, 500], "_view_count": null, "_view_module": "jupyter-mat<PERSON><PERSON>lib", "_view_module_version": "^0.11", "_view_name": "MP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capture_scroll": false, "footer_visible": true, "header_visible": true, "layout": "IPY_MODEL_7dc985aada224956b412cd53fa8425cd", "pan_zoom_throttle": 33, "resizable": true, "toolbar": "IPY_MODEL_d17beec8dcce46b1b2443b4b8902c4d8", "toolbar_position": "left", "toolbar_visible": "fade-in-fade-out"}}, "7dc985aada224956b412cd53fa8425cd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d17beec8dcce46b1b2443b4b8902c4d8": {"model_module": "jupyter-mat<PERSON><PERSON>lib", "model_name": "ToolbarModel", "model_module_version": "^0.11", "state": {"_current_action": "", "_dom_classes": [], "_model_module": "jupyter-mat<PERSON><PERSON>lib", "_model_module_version": "^0.11", "_model_name": "ToolbarModel", "_view_count": null, "_view_module": "jupyter-mat<PERSON><PERSON>lib", "_view_module_version": "^0.11", "_view_name": "ToolbarView", "button_style": "", "collapsed": true, "layout": "IPY_MODEL_df649aca8630420ab9fa551389bba4ca", "orientation": "vertical", "toolitems": [["Home", "Reset original view", "home", "home"], ["Back", "Back to previous view", "arrow-left", "back"], ["Forward", "Forward to next view", "arrow-right", "forward"], ["Pan", "Left button pans, Right button zooms\nx/y fixes axis, CTRL fixes aspect", "arrows", "pan"], ["Zoom", "Zoom to rectangle\nx/y fixes axis", "square-o", "zoom"], ["Download", "Download plot", "floppy-o", "save_figure"]]}}, "df649aca8630420ab9fa551389bba4ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63464999d97f4d75841657c57dd6eebf": {"model_module": "jupyter-mat<PERSON><PERSON>lib", "model_name": "MPLCanvasModel", "model_module_version": "^0.11", "state": {"_cursor": "pointer", "_data_url": "data:image/png;base64,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", "_dom_classes": [], "_figure_label": "Figure", "_image_mode": "full", "_message": "", "_model_module": "jupyter-mat<PERSON><PERSON>lib", "_model_module_version": "^0.11", "_model_name": "MPLCanvasModel", "_rubberband_height": 0, "_rubberband_width": 0, "_rubberband_x": 0, "_rubberband_y": 0, "_size": [500, 500], "_view_count": null, "_view_module": "jupyter-mat<PERSON><PERSON>lib", "_view_module_version": "^0.11", "_view_name": "MP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capture_scroll": false, "footer_visible": true, "header_visible": true, "layout": "IPY_MODEL_7724866db7a345f88a934d089b65ebba", "pan_zoom_throttle": 33, "resizable": true, "toolbar": "IPY_MODEL_5df18887aa444cb08371cea841f9d1b9", "toolbar_position": "left", "toolbar_visible": "fade-in-fade-out"}}}}}, "nbformat": 4, "nbformat_minor": 0}