{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Basic trigonometry\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If two right triangles (triangles with an angle of $90^o$ ($\\pi/2$ radians)) have equal acute angles, they are similar, so their side lengths are proportional.  \n", "These proportionality constants are the values of $\\sin\\theta$, $\\cos\\theta$, and  $\\tan\\theta$.  \n", "Here is a geometric representation of the main [trigonometric functions](http://en.wikipedia.org/wiki/Trigonometric_function) of an angle $\\theta$:  \n", "<br>\n", "<figure><img src='http://upload.wikimedia.org/wikipedia/commons/thumb/1/11/Academ_Base_of_trigonometry.svg/300px-Academ_Base_of_trigonometry.svg.png' alt='Main trigonometric functions'/><figcaption><center><i>Figure. Main trigonometric functions (<a href=\"http://en.wikipedia.org/wiki/Trigonometric_function\">image from Wikipedia</a>).</i></center></figcaption></figure>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON>dian\n", "\n", "An arc of a circle with the same length as the radius of that circle corresponds to an angle of 1 radian:  \n", "<br>\n", "<figure><img src='http://upload.wikimedia.org/wikipedia/commons/thumb/3/3d/Radian_cropped_color.svg/220px-Radian_cropped_color.svg.png' width=200/><figcaption><center><i>Figure. Definition of the radian (<a href=\"https://en.wikipedia.org/wiki/Radian\">image from Wikipedia</a>).</i></center></figcaption></figure>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Common trigonometric values\n", "\n", "<table>\n", "<tr>\n", "<th style=\"text-align: center; background-color:#FBFBEF\">$\\;\\theta\\;(^o)$</th>\n", "<th style=\"text-align: center; background-color:#FBFBEF\">$\\;\\theta\\;(rad)$</th>\n", "<th style=\"text-align: center; background-color:#FBFBEF\">$\\;\\sin \\theta\\;$</th>\n", "<th style=\"text-align: center; background-color:#FBFBEF\">$\\;\\cos \\theta\\;$</th>\n", "<th style=\"text-align: center; background-color:#FBFBEF\">$\\;\\tan \\theta\\;$</th>\n", "</tr>\n", "<tr>\n", "<td style=\"text-align: center\">$0^o$</td>\n", "<td style=\"text-align: center\">$0$</td>\n", "<td style=\"text-align: center\">$0$</td>\n", "<td style=\"text-align: center\">$1$</td>\n", "<td style=\"text-align: center\">$0$</td>\n", "</tr>\n", "<tr>\n", "<td style=\"text-align: center\">$30^o$</td>\n", "<td style=\"text-align: center\">$\\pi/6$</td>\n", "<td style=\"text-align: center\">$1/2$</td>\n", "<td style=\"text-align: center\">$\\sqrt{3}/2$</td>\n", "<td style=\"text-align: center\">$1\\sqrt{3}$</td>\n", "</tr>\n", "<tr>\n", "<td style=\"text-align: center\">$45^o$</td>\n", "<td style=\"text-align: center\">$\\pi/4$</td>\n", "<td style=\"text-align: center\">$\\sqrt{2}/2$</td>\n", "<td style=\"text-align: center\">$\\sqrt{2}/2$</td>\n", "<td style=\"text-align: center\">$1$</td>\n", "</tr>\n", "<tr>\n", "<td style=\"text-align: center\">$60^o$</td>\n", "<td style=\"text-align: center\">$\\pi/3$</td>\n", "<td style=\"text-align: center\">$\\sqrt{3}/2$</td>\n", "<td style=\"text-align: center\">$1/2$</td>\n", "<td style=\"text-align: center\">$\\sqrt{3}$</td>\n", "</tr>\n", "<tr>\n", "<td style=\"text-align: center\">$90^o$</td>\n", "<td style=\"text-align: center\">$\\pi/2$</td>\n", "<td style=\"text-align: center\">$1$</td>\n", "<td style=\"text-align: center\">$0$</td>\n", "<td style=\"text-align: center\">$\\infty$</td>\n", "</tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Trigonometric identities\n", "\n", "Some of the main trigonometric identities are (see a [complete list at Wikipedia](http://en.wikipedia.org/wiki/List_of_trigonometric_identities)):\n", "\n", "$$ \\sin^2{\\alpha} + \\cos^2{\\alpha} = 1 $$\n", "\n", "$$ \\sin(2\\alpha) = 2\\sin{\\alpha} \\cos{\\alpha} $$\n", "\n", "$$ \\cos(2\\alpha) = \\cos^2{\\alpha} - \\sin^2{\\alpha} $$\n", "\n", "$$ \\sin(\\alpha \\pm \\beta) = \\sin{\\alpha} \\cos{\\beta} \\pm \\cos{\\alpha} \\sin{\\beta} $$\n", "\n", "$$ \\cos(\\alpha \\pm \\beta) = \\cos{\\alpha} \\cos{\\beta} \\mp \\sin{\\alpha} \\cos{\\beta} $$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- [Trigonometric functions [Wikipedia]](http://en.wikipedia.org/wiki/Trigonometric_function).\n", "- [Trigonometry [S.O.S. Mathematics]](http://www.sosmath.com/trig/trig.html)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}