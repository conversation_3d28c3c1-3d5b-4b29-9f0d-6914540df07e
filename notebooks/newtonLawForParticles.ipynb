{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZhJwhjmOM8ie", "slideshow": {"slide_type": "slide"}}, "source": ["# Motion of a particle - Newtonian approach\n", "\n", "> <PERSON><PERSON>, <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1><br>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Study-of-motion\" data-toc-modified-id=\"Study-of-motion-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Study of motion</a></span></li><li><span><a href=\"#The-development-of-the-laws-of-motion-of-bodies\" data-toc-modified-id=\"The-development-of-the-laws-of-motion-of-bodies-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>The development of the laws of motion of bodies</a></span></li><li><span><a href=\"#Newton's-laws-of-motion\" data-toc-modified-id=\"Newton's-laws-of-motion-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Newton's laws of motion</a></span></li><li><span><a href=\"#Steps-to-find-the-motion-of-a-particle\" data-toc-modified-id=\"Steps-to-find-the-motion-of-a-particle-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Steps to find the motion of a particle</a></span><ul class=\"toc-item\"><li><span><a href=\"#Example-1:-Ball-kicked-into-the-air\" data-toc-modified-id=\"Example-1:-Ball-kicked-into-the-air-5.1\"><span class=\"toc-item-num\">5.1&nbsp;&nbsp;</span>Example 1: Ball kicked into the air</a></span><ul class=\"toc-item\"><li><span><a href=\"#Analytical-solution\" data-toc-modified-id=\"Analytical-solution-5.1.1\"><span class=\"toc-item-num\">5.1.1&nbsp;&nbsp;</span>Analytical solution</a></span><ul class=\"toc-item\"><li><span><a href=\"#Plot\" data-toc-modified-id=\"Plot-5.1.1.1\"><span class=\"toc-item-num\">5.1.1.1&nbsp;&nbsp;</span>Plot</a></span></li></ul></li><li><span><a href=\"#Numerical-solution\" data-toc-modified-id=\"Numerical-solution-5.1.2\"><span class=\"toc-item-num\">5.1.2&nbsp;&nbsp;</span>Numerical solution</a></span><ul class=\"toc-item\"><li><span><a href=\"#Plot\" data-toc-modified-id=\"Plot-5.1.2.1\"><span class=\"toc-item-num\">5.1.2.1&nbsp;&nbsp;</span>Plot</a></span></li></ul></li></ul></li><li><span><a href=\"#Example-2:-Ball-kicked-into-the-air-considering-the-air-drag\" data-toc-modified-id=\"Example-2:-Ball-kicked-into-the-air-considering-the-air-drag-5.2\"><span class=\"toc-item-num\">5.2&nbsp;&nbsp;</span>Example 2: Ball kicked into the air considering the air drag</a></span><ul class=\"toc-item\"><li><span><a href=\"#Analytical-solution\" data-toc-modified-id=\"Analytical-solution-5.2.1\"><span class=\"toc-item-num\">5.2.1&nbsp;&nbsp;</span>Analytical solution</a></span><ul class=\"toc-item\"><li><span><a href=\"#Plot\" data-toc-modified-id=\"Plot-5.2.1.1\"><span class=\"toc-item-num\">5.2.1.1&nbsp;&nbsp;</span>Plot</a></span></li></ul></li><li><span><a href=\"#Numerical-solution\" data-toc-modified-id=\"Numerical-solution-5.2.2\"><span class=\"toc-item-num\">5.2.2&nbsp;&nbsp;</span>Numerical solution</a></span><ul class=\"toc-item\"><li><span><a href=\"#Plot\" data-toc-modified-id=\"Plot-5.2.2.1\"><span class=\"toc-item-num\">5.2.2.1&nbsp;&nbsp;</span>Plot</a></span></li></ul></li></ul></li><li><span><a href=\"#Example-3:-Ball-kicked-into-the-air-considering-the-air-drag-proportional-to-square-of-speed\" data-toc-modified-id=\"Example-3:-Ball-kicked-into-the-air-considering-the-air-drag-proportional-to-square-of-speed-5.3\"><span class=\"toc-item-num\">5.3&nbsp;&nbsp;</span>Example 3: Ball kicked into the air considering the air drag proportional to square of speed</a></span><ul class=\"toc-item\"><li><span><a href=\"#All-numerical-solutions-plotted-together\" data-toc-modified-id=\"All-numerical-solutions-plotted-together-5.3.1\"><span class=\"toc-item-num\">5.3.1&nbsp;&nbsp;</span>All numerical solutions plotted together</a></span></li></ul></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-internet\" data-toc-modified-id=\"Video-lectures-on-the-internet-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Video lectures on the internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Study of motion\n", "\n", "In Mechanics we are interested in the study of motion (including deformation) and forces (and the relation between them) of anything in nature.  \n", "\n", "As a good rule of thumb, we model the phenomenon of interest as simple as possible, with just enough complexity to understand the phenomenon. \n", "\n", "For example, we could model a person jumping as a particle (the center of gravity, with no size) moving in one direction (the vertical) if all we want is to estimate the jump height and relate that to the external forces to the human body. So, mechanics of a particle might be all we need.  \n", "However, if the person jumps and performs a somersault, to understand this last part of the motion we have to model the human body as one of more objects which displaces and rotates in two or three dimensions. In this case, we would need what is called mechanics of rigid bodies.\n", "\n", "If, besides the gross motions of the segments of the body, we are interested in understanding the deformation in the the human body segments and tissues, now we would have to describe the mechanical behavior of the body (e.g., how it deforms) under the action of forces. In this case we would have to include some constitutive laws describing the mechanical properties of the body."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The development of the laws of motion of bodies  \n", "\n", "\"The theoretical development of the laws of motion of bodies is a problem of such interest and importance that it has engaged the attention of all the most eminent mathematicians since the invention of dynamics as a mathematical science by <PERSON>, and especially since the wonderful extension which was given to that science by <PERSON>.\"\n", "\n", "&#8212; <PERSON>, 1834 (<PERSON><PERSON><PERSON>, 2005).  \n", "\n", "**Let's start with the study of the forces and motion in Mechanics looking at the motion of a particle using the Newtonian approach.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Newton's laws of motion\n", "\n", "The Newton's laws of motion describe the relationship between the forces acting on a body and the resultant linear motion due to those forces:\n", "\n", "- **First law**: An object will remain at rest or in uniform motion in a straight line unless an external force acts on the body.\n", "- **Second law**: The acceleration of an object is directly proportional to the net force acting on the object and inversely proportional to the mass of the object: $\\vec{\\bf{F}} = m \\vec{\\bf{a}}$.\n", "- **Third law**: Whenever an object exerts a force $\\vec{\\bf{F}}_1$ (action) on a second object, this second object simultaneously exerts a force $\\vec{\\bf{F}}_2$ on the first object with the same magnitude but opposite direction (reaction): $\\vec{\\bf{F}}_2 = −\\vec{\\bf{F}}_1.$\n", "\n", "These three statements are astonishing in their simplicity and how much of knowledge they empower.   \n", "<PERSON> was born in 1943 and his works that resulted in these equations and other discoveries were mostly done in the years of 1666 and 1667, when he was only 24 years old!  "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "rnbQefDbM8iq", "lang": "en", "slideshow": {"slide_type": "slide"}}, "source": ["## Steps to find the motion of a particle\n", "\n", "1. Draw a free body diagram of the particle. Draw all the forces being applied to the particle.  \n", "2. Write the expression of each force applied to the particle. For external forces (for example gravity and air friction) write the constitutive laws of the phenomena.  \n", "3. Write the Newton's second Law $\\vec{\\bf{F}} = m \\vec{\\bf{a}}$, where $\\vec{\\bf{F}}$ is the sum of all forces applied to the particle and $\\vec{\\bf{a}}$ is the particle acceleration.  \n", "4. Separate the equation into the 3 cartesian components (or 2 components if the movement is bidimensional).  \n", "5. Solve the differential equations\n", " 1. If possible, solve the differential equations analytically.  \n", " 2. If not possible to solve the differential equations analytically, separate each equation into 2 first order differential equations and use some numerical method (e.g. <PERSON><PERSON><PERSON>, Runge-Ku<PERSON>) to solve the first order differential equations with the aid of a computer.   \n", "6. Use the solution to interpret the situation, or to find some error on your approach.  \n", "\n", "**Later, we will study in details how to draw a free-body diagram.**  \n", "Let's see now some examples on how to find the motion of a particle"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HlV8bOoJM8ir", "slideshow": {"slide_type": "slide"}}, "source": ["### Example 1: <PERSON> kicked into the air\n", "\n", "#### Analytical solution\n", "\n", "A football ball is kicked with an angle of 30 degrees with the ground (horizontal).  \n", "The mass of the ball is 0.43 kg. The initial speed of the ball is 20 m/s and the initial height is 0 m. Consider the gravitational acceleration as 9.81 $m/s^2$.  \n", "Find the motion of the ball. \n", "\n", "Solution:  \n", "We know that:  \n", "<span class=\"notranslate\">\n", "$$ x_0 = 0 m \\\\ y_0 = 0 m $$  \n", "</span>\n", "As the angle of the initial velocity of the ball with the ground is 30 degrees:  \n", "<span class=\"notranslate\">\n", "$$ v_{x0} = 20 \\cos(30^\\circ) = 20\\frac{\\sqrt{3}}{2} = 10\\sqrt{3} m/s \\\\\n", "v_{y0} = 20 \\sin(30^\\circ) = 20 \\frac{1}{2} = 10 m/s $$  \n", "</span>\n", "The free-body diagram of the ball is depicted below:\n", "   \n", "<figure><center><img src=\"../images/ballGrav.png\" alt=\"free-body diagram of a ball\" width=\"500\"/><figcaption><i>Figure. Free-body diagram of a ball under the influence of gravity.</i></figcaption></center></figure>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nOkZSRWyM8it", "slideshow": {"slide_type": "slide"}}, "source": ["The only force acting on the ball is the gravitational force:\n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{F}}_g = -mg \\; \\hat{\\bf{j}} $$ \n", "</span>\n", "So, we apply the Newton's second law:\n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{F}}_g = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\quad \\rightarrow \\quad - mg \\; \\hat{\\bf{j}} = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\quad \\rightarrow \\quad - g \\; \\hat{\\bf{j}} = \\frac{d^2\\vec{\\bf{r}}}{dt^2} $$\n", "</span>\n", "Now, we can separate the equation in two components (x and y):\n", "<span class=\"notranslate\">\n", "$$ 0 = \\frac{d^2x}{dt^2} $$\n", "</span>\n", "and\n", "<span class=\"notranslate\">\n", "$$ -g = \\frac{d^2y}{dt^2} $$\n", "</span>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "%matplotlib inline\n", "sns.set_context('notebook', font_scale=1.2)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "h_GGqUuXM8it", "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["m   = 0.43           # [kg]\n", "x0  = 0              # [m]\n", "y0  = 0              # [m]\n", "vx0 = 10*np.sqrt(3)  # [m/s]\n", "vy0 = 10             # [m/s]\n", "g   = 9.81           # [m/s^2]"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "KlXsUdmkM8iy", "lang": "en", "slideshow": {"slide_type": "slide"}}, "source": ["These equations can be easily solved  by integrating both sides of each equation:\n", "<span class=\"notranslate\">\n", "$$ 0 = \\frac{d^2x}{dt^2} \\quad \\rightarrow v_{x0} = \\frac{dx}{dt} \\quad \\rightarrow \\quad v_{x0}t + x_{0} = x(t) $$\n", "</span>\n", "and\n", "<span class=\"notranslate\">\n", "$$ -g = \\frac{d^2y}{dt^2} \\quad \\rightarrow - gt + v_{y0} = \\frac{dy}{dt} \\quad \\rightarrow \\quad -\\frac{g}{2}t^2 +v_{y0}t + y_{0} = y(t) $$\n", "</span>\n", "So the trajectory of the ball along time is:\n", "<span class=\"notranslate\">\n", "$$ x(t) = v_{x0}t $$\n", "</span>\n", "and \n", "<span class=\"notranslate\">\n", "$$ y(t) =  -\\frac{g}{2} t^2 + v_{y0}t $$\n", "</span>\n", "or \n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{r}}(t) =  v_{x0}t \\; \\hat{\\bf{i}} + \\left(-\\frac{g}{2} t^2 + v_{y0}t \\right) \\; \\hat{\\bf{j}} $$\n", "</span>"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "03OWHJ_lM8iy", "outputId": "1f99c1f4-d870-4738-b25a-0d167b3eac84", "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dt = 0.001  # [s]\n", "t  = np.arange(0, 2.05, dt)\n", "x1a = vx0*t\n", "y1a = -g/2*t**2 + vy0*t\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(x1a, y1a, lw=4)\n", "plt.xlim(0, 36)\n", "plt.ylim(0, 6)\n", "plt.title('Ball trajectory')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XSH7-IM-M8i2", "slideshow": {"slide_type": "slide"}}, "source": ["#### Numerical solution\n", "\n", "We can solve this problem numerically and compare both solutions.\n", "\n", "We start from the differential equations of each coordinate, and then break them into two first-order differential equations:\n", "<span class=\"notranslate\">\n", "$$ 0 = \\frac{d^2x}{dt^2} $$\n", "</span>\n", "and\n", "<span class=\"notranslate\">\n", "$$ - g = \\frac{d^2y}{dt^2} $$\n", "</span>\n", "The first equation can be broken as:\n", "<span class=\"notranslate\">\n", "$$ \\frac{dv_x}{dt} = 0 $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dx}{dt} = v_x(t) $$\n", "</span>\n", "And the second equation can be broken as:\n", "<span class=\"notranslate\">\n", "$$ \\frac{dv_y}{dt} = -g $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dy}{dt} = v_y(t) $$\n", "</span>\n", "You can use any numerical integration method you want (Euler, Runge-Kutta, etc), but here we will use the Euler method. Let's see the solution for the $x(t)$ variable.\n", "\n", "The derivative of $x(t)$ is given by:\n", "<span class=\"notranslate\">\n", "$$ \\frac{dx}{dt} = \\lim\\limits_{\\Delta t \\rightarrow 0} \\frac{x(t+\\Delta t) - x(t)}{\\Delta t} $$\n", "</span>\n", "Whcih can be approximated by: \n", "<span class=\"notranslate\">\n", "$$ \\frac{dx}{dt} \\approx \\frac{x(t+\\Delta t) - x(t)}{\\Delta t} \\quad \\rightarrow \\quad x(t+\\Delta t) \\approx x(t) + \\Delta t \\frac{dx}{dt} $$\n", "</span>\n", "So, with the initial conditions of all the variables, we can apply the equation above to find the values of the variables along time (for a revision of Ordinary Differential Equations, see the notebook [Ordinary Differential Equation](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/OrdinaryDifferentialEquation.ipynb)).  \n", "In the cell below, we apply the <PERSON><PERSON><PERSON> method for the four first-order differential equations."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "dMNLst17M8i4", "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["x  = x0\n", "y  = y0\n", "vx = vx0\n", "vy = vy0\n", "r  = np.array([x, y])\n", "while y >= 0:\n", "    dxdt = vx\n", "    x = x + dt * dxdt\n", "    dydt = vy\n", "    y = y + dt * dydt\n", "    dvxdt = 0\n", "    vx = vx + dt * dvxdt\n", "    dvydt = -g\n", "    vy = vy + dt * dvydt\n", "    r = np.vstack((r, np.array([x,y])))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {}, "colab_type": "code", "id": "RpDwAX9dM8i7", "outputId": "1b690a23-0e59-4ba0-9cc3-cda7f5b4e967", "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 4))\n", "x1n = r[:, 0]\n", "y1n = r[:, 1]\n", "plt.plot(x1n, y1n, lw=4)\n", "plt.xlim(0, 36)\n", "plt.ylim(0, 6)\n", "plt.title('Ball trajectory')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nfEL9e-tM8jA", "slideshow": {"slide_type": "slide"}}, "source": ["### Example 2: <PERSON> kicked into the air considering the air drag\n", "\n", "Now, besides the gravity, we consider the drag due to the air resistance ($b$ = 0.006 Ns/m). First we will consider the drag force  proportional to the speed and opposite direction to the velocity vector.\n", "       \n", "<figure><center><img src=\"../images/ballGravLinearRes.png\" alt=\"free-body diagram of the ball\" width=\"600\"/><figcaption><i>Figure. Free-body diagram of a ball under the influence of gravity and drag.</i></figcaption></center></figure>\n", "\n", "So the forces being applied on the ball are:\n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{F}} = -mg \\; \\hat{\\bf{j}} - b\\vec{\\bf{v}} = -mg \\; \\hat{\\bf{j}} - b\\frac{d\\vec{\\bf{r}}}{dt} = -mg \\;  \\hat{\\bf{j}} - b\\left(\\frac{dx}{dt} \\; \\hat{\\bf{i}}+\\frac{dy}{dt} \\; \\hat{\\bf{j}}\\right) = - b\\frac{dx}{dt} \\; \\hat{\\bf{i}} - \\left(mg + b\\frac{dy}{dt}\\right) \\; \\hat{\\bf{j}} $$\n", "</span>\n", "Writing down the Newton's second law:\n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{F}} = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\quad \\rightarrow \\quad - b\\frac{dx}{dt} \\; \\hat{\\bf{i}} - \\left(mg + b\\frac{dy}{dt}\\right) \\; \\hat{\\bf{j}} = m\\left(\\frac{d^2x}{dt^2} \\; \\hat{\\bf{i}}+\\frac{d^2y}{dt^2} \\; \\hat{\\bf{j}}\\right) $$\n", "</span>\n", "Now, we can separate into one equation for each coordinate:\n", "<span class=\"notranslate\">\n", "$$ - b\\frac{dx}{dt} = m\\frac{d^2x}{dt^2} \\quad \\rightarrow \\quad \\frac{d^2x}{dt^2} = -\\frac{b}{m} \\frac{dx}{dt} $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ -mg - b\\frac{dy}{dt} = m\\frac{d^2y}{dt^2} \\quad \\rightarrow \\quad \\frac{d^2y}{dt^2} = -\\frac{b}{m}\\frac{dy}{dt} - g $$ \n", "</span>\n", "We can solve these equations analytically, for example, by using Laplace Transform or classical methods to solve linear differential equations."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "x41TfRSQM8jB", "slideshow": {"slide_type": "slide"}}, "source": ["#### Analytical solution\n", "\n", "The solution of a linear differential equation can be found by finding the natural (or homogeneous) solution and the forced (or non-homogeneous) solution and then adding both solutions.\n", "\n", "First, we solve the first differential equation ($x$ coordinate). The characteristic polynomial of the equation is:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\lambda^2 + \\frac{b}{m}\\lambda = 0 $$ \n", "</span>\n", "\n", "The roots of this equation are $\\lambda = 0$ and $\\lambda = -\\frac{b}{m}$, and consequently, its natural modes are:\n", "\n", "<span class=\"notranslate\">\n", "$$ x_{n_1}(t) = Ae^{0t} = A \\\\\n", "x_{n_2}(t) = B e^{-\\frac{b}{m}t} $$ \n", "</span>\n", "\n", "As there is no external forces in the $x$ direction, there is no forced solution. So, the motion of the ball in the $x$ coordinate is:\n", "\n", "<span class=\"notranslate\">\n", "$$ x(t) = A + Be^{-\\frac{b}{m}t} $$\n", "</span>\n", "\n", "To find the values of the $A$ and $B$ constants, we must use the initial conditions $x(0)$ and $v_x(0)$.\n", "\n", "<span class=\"notranslate\">\n", "$$ x(0) = 0 = A + B $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ v_x(0) = v_{x0} = \\frac{dx(0)}{dt} = -\\frac{Bb}{m}e^{-\\frac{b}{m}0} \\quad \\rightarrow \\quad B = -\\frac{v_{x0}m}{b} \\quad \\rightarrow \\quad A = \\frac{v_{x0}m}{b} $$\n", "</span>\n", "So:\n", "<span class=\"notranslate\">\n", "$$ x(t) = \\frac{v_{x0}m}{b} - \\frac{v_{x0}m}{b}e^{-\\frac{b}{m}t} = \\frac{v_{x0}m}{b}\\left(1-e^{-\\frac{b}{m}t} \\right) $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "RqJr2O4NM8jC", "slideshow": {"slide_type": "slide"}}, "source": ["Now, we solve the differential equation for the $y$ coordinate.  \n", "First, we find the natural solution, which comprises the solution from the equation without the gravitational force (the force indepedent of y(t) and its derivatives), in this case:\n", "<span class=\"notranslate\">\n", "$$ \\frac{d^2y}{dt^2} = -\\frac{b}{m}\\frac{dy}{dt} $$\n", "</span>\n", "The solution of this equation is the same from the $x$ coordinate:\n", "<span class=\"notranslate\">\n", "$$ y_n(t) = A + Be^{-\\frac{b}{m}t} $$\n", "</span>\n", "The forced solution (including the gravitational force, which is constant) happens when every derivative, with the exception of the derivative with the lowest order (in this case order 1 but it could be order 0, i.e. no derivative) goes to zero. \n", "<span class=\"notranslate\">\n", "$$\\frac{d^2y}{dt^2} \\quad = \\quad 0 \\quad = \\quad -\\frac{b}{m}\\frac{dy_f}{dt} - g \\quad \\rightarrow \\quad \\frac{dy_f}{dt} = -\\frac{mg}{b} \\quad \\rightarrow \\quad y_f(t) = -\\frac{mg}{b}t $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "5EzIiZbUM8jE", "slideshow": {"slide_type": "slide"}}, "source": ["The complete solution of the motion of the ball in the $y$ coordinate is the sum of the natural and forced solutions:\n", "\n", "<span class=\"notranslate\">\n", "$$ y(t) = A + Be^{-\\frac{b}{m}t} - \\frac{mg}{b}t $$\n", "</span>\n", "\n", "To find the values of the constants A and B, we must use the initial conditions $y(0)$ and $v_y(0)$.\n", "\n", "<span class=\"notranslate\">\n", "$$ y(0) = 0 = A + B $$\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "$$ v_y(0) = v_{y0} = \\frac{dy(0)}{dt} = - \\frac{Bb}{m} - \\frac{mg}{b} \\\\\n", "B = -\\frac{m^2g}{b^2} -\\frac{v_{y0}m}{b} \\\\\n", "A = +\\frac{m^2g}{b^2} + \\frac{v_{y0}m}{b} $$\n", "</span>\n", "\n", "So, the motion of the ball in the y coordinate is:\n", "\n", "<span class=\"notranslate\">\n", "$$ y(t) = \\left(\\frac{m^2g}{b^2} + \\frac{v_{y0}m}{b}\\right) - \\left(\\frac{m^2g}{b^2} + \\frac{v_{y0}m}{b}\\right)e^{-\\frac{b}{m}t} -\\frac{mg}{b}t \\\\\n", "y(t) = \\left(\\frac{m^2g}{b^2} + \\frac{v_{y0}m}{b}\\right)\\left(1 - e^{-\\frac{b}{m}t}\\right) - \\frac{mg}{b}t $$\n", "</span>"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {}, "colab_type": "code", "id": "04ZSdYNWM8jF", "outputId": "9ea68054-1836-4455-f488-59e61bb184db", "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArEAAAGXCAYAAACkxwibAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/P9b71AAAACXBIWXMAAA9hAAAPYQGoP6dpAABbP0lEQVR4nO3dd3QUZdsG8Guzu+k9JKGkEQgJgRBCCb0KijQDxAACUkSaBXhtoCBFUVREVEBBQMAGiIAoCoLSkQ6hBQiEVEjvfbM73x9IPnFn00hmS67fOTni3LObO3OGcO3MM88jEwRBABERERGRETHTdwNERERERNXFEEtERERERochloiIiIiMDkMsERERERkdhlgiIiIiMjoMsURERERkdBhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0QkQiaToXfv3g9tW7hwIWQyGQ4dOqSXnqpCrG8iIlPEEEtERkUmk2l9WVhYwMfHB+PHj0dUVJS+Wyw3YcIEyGQyxMbG6ruVOhMbGwuZTIYJEybouxUiqmcU+m6AiKgmFixYUP7nnJwcnD59Gps3b8ZPP/2EY8eOoW3btvprTo+ioqJgbW2t7zaIiOocQywRGaWFCxdqbXvppZewcuVKrFixAhs3bpS8J0MQEBCg7xaIiCTB4QREZDIef/xxAEBaWtpD23NycvDRRx+hb9++8PDwgLm5OVxdXTF06FCcOHGiTnqRyWTYtGkTAKBp06blQx98fHzK9+nduzdkMhlKSkrw9ttvw8/PD+bm5uW35mvSt64xsWVlZVi9ejU6d+4Me3t7WFtbIyQkBCtXroRGoxF9r9OnT2PkyJFo0qQJLCws0KhRIzz++OPYtm0bgPsfJJo2bQoA2LRp00NDPP79IUKj0WD16tXo2LEjbG1tYWNjgw4dOmD16tWi3/vBz3D37l1MnDgRjRo1glwux8aNGzFq1CjIZDIcOXJEtOft27dDJpPhpZdeEq0TkenglVgiMhkHDhwAAISGhj60PSoqCm+99RZ69uyJQYMGwcnJCXFxcfj555/x22+/Yffu3Rg4cGCt9rJgwQLs2rULkZGRmDlzJhwdHQGg/L//NmLECJw9exZPPvkkwsLC4O7uXqt9q1QqDBkyBPv27UNAQACeeeYZWFpa4uDBg3jppZdw8uRJfPvttw+95quvvsL06dMhl8sxdOhQ+Pn5ITU1FWfOnMHq1asRERGB3r17Izs7G59++imCg4MRFhZW/vp/D+d45plnsHXrVnh5eWHy5MmQyWTYuXMnXnjhBRw5cgRbtmzR6jkjIwNdunSBnZ0dwsPDIQgC3NzcMGPGDGzduhVr1qxBz549tV63Zs0aAMCUKVMqPS5EZOQEIiIjAkAAICxYsKD8a/bs2UL37t0FmUwmPPXUU0JeXt5Dr8nOzhbS0tK03is2NlZwd3cX/P39Rb9Pr169Htq2YMECAYBw8ODBKvU6fvx4AYBw584d0XqvXr0EAEJQUJBof7Xd98yZM4WysrLy7WVlZcKkSZMEAMLOnTvLt1+9elVQKBSCk5OTcOXKFa3vER8fX/7nO3fuCACE8ePHi/6M3333nQBA6NChg5Cfn1++PT8/X2jXrp0AQPj222+1fgYAwrhx4wSVSqX1nq1btxYsLCyE9PT0h7bfunVLkMlkQteuXUV7ISLTwuEERGSUFi1aVP71ySef4NixY2jZsiXCw8Nha2v70L4ODg5o0KCB1nt4e3vj6aefxo0bNxAfHy9V61oWL14s2l9t9K3RaLBy5Uo0atQIH3/8MeRyeXlNLpfj448/hkwmw3fffVe+/YsvvkBZWRnmz5+PVq1aab2np6dnlX+2DRs2AADef/992NjYlG+3sbHB0qVLAQDr16/Xep25uTmWLVsGhUL7huH06dNRUlJSPlzjgbVr10IQBEybNq3K/RGR8eJwAiIySoIglP+5oKAAV69exZw5czBu3DhERUVhyZIlD+1//PhxfPrpp/j777+RmpqK0tLSh+pJSUnw8vKSpPf/6tSpk87ao/Z98+ZNZGRkwM/PD++8847oPlZWVrh+/Xr5/588eRIA8OSTT1bnxxB14cIFmJmZoVevXlq1Pn36QC6X4/z581o1Hx8fuLm5ib7nuHHj8MYbb2Dt2rX43//+BwAoLS3Fxo0b4ezsjKeffvqR+yYiw8cQS0RGz8bGBqGhodixYwc8PDzw4YcfYtq0aeVXDHfu3Inw8HBYWlqif//+aNasGWxsbGBmZoZDhw7h8OHDKCkp0Vv/DRs2FN1eG31nZGQAAKKjo7Fo0SKd++Xn55f/OTs7GwDQpEmTav4k2nJycuDs7AylUqlVUygUaNCgAVJTU7Vquo4JANjZ2WHs2LH48ssvcfjwYfTq1Qs7d+5EamoqZs+eDUtLy0fum4gMH0MsEZkMR0dH+Pv74/z58zh//nx5iJ0/fz7Mzc1x9uxZtGzZ8qHXTJ06FYcPH9ZHu+VkMpno9tro28HBAQAwbNgw7Nixo0r9PHj4LCkp6ZGn7HJwcEBmZiZUKpVWkC0rK0N6ejrs7e21XqfrmDwwY8YMfPnll1izZg169erFB7qI6iGOiSUik5KVlQUAD03ddOvWLQQGBmoFQY1Gg2PHjtVZLw/Gn6rV6hq9vjb6DggIgKOjI06ePAmVSlWl13Tu3BkAsG/fvkr3rexnDAkJgUajEZ0S68iRI1Cr1WjXrl2V+vq3oKAgdO/eHTt27MCJEydw6NAh9OrVi/PkEtUjDLFEZDJ27dqFO3fuQKlUomvXruXbfXx8EB0djaSkpPJtgiBg0aJFuHbtWp314+LiAgBISEio0etro2+FQoGXXnoJ9+7dw8svv4yioiKtfe7du/fQ+02fPh0KhQKLFy9+aKzsA4mJieV/dnJygkwm0/kzTpo0CQAwd+5cFBYWlm8vLCzEnDlzAADPPfdclX6W/5oxYwZKSkrKp+DiA11E9QuHExCRUfr3il0FBQW4du0afv/9dwDAe++9Vz7XKgDMnj0b06ZNQ7t27TBixAgolUocP34c165dw5AhQ/DLL7/USY+PPfYYPvroIzz//PMYMWIEbG1t4ejoiBdffLFKr6+tvufPn4/IyEh8+eWX+OWXX9C3b180adIEqampiI6OxvHjx7FkyRIEBgYCAAIDA7F69WpMmzYNbdu2LZ8nNj09HWfOnIGDgwMOHjwIALC1tUWnTp1w5MgRjB07Fn5+fuVzy7Zp0wbPPPMMfv75Z2zbtg2tWrVCWFgYZDJZ+QeOiIgIjBkzpkbHd8SIEXBzc8O9e/fg6uqK4cOH1+h9iMhI6XWCLyKiasI/c4j++0sulwsNGzYUhg4dKvzxxx+ir/v666+F4OBgwdraWnBxcRHCwsKES5cu6Zz7FbUwT6wgCMLHH38sBAQECObm5gIAwdvbu7z2YJ7YitRG34IgCBqNRti8ebPQt29fwcnJSVAqlULjxo2Fbt26CUuWLHlo7tcHTpw4IQwfPlxwdXUVlEql0KhRI+GJJ54Qfvzxx4f2i46OFgYPHiw4OzsLMplMACB8/fXX5XW1Wi2sWrVKaN++vWBlZSVYWVkJ7dq1E1auXCmo1Wqt76vrZxAze/ZsAYDw+uuvV2l/IjIdMkH41zw1RERktIqLi2FlZYXHH3+8SuNZTUHPnj1x7Ngx3Lx5E82bN9d3O0QkIY6JJSIyETdu3ABQvcUIjNnJkydx9OhRDBgwgAGWqB4y2BCbm5uLefPmoWXLlrCysoKzszM6deqktb43EVF9FxsbizfeeKN8kv/w8HA9d1S3Vq5ciTlz5mDYsGGQy+VYvHixvlsiIj0wyAe7kpKS0KdPH6SlpWHixIlo1aoVCgoKcPPmTcTFxem7PSIigxIbG4sVK1agefPmWL9+PQYMGKDvlurUsmXLkJiYiObNm+PTTz9Fhw4d9N0SEemBQY6Jfeyxx3D16lWcOnUK3t7e+m6HiIiIiAyMwQ0nOH78OP766y+88cYb8Pb2hlqtfmg5RCIiIiIigwuxe/bsAQD4+fkhIiICVlZWsLOzQ+PGjfHuu+/WeOUbIiIiIjIdBjcmNioqCsD9FVx8fHywbt06yGQyrF69GvPnz0d8fDzWrl0r+lpfX1+d7xsbGwsLCws0atSoTvomIiIiokdz7949WFhYIDs7u9J9DS7E5uXlAQCsra1x5MgRWFhYAABGjhyJwMBArFu3Dq+88gr8/f2r9b6CIKCsrKzW+yUiIiKi2lGdrGZwIdbKygoA8Mwzz5QHWAAwNzfHmDFjsHjxYhw8eFA0xMbExOh83wdXaSvah4iIiIj0p6K76v9lcGNiH0zSLXbb/8G2zMxMSXsiIiIiIsNicCG2c+fOAICEhAStWnx8PADA3d1d0p6IiIiIyLAY3DyxOTk58PHxgaWlJa5fvw4HBwcA98fKBgQEIC0tDbdv3672soocTkBERERk2KqT1wxuTKyDgwM+/fRTjB8/HqGhoZg8eTJkMhnWr1+Pu3fvYsmSJfVmXXAiIiIiEmdwIRYAnn32Wbi6umLp0qVYtGgRNBoNgoKC8MMPP2DUqFH6bo+IiIiI9MzghhPUFQ4nICIiIjJs1clrBvdgFxERERFRZRhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRERERGR2GWCIiIiIyOgyxRERERGR0GGKJiIiIyOgwxBIRERGR0WGIJSIiIiKjwxBLREREREaHIZaIiIiIjA5DLBEREREZHYZYIiIiIjI6DLFEREREZHQYYomIiIjI6DDEEhEREZHRYYglIiIiIqPDEEtERERERochloiIiIiMDkMsERERERkdhlgiIiIiMjoMsURERERkdBhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRERERGR2GWCIiIiIyOgyxRERERGR0GGKJiIiIyOgwxBIRERGR0WGIJSIiIiKjwxBLREREREaHIZaIiIiIjI5C3w2IkclkOmt5eXmwtbWVsBsiouorU2uQXaRCdqEKOUWlyCpQ/fP/pSgqVaO4TI2iUg2KVGoUq9QoKVNDrREgCIAAQBCA+38CzBVmsFDIYaEwu/+llMNSYQY7SyXsrRRwsFLC3koJe0slHK2VcLWzgIVCrs8fn4iozhlkiAWAHj16YMqUKVrbLS0t9dANEdH/EwQBGQWliMsoQFxGIe7lFONeThGSc0qQkluM5NxipOeX/BNE9cPBSgl3ewu42VnCzc4C7g6W8HK2Lv9q5GAJhZw344jIeBlsiPX19cXYsWP13QYR1WMqtQZ30gtwPTkPN5PzcCej4H5wTS9EXkmZvturUE6RCjlFKtxMyRety81kaOJoBW8Xa/i52aGFuy383O3g524Le0ulxN0SEVWfwYZYACgtLUVJSQns7Oz03QoRmbi8YhUuJ+YgMjEH15NzcSM5D7fT8qFS6/Fyah1SawTEZxYiPrMQR6PTH6o1crBEC3c7BDVxQJCHA9p4OKChvWWFQ72IiKQmEwR93vASJ5PJYGNjg+LiYqjVajg7O2PYsGFYsmQJ3N3da/Sevr6+AICYmJjabJWIjFCZWoNr93JxIT4bkYnZiEzIRkx6gV5v/xu6BrbmCGrigDYejujo44wQL0fYWBj0dRAiMkLVyWsGGWJDQ0MRHh6OFi1aID8/H/v378c333wDLy8vnDp1SmeQffCDi0lISICnpydDLFE9VFKmxuXEHJy6k4lTdzJxLjYTBaVqfbcFALBQmMHKXA5LhRxys/tXOmWyf74ggwABqjIBJWVqlJZpUFKmQZlG/7+25WYytGpsj44+zujo44SOPs5wsbXQd1tEZOSMPsSK+eKLLzBjxgxMnz4dq1evFt2HIZaIgPsPXkXdy8OR6DQcuZmGc3FZKCnT1On3lMkAV1sLNHSwhLu9JRraW8LVzgJO1ko4WJvD0UoJJ2tzOForYWOhgJXy/mwDZmbVv0Vfpr4/q0FucRly/xn7mlOkQm6RChkFpUjNLUFqXjFS80qQmluMlNwSFKnqPrQHNrJHjxYN0KO5Kzr4OMFSyRkSiKh6TDLEAoCbmxusrKwQFxdX7ddyOAGRacssKMXR6DQcvpmGo9HpSMsrqfXvYW0uh4+LDXwaWMPbxQY+Lvf/6+lsDTc7CygN9Gl/QRCQXahCfGYh4jILkZBZiPiMQsRlFuBWagHS82v/WFkozBDa1Bm9WriiX0t3+DSwqfXvQUSmx2RDbMeOHXH16lUUFhZW+7UMsUSmJy6jAPuvpeCPayk4G5uJ2rrLrjCToZmrLfwb2t3/cr//Xw8nK5N8uCmroBQ3U/JwMzUf0Sl5uJ6ch2t3c5FfizMwNHezRb+W7ugf6Ia2nk7lQyeIiP7NJEOsRqOBq6srHBwcahREGWKJjJ8gCLh6Nxf7ribjj6spuJGSVyvv28zVBsGejgj2cESwpyNaNrKr94sFaDQC7mQU4HJiDi4n5ZT/tzaGJTSwNUe/lu4Y3KYxOvs6c75aIipXnbxmcI+WpqSkiD64tXTpUmRmZmLcuHF66IqI9OlWaj5+ibyLXyLvIia94JHey1xuhraejght6ozQps5o6+XIeVFFmP1zNbqZqy3CQpoAuD9v7tW7uTgbm4nTdzJxNi4LmQWl1X7v9PxSbDmTgC1nEuBiY44BrRticJvGCG3qzCu0RFRlBncldtasWfjzzz8xePBgeHt7o7CwEPv378fevXsREBCA48ePw9nZudrvyyuxRMYlKbsIv0Texe6Ld3HtXm6N30dhJkN7byd0a94AnZo6I9jTkQ8c1RJBEHA7rQB/x2TgWHQaTtzOQF5xzYcguNpZYEibxghv74HAxva12CkRGQujHk6we/durF69GleuXEF6ejrMzMzQrFkzhIWF4fXXX6/xwgcMsUSGr1ilxr6ryfjxbCKO306v8byt3i7W6Onnip4tXNGlmQtsOZ+pJMrUGkQm5uBYdDqORqfhfHxWjccpt2xkj/D2HniqbWM04NRdRPWGUYfYusIQS2SYBEFAZGIOfjybgN2Rd2t0Jc9MBnT0cUb/QHc+CW9AsgpKcfBGKg5EpeDwjbQazc2rMJOht78bRnb0RN8ANw43IDJxDLEiGGKJDEt+SRl2XUjCtyfjcD25+g9oWSrN0MPPFY8HuuOxlu5wtjGvgy6ptpSUqXEyJhN/XE3G3ivJyKjBWNrGDpYYFeqFUR094WZvWQddEpG+McSKYIglMgw3U/Lw7ck47DifVO0pnMzlZujl74ohwY3Rr6UbrM05TMAYlak1OBmTiV8v3cXeq8nILlRV6/UKMxn6B7pjbGdvdG3mYpLTnhHVVwyxIhhiifRHrRGw72oyNp2Ixak7mdV6rZkM6Na8AYYEN8YTrRrCwYozCZgSlVqD47fSsTvyLn6/nFztKbyau9liUremGN6uCR/YIzIBDLEiGGKJpJdfUoZtZxLw9Yk7SMgsqtZrm7vZ4un2HhgW0oS3juuJ/JIy/H75Hn46n4iTMdX7sONkrcTYzt4Y19mb5wuREWOIFcEQSySdezlF2HgiFt+fiq/Wg1p2FgoMadsYT7f3QFtPR94mrscSMgux43wStp1NQFJ21T8AKeUyDAlujCk9fRHQkNN0ERkbhlgRDLFEde9Waj5WH7qF3RfvoqwacyuFeDlibCdvDAxqBCtz3hKm/6fWCDhyMw3fnozDXzdSqzXtWr+W7nihTzOEeDnVXYNEVKsYYkUwxBLVnat3c7D64G38duVelUOGpdIMYW2bYGxnb7Ru4lC3DZJJSMwqxJbT91f6Ss8vqfLrujZzwQt9mvMhMCIjwBArgiGWqPadi8vCqoO38Nf11Cq/xsvZGuO7+iC8nQccrPmQFlVfaZkGv1+5h3VH7+ByUk6VX9fW0xEzH/NDb39XhlkiA8UQK4Ihlqj2XIjPwvL9N3E0Or3Kr+no44Tnuvuif6A7J6ynWiEIAs7EZmH9sRj8cS2lyncB2nk54n/9/dGtOa/MEhkahlgRDLFEj+7a3Vws338DB6KqduVVbibDk60bYnIPX7T1dKzb5qhei88oxIbjd7D1TEKVp+kKbeqMV/q3QCdflzrujoiqiiFWBEMsUc3dSs3HJwduYs+le1Xa31xhhtEdPTG5hy88na3ruDui/5eRX4KNJ2Kx8URslWfG6N68AV4f4I82Ho512xwRVYohVgRDLFH1JecUY/n+G9h+LhFVmWzA2lyOcZ298VyPpnCz41ydpD95xSp8ezIe64/dqfJDYEOCG+O1x/3h5cIPXkT6whArgiGWqOryilVYczgG647FoFilqXR/e0sFJnRrioldfeBkYy5Bh0RVU6xSY+uZBKw6eAupeZWHWaVchrGdvfFSXz8481wmkhxDrAiGWKLKqdQabDkdjxUHopFRUFrp/rYWCkzu0RSTujeFvSVnGiDDVaxS49uTcfjy8G2k51d+bttZKDC9TzNM6taUy9kSSYghVgRDLJFugiDgz6hUvPdbFGLSCyrd30opx4RuPpjSw5dXXsmoFJaWYdOJOKw5chvZhapK9/dwssK8QS3xRKuGnMmASAIMsSIYYonE3UrNx+Jfr+HIzbRK9zWXm2FMZy/M6N0crnYWEnRHVDfyilVYd/QOvjoag8LSymcz6NrMBW8PCeRStkR1jCFWBEMs0cPyilX47M9ofH08ttIlYmUyYFhIE7zyuD+aOFpJ1CFR3UvNK8Znf0bjh9MJUFfy98BMBozt7I3Z/VrwDgRRHWGIFcEQS3SfRiPgp/OJ+GDvjSo9td29eQPMHRiAVo25NCyZrttp+fho7w3svZpc6b6O1krMGRCAiA6eMOPCHUS1iiFWBEMsERB1Lxdv7byM8/HZle7r726HuQMD0KsFl+ik+uNcXCbe+TUKFxOyK923nZcjlgwLQstGHGJAVFsYYkUwxFJ9VlhahhUHorH+2J1Kb5m62JjjtSf88XQHTy4PS/WSRiNg18UkLP39eqXTcsnNZJjY1Qez+reArYVCog6JTBdDrAiGWKqv9l9LwcLdV5GUXVThfgozGcZ39cHLj/nBwYrTZREVlJRh1cFbWHf0DkrVFc+X3NDeEguHBmJA60YSdUdkmhhiRTDEUn1zL6cIb/98FfuvpVS6bw+/BlgwJBDN3ewk6IzIuMRnFOLdPdfwRxX+Lg1o1RCLn2oFN3uuWEdUEwyxIhhiqb7QaAT8cCYe7/92HfklFa8d7+FkhbcHB6J/oDvHvRJV4uD1VLy9+woSMiu+q2FvqcC8wYF4ur0H/14RVRNDrAiGWKoP4jIK8MZPl3AyJrPC/ZRyGab09MWLffxgZc7ViIiqqqhUjVUHb2HNkdtQqSv+57OHXwO8NywIns7WEnVHZPwYYkUwxJIpU2sEfH38Dpb9cQPFqorH7oX6OGPJsNbwc+fQAaKaupWaj/m7ruDvmIwK97NSyvH6AH+M7+LD6biIqoAhVgRDLJmqW6n5eG17JC5UMm2Wk7USbw5siXDe4iSqFYJwfxaDxb9cQ1YlS9h28XXBR0+3gYcTr8oSVYQhVgRDLJkajUbApr9jsfT36ygpq/jq6/B2TTBvUCCcucoQUa3LyC/Bwl+u4ZfIuxXuZ2uhwPzBLRHRwZMfJIl0YIgVwRBLpiQpuwivbous9FZmYwdLLBkehD7+bhJ1RlR/7b+Wgnm7LiMlt+K5ZR8LcMP7w4M4gwGRCIZYEQyxZAoEQcBP55OwaPdV5FUy88DYzl54Y0AA7Cw55yuRVHKLVVj6+3V8fyq+wv0crZV4b1gQBgZxXlmif2OIFcEQS8YuI78Ec3dcrnSuSh8Xaywd0QadfV0k6oyI/uvErXS8tv1SpYuMRHTwwIIhrWDD1b6IAFQvr5nVdTNE9OiORqdhwKdHKw2wE7v54PeZPRlgifSsa/MG+H1WDzzd3qPC/badTcTgz4/hcmKORJ0RmQ5eiSUyYKVlGiz74wbWHqn4vG3iaIWPwtuga/MGEnVGRFW1/1oK5u64jPR83WNllXIZXnncH1N6+HIqLqrXOJxABEMsGZuYtHzM3HIRl5MqvkIT3t4Dbw8JhD3HvhIZrMyCUszbdRm/XU6ucL9uzV2wPKIt3PnQF9VTHE5AZMQEQcC2swn3bzFWEGBdbMyxdlx7LHs6mAGWyMA525hj1TPtsDwiGLYVjH89fisDAz89iiM30yTsjsg4McQSGZCCkjLM3noRr2+/hMJStc79+ga4Yd/snni8VUMJuyOiRyGTyTC8nQd+e7kH2no66twvo6AU478+jY//uAG1pl7cLCWqEYZYIgNxIzkPQ1cew66LuidMN1eYYeGQQKwf3wENbC0k7I6IaouXizV+nNYFL/VtDl1rHggC8PlftzBm3Umk5hZL2yCRkWCIJTIAP55NwFOrjuF2WoHOfZq72eLnF7phQremXO2HyMgp5WZ45XF/bHm+Mxo76B7/ejImEwM/O4pj0ekSdkdkHAw+xBYUFKBp0/v/aE+ePFnf7RDVqqJSNV79MRKvbb+EYpXupWPHdPLCLy92R8tG9hJ2R0R1rZOvC36f2RNPtHLXuU96finGbTiFTw9EQ8PhBUTlDD7Ezps3D+np/ARKpicmLR9hq45j+7lEnfvYWSrw5dh2WDIsCFbmcgm7IyKpOFgr8eXY9nh7cCCUcvG7LIIAfHLgJp7ffBY5RSqJOyQyTAYdYs+cOYPPP/8cixcv1ncrRLXqj6vJeGrlcdxIydO5TxsPB/z2cg8MaM1lKYlMnUwmw6TuTfHjtK7wcLLSud+f11MxdOUxXE/OlbA7IsNksCFWpVJh8uTJGDRoEIYNG6bvdohqhVoj4OM/bmDKN+eQV1Kmc78JXX3w47Qu8HS2lrA7ItK3tp6O2PNSD/QP1D28IC6jEMNWncDPF5Mk7IzI8BjsYs0ffvghYmJi8Ouvv0Kt1j3VEJGxyC4sxcwtF3G4gvkf7SwU+CC8DQYG8eorUX3lYK3E2nHtsf7YHSz9/TrKRMbBFqnUmLnlIi4mZOPNgS2hlBvsNSmiOmOQIfbGjRt45513sHTpUnh6eiI2NrZKr3uwyoOYhIQEeHp61lKHRNVz7W4upn57FgmZRTr3CWxkj9Vj2sGngY2EnRGRIZLJZJjcwxdtPBwx47vzOpes/fp4LK7fy8PqMe3gZGMucZdE+mVwH90EQcDzzz+PVq1a4aWXXtJ3O0SP7JfIuxj+xfEKA2x4ew/smNGVAZaIHhLa1Bl7Xu6O9t5OOvf5OyYDQ1cdw41k3WPsiUyRwV2JXbNmDU6cOIFTp05BLq/e09gVrbNb0VVaorqg0QhYvv8mVh68pXMfpVyGt4e0wthOXpz7lYhEudtb4ofnO+PdPdew+e840X0SMoswfPVxrBgVUuF4WiJTYlBXYnNycjBnzhxERETAxcUFsbGxiI2NRWLi/SmI8vPzERsbi+zsbP02SlSJ/JIyTP32XIUB1s3OAlumdMa4zt4MsERUIXOFGRY/1RrLI4JhoRD/p7ugVI0p35zFqoO3IAicT5ZMn0wwoDM9NjYWTZs2rXS/t956C++++2613vvBldiKrtYS1YaEzEJM3nS2wumzQn2csXJMCNzsdK/UQ0Qk5kpSDqZsPou7ObqXox3cphGWPR0MSyXnlybjUp28ZlDDCdzc3LBz506t7ampqZg6dSr69euHF154Af7+/nrojqhyJ2MyMP3bc8gq1D0Z+YSuPnhrEJ8mJqKaad3EAT+/2B3Tvz2Hs3FZovv8eukeErKKsO7ZDnC1s5C4QyJpGNSVWF0eXKF97rnnsG7duhq9B6/EUl3bdiYBb+68LDodDnB//Os7T7XGqFAviTsjIlNUUqbG27uuYuvZBJ37NHG0wtcTO6KFu52EnRHVXHXyGi8FET0ijUbAh3uv4/WfLukMsC425vhucmcGWCKqNRYKOZaOCMLCIYGQm4mPq0/KLsKI1SdwNFr3/NRExsoorsTWBl6JpbpQrFLjlW2R2HP5ns59AhraYd34DvBw4upbRFQ3jkWn44XvzyOnSHwok9zs/p2gZzrxgzQZNl6JJZJAen4JRn91ssIA+0Qrd/w0vSsDLBHVqe5+DbBzRlf4uIj/rlFrBLy58zLe+y0KGh13jIiMDUMsUQ3cSs3DsNXHcSE+W+c+L/Zpji/GtIeNhUE9P0lEJsrX1RY7ZnRDRx/dCyOsPRKDl7dcQEkZl3Mn48cQS1RNJ2MyMHz1CZ0rcCnlMix7OhivPuEPMx3j1IiI6oKzjTm+ndwJw0Ka6Nzn10v3MH7DaZ1DD4iMBUMsUTXsuXQPz64/jdziMtG6vaUCmyd1Qnh7D4k7IyK6z0Ihx/KIYMzq56dzn5MxmYj48m/cy9G9HDaRoWOIJaqiDcfu4MUfzqNUrRGtezlbY+cL3dClmYvEnRERPUwmk2FWvxZYMbItzHXMSX0jJQ/DV5/AzQoWZiEyZAyxRJXQaAS891sUFv96Dbrm8mjv7YSdM7qimauttM0REVUgLKQJNk0KhZ2l+Nj8eznFCP/iBE7FZEjcGdGjY4glqkBJmRqztl7E2iO6p/oY1KYRvpvcCS62XBWHiAxPl2Yu2D6tKxraiy9znVtchnEbTmPf1WSJOyN6NAyxRDrkl5Rh0sYz2B15V+c+k7o1xeejQrg+OREZNP+Gdtj5Qlf461i5q7RMg+nfnsOPFaz+RWRoGGKJRGQWlOKZr07i+C3dt9jmDWqJt4cEcgYCIjIKjRyssG1aF3Rq6ixa1wjAa9svYd1RLgpExoEhlug/knOKMXLN37iUmCNaV8pl+Gx0CCb38JW4MyKiR+NgpcTm50IxqE0jnfu8uycKH+69jnqyoCcZMYZYon+JTS9A+JcnEJ2aL1q3s1Bg08RQDA1uLHFnRES1w0Ihx+ejQjChq4/OfVYfuo03d16Bmqt7kQFjiCX6R9S9XIR/+TcSs8TnTXSzs8DWqV3QtXkDiTsjIqpdZmYyLBgSWOFcsj+cjsfLP1yASse0gkT6xhBLBOBcXBZGrvkb6fklonVPZytsn9YVgY3tJe6MiKhuPJhLduGQQJ377Ll8D9O/Pc9laskgMcRSvXc0Og1j153SuQqXv7sdtk/rCi8Xa4k7IyKqexO6NcWKkW0h1/GQ6oGoFDy/+RyKShlkybAwxFK99vvle5i08QyKVOK/nNt6OmLr1M5w1zG/IhGRKQgLaYKvnm0PC4V4LDhyMw2TNp5BQYn4h30ifWCIpXpr29kEvPD9eajU4g8udGvugu8md4KjtbnEnRERSa9vgDu+ea4TbC3EV/f6OyYD4zecRm6xSuLOiMQxxFK99O3JOLy+/RJ0PXj7eKA71o/vCBsdv8yJiExRaFNnfDu5E+x1LFN7Ni4L49adQk4hgyzpH0Ms1TubTsRi3q4rOusj2nlg9Zh2XIWLiOqltp6O+P75znCyVorWIxNzMPqrk8jQ8SAskVQYYqleWX/sDhbsvqqzPrGbDz4KbwOFnH81iKj+at3EAVumdEEDWwvR+rV7uRj91Umk5hVL3BnR/+O/1FRvfHUkBu/8ek1nfVY/P7w9mMvIEhEBgH9DO2yd2hkNdTzYejMlH6PWnMS9HPG5tYnqGkMs1QtfHLqNJb9F6azPeTIAs/q1gEzGAEtE9EAzV1tsm9oFTRytROsx6QUYvfYkknN4RZakxxBLJm/lX9H4YO91nfV5g1piWq9mEnZERGQ8vFyssW1aF3jrmCs7NqMQz3BoAekBQyyZtBUHbmLZHzd11hcMCcTkHr4SdkREZHyaOFph29QuaOZqI1qPSS/AM1+d0rnqIVFdYIglkyQIApb/cQMrDkTr3GfxU60wsVtTCbsiIjJe7vaW2Dq1CwIa2onWb6XmY8xXp5BZUCpxZ1RfMcSSyREEAR/tu4HP/rqlc58lw1rj2S4+0jVFRGQCGtha4LvJndDC3Va0fiMlD2PWnUJ2IYMs1T2GWDI5Kw5EY/Wh26I1mQz4YEQQxnTylrgrIiLT4GJrge8md9Y5tCDqXi7GrueCCFT3GGLJpHxx6DY+/VN8CIFMBnw4og1GdvSSuCsiItPiameBH57vDN8G4kH2SlIunt1wikvUUp1iiCWTseHYHZ2zEJjJgOURwXi6g6fEXRERmSY3e0t8/3xnnbMWRCbmYMKG08gvKZO4M6ovGGLJJHx3Kg6LdSxkYCYDPhnZFsNCPCTuiojItDV0sMQPz3eGp7P4PLLn47Mx8evTKGCQpTrAEEtGb/u5RLy184rO+ofhwXiqbRMJOyIiqj8aO1rhh+c761wQ4UxsFqZ8cxYlZWqJOyNTxxBLRm135F28vj1SZ33JsNYIb88rsEREdcnDyRo/PN8ZjRzEl6g9fisDL/9wAWVqjcSdkSljiCWjtfdKMmZvvQiNIF5/e3AgZyEgIpKIl8v9IOtubyFa33c1BXN2XIZG1y9tompiiCWjdPB6Kl764TzUOn4ZvjEgAJO6cyEDIiIp+TSwwffPd0YDW/Egu/1cIt7dEwVBYJClR8cQS0bn+K10TP32HFRq8V+CMx/zw/TezSTuioiIAKCZqy2+eS4U9pYK0fqG43fw2Z+6F6MhqiqGWDIqF+KzMHnTWZSWiY+rmtrLF7P6+UncFRER/VvLRvb4emIorJRy0fonB27i6+N3JO6KTA1DLBmN6JQ8TNx4BkUq8SdcJ3T1wZwBAZDJZBJ3RkRE/9Xe2wlrn20Pc7l41Fj0yzVsP5cocVdkShhiySgkZhVi3PrTyNaxjOHoUC8sGBLIAEtEZEB6+Lnis9FtYabjV/MbP13CvqvJ0jZFJsPgQmx6ejomTZqE4OBguLi4wNLSEr6+vhg9ejQiI3VPpUSmKyO/BM+uP43k3GLR+vCQJlgS1poBlojIAA1o3QhLR7QRrak1Al76/gKO30qXuCsyBeKjrqsgNTUVbm5utdkLACA7OxvXr19Hv3794O3tDRsbG8TGxmLjxo3o2LEj9uzZg/79+9f69yXDlFeswoSvzyAmvUC03q+lOz4MbwMzXR/ziYhI7yI6eCK3SIV390Rp1UrVGjy/+Sy2TOmMNh6O0jdHRksm1HCeCwsLC4SFhWHq1Kno27dvbfelJSkpCd7e3ujZsyf++uuvar/e19cXABATE1PbrVEdKVapMfHrM/g7JkO0HtrUGZsnhcJSx4MDRERkWJb/cQOf/SU+M0EDW3P8NL0rvF1sJO6KDEl18lqNhxO0aNECP/74I/r3748WLVrg448/RkaGeNioDQ0bNoSVlRWysrLq7HuQ4ShTazBzywWdATawkT3Wje/AAEtEZERm92+BCV19RGvp+aUYv+E0MvJLpG2KjFaNQ+zly5dx9OhRjBkzBomJiXjttdfg4eGBsWPH4ujRo4/cmEqlQnp6OpKTk3H69GmMGTMG+fn5GDRo0CO/Nxk2QRDw1s4r2Hc1RbTu42KNTZNCYW+plLgzIiJ6FDKZDG8PDsTwkCai9diMQkzadBaFpWUSd0bGqMbDCf4tOzsbGzduxNq1a3H9+nXIZDL4+/tj2rRpePbZZ+Ho6Fjt9zx06BD69OlT/v/29vaYOnUqlixZAqVSPLw8uAQtJiEhAZ6enhxOYASW/n4dXx6+LVpzt7fA9mld4elsLXFXRERUW1T/jIM9dCNNtN43wA1rx7WHQsf0XGS6JBlO8G+Ojo6YNWsWrl27hkOHDmH06NG4c+cOZs+ejSZNmmDixIk4e/Zstd4zODgY+/fvx6+//orly5fDz88PhYWFKC0trY2WyUB9dSRGZ4B1sFJi86RODLBEREZOKTfDqmfaoY2Hg2j9r+upmLfrCpenpQrVypXYf4uOjsaaNWuwceNGZGZmwszMDBqNBjKZDGFhYVi/fn2NrsxmZWUhODgYgYGB2Lt3b7Vfzwe7DN/uyLt4+YcLojUrpRzfTu6E9t5OEndFRER1JT2/BCO+OIG4jELR+qx+fpjVr4XEXZE+SX4lVqVSYevWrXjssccQEBCA5cuXw9XVFZ988gnS09Nx4MAB9O/fHzt37sQLL7xQo+/h5OSEoUOHYt++fYiNja2NtsmAnIzJwKvbxOcBVpjJ8MXYdgywREQmpoGtBTZNDIWLjblofcWBaGw5HS9xV2QsajxPLADcunULa9euxaZNm5Ceng65XI5hw4ZhxowZD0271bdvX/Tt2xdhYWH47bffavz9ioqKANy/Kuvj4/MorZMBiU7Jw5TNZ1Gq1mjVZDLg44hg9Pav/TmJiYhI/3wa2GDDhI4Ytfak6LLib+26Alc7CzzW0l0P3ZEhq/GV2H79+sHf3x/Lli2DUqnE22+/jdjYWGzfvl3nvLHt27dHbm5uhe+bkiL+RHpsbCx27doFBwcHtGzZsqZtk4FJyS3GhK/PILdY/EnUtwcH4qm24k+xEhGRaQj2dMTqMe0gF1m4Rq0R8ML353EhnlNs0sNqfCX2r7/+Qp8+fTBjxgyEhYVBLq98vs6wsDB4eXlVuM/777+P/fv3Y+DAgfDx8YFMJkNUVBQ2b96M/Px8bNq0CZaWljVtmwxIfkkZJn59BknZRaL1KT19MbFbU4m7IiIifegT4Ib3hwXh9Z8uadWKVRo8t+ksds3oBi8XPtxL99U4xEZFRcHf379arwkKCkJQUFCF+wwePBhJSUnYvn07UlNTUVZWhkaNGmHw4MGYOXMmQkNDa9oyGRCVWoMZ353HtXviV+YHt2mEOQMCJO6KiIj0KaKjJ5Jzi7F8/02tWmZBKSZuPI0dM7rBwYrzhFMdzE5gqDg7geEQBAGvb7+EH88lita5nCwRUf0lCALe3HkFP+h4oKtbcxdsnBgKJeeQNUmSz05AVB2f/hmtM8A2c7XB2nHtGWCJiOopmUyGd55qhX4txR/oPX4rA/M5hyyBIZYktu1sAlYciBatudpZYOPEUDhai0+1QkRE9YNCbobPRoegVWN70fqWMwn46ijvrNZ3DLEkmSM30/DmjsuiNWtzOb6e0JGrcREREQDA2lyB9eM7oqG9+MPc7/9+HXuvJEvcFRkShliSxM2UPMz47jzKNNq3f+RmMqwa0w6tm4gvP0hERPVTQwdLrBvfAdbm2kPMBAGYtfUCLifm6KEzMgQMsVTnMvJL8NymM8gvEZ8LdklYa/ThYgZERCSidRMHfDYqBDLtKWT/mXrrDO7qmKqRTBtDLNWpkjI1pn5zDgmZ4r9gXu7bHKNCK547mIiI6rd+ge6YNyhQtJaaV4LnNp3VeaGETBdDLNUZQRAw96fLOBsnvsrK8JAmmN2/hcRdERGRMZrUzQdjO4tf9Ii6l4uXf7gAtciQNTJdDLFUZ1Yfuo0dF5JEax28nfD+iCDIxO4PERER/YdMJsPCIa3Qw6+BaP2v66lYsidK4q5InxhiqU78fvkePtp3Q7Tm6WyFNePaw0LBuWCJiKjqFHIzrBrTDi3cbUXrG47fwbazCRJ3RfrCEEu17nJiDmZvuyhas7O4P2WKi62FtE0REZFJsLdUYv34jmhgKz6n+LydV3BOxzA2Mi0MsVSrknOKMXnzGRSrNFo1Mxnw+TMhaOFup4fOiIjIVHg6W2Ptsx1godCOMaVqDaZ9ew7JOcV66IykxBBLtaawtAyTN59BSm6JaH3BkFbozam0iIioFrTzcsKH4W1Ea2l5JZjyzVkUq9QSd0VSYoilWqHRCPjf1khcScoVrY/r7I3xXX2kbYqIiEzaU22bYFqvZqK1S4k5mLvjMgSBMxaYKoZYqhUf77+BvVfFl//r4dcAC4aIz+9HRET0KF57wh99/F1FazsvJOGrozESd0RSYYilR/brpbtYdfC2aK2Zqw1WPtMOCjlPNSIiqn1yMxk+HR0CX1cb0frS36/j0I1UibsiKTBZ0CO5djcXr/14SbTmZK3Ehgkd4WCllLgrIiKqT+wtlfjq2Q6ws1Ro1TQC8NIPFxCTlq+HzqguMcRSjWUWlGLKN2dRJDJwXimX4cux7eHtIv7JmIiIqDY1c7XFZ6NDILaGTl5xGSZvPovcYpX0jVGdYYilGilTa/Di9+eRmFUkWn/nqdbo5OsicVdERFSf9fF3w5wBAaK1mLQCzOTStCaFIZZq5L3fruPE7QzR2rjO3hgVKr6+NRERUV2a0tMXw0KaiNYO3kjD8v3iq0mS8WGIpWr76VwiNhy/I1oL9XHG/MGciYCIiPRDJpPh/eFBaOPhIFpfdfA2/tAxmw4ZF4ZYqpbIhGzM3XlZtNbYwRKrx7aDucgKKkRERFKxVMqxZlx7NNCxxPkr2yL5oJcJYNqgKkvNK8bUb86htEx7SVkLhRnWjOug8xcGERGRlBo5WGHNuPZQyrWf9MorKcO0b8+hsLRMD51RbWGIpSopLdNgxrfnkZwrvhb1ByPaIEjHrRsiIiJ9aO/thAVDWonWbqbkY85PXNHLmDHEUpUs/OUqzsZlidae79EUYToG0RMREenTmE5eCG/vIVrbHXkXG0/EStsQ1RqGWKrU96fi8f2peNFaD78GeEPHdCZERET6JpPJ8G5YawQ2shetL9kThTOxmRJ3RbWBIZYqFJmQjYW7r4rWvJyt8fnoEC4pS0REBs1SKceXY9uLriBZphEw47vzSNUxXI4MF9MH6ZRZUIoZ351HqVr7QS5rcznWPtsejtbmeuiMiIioerxcrLFiVFvRFb3S8krwwvfnoRL5944MF0MsiVJrBMzccgFJ2eIrcn38dDACGorfmiEiIjJEffzdMOuxFqK1M7FZeP+36xJ3RI+CIZZEffpnNI5Gp4vWpvduhieDGkncERER0aN7qW9z9PF3Fa1tOH4HuyPvStwR1RRDLGk5eD0Vn/0ZLVrr4uuCV/qLf4olIiIydGZmMqwYGQIvZ2vR+hvbL+FmSp7EXVFNMMTSQxIyCzFr60XRmru9BT5/hg9yERGRcXOwVuKLse1gIbLCZJFKjRe+O8+FEIwA0wiVK1apMf27c8gpUmnVFGYyrB7TjityERGRSWjV2AHvDQsSrUWn5mP+LvGZechwMMRSuYW7r+JKUq5obd6glmjv7SxxR0RERHVnRHsPjOvsLVr76Xwitp1NkLgjqg6GWAIAbDuTgC1nxP+yDg1ujPFdfaRtiIiISALzBrdEUBPxZdPf/vkKbiRzfKyhYoglXEnKwfyfr4jW/Nxs8f7wIMjEJtYjIiIychYKOVY90w52lgqtWrFKgxnfnUNBCcfHGiKG2Houp1CF6d+dQ0mZ9gTPNuZyfDG2PWwstP9iExERmQovF2t8FN5GtHY7rQDzdl2BIAgSd0WVYYitxwRBwCs/RiIhU3xBg4+eDkZzN1uJuyIiIpLegNaNMEHH0LmdF5Lw49lEaRuiSjHE1mPrj93BgagU0drk7k0xkAsaEBFRPTJ3YACCPcTHx87/+QquJ4s//Ez6YXAh9ubNm1iwYAG6du0KNzc32NraIigoCHPnzkVWVpa+2zMZFxOy8cFe8eX1Qn2c8caTARJ3REREpF8WCjlW6hgfW1KmwYzvznN8rAExuBC7YcMGfPzxx/D29sa8efOwbNkyBAYGYunSpWjbti1SUsSvHFLV5RSp8OL356FSa4/vaWBrjpXPhEDJBQ2IiKge8nS2xkfhwaK1mLQCvLXzMsfHGgiDSyrh4eFITEzEDz/8gJdffhnTpk3D1q1b8dZbbyE+Ph7Lli3Td4tGTRAEvL49EolZ2uNgZTJgxcgQuNlb6qEzIiIiwzCgdUNM6tZUtLbr4l1s1TElJUnL4EJshw4d4OjoqLU9IiICAHD58mWJOzItm07EYt9V8avZL/Zpju5+DSTuiIiIyPDMeVL3+NgFu69y/lgDYHAhVpekpCQAgJubm547MV6XE3Pw3m86xsE2dcbMx/wk7oiIiMgwmSvMsPKZdrDXMT725R8uoFil1kNn9IBRTACqVqvx7rvvAgAmTJigcz9fX1+dtYSEBHh6etZ2a0Yjt1iFF74/j1K19nywzjbm+GxUCBQcB0tERFTO09kay54OxpRvzmnVbqTk4b3forD4qdZ66IwAI7kSO3PmTJw4cQJTp05F37599d2O0REEAXN/uoz4zELR+vKIYDR04DhYIiKi/3q8le7xsZv/jsMfV5Ml7ogeMPgrsfPmzcOqVaswfPhwrFy5ssJ9Y2JidNYqukpr6r49FY89l++J1qb3bobe/hyiQUREpMsbT/rjZEwGrt3Tnif29Z8uoY2HIy8G6YFBX4lduHAhlixZgmHDhmHLli1QKAw+cxucq3dz8M6v10RrHbyd8Er/FhJ3REREZFwsFHJ8/kwIrJRyrVp2oQqzt16EWsNpt6RmsCF20aJFWLRoEUaMGIFt27ZBqVTquyWjk19Shhe/v4DSMu1xsI7WSnw2muNgiYiIqqKZqy0WDg0Urf0dk4EvD9+WuCMyyASzePFiLFy4EBEREbwC+wjm77qCO+kForXlEcFo7GglcUdERETGK6KDJwbpWJJ9+f6bOB/PlUWlZHDpcNWqVViwYAE8PT0xcOBAbNmy5aG6ra0twsLC9NOcEdl5IRE7LySJ1qb09EXfAHeJOyIiIjJuMpkM7w0PwsWEbCRlP7xokFojYOaWC9jzcg/YW/LusRQMLsSeOXMGwP0pscSm0/L29maIrUR8RiHm77oqWgvxcsRrT/hL3BEREZFpcLBS4tNRbRGx5m/8dxhsQmYR5u+6ghUj20Imk+mnwXrE4IYTbNy4EYIg6PyKjY3Vd4sGTaXW4OUtF5BfUqZVs7NU4PPRIVByHCwREVGNdfBxxszHxB+M/vniXew4L34nlGoX04yJ+ezPaFxMyBatvT88CB5O1tI2REREZIJe7NscoU2dRWtv/3wFsTqeSaHawxBrQk7GZGDlwVuitafbe2Bwm8YSd0RERGSa5GYyrBjZFg5W2uNfC0rVmLnlAlQiq2RS7WGINRE5/8xTJ4hMU9e0gQ0WDm0lfVNEREQmrLGjFT4YESRai0zMwed/iV9YotrBEGsCBEHA3J2XcC+nWKumlMvw2agQ2FgY3DN8RERERm9A60YYHeolWlt18Ban3apDDLEmYNvZBPx2WXzt5lcf90eQh4PEHREREdUfbw8ORDNXG63tao2A/229iAKRh63p0THEGrnbaflYuFt8WdluzV3wfA9fiTsiIiKqX6zM5VgxMgQKM+1ptWIzCrHktyg9dGX6GGKNWEmZGi//cAFFKrVWzclaieURbWEm8heKiIiIaleQhwNm9fMTrX1/Kh5/RqVI3JHpY4g1Yh//cRNX7+aK1j4MD4a7vaXEHREREdVf03o1Q3tvJ9HaGz9dQkZ+icQdmTaGWCN1LDoda4/EiNbGdfZG/0AuK0tERCQlhdwMyyOCYW0u16ql55dizo7LEMSmEaIaYYg1QjmFKrz6Y6Rozc/NFm8NailxR0RERAQA3i42eHtwoGht/7UUbDubIHFHposh1gjN//kKknO1p9MyV5jhs9EhsFRqfwIkIiIiaYzs6Il+LcXviC765RriMriaV21giDUyuyPvYnfkXdHa3CcD0LKRvcQdERER0b/JZDIsHRGEBrbmWrXCUjX+ty0SZVzN65ExxBqR5JxizNt5WbTWw68BxnfxkbYhIiIiEtXA1gJLh7cRrZ2Ly8IaHc+1UNUxxBoJjUbAa9sjkVusPWGyg5USH4UHczotIiIiA9Iv0B2jQz1Fa5/sv4nLiTkSd2RaGGKNxDcn43A0Ol209m5YazR04HRaREREhmbeoEB4u1hrbS/TCHjlx4soKdOe652qhiHWCNxKzcd7Olb7eKptYwwJbixxR0RERFQVNhaK+4sPidwsvZmSjxUHoqVvykQwxBo4lVqD/227iJIy7QHgDe0tsXhoaz10RURERFXV3tsJL/RpLlpbc/g2LsRnSdyRaWCINXCf/3ULl3SMmVn2dDAcrJUSd0RERETV9VJfPwSKzCCkEYBXf4xEscgS8lQxhlgDdiE+C6sO3hKtTejqg+5+DSTuiIiIiGrCXGGGjyOCoZRrjyu4nVaA5ftv6qEr48YQa6AKS8vwv22RUGu0l6dr5mqDOU8G6KErIiIiqqmWjewx8zE/0dpXR2NwNjZT4o6MG0OsgXr/t+u4k669oofCTIYVI7kqFxERkTGa1qsZgpo4aG0X/hlWUFTKYQVVxRBrgA7fTMM3J+NEazMf80OQh/bJT0RERIZPIb8/rMBcrh3BYjMK8eG+63royjgxxBqY3GIV5vx0SbQW4uWI6b2bSdwRERER1aYW7nb43+MtRGtfH4/FyZgMiTsyTgyxBubdX6/hXk6x1nYrpRzLI9pCIfLJjYiIiIzL8z18EeLlKFp7bXskCkq0V+ikhzERGZCD11Ox7WyiaO3NQS3RtIGNxB0RERFRXZCbybDs6WBYKLSjWEJmEZb+zmEFlWGINRA5hSrM2SE+jKB78wYY28lL4o6IiIioLjVztcVrT/iL1r45GYfjt8SXm6f7GGINxOJfryElt0Rru62FAktHBEEmE1mvjoiIiIzaxG5N0dHHSbT2+vZLyCtWSdyR8WCINQB/RqXgp/PiwwjeGtQSHk7WEndEREREUpCbyfBReDAsldqRLCm7CB/s5bACXRhi9SynUIW5Oy6L1nr4NcCojp4Sd0RERERS8mlggzkDxBcx+vZkPE5xtgJRDLF6tuiXq0jN0x5GYGehwAcj2nAYARERUT3wbBcfdPZ1Fq3N2XEZxSougvBfDLF6tP9aCnZcSBKtzRvcEo0drSTuiIiIiPTBzEyGD0a0ER1WcCe9AJ8cuKmHrgwbQ6yeZBWU4s2d4sMIerVwRUQHDiMgIiKqT7xdbPDq4+KzFXx1JAaXErOlbcjAMcTqycJfriJNbBiBJWcjICIiqq8mdmuKYE9Hre0a4f5sBaVlGumbMlAMsXqw90oyfr54V7T29uBANHLgMAIiIqL66P5sBW2glGtfzLqenIc1h2/roSvDxBArsayCUszbJT6MoG+AG8Lbe0jcERERERmSFu52eLGPn2jt879uITolT+KODBNDrMQW/3oN6fmlWtvtLBV4bxiHERAREREwvXczBDS009peqtbg9Z8uQa0R9NCVYWGIldDB66nYqWM2goVDWqGhg6XEHREREZEhMleY4YMRbWAmcm3rQnw2Np2IlbwnQ2OQIXbp0qUYOXIk/Pz8YGZmBoVCoe+WHllesQpv6ZiN4LEANwxv10TijoiIiMiQBXs6YnIPX9HaR/tuID6jUOKODItBhti5c+fijz/+gKenJ9zd3fXdTq34cO8N3M0p1tpuZ6HAEg4jICIiIhGz+7WAj4v28vNFKjXm7rwEQai/wwoMMsTeunULWVlZ+Ouvv+DvLz5fmjE5fScT35yME629OaglhxEQERGRKCtzOZaOaCNaO34rA9vOJkjckeEwyBDbrFkzfbdQa4pVasz56ZJorYuvC0Z15KIGREREpFtnXxeM6eQlWnt3TxRSc7Xv9NYHBhliTclnf0YjJr1Aa7ul0gzvD+cwAiIiIqrcnCcD0Ejkzm1ecRkW/XpNDx3pn/E/MfUvvr7ig58BICEhAZ6e0l71vJKUgzVHYkRrr/T3h08DG0n7ISIiIuNkZ6nEkmGtMWnjWa3ankv3EN4uFX0C3PTQmf7wSmwdUak1eH27+DxuwR4OmNjNR/qmiIiIyGj1DXDH0ODGorV5u66gsLRM4o70y6SuxMbEiF/1BCq+SlsXvjoag2v3crW2K8xk+CC8DRRyfn4gIiKi6pk/OBCHbqQit/jhwJqUXYRP9t/EW4MC9dSZ9Jik6kBMWj5WHIgWrc3o0xwBDe0l7oiIiIhMgaudBd4c2FK0tuF4LK4k5Ujckf4wxNYyjUbAnJ8uo7RMo1Xzc7PFC31MZ+YFIiIikl5EB0+E+jhrbVdrBLy583K9WZKWIbaWfXc6HqdjM7W2y2TAB+FtYKGQ66ErIiIiMhVmZjK8N7w1lHLtGY4uJeZg89+x0jelBwY5Jvabb75BXNz9xQHi4uIgCALefffd8vq8efP01VqF7mYXYelvUaK1iV2bop2Xk8QdERERkSlq7maH6b2b47M/tYcvLtt3A0+0aojGjlZ66Ew6MsEA1yvr3bs3Dh8+rLNek5YfPNhV0cNfj+r5zWex/1qK1nYPJyv8MbsnrM0N8jMDERERGaFilRoDPz0qOh99/0B3fPVsBz109Wiqk9cMcjjBoUOHIAiCzi9DtPdKsmiABYD3hwcxwBIREVGtslTKsWRYkGht/7UU7L2SLHFH0jLIEGts8opVWLj7qmgtvL0Hevi5StwRERER1QddmrkgvL2HaG3h7qvIK1ZJ3JF0GGJrwcd/3ESyyLrFzjbmeEvHNBhEREREteGtgS3hbGOutT05txjL9t3QQ0fSYIh9RBcTsrFJx1OA8we3hJPISUVERERUW5xszDFvkPhFs80n43AhPkvijqTBEPsIytQazN1xGWLDdLs1d0FY2ybSN0VERET1zrCQJujW3EVruyAAb+68gjK19vz1xo4h9hFsOH4HUSJLy5orzLAkLAgymfb8bURERES1TSaTYUlYECwU2tEu6l4uvjkZp4eu6hZDbA0lZBbik/3iS8u+3Lc5fBrYSNwRERER1Wc+DWzw8mN+orWP/7iJVJHnd4wZQ2wNCIKAt3++giKVWqvWwt0WU3pyaVkiIiKS3vM9fNHczVZre35JGd7dI74gk7FiiK2BPZfv4eCNNNHae8OCYC5yKZ+IiIiorpkrzPDOU61Fa7sj7+L4rXSJO6o7TFvVlFOkwqJfronWRod6oYOPs8QdEREREf2/Ls1cMCxE/OHy+T9fQUmZ9p1kY8QQW00f7r2OtLwSre0NbC0wZ0CAHjoiIiIietjcgQGws9ReLTQmrQDrjt7RQ0e1jyG2Gs7FZeG7U/GitbeHBMLBWilxR0RERETa3Ows8doT/qK1z/+KRkJmocQd1T6G2CpSqTV4c8dl0VqvFq4Y0qaRxB0RERER6TamkzdaN7HX2l6s0ugcGmlMGGKraMOxO7iRkqe13VJphnfDWnNOWCIiIjIocjMZ3nmqNcQiyoGoFOy/liJ9U7WIIbYK7mYXYcUB8TlhZ/VrAU9na4k7IiIiIqpciJcTRod6idYW7r6KolLjfciLIbYKFv9yTXRO2ICGdniue1M9dERERERUNa8/4Q9nG3Ot7UnZRVh5UPwinTFgiK3EwRup2Hs1WbS2ZFhrKOU8hERERGS4HK3NMfdJ8RmU1h6Jwa3UfIk7qh1MYBUoVqmx4OerorWIDh5o7805YYmIiMjwjWjngY4+TlrbVer7q5AKgqCHrh4NQ2wFvjh0G/EiU1A4Wisx58mWeuiIiIiIqPrMzGR4J6w15GbaT3mduJ2B3ZF39dDVo2GI1SE2vQBfHL4tWntjQIDo2BIiIiIiQxXQ0B6TuvmI1t77LQoFJWXSNvSIGGJFCIKAt3dfRWmZRqvW1tMRIzt46qErIiIiokczs18LNLS31NqekluClQdv6aGjmmOIFbH3SjKO3EzT2m4mA94Naw0zkUvxRERERIbO1kKBeYPFh0SuOxqDO+kFEndUcwyx/1FQUqZzFYtxnb3RuomDxB0RERER1Z5BQY3QxddFa7tKLWDxL+IPtBsihtj/+OzPaCTnFmttb2Brgf89Lr4GMREREZGxkMlkWDi0lehDXgdvpOHPKONYyYsh9l9uJOdh/bE7orV5g1rCwUopcUdEREREtc+/oR3GdfYWrS3+9RqKRRZ5MjQMsf8QBAHzf76CMo32PGmdfZ3xVNvGeuiKiIiIqG7M7t8CLiKzLcVlFOq8qGdIGGL/sfNCEk7fydTarjCT4Z2nWkMm48NcREREZDocrJR4Y4D4Sl4r/7qFu9lFEndUPQyxAHKKVHjvtyjR2nM9msLP3U7ijoiIiIjqXnh7DwR7OmptL1KpdWYjQ8EQC+CT/TeRnl+qtb2xgyVe7uunh46IiIiI6p6ZmQyLhrYSrf166R7+vp0hcUdVV+9D7I3kPHxzMk609vaQQNhYKCTuiIiIiEg6bT0dEdHBQ7S2cPdVlKm1F38yBPU6xAqCgAW7r0At8jBXrxaueKJVQz10RURERCSt1wcEwM5S+8LdjRTdF/v0rV6H2D2X7+FkjPbDXEq5DAuGBPJhLiIiIqoXGthaYHa/FqK15ftvIj2/ROKOKldvQ2xhaRmW7NHxMFd3X/i62krcEREREZH+jOvijRbu2vknr7gMH+29oYeOKlZvQ+zqg7dxL0d7ZS53ewu81Le5HjoiIiIi0h+l3AwLdTzkte1cAi4lZkvbUCXqZYiNyyjA2iMxorU3B7bkw1xERERUL3Vt1gCD2jTS2i4IwOJfrkEQtJ8j0pd6GWLf+fUaSkWetAv1ccbQYK7MRURERPXXWwNbwkop19p+Ni4Lv166p4eOxNW7EHvwRioORKVqbTeTAQuHtuLDXERERFSvNXa0wvTezURrS3+/jmKVWuKOxNWrECvg/qVwMWM6eSOwsb20DREREREZoOd7+KKxg6XW9qTsInylY0im1Aw2xO7YsQOdO3eGjY0NnJycMGTIEFy6dOmR3jOvuAx30gu0tjtZK/HK4+LTShARERHVN1bmcswZ2FK0tvrQbaTkaj8cLzWDDLHr16/HiBEjUFBQgA8++ADz5s3DlStX0K1bN0RGRtb4fXOLVKLbX33CH47W5jV+XyIiIiJTM6RNI7T3dtLaXqRS44O91/XQ0cNkgiE9ZgYgOzsb3t7esLe3x9WrV2Fvf/8Wf2JiIgIDAxESEoLDhw9X+319fX2RkFmIJtPWP7S9dRN7/PxCd8jNOBaWiIiI6N8iE7Lx1KrjorVdL3RDW0/HWv1+vr6+AICYmMqHLBjcldhdu3YhNzcXkydPLg+wAODh4YGIiAgcOXIEsbGxtfb9Fg1txQBLREREJCLY0xHD2zURrS3+5apep9wyuBB76tQpAEDXrl21ag+2nT59ula+1/CQJmjv7Vwr70VERERkit4YECA65db5+Gzsjryrh47uM7hZ/RMTEwHcv/L6Xw+2Pdjnvx5cghZz584dQCZH0pfPAQBkMhl+crDErrm8CktERERUkdwiFXJEni0auVaGRg5WqK0ZShMSEqBQVC2eGlyILSwsBABYWFho1SwtLR/ap7pk0MDT2brmzZGWhIQEAICnp6eeOzE9PLZ1g8e1bvC41g0e17rB41p99lZK2FspK9ynNo6rQqEQzYCi+9b4u9QRa+v7IbOkpESrVlRU9NA+/1XRIODqDBSmquNxrTs8tnWDx7Vu8LjWDR7XusHjWjekPq4GNya2oiEDSUlJD+1DRERERPWTwYXY0NBQAMCJEye0ag+2dezYUdKeiIiIiMiwGFyIDQsLg52dHdatW4fc3Nzy7YmJidi2bRu6d++Opk2b6rFDIiIiItI3gwuxTk5OWLZsGRITE9GtWzesXLkSy5cvR48ePaDRaPDZZ5/pu0UiIiIi0jODe7ALAKZMmQJnZ2d89NFHeP3112Fubo7u3btjyZIlCA4O1nd7RERERKRnBhliASA8PBzh4eH6boOIiIiIDJBM0Od6YURERERENWBwY2KJiIiIiCrDEEtERERERochloiIiIiMDkMsERERERkdhlgiIiIiMjoMsURERERkdOpFiN2xYwc6d+4MGxsbODk5YciQIbh06ZK+2zJqMplM51d+fr6+2zN4S5cuxciRI+Hn5wczMzMoFBVP2VxYWIg5c+bAx8cHFhYW8PHxwZw5c1BYWChRx8ahOsd148aNOs9hzlH9sJs3b2LBggXo2rUr3NzcYGtri6CgIMydOxdZWVla+/N8rZrqHFeer1WXnp6OSZMmITg4GC4uLrC0tISvry9Gjx6NyMhIrf15vlZNdY6rVOerwS52UFvWr1+PyZMno3Xr1vjggw9QUlKClStXolu3bjh27BhXAHsEPXr0wJQpU7S2W1pa6qEb4zJ37lw4OjoiJCQE+fn5SEtL07mvWq3GwIEDcfjwYYwbNw49e/bE5cuXsWzZMpw6dQoHDhyAXC6XsHvDVZ3j+sCbb76Jli1bPrTN29u7rlo0Shs2bMDKlSsxZMgQjBo1Cubm5jh48CCWLl2K77//HqdPn4a7uzsAnq/VUZ3j+gDP18plZ2fj+vXr6NevH7y9vWFjY4PY2Fhs3LgRHTt2xJ49e9C/f38APF+rozrH9YE6P18FE5aVlSXY29sLHh4eQk5OTvn2hIQEwc7OTujZs6ceuzNuAITx48fruw2jdevWrfI/9+rVS5DL5Tr3Xb9+vQBAeOmllx7avmLFCgGAsGHDhjrr09hU57h+/fXXAgDh4MGDEnRm3M6cOSNkZWVpbX/rrbcEAMKrr75avo3na9VV57jyfH10iYmJglwuF/r06VO+jefroxM7rlKdryY9nGDXrl3Izc3F5MmTYW9vX77dw8MDEREROHLkCGJjY/XXoAkoLS1FXl6evtswOs2aNavyvps3bwYAvPLKKw9tnzp1KmxsbMrrVL3j+m/5+fkoLS2t5W5MR4cOHeDo6Ki1PSIiAgBw+fLl8m08X6uuOsf133i+1kzDhg1hZWX10FANnq+PTuy4/ltdnq8mHWJPnToFAOjatatW7cG206dPS9qTKdm+fTusra1hb28PFxcXTJ48GSkpKfpuy6QIgoCzZ8+icePGWrdgLC0t0a5dO5w5cwYCV4+usaeeegp2dnawsLBAYGAgvvjiCx7PKkpKSgIAuLm5AeD5Wlv+e1z/jedr1alUKqSnpyM5ORmnT5/GmDFjkJ+fj0GDBgHg+VpTlR3Xf6vr89Wkx8QmJiYCuH/l9b8ebHuwD1VPx44dER4ejhYtWiA/Px/79+/Hhg0bcODAAZw6dUprHBfVTGZmJgoKCtCqVSvRuoeHB44ePYqsrCw4OztL3J1xs7a2xqhRo/DYY4+hYcOGiIuLw5o1azBjxgxERkbiyy+/1HeLBk2tVuPdd98FAEyYMAEAz9faIHZcAZ6vNXH8+HH06dOn/P/t7e3x2muvYcGCBQB4vtZUZccVkO58NekQ++DJQgsLC63ag4eP+PRhzfz3CvbYsWPRuXNnzJgxA4sWLcLq1av11JlpqegcBh4+j/lLtnoiIiLKb9s+MHXqVPTu3Rtr1qzBxIkT0alTJz11Z/hmzpyJEydOYOrUqejbty8Anq+1Qey4AjxfayI4OBj79+9HSUkJbt68ie+++w6FhYUoLS2FUqnk+VpDlR1XQLrz1aSHE1hbWwMASkpKtGpFRUUP7UOPbvr06XB1dcWePXv03YrJqOgcBnge1zaFQoG5c+cCAH777Tc9d2O45s2bh1WrVmH48OFYuXJl+Xaer49G13HVhedrxZycnNCvXz8MGjQIs2fPxv79+7F7926MGDECAM/XmqrsuOpSF+erSYfYioYMPBhzJDbUgGrO29u7StMaUdU4OzvD2tpa57CXpKSk8vmPqXb4+PgAAM9jHRYuXIglS5Zg2LBh2LJly0Nz8fJ8rbmKjmtFeL5WnZOTE4YOHYp9+/YhNjaW52st+e9xrUhtn68mHWJDQ0MBACdOnNCqPdjWsWNHSXsyZRqNBjExMWjYsKG+WzEZMpkMHTp0wN27dxEXF/dQrbi4GOfPn0eHDh0gk8n01KHpiY6OBgCexyIWLVqERYsWYcSIEdi2bVv5rcMHeL7WTGXHtSI8X6vnwdXVrKwsnq+16N/HtSK1fr7W6QReepaZmSnY2dnpnCe2e/fueuzOeCUnJ4tuX7JkiQBAmDlzprQNGbnK5jP96quvROcx/PTTTwUAwrp16+q6RaNU2XEVO48LCgqEtm3bCgCE8+fP12V7RmfRokUCACEiIkJQqVQ69+P5Wj1VPa48X6tO179Rd+7cEZydnQUHBwehqKhIEASer9VRneMq1fkqEwTTnjti7dq1mDp1Klq3bo2pU6eitLQUn3/+OdLS0nD06FGEhITou0WjM2vWLPz5558YPHgwvL29UVhYiP3792Pv3r0ICAjA8ePHOQi+Et988035J//169cjPj4eixYtKq/Pmzev/M9qtRp9+vTB0aNH8eyzz6Jnz564dOkSVq1aha5du+LgwYNcUeYf1Tmurq6u6NGjB9q3bw93d3fEx8dj06ZNiI+Px2uvvYYPP/xQ8v4N1apVq/Diiy/C09MT77zzjtb5Zmtri7CwMAA8X6ujOseV52vVzZo1C/v378fAgQPh4+MDmUyGqKgobN68Gfn5+di0aRPGjh0LgOdrdVTnuEp2vtZKFDZwP/74oxAaGipYWVkJDg4OwqBBg4SLFy/quy2j9fPPPwtPPPGE0KRJE8HCwkKwsrISWrduLcybN0/Izc3Vd3tGoVevXgIAnV//lZeXJ7z22muCl5eXoFQqBS8vL+G1114T8vLy9NC94arOcf3f//4ntG/fXnBxcREUCoXg5OQk9OvXT9i+fbueujdc48ePr/C4ent7P7Q/z9eqqc5x5fladfv37xfCw8MFHx8fwdraWjA3Nxe8vb2FZ555Rjh16pTW/jxfq6Y6x1Wq89Xkr8QSERERkekx6Qe7iIiIiMg0McQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRERERGR2GWCIiIiIyOgyxRERERGR0GGKJiIiIyOgwxBIRERGR0WGIJSIiIiKjwxBLREREREaHIZaIyIiEhYVBJpPh888/16rNnz8fMpkMU6dO1UNnRETSkgmCIOi7CSIiqprMzEyEhIQgJSUFf//9N0JCQgAAf/75Jx5//HEEBgbi9OnTsLKy0nOnRER1iyGWiMjInDhxAr169ULTpk1x/vx5FBYWIjg4GLm5uThz5gwCAwP13SIRUZ3jcAIiIiPTtWtXvPPOO4iOjsbUqVMxduxYJCcn4/PPP2eAJaJ6g1diiYiMkCAIePLJJ7Fv3z4AwOjRo/H999/ruSsiIunwSiwRkRGSyWQYNmxY+f/PmjVLf80QEekBr8QSERmh6OhotGvXDkqlEjk5OWjdujVOnToFS0tLfbdGRCQJXoklIjIyJSUlGDlyJAoKCrB161bMnTsXly5dwuzZs/XdGhGRZBhiiYiMzKuvvooLFy7gjTfeQP/+/bFo0SJ069YNX375JbZv367v9oiIJMHhBERERmTXrl0YNmwYunTpgiNHjkChUAAAEhIS0LZtW6jValy8eBE+Pj76bZSIqI4xxBIRGYn4+Hi0bdsWgiDg4sWL8Pb2fqj+888/IywsDJ06dcLRo0ehVCr11CkRUd1jiCUiIiIio8MxsURERERkdBhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRERERGR2GWCIiIiIyOgyxRERERGR0GGKJiIiIyOgwxBIRERGR0WGIJSIiIiKjwxBLREREREbn/wDgUInTWoaewAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["b = 0.006  # [Ns/m]\n", "t = np.arange(0, 2.05, 0.01)\n", "x2a = vx0*m/b*(1 - np.exp(-b/m*t))\n", "y2a = (vy0*m/b + g*m**2/b**2) * (1 - np.exp(-b/m*t)) - g*m/b*t\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(x2a, y2a, lw=4)\n", "plt.xlim(0, 36)\n", "plt.ylim(0, 6)\n", "plt.title('Ball trajectory')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "fjWZclG7M8jK", "slideshow": {"slide_type": "slide"}}, "source": ["#### Numerical solution\n", "\n", "Now, we will solve the same situation using a numerical method (Euler method).  \n", "We start from the equations previously found for each coordinate:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\frac{d^2x}{dt^2} = -\\frac{b}{m} \\frac{dx}{dt} $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{d^2y}{dt^2} = -\\frac{b}{m}\\frac{dy}{dt} - g $$\n", "</span>\n", "\n", "We can separate each equation into two first order equations and apply the <PERSON><PERSON><PERSON> method:  \n", "<br>\n", "\n", "<span class=\"notranslate\">\n", "$$ \\frac{dv_x}{dt} = -\\frac{b}{m} v_x $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dx}{dt} = v_x $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dv_y}{dt} = -\\frac{b}{m}v_y - g $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dy}{dt} = v_y $$\n", "</span>"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {}, "colab_type": "code", "id": "YkEz1B5VM8jL", "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["x  = x0\n", "y  = y0\n", "vx = vx0\n", "vy = vy0\n", "r  = np.array([x, y])\n", "while y >= 0:\n", "    dxdt = vx\n", "    x = x + dt * dxdt\n", "    dydt = vy\n", "    y = y + dt * dydt\n", "    dvxdt = -b/m*vx\n", "    vx = vx + dt * dvxdt\n", "    dvydt = -g - b/m*vy\n", "    vy = vy + dt * dvydt\n", "    r = np.vstack((r, np.array([x, y])))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {}, "colab_type": "code", "id": "-UgCUusuM8jO", "outputId": "27e3ee61-eb8a-4c9b-9ff1-69def6ce0b6a", "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArEAAAGXCAYAAACkxwibAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/P9b71AAAACXBIWXMAAA9hAAAPYQGoP6dpAABa9klEQVR4nO3dd3gUVdsG8Ht2N70npFBCCoSEUELo0qsiCIYuTVBRiiJgAwSlKK+ooCJFQFHQVylSFRAFpUdCJwECBEIqgfTed+f7g5d84s6mkcyW3L/r4hLPM7t5GEdyZ/bMOYIoiiKIiIiIiIyIQt8NEBERERFVFUMsERERERkdhlgiIiIiMjoMsURERERkdBhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRkQRBENCrV69HxhYtWgRBEHD06FG99FQZUn0TEZkihlgiMiqCIGj9srCwgLe3NyZOnIjIyEh9t1hm0qRJEAQBMTEx+m6l1sTExEAQBEyaNEnfrRBRHaPSdwNERNWxcOHCst9nZWXhzJkz+P7777Fz506cPHkSbdq00V9zehQZGQlra2t9t0FEVOsYYonIKC1atEhrbMaMGVi9ejW++OILbNq0SfaeDEFAQIC+WyAikgWnExCRyXjyyScBACkpKY+MZ2Vl4dNPP0WfPn3QqFEjmJubw9XVFUOGDEFoaGit9CIIAjZv3gwA8PHxKZv64O3tXXZMr169IAgCioqK8P7778PPzw/m5uZlH81Xp29dc2JLS0uxdu1adO7cGfb29rC2tkZwcDBWr14NjUYj+V5nzpzB6NGj0bBhQ1hYWKB+/fp48sknsX37dgAPfpDw8fEBAGzevPmRKR7//CFCo9Fg7dq16NChA2xtbWFjY4P27dtj7dq1kl/74Z/h7t27eOGFF1C/fn0olUps2rQJzz33HARBwPHjxyV73rFjBwRBwIwZMyTrRGQ6eCeWiEzG4cOHAQAdO3Z8ZDwyMhLz589Hjx49MGjQIDg5OSE2NhZ79+7FgQMH8Msvv2DgwIE12svChQuxZ88eXL58GTNnzoSjoyMAlP3zn4YPH45z587h6aefRkhICNzd3Wu075KSEgwePBi///47AgICMHbsWFhaWuLIkSOYMWMGTp8+jf/+97+PvObrr7/GtGnToFQqMWTIEPj5+SE5ORlnz57F2rVrMWrUKPTq1QuZmZlYuXIlgoKCEBISUvb6f07nGDt2LLZt24bGjRtj8uTJEAQBu3fvxquvvorjx49j69atWj2npaXhiSeegJ2dHUaMGAFRFOHm5obp06dj27ZtWL9+PXr06KH1uvXr1wMAXnnllQrPCxEZOZGIyIgAEAGICxcuLPs1e/ZssVu3bqIgCOKzzz4r5uTkPPKazMxMMSUlReu9YmJiRHd3d9Hf31/y6/Ts2fORsYULF4oAxCNHjlSq14kTJ4oAxDt37kjWe/bsKQIQW7VqJdlfTfc9c+ZMsbS0tGy8tLRUfPHFF0UA4u7du8vGr169KqpUKtHJyUm8cuWK1teIi4sr+/2dO3dEAOLEiRMl/4w//vijCEBs3769mJubWzaem5srtm3bVgQg/ve//9X6MwAQJ0yYIJaUlGi9Z8uWLUULCwsxNTX1kfFbt26JgiCIXbp0keyFiEwLpxMQkVFavHhx2a/PP/8cJ0+eRPPmzTFixAjY2to+cqyDgwPq1aun9R5eXl4YOXIkbty4gbi4OLla17JkyRLJ/mqib41Gg9WrV6N+/fpYsWIFlEplWU2pVGLFihUQBAE//vhj2fhXX32F0tJSvPfee2jRooXWe3p6elb6z/btt98CAD766CPY2NiUjdvY2GDZsmUAgI0bN2q9ztzcHMuXL4dKpf2B4bRp01BUVFQ2XeOhDRs2QBRFTJ06tdL9EZHx4nQCIjJKoiiW/T4vLw9Xr17F3LlzMWHCBERGRmLp0qWPHH/q1CmsXLkSf//9N5KTk1FcXPxIPTExEY0bN5al93/r1KmTztrj9n3z5k2kpaXBz88PH3zwgeQxVlZWuH79etm/nz59GgDw9NNPV+WPIenixYtQKBTo2bOnVq13795QKpW4cOGCVs3b2xtubm6S7zlhwgTMmTMHGzZswBtvvAEAKC4uxqZNm+Ds7IyRI0c+dt9EZPgYYonI6NnY2KBjx47YtWsXGjVqhE8++QRTp04tu2O4e/dujBgxApaWlujfvz+aNGkCGxsbKBQKHD16FMeOHUNRUZHe+vfw8JAcr4m+09LSAABRUVFYvHixzuNyc3PLfp+ZmQkAaNiwYRX/JNqysrLg7OwMMzMzrZpKpUK9evWQnJysVdN1TgDAzs4O48ePx7p163Ds2DH07NkTu3fvRnJyMmbPng1LS8vH7puIDB9DLBGZDEdHR/j7++PChQu4cOFCWYh97733YG5ujnPnzqF58+aPvGbKlCk4duyYPtotIwiC5HhN9O3g4AAAGDp0KHbt2lWpfh4+fJaYmPjYS3Y5ODggPT0dJSUlWkG2tLQUqampsLe313qdrnPy0PTp07Fu3TqsX78ePXv25ANdRHUQ58QSkUnJyMgAgEeWbrp16xYCAwO1gqBGo8HJkydrrZeH80/VanW1Xl8TfQcEBMDR0RGnT59GSUlJpV7TuXNnAMDvv/9e4bEV/RmDg4Oh0Wgkl8Q6fvw41Go12rZtW6m+/qlVq1bo1q0bdu3ahdDQUBw9ehQ9e/bkOrlEdQhDLBGZjD179uDOnTswMzNDly5dysa9vb0RFRWFxMTEsjFRFLF48WJcu3at1vpxcXEBAMTHx1fr9TXRt0qlwowZM5CUlITXX38dBQUFWsckJSU98n7Tpk2DSqXCkiVLHpkr+1BCQkLZ752cnCAIgs4/44svvggAmDdvHvLz88vG8/PzMXfuXADASy+9VKk/y79Nnz4dRUVFZUtw8YEuorqF0wmIyCj9c8euvLw8XLt2Db/99hsA4D//+U/ZWqsAMHv2bEydOhVt27bF8OHDYWZmhlOnTuHatWsYPHgwfv3111rpsW/fvvj000/x8ssvY/jw4bC1tYWjoyNee+21Sr2+pvp+7733cPnyZaxbtw6//vor+vTpg4YNGyI5ORlRUVE4deoUli5disDAQABAYGAg1q5di6lTp6JNmzZl68Smpqbi7NmzcHBwwJEjRwAAtra26NSpE44fP47x48fDz8+vbG3Z1q1bY+zYsdi7dy+2b9+OFi1aICQkBIIglP3AMWrUKIwbN65a53f48OFwc3NDUlISXF1dMWzYsGq9DxEZKb0u8EVEVEX43xqi//ylVCpFDw8PcciQIeIff/wh+brvvvtODAoKEq2trUUXFxcxJCREDA8P17n2K2pgnVhRFMUVK1aIAQEBorm5uQhA9PLyKqs9XCe2PDXRtyiKokajEb///nuxT58+opOTk2hmZiY2aNBA7Nq1q7h06dJH1n59KDQ0VBw2bJjo6uoqmpmZifXr1xefeuop8eeff37kuKioKPGZZ54RnZ2dRUEQRADid999V1ZXq9XimjVrxHbt2olWVlailZWV2LZtW3H16tWiWq3W+rq6/gxSZs+eLQIQ33nnnUodT0SmQxDFf6xTQ0RERquwsBBWVlZ48sknKzWf1RT06NEDJ0+exM2bN9G0aVN9t0NEMuKcWCIiE3Hjxg0AVduMwJidPn0aJ06cwIABAxhgieoggw2x2dnZWLBgAZo3bw4rKys4OzujU6dOWvt7ExHVdTExMZgzZ07ZIv8jRozQc0e1a/Xq1Zg7dy6GDh0KpVKJJUuW6LslItIDg3ywKzExEb1790ZKSgpeeOEFtGjRAnl5ebh58yZiY2P13R4RkUGJiYnBF198gaZNm2Ljxo0YMGCAvluqVcuXL0dCQgKaNm2KlStXon379vpuiYj0wCDnxPbt2xdXr15FWFgYvLy89N0OERERERkYg5tOcOrUKfz111+YM2cOvLy8oFarH9kOkYiIiIjI4ELs/v37AQB+fn4YNWoUrKysYGdnhwYNGuDDDz+s9s43RERERGQ6DG5ObGRkJIAHO7h4e3vjm2++gSAIWLt2Ld577z3ExcVhw4YNkq/19fXV+b4xMTGwsLBA/fr1a6VvIiIiIno8SUlJsLCwQGZmZoXHGlyIzcnJAQBYW1vj+PHjsLCwAACMHj0agYGB+Oabb/Dmm2/C39+/Su8riiJKS0trvF8iIiIiqhlVyWoGF2KtrKwAAGPHji0LsABgbm6OcePGYcmSJThy5IhkiI2Ojtb5vg/v0pZ3DBERERHpT3mfqv+bwc2JfbhIt9TH/g/H0tPTZe2JiIiIiAyLwYXYzp07AwDi4+O1anFxcQAAd3d3WXsiIiIiIsNicOvEZmVlwdvbG5aWlrh+/TocHBwAPJgrGxAQgJSUFNy+fbvK2ypyOgERERGRYatKXjO4ObEODg5YuXIlJk6ciI4dO2Ly5MkQBAEbN27E3bt3sXTp0jqzLzgRERERSTO4EAsAzz//PFxdXbFs2TIsXrwYGo0GrVq1wpYtW/Dcc8/puz0iIiIi0jODm05QWzidgIiIiMiwVSWvGdyDXUREREREFWGIJSIiIiKjwxBLREREREaHIZaIiIiIjA5DLBEREREZHYZYIiIiIjI6DLFEREREZHQYYomIiIjI6DDEEhEREZHRYYglIiIiIqPDEEtERERERochloiIiIiMDkMsERERERkdhlgiIiIiMjoMsURERERkdBhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRERERGR2GWCIiIiIyOgyxRERERGR0GGKJiIiIyOgwxBIRERGR0WGIJSIiIiKjwxBLREREREaHIZaIiIiIjA5DLBEREREZHYZYIiIiIjI6DLFEREREZHQYYomIiIjI6DDEEhEREZHRYYglIiIiIqPDEEtERERERochloiIiIiMDkMsERERERkdhlgiIiIiMjoqfTcgRRAEnbWcnBzY2trK2A0RUeXlF5ciKasQabnFSM8rRkb+/37lFSM9rwQ5hSUoKFGjoFiN/GI1Ckse/LOoVA2NCIiiCBGA+I/fm6sUsFApYKFS/uP3CthamsHBygwOVqr//fPBLxcbC7jbW8Ld3gIuthZQKnT/nUpEZKwMMsQCQPfu3fHKK69ojVtaWuqhGyKiBwqK1YhJy8Od1Ae/7mYWICmrsOyfWQUlNf4184vV1X6tQgBc7SzgYW8JDwdLeLnYoLGzNbxdbODlYo0GjlYMuURklAw2xPr6+mL8+PH6boOI6qjcolLcuJeNa0k5uHkvpyy0JmYW6Lu1KtGIwP3sItzPLsLlhCytuplSgKeTNfzcbeHvYQ9/dzv4e9jB28UaKiVnnBGR4TLYEAsAxcXFKCoqgp2dnb5bISITlpFXjEvxmYhIzEJkUjYik7IRk5av77ZkUaIWEZ2ah+jUPPx+9X7ZuLlKgaautmjV0AFBno4I8nRAM3c7mDHYEpGBEERRFPXdxL8JggAbGxsUFhZCrVbD2dkZQ4cOxdKlS+Hu7l6t9/T19QUAREdH12SrRGRk1BoRN+7l4EJcBi7GZeJiXAaiU/P03ZZRsDRToGWDB6G2bWMndPRxhqudhb7bIiITUpW8ZpAhtmPHjhgxYgSaNWuG3NxcHDp0CD/88AMaN26MsLAwnUH24R9cSnx8PDw9PRliieoYjUZE5L1s/H07DaG303DmTjpyi0r10outxYMHsKzMlbAyU5b909pcCQuVAgqFAAECBOHBXFYBAkSIKFGLKC7VoKhUjaJSDYpKNCgsVSOnsBRZBSXIKiiBWqOfv8qbuNqgk68LOvk4o5OPCzwc+NwCEVWf0YdYKV999RWmT5+OadOmYe3atZLHMMQSEQDEpeXj6M1khN5Kw+k7acjMr/mHrR5SCICbnSXqO1qigYMVPBwsUd/BEu72lnCxMYeTjTmcbczhaG0GC5WyVnoQRRF5xWpkFZQgM78YKTlFuJ9d+L+5sIW4n12Ie9mFiEvLR3Zh7QZ4n3o26NnMFT2buaKzrwuszGvnz0xEpskkQywAuLm5wcrKCrGxsVV+LacTEJmuErUGZ2PSceR6Mv66nozbKTU/PcDD3hK+rjbwqWcDX1db+NZ78PuGTlZGNU80M78YsWn5iE3PR2xqHmLS8nErOQc37+eioKT6qyBIMVcp0MnHGT2buaKXvxuaunF5RCIqn8mG2A4dOuDq1avIz6/6AxcMsUSmJaugBH9G3sfhyPs4cTMVOTU0RcBcpUCAhx2ae9ijeX07NK9vj4D69nCwMquR9zdUGo2I+Ix83LiXgxv3cnD9fg6uJmbV6ANuvq42GNDCA0+18EDrRg7lrglORHWTSYZYjUYDV1dXODg4VCuIMsQSGb+sghIcunYfByKScCIqBSXqx//ry9fVBm0bOyG4sSOCPZ3QzN2WS0v9Q0ZeMcITs3A5PvPBr4RMpOYWP/b7NnCwxJP/C7QdfZy5Vi0RAahaXjO4Jbbu378v+eDWsmXLkJ6ejgkTJuihKyLSl+zCEvx+5R4ORCTh5K3UxwquSoWA1o0c8ISvCzr4OCPY0xGO1uY12K3pcbIxL5vjCjyYfxublo/T0WkIu5OOsOg03M0qrPL73s0qxKbQGGwKjYGbnQUGBzVASJuGaNnQnndoiahSDO5O7KxZs/Dnn3/imWeegZeXF/Lz83Ho0CEcPHgQAQEBOHXqFJydnav8vrwTS2Q8StUanLiVil0XEvHH1XsoKtVU630EAQisb48uTVzQpUk9dPBxhq2Fwf3sbtREUURCRgFOR6fh1K1UHI9KRXpe9e/U+rraIKRNQ4S0aYjGLtY12CkRGQOjnk7wyy+/YO3atbhy5QpSU1OhUCjQpEkThISE4J133qn2xgcMsUSGLzIpG7suJGDPpbtIySmq1nvYWarQo5kr+ga4oWczV7jYch1TOWk0IiISs3DsZgqO3kjGpfhMVHf1r3ZeThjdwRPPtK4Pa3P+8EFUFxh1iK0tDLFEhim3qBR7LyXip7A4XL2bXa33aOJqg37N3dE7wA3tvJyMarUAU5eZX4yjN1Lw+9V7OHojpVorINhZqDCkTQM816ExWjVyqIUuichQMMRKYIglMizX7mbjx7BY7LmYiLziqgebAA87PN2yPga19kBTN25NbQwKitU4EZWCg1fv4fC1+9Vas7ZFA3s817ExQto0gJ2laa8YQVQXMcRKYIgl0r/CEjX2hSfhx7BYXIzLrPLrAzzsMKhVfQxsXR9NXLnmqDErUWtw6lYq9l66i9+v3kN+FX+QsbVQYUS7RpjYxRs+9WxqqUsikhtDrASGWCL9Sckpwg+nY/Hj6VikVfGhH3d7CwwNboThbRvCz513XE1RfnEpDl27j72X7uL4zRSUVmESrSAAvf3dMKmLN7r71ePKBkRGjiFWAkMskfxu3MvBxpPR2HPxLorVlV9hwNJMgadb1sewtg3RpUk9riFah6TnFWNf+F1sPROPa0lVmyPdxNUGk7r6YGS7RrA043a3RMaIIVYCQyyRPERRxLGbKdh48g5ORKVW6bVBno4Y29ETg1o34FJYdZwoiriSmI0tZ+Pwy6W7yK3Cjmz1bM3xQlcfjO/sZfI7rRGZGoZYCQyxRLVLoxHx+9V7WH3kVpVWGbA2V+LZNg0xrlNjtGzIJ89JW15RKfZHJGHrmThcqMJcalsLFcZ1boyXuvrAzd6y9hokohrDECuBIZaodpSqNfjl8l2sPXobt5JzK/26AA87jO/shWf5lDlVwZXELHx3Kga/Xq78FBVzlQIj2jXCtJ5N4OnMDRSIDBlDrASGWKKaVVSqxs7zifjq2C3EpxdU6jWCAPRr7o6Xuvmgk48zH8KhakvNLcJPYXH47+lYJFdyYwyVQsDI9o3wau+maOTEMEtkiBhiJTDEEtWMUrUGOy8k4Ms/byExs3Lh1cpMiVHtG2FSVx8uh0Q1qrhUg9+uJGHjyTsIT8iq1GvMlAJGtffEq72booGjVS13SERVwRArgSGW6PFoNCJ+Db+LLw5H4U5qXqVe425vgUldfDC2Y2M4WHPKANUeURQRejsN647drvQDheZKBZ7r6InpvZrCw4FzZokMAUOsBIZYouoRRRF/XLuPz/64iRv3cyr1Gk9nK0zr2RTD2zWEhYpLHZG8IhKysO7YbRy4koTKfIezUCnwYjcfTOvVBPacn02kVwyxEhhiiarudHQaPvrtOi7HZ1bq+KZutpjeqwmGBDWASqmo3eaIKhCdkov1x6Kx80JCpTZQcLI2w2t9/DC+c2P+8EWkJwyxEhhiiSovOiUXH/12HYeu3a/U8S0a2OO13k3xVAsPKLgxARmY+PR8rPorCjsvJEJdiTDr6WyFt570x+DWDXg9E8mMIVYCQyxRxdLzirHy8E38GBZXqTtXzdxt8UZ/fzzVwp0rDZDBi0nNw6q/bmH3xQRUZmfbVg0dsGBQc3Tydan95ogIAEOsJIZYIt0KS9TYFBqDNX/dQk4ldkbycrHG7H7NMDioAbeEJaMTnZKLL/+Mwt7Ldys1Z/aZ1vXx7sDmXMmASAYMsRIYYom0iaKIQ9fuY8m+a0jIqHi5rAYOlni9rx+Gt2sEM855JSMXmZSNjw9ex9EbKRUea2mmwLSeTTGlpy8szThflqi2MMRKYIgletSd1Dws+uUqjt2s+Bu4vaUKM/r4YcITXvwGTiYn9FYqPvrtOiISK15ntqGjFRYMao4BLT04hYaoFjDESmCIJXogv7gUa47cwtfH71S4baeZUsCEzt54vW9TOFqby9QhkfweroO8/I8bldqBrksTFyx5tgWautnJ0B1R3cEQK4Ehluo6URTx25V7+HDfNdzNKqzw+AEtPDD36QB4c4ctqkOKStX44e9YrDwcVeH8cDOlgGk9m2B676b8hIKohjDESmCIpbosLi0f8/dEVGono6BGDpg/KBAdfZxl6IzIMKXmFuHTgzew/Xx8hQ9/ebtY48OQVujmV0+e5ohMGEOsBIZYqotK1RpsPHkHnx++icKS8qcO1LM1x9ynm2NYcEOujUn0P+EJmVj0y1VciMus8NiQNg2w4JlA1LO1qP3GiEwUQ6wEhliqayISsjBnZziuJWWXe5xCAJ5/whuz+zeDgxW33CT6N1EUsedSIj46cB3JOUXlHmtvqcK8gc3xXAdPPvhFVA0MsRIYYqmuyC8uxWd/3MS3p+5UuKB7R29nLH62BZrXt5enOSIjlltUipWHb+LbUzEV7vzVtakLlg1rDU9na5m6IzINDLESGGKpLjh+MwXv7o6ocM1XVzsLzB/YHM+2acC7RURVdPVuFt7dfQWX4zPLPc7aXIl5TwdgXCcvTtEhqiSGWAkMsWTKcotKsXR/JLaciavw2PGdG+OdAQGwt+TUAaLqUmtE/BQWi08O3qhwFYPOvs74eHhreLlwpQ+iijDESmCIJVMVejsVb/8cjsTM8u+++rnZ4qNhrdDem6sOENWU+9mFWLLvGvaHJ5V7nJWZEnMG+OP5J7x5V5aoHAyxEhhiydTkF5fi49+uY/PfseUeZ65U4NXeTTG1ly8sVFzLkqg2HLmejHd3RyCpgjWYO/o447NRQWjkxLmyRFIYYiUwxJIpORuTjrd+vozYtPxyj+vg7YSPhrXirkJEMsguLMFHByKx5Ux8ucfZWaiwJKQFQto05Jx0on9hiJXAEEumoLhUgxWHbmDD8ehyF2C3Nldi3sDmGNexMT+6JJLZiagUzN0ZUeEUn2da18fSkFZwsOb8dKKHGGIlMMSSsbuVnItZ2y7iSmL567528nHGpyOC0NiFH1cS6UtuUSk+OhCJH8PKf9iyvoMlVowMQpem3O2LCGCIlcQQS8ZKFEVsOROPJfuulrvrlqWZAnMGBGAiHxwhMhiht1Lxzs7wCpe9m9zNB2895Q9LM85bp7qNIVYCQywZo4y8YszdFY7fr94v97h2Xk74dERr+LraytQZEVVWTmEJFv1yDTsvJJR7XGB9e6weG8z/j6lOq0peU9R2M0RUPSejUjFg5fFyA6y5UoF3BwZg+5Qn+I2PyEDZWZphxaggrB3XFo7lzH+9lpSNwatOYu+lRBm7IzJeKn03QESPKlVr8Nmhm1h79Ha5xzVzt8XK54K5ZSyRkRjYqj7aeTnhrZ8v40RUquQxecVqzNx6CaG30rBoSAtYmXN6AZEuvBNLZEDuZRVi7NdhFQbYiU944ZfXujHAEhkZd3tLbH6hIxYODoS5Sve34G3n4jFk9UncvJ8jY3dExoUhlshAHL+ZgkFfnsCZmHSdx7jYmOPbSe2x+NmWfACEyEgpFAJe6OqDfTO6wd9d9xrOUcm5GLL6JLadjUMdeXyFqEoYYon0TK0R8dkfNzDxuzNIyyvWeVzPZq74bVZ39Alwl7E7IqotzdztsPe1rhjTsbHOYwpLNJizMwJv/RyOwhK1jN0RGT6DD7F5eXnw8fGBIAiYPHmyvtshqlHJOYUY/00Yvvzrls7NC8yUAt57JhDfTeoANztLeRskolplaabER8Na4csxwbC10P2Yys4LCRi2NhRxFezSR1SXGHyIXbBgAVJTpSfAExmzv2+nYeDKk/g7Ok3nMZ7OVtg5rQte6ubDtV+JTNiQoAbYN6MbWjbUPc/9WlI2Bq8+iSM3kmXsjMhwGXSIPXv2LFatWoUlS5bouxWiGiOKIr45EY3xG8OQmluk87inWrhj34zuaN3IUb7miEhvvOvZYOe0LpjUxVvnMVkFJXhx01l8cfgmNBrOk6W6zWBDbElJCSZPnoxBgwZh6NCh+m6HqEYUFKsxa9slfLg/Emod34DMlALefyYQ68a3g4MV91QnqkssVEosGtIC68a3g52O6QWiCHxxOAovbT6LzHzd8+iJTJ3BhthPPvkE0dHRWL16tb5bIaoRcWn5GPZVKPZeuqvzmIaOVvh5ahe82O3BPHAiqpsGtPTA3te6opm77k1MjtxIweDVJxGZlC1jZ0SGwyA3O7hx4wY++OADLFu2DJ6enoiJianU6x5uVSYlPj4enp6eNdQhUdUcv5mCGVsuIqugROcx/Zq7YfnIIDham8vYGREZKl9XW+x5tSvm7ozAL5elf/iNTy/A8K9C8dmoNhjQ0kPmDon0y+DuxIqiiJdffhktWrTAjBkz9N0O0WMRRRFrj97CxO/O6AywggC8/ZQ/NkxozwBLRI+wNldh5XNtsHBwIFQ6Hu7ML1Zj6n/PY9WfUVxPluoUg7sTu379eoSGhiIsLAxKZdUWc4+OjtZZK+8uLVFtKChW460dl7E/PEnnMfaWKqwcE4ze/m4ydkZExkQQHmyO0LKhA6b/eAEpOdIPhK44dBPX7+dg+YggbldLdYJB3YnNysrC3LlzMWrUKLi4uCAmJgYxMTFISEgAAOTm5iImJgaZmZn6bZSoAveyCjFq/d/lBtgADzv8OqMbAywRVUoHb2fsn9ENHb2ddR6zPzwJI9eHIimrQMbOiPRDEA3os4eYmBj4+PhUeNz8+fPx4YcfVum9H96JLe9uLVFNiEjIwuTvz+J+tu7ls55pXR+fjGgNa3OD+zCEiAxccakGi369ip/C4nQeU8/WAhueb4e2jZ1k7Izo8VUlrxnUd1A3Nzfs3r1bazw5ORlTpkxBv3798Oqrr8Lf318P3RFVbH94Et78+RIKSzSSdYUAzH06AC939+XqA0RULeYqBZaGtESAhx0W/3pNcrm+1NwiPLf+ND4Z0RohwQ310CVR7TOoEGttbY2QkBCt8YerE3h5eUnWifRNFEWs+usWPjt0U+cxDlZmWDO2Lbr51ZOxMyIyRYIg4PknvNHE1RbTf7wg+eBosVqDWdsuIT49H6/1acofnMnkGNScWCJjVFiixutbL5UbYH1dbbDn1a4MsERUo7o2rYe9r3ZFUzfd68muOHQTb+8IR3Gp9CdERMbKoObE1ibOiaXakJpbhMmbz+FSfKbOY7r71cPqsW25+xYR1ZrswhLM3HIRR26k6DymSxMXfMWdAMnAVSWv8U4sUTVFp+Ri2NrQcgPsxCe88N2kDvymQUS1yt7SDN9M7IBXeuheTjL0dhpGfBWK+PR8GTsjqj0MsUTVcD42HcO/CkWcjm8GSoWAD0JaYvGzLaFS8n8zIqp9SoWAdwc2x9KhLaHUsTFCVHIuhq4NxeVyfvgmMhb87kpURb9FJGHs12HIyJfegcveUoXNL3TEhM5eMndGRASM6+SFbya2h42ODQ9Sc4swesPfOHztvsydEdUshliiKth48g6m/3QBRToekPByscZuPsBFRHrW298NP0/tAg97S8l6YYkGU/57HtvPxsvcGVHNYYglqgS1RsTiX6/ig33XoOtRyDaejtg1rQuauOp+SpiISC6BDeyx+9UuaF7fXrKu1oh4Z2c41hy5hTryjDeZGIZYogoUlqjx2k8X8N2pGJ3HPBnoji0vd4aLrYV8jRERVaC+gxV+nvoEevm76jzm099vYPGv16CR2DSByJAxxBKVI6ewBC98dxa/Xbmn85hJXbzx1fh2sNIx/4yISJ9sLVT45vn2GNupsc5jNoXG4PWtF1FUqpaxM6LHY1A7dhEZktTcIkz67gyuJGZL1gUBmD+wOV7q5sOdcIjIoKmUD7aqdbezxOeHpTdm2ReehMz8Eqyb0A62FowHZPh4J5ZIQnx6PkZ8FaozwJqrFFgzti0md/dlgCUioyAIAmb288PSoS2hYwUunLyViuc2/I3U3CJ5myOqBoZYon+5cS8Hw78KRUya9BqwDlZm+GlyJwxsVV/mzoiIHt+4Tl5YO64tzFXSEeBKYjZGrfsbdzMLZO6MqGoYYon+4XxsOkauC0VyjvRdCA97S/w89Qm093aWuTMiopozoGV9fP9iR9jpmDYQnZqHkev+RmxansydEVUeQyzR/xy5noxx34Qhu7BUsu5bzwY7pj2BZu52MndGRFTzOvu6YNuUJ+BqJ72qSmJmAUau+xs37+fI3BlR5TDEEgH45fJdvPz9ORSWSG9i0KqhA36e+gQaOVnL3BkRUe0JbGCPXdO6wKeejWQ9OacIo9f/jYiELJk7I6oYQyzVedvPxWPm1oso1bFGYpcmLvjp5U5cA5aITJKnszW2T3kCAR7SnzJl5Jdg7NencTYmXebOiMrHEEt12vd/x+CdHeE6d+Ea0MID307qADtLM3kbIyKSkaudBba+0hltPB0l6zlFpZiwMQwnolLkbYyoHAyxVGdtOH4b7++9qrM+pqMn1oxrC0szbmJARKbP0doc/53cCZ19pR9cLSzR4KVN5/DHVd2bvxDJiSGW6hxRFLHycBT+c+C6zmOm9myC/wxtBaWuxRSJiEyQrYUKm17oiN46tqktVmsw/ccL+C0iSebOiLQxxFKdIooiPj54Q+eONQDwRv9mmDPAn5sYEFGdZGmmxPoJ7TFIx1rYpRoRr225iP3hDLKkX9xXjuoMjUbEkn3XsCk0Rucx8wc2x8s9fOVriojIAJmrFPhyTDCszZX4+XyCVl2tEfH61ovQiCIGBzXQQ4dEDLFUR2g0IubvicCWM/E6j/ng2RaY8IS3fE0RERkwpULAx8Nbw8ZCJfnDv1ojYub/guyzbRrK3yDVeQyxZPI0GhHv7o7A1rPSAVYhAMuGt8ao9p4yd0ZEZNgUCgELBwdCqRCw8eQdrbpGBGZvuwQADLIkO4ZYMmkP78DqCrBKhYDPR7fBEH4cRkQkSRAELBjUHAoB+PqE7iCrEUUMDW6khw6prmKIJZNV0RQCc6UCq8YG46kWHjJ3RkRkXARBwLsDm0MhCFh/PFqrrhGBN7ZfhkYDDG/HIEvyYIglk/QgwF7RHWBVCqyf0A69/d1k7oyIyDgJgoC5TwdAEASsO3Zbqy6KwFs7LkOpEBASzKkFVPu4xBaZHI1GxIK9V7DlTJxk3VylwAYGWCKiKhMEAXMG+GN6ryaSdVEE3vz5Mg5wHVmSAUMsmZSHAfanMB0BVvkgwPZigCUiqhZBEPD2U/54rXdTybpaI+L1LRdx+Np9mTujuoYhlkyGRiPivQoC7PrnGWCJiB6XIAh488lmeL2vn2S9VCNi+o8XcPxmisydUV3CEEsmQRQfbGTwYwUBllMIiIhqhiAImN3PD6/2lp5aUKzW4JUfzuHv22kyd0Z1BUMsGT1RFPHJ7zd07sRlruRDXEREtUEQBLz1pD9e6uYjWS8s0eClzWdxPjZd5s6oLmCIJaO3+q9b+Oqo9pOywIMAu25CW/QOYIAlIqoND9eRHd+5sWQ9v1iNSd+eRXhCpryNkcljiCWj9s2JaKw4dFOyZqYU8NX4tugT4C5zV0REdYsgCFgypCVG6lgjNqeoFBM2nkFkUrbMnZEpY4glo/VTWBw+3B8pWVMqBKwe2xZ9mzPAEhHJQaEQsGx4azzbRnoHxKyCEkzYeAYxqXkyd0amiiGWjNLuiwmYvydCsiYIwGejgrgTFxGRzJQKAStGBmGAjr9/U3OLMH5jGO5nF8rcGZkihlgyOr9FJOHN7ZchitL1ZcNa4dk23C2GiEgfVEoFvhwTjD46nkVIyCjAhI1hyMwvlrkzMjUMsWRUjlxPxutbL0KjI8AuGhyI0R2kHy4gIiJ5mKsUWDuuLbo2dZGs37yfi0nfnUVeUanMnZEpYYglo3E2Jh1T/3seJWrpBPvOAH9M6iq9zAsREcnL0kyJDRPaI8jTUbJ+KT4TU/97HkWlankbI5PBEEtGITIpGy9uOouiUo1kfUafppjeS3oLRCIi0g8bCxU2TeoAPzdbyfqJqFTM3nYJal0frxGVw+BCbGpqKl588UUEBQXBxcUFlpaW8PX1xZgxY3D58mV9t0d6EJeWj+e/PYOcQumPnV7q5oM3+jeTuSsiIqoMJxtz/PBSJzRyspKsH4i4h/m7IyDqetCBSAdVdV+YnJwMN7eaX0A+MzMT169fR79+/eDl5QUbGxvExMRg06ZN6NChA/bv34/+/fvX+Nclw5ScU4gJ34YhJadIsj6mY2MsGNQcgiDI3BkREVWWh4Ml/vtSJ4xY9zdSc7X/Pt96Nh6O1uaY+3SAHrojYyWI1fzRx8LCAiEhIZgyZQr69OlT031pSUxMhJeXF3r06IG//vqryq/39fUFAERHR9d0a1RLsgtLMHr9aZ2LYw9s5YFVY9pCqWCAJSIyBtfuZmP0hr91frK2cHAgXuCzDXVaVfJatacTNGvWDD///DP69++PZs2aYcWKFUhLS6vu21XIw8MDVlZWyMjIqLWvQYajsESNyZvP6Qyw3ZrWw+ej2zDAEhEZkcAG9vhuUgdYmknHjyX7ruFARJLMXZGxqnaIjYiIwIkTJzBu3DgkJCTg7bffRqNGjTB+/HicOHHisRsrKSlBamoq7t27hzNnzmDcuHHIzc3FoEGDHvu9ybCVqjV47aeLOHMnXbIe1MgB6ya0g4VKKXNnRET0uNp7O+Or8e2gkrgJIYrArG2XEBZdezfFyHRUezrBP2VmZmLTpk3YsGEDrl+/DkEQ4O/vj6lTp+L555+Ho6Njld/z6NGj6N27d9m/29vbY8qUKVi6dCnMzMwkX/PwFrSU+Ph4eHp6cjqBgRNFEW/vCMeO8wmSdV9XG+yY2gXONuYyd0ZERDVp76VEzNx6SbJmZ6nCjqld4O9hJ29TpHeyTCf4J0dHR8yaNQvXrl3D0aNHMWbMGNy5cwezZ89Gw4YN8cILL+DcuXNVes+goCAcOnQI+/btw2effQY/Pz/k5+ejuJg7fJiyjw/e0Blg6ztY4oeXOjHAEhGZgGfbNMT8gc0lazmFpZj03RkkZRXI3BUZkxq5E/tPUVFRWL9+PTZt2oT09HQoFApoNBoIgoCQkBBs3LixWndmMzIyEBQUhMDAQBw8eLDKr+eDXYbv+79j8P7eq5I1R2sz7Jj6BJq68adyIiJTIYoiPtgXiW9P3ZGs+7vbYfvUJ+BgJf0JLJke2e/ElpSUYNu2bejbty8CAgLw2WefwdXVFZ9//jlSU1Nx+PBh9O/fH7t378arr75ara/h5OSEIUOG4Pfff0dMTExNtE0G5Per97DwF+kAa22uxHeTOjDAEhGZGEEQsGBQcwxqXV+yfuN+Dl75/hx39SJJ1V4nFgBu3bqFDRs2YPPmzUhNTYVSqcTQoUMxffr0R5bd6tOnD/r06YOQkBAcOHCg2l+voODBxwoZGRnw9vZ+nNbJgJyPzcDrWy5C6jMBM6WAdePbIbixk/yNERFRrVMoBKwYGYTUnCKESTzQG3YnHW9sv4xVzwVDwRVp6B+qfSe2X79+8Pf3x/Lly2FmZob3338fMTEx2LFjh851Y9u1a4fsbOklkx66f/++5HhMTAz27NkDBwcHNG8uPYeGjE90Si4mb9a9neynI4LQo5mrzF0REZGcLM2U2PB8ezRzl96edn94EpYdvC5zV2Toqn0n9q+//kLv3r0xffp0hISEQKmseLmjkJAQNG7cuNxjPvroIxw6dAgDBw6Et7c3BEFAZGQkvv/+e+Tm5mLz5s2wtLSsbttkQFJzizDpu7PIyC+RrL8zwB8hwQ1l7oqIiPTBwcoMm1/siGFrQ5GUVahV33A8Gl4u1hjXyUsP3ZEhqnaIjYyMhL+/f5Ve06pVK7Rq1arcY5555hkkJiZix44dSE5ORmlpKerXr49nnnkGM2fORMeOHavbMhmQ/OJSvLTpLOLS8yXr4zs3xrSeTWTuioiI9Km+gxU2vdARI9eFIltiV6/3915FIydr9OQndIRaWJ3AUHF1AsNRqtZgyg/n8ef1ZMl6v+buWD+hHXfjIiKqo8Ki0zBh4xkUq7WnmtlaqLBzGteQNVWyr05AVFmiKOL9X67qDLBtPB2xakwwAywRUR3WydcFn45sLVnLLSrFi5vOIjlHe8oB1S0MsSSr9cej8VNYnGTN28UaGye2h5U5t5MlIqrrnm3TELP7NZOsJWYW4OXN51BQzKW36jKGWJLNwStJWPab9NOlzjbm2PRCR7jYWsjcFRERGarX+zbFMB0P+F5OyMKsbReh0dSJWZEkgSGWZBGekIlZ2y5J1izNFNg4sT2869nI2xQRERk0QRDw0fBW6OjjLFn//ep9Lr1VhzHEUq27m1mAlzafQ2GJ9gR9hQCsGtOWmxkQEZEkC5US68e3g4+OGx0bjkfjx7BYmbsiQ8AQS7Uqr6gUL20+h5ScIsn6gkGB6B/oLnNXRERkTJxszPHdpA5wsjaTrL+/9ypCb6XK3BXpG0Ms1Rq1RsTrWy4iMkl6l7bxnRvjha7e8jZFRERGybueDTY83x7mSu3ootaImPbjBcSk5umhM9IXhliqNUv3R+pcSqu7Xz0sGtwCgsCltIiIqHI6eDvrXHorq6AEk78/h+xC6V0gyfQwxFKt+OF0LL49dUey1szdFmvGtYVK4qdpIiKi8jzbpiFm9fOTrN1KzsXMLReh5ooFdQJTBNW4YzdTsOiXq5K1erbm2DixA+wtpec1ERERVWRmXz8Mal1fsnbkRgo+5ooFdQJDLNWoW8m5eO3HC5I/BZurFNjwfHt4OlvroTMiIjIVgiBg+YggtGxoL1nfcDwaO84nyNwVyY0hlmpMVn4JXv7+HHKKSiXrK0YGoS2X0iIiohpgZa7EhgntUU/HJjnv7orA+dgMmbsiOTHEUo0oVWswY+tF3NHxZOib/ZthcFADmbsiIiJT1sDRChuebwdzlXacKVZrMOWH87ibWaCHzkgODLFUI5b9dh3Hb6ZI1oYGN8RrfZrK3BEREdUFbRs7YdmwVpK11NwivPz9OeQXS39CSMaNIZYe247zCfjmpPRKBG08HfHRsFZcSouIiGrNsLaNMKWnr2Tt6t1svLMjHKLIFQtMDUMsPZYLcRl4d1eEZM3d3gLrJ7SDpZlS5q6IiKiueeepAPQJcJOs7QtPwtcnomXuiGobQyxV272sQkz54TyK1RqtmrlKgQ0T2sPd3lIPnRERUV2jVAhY+Vwb+LnZStaX/XYdJ6O4Na0pYYilaiksUeOVH84hJadIsv7x8FYI8nSUtykiIqrT7CzN8M3E9nC01l6LXCMCM7ZcQHx6vh46o9rAEEtVJooi5uwMR3hClmR9Sg9fDA1uJHNXREREgJeLDVaNCYZC4lGMjPwSTPnhPAqK1fI3RjWOIZaqbP3xaOy9dFey1svfFe8MCJC5IyIiov/X3c8Vbz8l/b3oWlI23t0dwQe9TABDLFXJ8Zu6t/PzdbXBl2OCoZT68ZeIiEhGU3v6YmArD8na7ouJ2BQaI29DVOMYYqnS4tPz8frWi5D64dXOUoVvnm8Pe0vteUhERERyEwQBn44IQjN36Qe9PtwfidPRaTJ3RTWJIZYqpaBYjSk/nEdmfolWTSEAq8e2ha+r9F8URERE+mBjocKGCe1hZ6nSqqk1Il798QJ39DJiDLFUIVEUMX93BK4lZUvW5z4dgJ7NXGXuioiIqGLe9Wyw8rk2kNpzJy2vGNP+ex6FJXzQyxgxxFKFfjgdi10XEyVrg1rXx8vdpXdJISIiMgR9AtzxRr9mkrXLCVlYsu+azB1RTWCIpXKdi0nHkl+l/+du5m6LT4a35payRERk8F7t3RT9A90laz+FxWHXhQSZO6LHxRBLOt3PLsS0Hy+gVKP9JJedhQrrJ7SHjYX2PCMiIiJDo1AI+GxUEHxdbSTr7+6OwI17OTJ3RY+DIZYkFZdqMP3HCzp35Pp8dBv41JP+i4CIiMgQ2VmaYcOEdrA2V2rVCks0mPbf88gtKtVDZ1QdDLEk6cP913A+NkOy9npfP/TT8ZEMERGRIWvqZodlw1tL1qJT8zBnRzg3QjASDLGkZcf5BHz/d6xkrbe/K2b19ZO5IyIiopozJKgBJj7hJVnbH5HEjRCMBEMsPeLa3WzM3x0hWWvsbI0vRgdDwR25iIjIyM0fFIg2no6StaX7I3V+GkmGgyGWyuQUlmD6j+dRVKrRqlmaKbB+Qjs4WHNHLiIiMn7mKgXWjGsLJ4nva6UaEa/9dAFpudLPhZBhYIglAA82NJizMxwxafmS9Y+Ht0bz+vYyd0VERFR7Gjpa4fPR0hshJGUVYta2S1BLrNBDhoEhlgAAm0JjcCDinmTtha7eeLZNQ5k7IiIiqn29/N0wo4/0sx4nolLx5Z9RMndElcUQS7gYl4H/HIiUrAU3dsS8p5vL3BEREZF8Zvb1Q3e/epK1L/+KwsmoVJk7ospgiK3jMvKK8eqPF1Ci1v64xMnaDGvGtoW5ipcJERGZLqVCwBej26C+g6VWTRSBWdsuITmnUA+dUXmYTuowjUbE7O2XcDdL+39MQXiwoUEDRys9dEZERCQvF1sLrB7bFiqJFXhSc4swm/NjDQ5DbB321bHbOHojRbL2Wu+m6OXvJnNHRERE+tPOywnzBkpPoTt1Kw1fHb0lc0dUHoMLsTdv3sTChQvRpUsXuLm5wdbWFq1atcK8efOQkcE122rK37fTsOKPG5K1Lk1cMKtfM5k7IiIi0r8Xu3qjX3PpXSk/O3QTZ+6ky9wR6WJwIfbbb7/FihUr4OXlhQULFmD58uUIDAzEsmXL0KZNG9y/f1/fLRq95JxCzNhyEVKfirjZWWDlc8FQckMDIiKqgwRBwPKRrdFAYn6sRgRe33IR6XnFeuiM/s3gQuyIESOQkJCALVu24PXXX8fUqVOxbds2zJ8/H3FxcVi+fLm+WzRqpWoNXt9yEakSCzgrFQJWjQmGq52FHjojIiIyDI7W5vhyjPQNnXvZhXjr58sQRc6P1TeDC7Ht27eHo6Oj1vioUaMAABER0luiUuWs+usWTkdLfxTy1pP+6OTrInNHREREhqe9tzPe6C89te6v68nYePKOzB3RvxlciNUlMTERAODmxoeNquvv22lY9Zf0os19A9wwpYevzB0REREZrmk9m+hcP/bjg9dxKT5T3oboESp9N1AZarUaH374IQBg0qRJOo/z9dUdwuLj4+Hp6VnTrRmNtNwizNomPQ+2oaMVVowKgoLzYImIiMooFAI+G9UGA788gZScR6fhlahFzNhyAftmdIeDlZmeOqzbjOJO7MyZMxEaGoopU6agT58++m7H6IiiiLd+voz72drzYFUKAavHBsPR2lwPnRERERk2VzsLfDG6DQSJ+zzx6QWYtyuc82P1xODvxC5YsABr1qzBsGHDsHr16nKPjY6O1lkr7y6tqdt48g6O6FgP9u2n/BHc2EnmjoiIiIxH16b1MKN3U3z5l/Y6sQci7mH7uXiM7tBYD53VbQZ9J3bRokVYunQphg4diq1bt0KlMvjMbXDCEzLx8cHrkrWezVzxcve6G+6JiIgq6/W+fujo4yxZW/TLNdxOyZW5IzLYELt48WIsXrwYw4cPx/bt22FmxvkmVZVTWILXfrqIErX2xxyudhacB0tERFRJKqUCXz4XDCdr7TxSUKLGrK2XUFyq0UNndZdBhtglS5Zg0aJFGDVqFO/AVpMoinh39xXEpedr1QQB+GJ0G9Sz5XqwREREleXhYIlPRgRJ1iISs7DikPROmFQ7DC4drlmzBgsXLoSnpycGDhyIrVu3PlK3tbVFSEiIfpozIj+fS8Cvl+9K1l7r3RRdm0ovGUJERES69Q90x4TOXvjhdKxWbf2xaPTwc+X3WJkYXIg9e/YsgAdLYkktp+Xl5cUQW4Go+zl4/5crkrUO3k6Y2ddP5o6IiIhMx/xBzXE6Og1RydrzYN/YfgkHZ/aAkw1X/altBjedYNOmTRBFUeevmJgYfbdo0ApL1Hjtp4soLNGel+NgZYaVzwVDpTS4/+xERERGw9JMiS/HBMNc4vvp/ewizNnJZbfkwDRjYpb9dh037udI1paPDEIDRyuZOyIiIjI9zevbY+7TAZK1P67dx09n4mTuqO5hiDUhR64nY1NojGRtUhdv9A90l7chIiIiE/ZCV2/0bOYqWftg3zXcSpa+qUQ1gyHWRKTkFOHtHZcla4H17TFvoPRPi0RERFQ9giBg+cgguEjMfy0s0WDGlksoKlXrobO6gSHWBIiiiHd2XEZqbrFWzdJMgVVjg2GhUuqhMyIiItPmameB5SOll92KTMrGpwe57FZtYYg1AT+cjtW5rez7z7RAE1dbmTsiIiKqO3oHuGFSF2/J2jcn7yD0dqq8DdURDLFG7ub9HCzdHylZ6x/ojjEdPWXuiIiIqO6Z+3QA/N3tJGtvbb+M7MISmTsyfQyxRqyoVI3Xt1xEkcQ2d652Fvh4eGsIAreVJSIiqm1ly26ptKPV3axCLNp7VQ9dmTaGWCP26cEbuH5P+snHFSOD4MyFlomIiGTj72GHOQOkH6TedTERByKSZO7ItDHEGqkTUSn45uQdydpL3XzQQ8eSH0RERFR7XujijS5NXCRr7+6OQHJ2ocwdmS6GWCOUnleMN7dLL6cV4GGHdwb4y9wRERERAYBCIeDTkUGws1Rp1TLzS/AOd/OqMQyxRkYURczZGY7knCKtmoVKgS/HcDktIiIifWroaIUlz7aQrB29kcLdvGoIQ6yR2Xo2Hoeu3ZeszR/UHM10PBlJRERE8glp0xADW3lI1j7cF4k7qXkyd2R6GGKNSGxaHj7Yd02y1tvfFRM6e8ncEREREUkRBAFLQ1rBzc5Cq1ZQosYb2y+hVK29uhBVHkOskVBrRLy5/TLyi7W3r6tna45PRgRxOS0iIiID4mRjjo9HtJasXYzLxLpjt2XuyLQwxBqJr09E41xshmTtkxGt4Srxkx4RERHpV29/N4zr1Fiy9sXhKEQkZMnckelgiDUCkUnZ+OyPm5K1sZ0ao0+Au8wdERERUWXNH9Qc3i7WWuOlGhGzt19CYYn2p6xUMYZYA1dUqsbsbZdQLDFvprGzNeYPbK6HroiIiKiyrM1V+Gx0GygkZv3dSs7F54elb1RR+RhiDdwXh6Mkd+VSCMBno4JgY6G9Dh0REREZlraNnfBq76aSta+PR+NCnPSUQdKNIdaAnYtJx3odk76n9GyC9t7OMndERERE1fV6Xz+0bGivNa4Rgbd+vsxpBVXEEGug8opK8ebPl6GR2NQjwMMOs/r5yd8UERERVZuZUoHlI4NgptSeVxCdkocVf9zQQ1fGiyHWQC09EInYtHytcTOlgM9Ht+GuXEREREYowMMes/o1k6x9c/IOzsemy9yR8WKINUBHbiTjpzDpLene6O+P5vW1P4ogIiIi4zClhy9aN3LQGhdF4K2fw1EgsSY8aWOINTAZecWYsyNcstbeywmv9PCVuSMiIiKqSar/TSswV2rHsDupeVjOaQWVwhBrYN7bewXJOUVa49bmSqwYFQSl1PocREREZFSaudthVn/p51u+PXUHZ+5wWkFFGGINyIGIJOwLT5KsLRgUCC8XG5k7IiIiotrySndfBHk6ao2LIvDOjsvILy6VvykjwhBrINJyi/DeniuStd7+rhjT0VPmjoiIiKg2qZQKrBjZGuYq7TgWk5aPTw5yWkF5GGINxPu/XEVaXrHWuKO1GT4e3hqCwGkEREREpqapmx3e7C+9WsGm0Bicjk6TuSPjwRBrAA5EJGG/jmkEi4e0gJu9pcwdERERkVwmd/dFcGNHydo7O8I5rUAHhlg9K28awZOB7hgS1EDmjoiIiEhOSoWA5SODYCExrSAuPR8r/riph64MH0OsnpU3jeDDoS05jYCIiKgOaOJqi7ef8pesfXvqDi7EZcjckeFjiNWjCqcR2HEaARERUV3xQlcftPNy0hoXRWDOjnAUlXIThH9iiNWT8qYR9Oc0AiIiojpHqRDw8XDp1QqiknOx5shtPXRluBhi9UTXNAIHKzMs5TQCIiKiOqmpmy1m9pXeBGHtkVuITMqWuSPDxRCrB5xGQERERLq80sMXgfXttcZLNSLm7AxHqVqjh64MD0OszCqaRvBsG04jICIiqsvMlAp8MqK15Fbz4QlZ+O5UjPxNGSCGWJlxGgERERFVpGVDB7zSw1eytuLQDcSk5snckeFhiJXRwSv3OI2AiIiIKmVmXz/41rPRGi8s0WDurnCIoqiHrgyHQYbYZcuWYfTo0fDz84NCoYBKpdJ3S48tK78E7+3lNAIiIiKqHEszJT4e0Vqydjo6HVvPxsvckWExyBA7b948/PHHH/D09IS7u7u+26kR/zkQiZScIq1xByszLA3hNAIiIiLS1sHbGc8/4SVZ+8/+SCRlFcjckeEwyBB769YtZGRk4K+//oK/v/TuFcYk9FYqtp2T/mlp4eBAuNlzGgERERFJe2dAABo4aGeFnKJSLNh9pc5OKzDIENukSRN9t1BjCorVmLsrQrLWs5krhgY3lLkjIiIiMia2Fir8Z1grydqf15OxT8fzNqbOIEOsKfns0A3EpedrjVubK7kaAREREVVKL383DGsrfeNr8a/XkFVQInNH+mf8T0z9g6+v9FIUABAfHw9PT08ZuwEux2di48k7krV3nvJHIydrWfshIiIi4/XeoEAcv5mC1NxHl+pMzS3Cxwev4z9Dpe/Wmireia0lxaUazNkZDo3ENJV2Xk6Y8IS37D0RERGR8XKyMcfCwS0kaz+FxeFcTLrMHemXSd2JjY6O1lkr7y5tbVh/7Dau38vRGjdXKvDx8FaSu3AQERERleeZ1vWx80ICjt5I0arN2xWB/a93h7mqbtyjrBt/SpndSs7Bqr9uSdZe69MUTd3sZO6IiIiITIEgCPjg2ZawMlNq1aKSc7Hh+G09dKUfDLE1TKMRMWdnBIrVGq1agIcdpvY0nZUXiIiISH6eztaY3d9PsvblX7dwp45sScsQW8N+OB2L87EZWuMKAfh4eOs6c4ufiIiIas+LXX0QWN9ea7y4VIP5uyPqxNqxBjkn9ocffkBsbCwAIDY2FqIo4sMPPyyrL1iwQF+tlSsxswCfHLwuWXuxqw+CPB3lbYiIiIhMkkqpwEfDWiFk7Sn8O6+G3k7DrguJGN6ukX6ak4kgGmBU79WrF44dO6azXp2WHz7YVd7DX49DFEW8sOms5ETrxs7WODirO6zNDfJnBiIiIjJSi365ik2hMVrjTtZm+PPNXnC2MZe/qcdQlbxmkJ9tHz16FKIo6vxliPaFJ0kGWAD4aFgrBlgiIiKqcW8+2QweEtvXZ+SXYOn+SD10JB+DDLHGJqugBIt/vSZZG93eE12b1pO5IyIiIqoL7CzNsPhZ6bVjd15IQOitVJk7kg9DbA345OB1pOYWaY3Xs7XAuwOb66EjIiIiqiueauGBJwPdJWvz91xBYYla5o7kwRD7mM7HZuDHsDjJ2sLBgXCwNpO5IyIiIqprFj/bArYW2lMX76TmYc0R6bXrjR1D7GMoUWvw7q4IyVovf1c807q+zB0RERFRXVTfwQpvPdlMsrb+WDRup+TK3FHtY4h9DF+fiMaN+9pby1qaKfDBsy0hCNxaloiIiOQx4QlvyeU8i9UaLNx71WAfjq8uhthqikvLx8rDUZK1Wf2awdPZWuaOiIiIqC5TKgR8NLQVlArtm2gnb6ViX3iSHrqqPQyx1SCKIhbsvYKiUumtZV/q5qOHroiIiKiuC2xgj0ldvCVrH+y7hpzCEnkbqkUMsdXwy+W7OH5Te01YQQD+M6wVzJQ8rURERKQfs/r5wd3eQms8OacInx+S/hTZGDFtVVFWfgk+2Ce9Juz4Tl5o29hJ5o6IiIiI/p+dpRneeyZQsrYp9A6u3s2SuaPawRBbRcsOXkdqbrHWuKudBd4e4K+HjoiIiIgeNahVfXT3095sSSMCC/ZcgUZj/A95McRWwdmYdGw5I70m7KLBLWBvyTVhiYiISP8EQcDiIS1gLjHF8WJcJrafi9dDVzWLIbaSikt1rwnbJ8ANA1t5yNwRERERkW6+rraY2tNXsrbs4HWk52l/smxMGGIr6esT0YhK1l4o2MpMicVDWnBNWCIiIjI403s3haezldZ4Zn4JPv7tuh46qjkMsZUQn56PVX9JP803u78f14QlIiIig2RppsSSIS0la9vOxeN8bLrMHdUchthKWPzrNRSWaK8J27y+PV7oyjVhiYiIyHD1DnDDUy3cJWvzd19BqVo74xgDhtgKHL52H4cj72uNCwLwn6EtuSYsERERGbyFg1vA2lypNX79Xg42/x2rh44eHxNYOQqK1Vj061XJ2nMdGiOYa8ISERGREWjgaIWZff0ka5/9cQP3swtl7ujxMcSWY+3RW0jIKNAad7I2wztPcU1YIiIiMh4vdvOBn5ut1nhesRrLjPAhL4ZYHaJTcrH+WLRkbd7TzeFkYy5zR0RERETVZ6ZU4MMQ6Ye8dl9MxNkY43rIiyFWgiiKWPjLVRRLTHRu29gRI9o10kNXRERERI+nk68LhgU3lKy9v/cq1Ea0kxdDrIQDEfdwIipVa1whAB+EtIRCwTVhiYiIyDjNfToAthYqrfHIpGz8FGY8D3kxxP5LblEpluyTfphrYhdvtGjgIHNHRERERDXHzd4Sr/dtKllb/sdNo9nJiyH2X1Yevon72UVa4652Fpjdv5keOiIiIiKqWZO6+KCJq43WeFZBCZb/cUMPHVUdQ+w/XL+XjW9PxUjWFgxqDntLM3kbIiIiIqoF5ioFFg1pIVnbciYOEQlZMndUdQyx/yOKIt7bc0VyQvMTvi4YEtRAD10RERER1Y7ufq6SO3mJIrDwlyvQGPhDXgyx/7PzQiLOxmRojZspBXwQ0gKCwIe5iIiIyLQsGBQIC5V2HLwQl4ndFxP10FHlMcQCyMovwUcHIiVrk7v7oqmbncwdEREREdU+T2drTOvVRLL20W/XkV1YInNHlccQC+DzwzeRJvEkXkNHK8zoI/30HhEREZEpmNqzCRo5WWmNp+YW4cvDUXroqHLqfIi9fi8bP5yWXhPt/cGBsDbXXkeNiIiIyFRYminx3jOBkrVNoTGIup8jc0eVU6dDrCiKWKhjd4pe/q54MlB7sjMRERGRqXky0B3d/eppjZdqRCz69SpE0fAe8qrTIXZfeBLC7mjvE2yuVGDhYD7MRURERHWDIAhYNKQFzJTa2efUrTQcvHJPD12Vr86G2PziUvxHx8NcL3X3gU897QWAiYiIiExVE1dbvNjVR7K29EAkCkvUMndUvjobYtceuY2krEKtcQ97S7zWmw9zERERUd0zo68f3OwstMYTMgrwzYloPXSkW50MsbFpedhwXPo/xLyBAbCx4MNcREREVPfYWqgwb2CAZG3t0du4J3EDUF/qZIj9YN81FKs1WuMdfZy5MxcRERHVaSFtGiK4saPWeH6xGp8cvC5/QzrUuRB75EYyDkcma40rBGARH+YiIiKiOk4QBCwc3EKytutiIi7Gae9wqg91KsSKAJb8ek2yNr6zFwIb2MvbEBEREZEBauPpiGFtG0rWFv96DRqJ5UnlZrAhdteuXejcuTNsbGzg5OSEwYMHIzw8/LHeM6ewFHdS87TGnazN8Eb/Zo/13kRERESmZM6AAFibK7XGL8VnYu/lRD109CiDDLEbN27E8OHDkZeXh48//hgLFizAlStX0LVrV1y+fLna75tdIL3/79tPBcDR2rza70tERERkatztLfGqjhWblv12HXlFpTJ39ChBNLAtGDIzM+Hl5QV7e3tcvXoV9vYPPuJPSEhAYGAggoODcezYsSq/r6+vL+LT89Fw6sZHxls2tMfeV7tBqeBcWCIiIqJ/KixRo99nx5CQUaBVm9GnKd580r9Gv56vry8AIDq64uW8DO5O7J49e5CdnY3JkyeXBVgAaNSoEUaNGoXjx48jJiamxr7e4iEtGWCJiIiIJFiaKTF/YHPJ2vrj0YhPz5e5o/9ncCE2LCwMANClSxet2sOxM2fO1MjXGta2Idp5OdXIexERERGZogEtPdDJx1lrvLhUg2W/6W/JLYNb1T8hIQHAgzuv//Zw7OEx//bwFrSUO3fuAIISieteAvBg+YidDpbYM5d3YYmIiIjKU6LWSG50sAHAnnmWsFDVzH3R+Ph4qFSVi6cGF2Lz8x/clraw0N7yzNLS8pFjqkqABp7O1tVvjrTEx8cDADw9PfXcienhua0dPK+1g+e1dvC81g6e16ozUyoqzFA1cV5VKpVkBpQ8ttpfpZZYWz84QUVFRVq1goKCR475t/ImAVdlojBVHs9r7eG5rR08r7WD57V28LzWDp7X2iH3eTW4ObHlTRlITEx85BgiIiIiqpsMLsR27NgRABAaGqpVezjWoUMHWXsiIiIiIsNicCE2JCQEdnZ2+Oabb5CdnV02npCQgO3bt6Nbt27w8fHRY4dEREREpG8GF2KdnJywfPlyJCQkoGvXrli9ejU+++wzdO/eHRqNBl9++aW+WyQiIiIiPTO4B7sA4JVXXoGzszM+/fRTvPPOOzA3N0e3bt2wdOlSBAUF6bs9IiIiItIzgwyxADBixAiMGDFC320QERERkQESRFEU9d0EEREREVFVGNycWCIiIiKiijDEEhEREZHRYYglIiIiIqPDEEtERERERochloiIiIiMDkMsERERERmdOhFid+3ahc6dO8PGxgZOTk4YPHgwwsPD9d2WURMEQeev3Nxcfbdn8JYtW4bRo0fDz88PCoUCKlX5Szbn5+dj7ty58Pb2hoWFBby9vTF37lzk5+fL1LFxqMp53bRpk85rmGtUP+rmzZtYuHAhunTpAjc3N9ja2qJVq1aYN28eMjIytI7n9Vo5VTmvvF4rLzU1FS+++CKCgoLg4uICS0tL+Pr6YsyYMbh8+bLW8bxeK6cq51Wu69VgNzuoKRs3bsTkyZPRsmVLfPzxxygqKsLq1avRtWtXnDx5kjuAPYbu3bvjlVde0Rq3tLTUQzfGZd68eXB0dERwcDByc3ORkpKi81i1Wo2BAwfi2LFjmDBhAnr06IGIiAgsX74cYWFhOHz4MJRKpYzdG66qnNeH3n33XTRv3vyRMS8vr9pq0Sh9++23WL16NQYPHoznnnsO5ubmOHLkCJYtW4affvoJZ86cgbu7OwBer1VRlfP6EK/XimVmZuL69evo168fvLy8YGNjg5iYGGzatAkdOnTA/v370b9/fwC8XquiKuf1oVq/XkUTlpGRIdrb24uNGjUSs7Kyysbj4+NFOzs7sUePHnrszrgBECdOnKjvNozWrVu3yn7fs2dPUalU6jx248aNIgBxxowZj4x/8cUXIgDx22+/rbU+jU1Vzut3330nAhCPHDkiQ2fG7ezZs2JGRobW+Pz580UA4ltvvVU2xuu18qpyXnm9Pr6EhARRqVSKvXv3Lhvj9fr4pM6rXNerSU8n2LNnD7KzszF58mTY29uXjTdq1AijRo3C8ePHERMTo78GTUBxcTFycnL03YbRadKkSaWP/f777wEAb7755iPjU6ZMgY2NTVmdqnZe/yk3NxfFxcU13I3paN++PRwdHbXGR40aBQCIiIgoG+P1WnlVOa//xOu1ejw8PGBlZfXIVA1er49P6rz+U21eryYdYsPCwgAAXbp00ao9HDtz5oysPZmSHTt2wNraGvb29nBxccHkyZNx//59fbdlUkRRxLlz59CgQQOtj2AsLS3Rtm1bnD17FiJ3j662Z599FnZ2drCwsEBgYCC++uorns9KSkxMBAC4ubkB4PVaU/59Xv+J12vllZSUIDU1Fffu3cOZM2cwbtw45ObmYtCgQQB4vVZXRef1n2r7ejXpObEJCQkAHtx5/beHYw+Poarp0KEDRowYgWbNmiE3NxeHDh3Ct99+i8OHDyMsLExrHhdVT3p6OvLy8tCiRQvJeqNGjXDixAlkZGTA2dlZ5u6Mm7W1NZ577jn07dsXHh4eiI2Nxfr16zF9+nRcvnwZ69at03eLBk2tVuPDDz8EAEyaNAkAr9eaIHVeAV6v1XHq1Cn07t277N/t7e3x9ttvY+HChQB4vVZXRecVkO96NekQ+/DJQgsLC63aw4eP+PRh9fz7Dvb48ePRuXNnTJ8+HYsXL8batWv11JlpKe8aBh69jvmXbNWMGjWq7GPbh6ZMmYJevXph/fr1eOGFF9CpUyc9dWf4Zs6cidDQUEyZMgV9+vQBwOu1JkidV4DXa3UEBQXh0KFDKCoqws2bN/Hjjz8iPz8fxcXFMDMz4/VaTRWdV0C+69WkpxNYW1sDAIqKirRqBQUFjxxDj2/atGlwdXXF/v379d2KySjvGgZ4Hdc0lUqFefPmAQAOHDig524M14IFC7BmzRoMGzYMq1evLhvn9fp4dJ1XXXi9ls/JyQn9+vXDoEGDMHv2bBw6dAi//PILhg8fDoDXa3VVdF51qY3r1aRDbHlTBh7OOZKaakDV5+XlValljahynJ2dYW1trXPaS2JiYtn6x1QzvL29AYDXsQ6LFi3C0qVLMXToUGzduvWRtXh5vVZfeee1PLxeK8/JyQlDhgzB77//jpiYGF6vNeTf57U8NX29mnSI7dixIwAgNDRUq/ZwrEOHDrL2ZMo0Gg2io6Ph4eGh71ZMhiAIaN++Pe7evYvY2NhHaoWFhbhw4QLat28PQRD01KHpiYqKAgBexxIWL16MxYsXY/jw4di+fXvZR4cP8XqtnorOa3l4vVbNw7urGRkZvF5r0D/Pa3lq/Hqt1QW89Cw9PV20s7PTuU5st27d9Nid8bp3757k+NKlS0UA4syZM+VtyMhVtJ7p119/LbmO4cqVK0UA4jfffFPbLRqlis6r1HWcl5cntmnTRgQgXrhwoTbbMzqLFy8WAYijRo0SS0pKdB7H67VqKnteeb1Wnq7vUXfu3BGdnZ1FBwcHsaCgQBRFXq9VUZXzKtf1Koiiaa8dsWHDBkyZMgUtW7bElClTUFxcjFWrViElJQUnTpxAcHCwvls0OrNmzcKff/6JZ555Bl5eXsjPz8ehQ4dw8OBBBAQE4NSpU5wEX4Effvih7Cf/jRs3Ii4uDosXLy6rL1iwoOz3arUavXv3xokTJ/D888+jR48eCA8Px5o1a9ClSxccOXKEO8r8T1XOq6urK7p374527drB3d0dcXFx2Lx5M+Li4vD222/jk08+kb1/Q7VmzRq89tpr8PT0xAcffKB1vdna2iIkJAQAr9eqqMp55fVaebNmzcKhQ4cwcOBAeHt7QxAEREZG4vvvv0dubi42b96M8ePHA+D1WhVVOa+yXa81EoUN3M8//yx27NhRtLKyEh0cHMRBgwaJly5d0ndbRmvv3r3iU089JTZs2FC0sLAQraysxJYtW4oLFiwQs7Oz9d2eUejZs6cIQOevf8vJyRHffvttsXHjxqKZmZnYuHFj8e233xZzcnL00L3hqsp5feONN8R27dqJLi4uokqlEp2cnMR+/fqJO3bs0FP3hmvixInlnlcvL69Hjuf1WjlVOa+8Xivv0KFD4ogRI0Rvb2/R2tpaNDc3F728vMSxY8eKYWFhWsfzeq2cqpxXua5Xk78TS0RERESmx6Qf7CIiIiIi08QQS0RERERGhyGWiIiIiIwOQywRERERGR2GWCIiIiIyOgyxRERERGR0GGKJiIiIyOgwxBIRERGR0WGIJSIiIiKjwxBLREREREaHIZaIiIiIjA5DLBEREREZHYZYIiIjEhISAkEQsGrVKq3ae++9B0EQMGXKFD10RkQkL0EURVHfTRARUeWkp6cjODgY9+/fx99//43g4GAAwJ9//oknn3wSgYGBOHPmDKysrPTcKRFR7WKIJSIyMqGhoejZsyd8fHxw4cIF5OfnIygoCNnZ2Th79iwCAwP13SIRUa3jdAIiIiPTpUsXfPDBB4iKisKUKVMwfvx43Lt3D6tWrWKAJaI6g3diiYiMkCiKePrpp/H7778DAMaMGYOffvpJz10REcmHd2KJiIyQIAgYOnRo2b/PmjVLf80QEekB78QSERmhqKgotG3bFmZmZsjKykLLli0RFhYGS0tLfbdGRCQL3oklIjIyRUVFGD16NPLy8rBt2zbMmzcP4eHhmD17tr5bIyKSDUMsEZGReeutt3Dx4kXMmTMH/fv3x+LFi9G1a1esW7cOO3bs0Hd7RESy4HQCIiIjsmfPHgwdOhRPPPEEjh8/DpVKBQCIj49HmzZtoFarcenSJXh7e+u3USKiWsYQS0RkJOLi4tCmTRuIoohLly7By8vrkfrevXsREhKCTp064cSJEzAzM9NTp0REtY8hloiIiIiMDufEEhEREZHRYYglIiIiIqPDEEtERERERochloiIiIiMDkMsERERERkdhlgiIiIiMjoMsURERERkdBhiiYiIiMjoMMQSERERkdFhiCUiIiIio8MQS0RERERGhyGWiIiIiIwOQywRERERGZ3/AwACui9+oAznAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 4))\n", "x2n = r[:, 0]\n", "y2n = r[:, 1]\n", "plt.plot(x2n, y2n, lw=4)\n", "plt.xlim(0, 36)\n", "plt.ylim(0, 6)\n", "plt.title('Ball trajectory')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "M_NMu46AM8jU", "slideshow": {"slide_type": "slide"}}, "source": ["### Example 3: <PERSON> kicked into the air considering the air drag proportional to square of speed\n", "\n", "Now, we will consider the drag force due to the air resistance proportional to the square of speed and still in the opposite direction of the velocity vector. \n", "           \n", "<figure><center><img src=\"../images/ballGravSquareRes.png\" alt=\"free-body diagram of the ball\" width=\"600\"/><figcaption><i>Figure. Free-body diagram of a ball under the influence of gravity and drag proportional to the square of speed.</i></figcaption></center></figure>"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "36ViR3o1M8jU"}, "source": ["So the forces being applied on the ball are (for a revision of Time-varying frames and the meaning of the $\\hat{\\bf{e_t}}$, see [Time-varying frames notebook](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Time-varying%20frames.ipynb)):\n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{F}} = -mg \\; \\hat{\\bf{j}} - bv^2\\hat{\\bf{e_t}} \\\\\n", "\\vec{\\bf{F}} = -mg \\; \\hat{\\bf{j}} - b (v_x^2+v_y^2) \\frac{v_x \\; \\hat{\\bf{i}} + v_y \\; \\hat{\\bf{j}}}{\\sqrt{v_x^2+v_y^2}} \\\\\n", "\\vec{\\bf{F}} = -mg \\; \\hat{\\bf{j}} - b \\sqrt{v_x^2+v_y^2} \\,(v_x \\; \\hat{\\bf{i}}+v_y \\; \\hat{\\bf{j}}) \\\\\n", "\\vec{\\bf{F}} = -mg \\; \\hat{\\bf{j}} - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\left(\\frac{dx}{dt} \\hat{\\bf{i}} + \\frac{dy}{dt} \\; \\hat{\\bf{j}}\\right) $$\n", "</span>\n", "Writing down the Newton's second law:\n", "<span class=\"notranslate\">\n", "$$ \\vec{\\bf{F}} = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\\\\n", "-mg \\; \\hat{\\bf{j}} - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\left(\\frac{dx}{dt} \\hat{\\bf{i}}+\\frac{dy}{dt}\\hat{\\bf{j}}\\right) = m\\left(\\frac{d^2x}{dt^2}\\hat{\\bf{i}}+\\frac{d^2y}{dt^2}\\hat{\\bf{j}}\\right) $$\n", "</span>\n", "Now, we can separate into one equation for each coordinate:\n", "<span class=\"notranslate\">\n", "$$ - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dx}{dt} = m\\frac{d^2x}{dt^2} \\quad \\rightarrow \\\\\n", "\\frac{d^2x}{dt^2} = - \\frac{b}{m} \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dx}{dt} $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ -mg - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dy}{dt} = m\\frac{d^2y}{dt^2} \\quad \\rightarrow \\\\\n", "\\frac{d^2y}{dt^2} = - \\frac{b}{m} \\sqrt{\\left(\\frac{dx}{dt} \\right)^2 + \\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dy}{dt} -g $$\n", "</span>\n", "These equations are very difficult to solve analytically, but they can be easily  solved by using numerical methods. Below we  will use the same numerical method (Euler method) to solve these equations.  \n", "For that, again we must break each equation into two first-order differential equations:\n", "<span class=\"notranslate\">\n", "$$ \\frac{dv_x}{dt} = - \\frac{b}{m} \\sqrt{v_x^2+v_y^2} \\,v_x $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dx}{dt} = v_x $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dv_y}{dt} = - \\frac{b}{m} \\sqrt{v_x^2+v_y^2} \\,v_y -g $$\n", "</span>\n", "<span class=\"notranslate\">\n", "$$ \\frac{dy}{dt} = v_y $$\n", "</span>\n", "Now, we can apply the <PERSON><PERSON><PERSON> method to find a solution. "]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {}, "colab_type": "code", "id": "QyHkIliGM8jV", "outputId": "09a23149-de85-41e7-9282-169a7c0f158c", "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["x  = x0\n", "y  = y0\n", "vx = vx0\n", "vy = vy0\n", "r  = np.array([x, y])\n", "while y >= 0:\n", "    dxdt = vx\n", "    x = x + dt * dxdt\n", "    dydt = vy\n", "    y = y + dt * dydt\n", "    dvxdt = -b/m * np.sqrt(vx**2 + vy**2)*vx\n", "    vx = vx + dt * dvxdt\n", "    dvydt = -b/m * np.sqrt(vx**2 + vy**2)*vy - g\n", "    vy = vy + dt * dvydt\n", "    r = np.vstack((r, np.array([x, y])))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArEAAAGXCAYAAACkxwibAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/P9b71AAAACXBIWXMAAA9hAAAPYQGoP6dpAABSvElEQVR4nO3dd3gUVdsG8Huym94TCCUJm4QWQiD03gWVHgQBqYooiCKirwgKQkAUBVReAQEFKTYUKQoIgvQO0iGQhJAKpJDek935/vAjr7izIQnJ7M7u/bsuLvU8s8vDOCR3Zs+cI4iiKIKIiIiISEGsjN0AEREREVFFMcQSERERkeIwxBIRERGR4jDEEhEREZHiMMQSERERkeIwxBIRERGR4jDEEhEREZHiMMQSERERkeIwxBIRERGR4jDEEhFJEAQBPXr0eGhs3rx5EAQBhw4dMkpP5SHVNxGROWKIJSJFEQRB75etrS38/Pwwfvx4hIeHG7vFUs8//zwEQUBMTIyxW6k2MTExEAQBzz//vLFbISILozZ2A0RElTF37tzSf8/MzMSZM2ewceNG/PLLLzh27BhatGhhvOaMKDw8HA4ODsZug4io2jHEEpEizZs3T29s6tSpWL58OT7//HOsX79e9p5MQWBgoLFbICKSBacTEJHZePLJJwEAKSkpD41nZmZi8eLF6NWrF3x8fGBjY4OaNWti0KBBOHHiRLX0IggCNmzYAADw9/cvnfrg5+dXekyPHj0gCAIKCwvx/vvvo2HDhrCxsSn9aL4yfRuaE1tSUoKVK1eiQ4cOcHFxgYODA1q2bInly5dDp9NJvteZM2cwYsQIeHt7w9bWFnXq1MGTTz6Jn376CcDfP0j4+/sDADZs2PDQFI9//hCh0+mwcuVKtG3bFk5OTnB0dESbNm2wcuVKyd/7wZ/hzp07eOGFF1CnTh2oVCqsX78eI0eOhCAIOHLkiGTPW7ZsgSAImDp1qmSdiMwH78QSkdnYv38/AKBdu3YPjYeHh+O9995Dt27d0L9/f7i7uyM2NhY7duzA7t278euvv6Jfv35V2svcuXOxfft2XLp0CdOmTYObmxsAlP7zn4YOHYpz586hb9++CA0NRa1ataq07+LiYgwcOBB79+5FYGAgRo0aBTs7Oxw8eBBTp07FqVOn8O233z70mq+++gqvvPIKVCoVBg0ahIYNGyI5ORlnz57FypUrMXz4cPTo0QMZGRlYtmwZQkJCEBoaWvr6f07nGDVqFDZv3ox69eph4sSJEAQB27Ztw6uvvoojR47gxx9/1Ov5/v376NixI5ydnTFs2DCIoggvLy9MmTIFmzdvxurVq9GtWze9161evRoA8PLLLz/yvBCRwolERAoCQAQgzp07t/TX9OnTxS5duoiCIIiDBw8Ws7OzH3pNRkaGmJKSovdeMTExYq1atcTGjRtL/j7du3d/aGzu3LkiAPHgwYPl6nX8+PEiAPH27duS9e7du4sAxGbNmkn2V9V9T5s2TSwpKSkdLykpESdMmCACELdt21Y6fu3aNVGtVovu7u7i1atX9X6PuLi40n+/ffu2CEAcP3685J/xu+++EwGIbdq0EXNyckrHc3JyxFatWokAxG+//VbvzwBAHDt2rFhcXKz3nsHBwaKtra2Ympr60HhUVJQoCILYqVMnyV6IyLxwOgERKVJYWFjpr88++wzHjh1DkyZNMGzYMDg5OT10rKurK2rUqKH3HhqNBs8++yxu3ryJuLg4uVrXM3/+fMn+qqJvnU6H5cuXo06dOli6dClUKlVpTaVSYenSpRAEAd99913p+JdffomSkhLMmTMHTZs21XtPX1/fcv/Z1q1bBwD46KOP4OjoWDru6OiIRYsWAQDWrl2r9zobGxssWbIEarX+B4avvPIKCgsLS6drPLBmzRqIoojJkyeXuz8iUi5OJyAiRRJFsfTfc3Nzce3aNcycORNjx45FeHg4Fi5c+NDxx48fx7Jly3Dy5EkkJyejqKjooXpiYiLq1asnS+//1r59e4O1x+07IiIC9+/fR8OGDbFgwQLJY+zt7XHjxo3S/z516hQAoG/fvhX5Y0i6cOECrKys0L17d71az549oVKpcP78eb2an58fvLy8JN9z7NixeOedd7BmzRq8+eabAICioiKsX78eHh4eePbZZx+7byIyfQyxRKR4jo6OaNeuHbZu3QofHx988sknmDx5cukdw23btmHYsGGws7NDnz59UL9+fTg6OsLKygqHDh3C4cOHUVhYaLT+a9euLTleFX3fv38fABAZGYmwsDCDx+Xk5JT+e0ZGBgDA29u7gn8SfZmZmfDw8IC1tbVeTa1Wo0aNGkhOTtarGTonAODs7IwxY8Zg1apVOHz4MLp3745t27YhOTkZ06dPh52d3WP3TUSmjyGWiMyGm5sbGjdujPPnz+P8+fOlIXbOnDmwsbHBuXPn0KRJk4deM2nSJBw+fNgY7ZYSBEFyvCr6dnV1BQAMGTIEW7duLVc/Dx4+S0xMfOwlu1xdXZGWlobi4mK9IFtSUoLU1FS4uLjovc7QOXlgypQpWLVqFVavXo3u3bvzgS4iC8Q5sURkVtLT0wHgoaWboqKiEBQUpBcEdTodjh07Vm29PJh/qtVqK/X6qug7MDAQbm5uOHXqFIqLi8v1mg4dOgAA9u7d+8hjH/VnbNmyJXQ6neSSWEeOHIFWq0WrVq3K1dc/NWvWDF26dMHWrVtx4sQJHDp0CN27d+c6uUQWhCGWiMzG9u3bcfv2bVhbW6NTp06l435+foiMjERiYmLpmCiKCAsLw/Xr16utH09PTwBAfHx8pV5fFX2r1WpMnToVd+/exeuvv478/Hy9Y+7evfvQ+73yyitQq9WYP3/+Q3NlH0hISCj9d3d3dwiCYPDPOGHCBADArFmzkJeXVzqel5eHmTNnAgBefPHFcv1Z/m3KlCkoLCwsXYKLD3QRWRZOJyAiRfrnjl25ubm4fv06fv/9dwDAhx9+WLrWKgBMnz4dkydPRqtWrTB06FBYW1vj+PHjuH79OgYOHIjffvutWnp84oknsHjxYrz00ksYOnQonJyc4Obmhtdee61cr6+qvufMmYNLly5h1apV+O2339CrVy94e3sjOTkZkZGROH78OBYuXIigoCAAQFBQEFauXInJkyejRYsWpevEpqam4uzZs3B1dcXBgwcBAE5OTmjfvj2OHDmCMWPGoGHDhqVryzZv3hyjRo3Cjh078NNPP6Fp06YIDQ2FIAilP3AMHz4co0ePrtT5HTp0KLy8vHD37l3UrFkTzzzzTKXeh4gUyqgLfBERVRD+fw3Rf/5SqVRi7dq1xUGDBol//PGH5Ou++eYbMSQkRHRwcBA9PT3F0NBQ8fLlywbXfkUVrBMriqK4dOlSMTAwULSxsREBiBqNprT2YJ3YslRF36IoijqdTty4caPYq1cv0d3dXbS2thbr1q0rdu7cWVy4cOFDa78+cOLECfGZZ54Ra9asKVpbW4t16tQRn3rqKfHnn39+6LjIyEhxwIABooeHhygIgghA/Oabb0rrWq1WXLFihdi6dWvR3t5etLe3F1u1aiUuX75c1Gq1er+voT+DlOnTp4sAxBkzZpTreCIyH4Io/mOdGiIiUqyCggLY29vjySefLNd8VnPQrVs3HDt2DBEREWjQoIGx2yEiGXFOLBGRmbh58yaAim1GoGSnTp3C0aNH8fTTTzPAElkgkw2xWVlZmD17Npo0aQJ7e3t4eHigffv2evt7ExFZupiYGLzzzjuli/wPGzbMyB1Vr+XLl2PmzJkYMmQIVCoV5s+fb+yWiMgITPLBrsTERPTs2RMpKSl44YUX0LRpU+Tm5iIiIgKxsbHGbo+IyKTExMTg888/R4MGDbB27Vo8/fTTxm6pWi1ZsgQJCQlo0KABli1bhjZt2hi7JSIyApOcE/vEE0/g2rVrOH36NDQajbHbISIiIiITY3LTCY4fP44DBw7gnXfegUajgVarfWg7RCIiIiIikwuxu3btAgA0bNgQw4cPh729PZydnVG3bl188MEHld75hoiIiIjMh8nNiQ0PDwfw9w4ufn5++PrrryEIAlauXIk5c+YgLi4Oa9askXxtQECAwfeNiYmBra0t6tSpUy19ExEREdHjuXv3LmxtbZGRkfHIY00uxGZnZwMAHBwccOTIEdja2gIARowYgaCgIHz99dd466230Lhx4wq9ryiKKCkpqfJ+iYiIiKhqVCSrmVyItbe3BwCMGjWqNMACgI2NDUaPHo358+fj4MGDkiE2Ojra4Ps+uEtb1jFEREREZDxlfar+byY3J/bBIt1SH/s/GEtLS5O1JyIiIiIyLSYXYjt06AAAiI+P16vFxcUBAGrVqiVrT0RERERkWkxundjMzEz4+fnBzs4ON27cgKurK4C/58oGBgYiJSUFt27dqvC2ipxOQERERGTaKpLXTG5OrKurK5YtW4bx48ejXbt2mDhxIgRBwNq1a3Hnzh0sXLjQYvYFJyIiIiJpJhdiAWDcuHGoWbMmFi1ahLCwMOh0OjRr1gw//PADRo4caez2iIiIiMjITG46QXXhdAIiIiIi01aRvGZyD3YRERERET0KQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKQ5DLBEREREpDkMsERERESkOQywRERERKY5JhlhBEAz+ysnJMXZ7RERERGRkamM3YEjXrl3x8ssv643b2dkZoRsiIiIiMiUmG2IDAgIwZswYY7dBRERERCbIJKcTPFBUVITs7Gxjt0FEREREJsZkQ+yWLVvg4OAAFxcXeHp6YuLEiUhKSjJ2W0RERERkAkxyOkHbtm0xbNgwNGrUCDk5Odi3bx/WrVuH/fv34/Tp06hVq5bk6wICAgy+Z3x8PHx9faurZSIiIiKSkUmG2DNnzjz032PGjEGHDh0wZcoUhIWFYeXKlUbqjIiIiIhMgSCKomjsJsrLy8sL9vb2iI2NrfBrH9yljY6Oruq2iIiIiKgKVCSvmeycWCkajQYpKSnGboOIiIiIjEwxIVan0yE6Ohq1a9c2ditEREREZGQmF2INrUCwaNEipKWlYdCgQTJ3RERERESmxuQe7Proo4/w559/YsCAAdBoNMjLy8O+ffuwZ88eBAYG4v333zd2i0RERERkZCYXYnv16oUbN25g06ZNSE1NhZWVFerXr4/Zs2djxowZcHZ2NnaLRERERGRkilqd4HFwdQIiIiIi02a2qxMQEREREQEMsURERESkQAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4amM3QESWSxRFpOUWITEjH3cyCpCWW4T0vCJk5BUhPa8YGXlFyC3UokirQ2GJFoXFOhSW6KDViVBZCVBZCbASALWVFazVApxs1XC2s4azrRrOdmq42FvDy9kWXi52qOViBy9nW9R0toW1ij+/ExEpHUMsEVUrURSRkl2IqOQcRKXkICo5B9EpubiTkY/EjHwUluhk7cdKAOq62cO/hiP8PB3hV8MR/jUc0Li2C+q62kEQBFn7ISKiymGIJaIqI4oi4tLycDkhE1cSM3E5IQPX72Qhq6DE2K2V0olAQno+EtLzcTQy9aGam4M1guq4oGldFwTVdUFLX3doPB0YbImITBBDLBFVWlGJDlcSM3AqOg1nbqfhYnwGMvOLjd1WpWXkFePErfs4cet+6VhNZ1u00bijjZ8H2vq5o2ldV6isGGqJiIyNIZaIyk2nE3H9bhYO3UzGyej7OB+bgfxirbHbqlYp2YX4/eo9/H71HgDA1d4aXRrUQLdGNdCtUU3UcbU3codERJaJIZaIypRVUIxjkak4eCMZhyJSkJJdKMvv62CjgruDDZzt1LC1VsFWbVX6S2UlQKsDtDodtOLf/yws1iGnsATZBSXIKihGTmEJRLHq+8rML8auK3ex68pdAECjWk54okkt9A2ujWberpx6QEQkE4ZYItKTlluEvdfuYdfluzgVfR8luqpNg7Vd7KDxdIC3uz283exR1+3vf3q52MLdwQZuDtawVase6/cQRREZecVIzi5EcnYBkrIKkZRVgIT0PNxOzUVMah7uZRU89p8lIikHEUk5+PLQLXi72ePp4Np4Org2WtdzhxWnHRARVRtBFKvjXoXpCQgIAABER0cbuRMi0/QguO6+chcnbt2HtgqCq5uDNZr7uKFJHWc0qOmEBl5OqO/lBBc76yro+PHlF2kRcz8XEUnZuHYnC9fvZOHanUyk5z3+vN66rnYY0sobz7TyQf2aTlXQLRGR+atIXmOIJbJgxVodDtxIxpa/EnDwRvJj3XG1UVkhxNcVreq5o7mPG5r7uMLH3V5xH6+Looi7mQU4H5eOczHpOBebhut3svA4mb6FrxuGtvbBoOZ14epgGgGeiMgUMcRKYIgl+p8b97Lw87kEbL+QiPu5RZV6DxuVFVpp3NDe3xPtAzzQqp477KwfbwqAqcopLMFfsek4FpmCIxGpuJmUXan3sVVbYXCLuhjX0Q/B3q5V3CURkfIxxEpgiCVLV1Csxa7Ld7HxZAwuJWRW6j283ezRM7Amejb2Qsf6nnCwscxp9Xcz83E0IhUHbybj0M2USq3Q0LKeG8Z20KBfszpmG/6JiCqKIVYCQyxZqjsZ+fj2VCx+PBuPtErcdW3u44q+wXXwRBMvNPRyUtz0gOqWX6TF4YgU7Ll6F3+GJyO7sGIbO3g62uCFzn4Y28GPUw2IyOIxxEpgiCVLcy4mDV8fvY0/rt+r8HzOZt6u6N+8DvoF10E9T4fqadAMFZXocCQiBVsvJGD/9WQUacu/pa6jjQrPtauHF7v6c+1ZIrJYDLESGGLJEuh0Ig7cSMaqw7dwLja9Qq/1cbfHsNY+GNLSGxpPx2rq0HJk5hXjt8t38Mv5BFyIyyj366xVAga38MZrPRvArwb/PxCRZWGIlcAQS+asWKvDrxfvYPWRW4hIyin36+ysrdAvuA6GtfFBB39PrmtaTaKSs7HpZCx+OZ+InHJON1BZCRjWygdTn2gAH3feDSciy8AQK4EhlsxRsVaHn88lYMXBKCRm5Jf7dY1rOWNcJw0GhdSFs4ms2WoJcgpLsO1CIjadjCn3DxvWKgEj29bDqz0boLarXTV3SERkXGYVYnNzcxEcHIyYmBi8+OKL+Prrryv1PgyxZE5KtDpsvZCILw5EIj6tfOFVZSXgqaa1MK6jH9r7e/ABLSMSRREnb93HqiPROBKRUq7X2Kit8GIXf0zpUZ8/eBCR2apIXjP59XFmz56N1NRUY7dBZBK0OhG/XbqDZX9G4nZqbrle4+ZgjTHtNRjdoR4fGDIRgiCgU4Ma6NSgBq7dycTqw9HYeflOmQ/gFZXo8OWhW/jpbDym92mEkW19oVZZydc0EZGJMek7sWfPnkXHjh2xePFivPnmm7wTSxZLFEUcikjBot03yr3Qfl1XO0zsGoARbX3haGvyP69avPi0PKw+cgubz8ajWPvoL8sNvZzwbv8m6NGoJu+qE5HZMIvpBMXFxWjTpg38/PywbNky+Pv7M8SSRbp2JxMf7b6BY1Hl+0SioZcTJnevj0Et6sKad+oUJz4tD8sPRGHL+QRoy7E2Wq9AL8wb2JRLoRGRWTCL6QSffPIJoqOjsXPnTmi1Fd8Nh0jp7mbmY8neCGy9kIDy/KgZWNsZb/RuhCeDanGVAQXz9XDAx8Oa45Ue9bHsz0hsv5hY5v//AzeScTwqFa/1bICXuwfAVs3dv4jIMphkiL158yYWLFiARYsWwdfXFzExMeV63YP0LiU+Ph6+vr5V1CFR9ckv0uLLw7ew5sgtFBQ/erH8Bl5OmN67EfoG12Z4NSN+NRzx2YgWmNy9PhbuDi/zAbDCEh2W7ovAtguJWBAajM4NasjYKRGRcZhciBVFES+99BKaNm2KqVOnGrsdItmIooh915Mwf+d1JKQ/esUB/xqOmPZEQwwMqQsVw6vZalzbGRsntMOhm8n4cHd4mUtzRafmYvTXpzG4RV3MHdgUHo42MnZKRCQvkwuxq1evxokTJ3D69GmoVBX7WKys+RNl3aUlMraY1FyE/XYNB28+erklT0cbPp1ugXo09kKXBjWw+Vw8Pv0jAvdziwweu+PiHRyPSsX8wcHo16yOjF0SEcnHpEJsZmYmZs6cieHDh8PT07N0GkFCQgIAICcnBzExMXBzc4Obm5vxGiWqIvlFWnx5KAqrDkejSFv21AFbtRVe6hqASd0DuE6ohVKrrDC6vQYDmtXFkj9u4tvTsQbny6bmFGHKd+fRr1ltzB8cjBpOtvI2S0RUzUxqdYKYmBj4+/s/8rj33nsPH3zwQYXem6sTkKk5EZWKmVuvIC4tr8zjBAF4pqUP/vNUI67zSg+5FJ+B2duv4kpiZpnHuTtYI2xwMAaF1JWpMyKiylHsElt5eXn4448/9MaTk5MxadIk9O7dG6+++ioaN26MJk2aVOi9GWLJVGTmFePD3eHYfC7+kce2queG+YODEeztKkNnpERanYjvT8fik703kV1QUuaxg1vUxfzBwXC15518IjJNil1iy8HBAaGhoXrjD6YVaDQayTqRUuy5ehdzdlxDSnZhmcd5OtpgZt9ADG3lwxUHqEwqKwFjO/rhqaa18e62q9gfnmTw2B0X7+BcTDo+H9kCbf08ZOySiKjq8akQIhkkZxdg8qa/MPnb82UGWCsBGN9RgwNv9cCzbXwZYKncvFzs8NW41lg2sgXcHAzfaU3MyMeI1Sex9I+bKH7EPGwiIlNmUtMJqhOnE5Cx7Lp8F+9tv4KMvOIyj2tZzw0fhAajaV1OHaDHk5JdiDnbr2LPtXtlHhfi64YvRrbkbl9EZDIUOye2OjHEktwy84rx/q9XsePinTKPc7RRYcbTgRjbQcM7r1RlRFHEzst38d62K8gqY66ss50anw5vgT5BtWTsjohIGkOsBIZYktORiBTM2HIZ97IKyjyuR+OaWDikGbzduOoAVY87Gfl486eLOBWdVuZxk7oF4D9PNYY11x4mIiNiiJXAEEtyyCsqwUe7b2DTqdgyj3N3sMb7A4MQ2sIbgsC7r1S9tDoRXx2NxpK9N1GiM/wlv62fO5aPaoVaLnYydkdE9D8MsRIYYqm6Xb+Thak/nMetlNwyj+vfrA7mD24KTy4+TzK7kpCJaT9eQHSq4WvU09EGXzzXEp0a1JCxMyKiv1Ukr/FzI6LHJIoiNp2MQejK42UGWBc7NZaNbIEVo1sxwJJRNPNxxc7Xu2BIS2+Dx9zPLcLYdWew/vhtWMg9DiJSKJNaJ5ZIaTLzivHOL5cf+RR414Y1sHhYCGq78mNaMi4HGzU+HR6Ctn4emPfrNcntjrU6EfN+u47wu9mYH9oUtmqVETolIiobQyxRJf0Vm4bXf7iIxIx8g8fYWVvhvX5NMKaDhnNfyWQIgoBR7euhuY8rXvnuL8SnSV/Dm8/FIyolB1+OaQUvZ/4ARkSmhdMJiCpIpxOx8lAUhq8+VWaAbe7jit+ndcPYjn4MsGSSgr1dsXNq1zKX1/orNh2Dlx/H5YQM+RojIioHhliiCsjML8bLm87hkz03oS3jKe+XuwVgy+RO8K/hKGN3RBXnam+NNWNb460+jQweczezAM+uOonfr9yVsTMiorIxxBKVU/jdLAxafgz7w5MNHuPhaINvXmiLd/s1gY2af71IGQRBwNQnGmLN2NZwtJGe/1pYosOU78/j66PRfOCLiEwCv8sSlcO2CwkYsvI4Yu/nGTymY4Anfp/WFT0be8nYGVHVebJpbWx7tTM0BrahFUXgg13hmPfrtTI/iSAikgNDLFEZikp0eH/HVUzffAkFxfpPcQOAlQC81acRvp3YnovEk+I1quWMHa92Rpcy1ondcDIWkzadQ16R4e1siYiqG0MskQFJWQUYseYkNp40vPtWDScbfP9SB0x9oiFUVnx4i8yDm4MN1r/QFuM7agwesz88GSNWn0JydtlbKxMRVReGWCIJl+IzMGj5MVyIyzB4TGuNO3ZO7YoOAZ7yNUYkE7XKCvMGNcXs/k1gaHGNK4mZGPblScSVMc2GiKi6MMQS/cuvl+5g+OqTSMoqNHjM85388MNLHbh5AZk1QRAwsWsAvhzdCrYGHlSMS8vD0FUnEH43S+buiMjSMcQS/T+dTsSSvTfx+g8XUFgiPf/V3lqFZSNbYN6gplx9gCzG08F18MPLHeDhaCNZT8kuxIjVJ3EuJk3mzojIkvG7MBGA3MISTP72Lyw/GGXwGD9PB2x7tRMGtzC87zyRuWpVzx3bpnRCgIG1j7MKSjBm7WkcvGl4CToioqrEEEsWLyE9D0O/PIE/ricZPKZrwxrY8WoXBNZ2kbEzItOi8XTEllc6IcTHVbJeUKzDSxvOYcfFRJk7IyJLxBBLFu1SfAZCVxzHjXvZBo95obMfvnm+LVwdrGXsjMg0eTja4LuXOqBzA+kHGkt0It7YfBE/nYuXuTMisjQMsWSx9l1Pwsg1p5CaUyRZt1YJWPRMM8wd2BRqFf+qED3gZKvGuufb4ummtSXrogjM2HIZ35+Ok7kzIrIk/M5MFmnjyRhM2nQO+cVaybqHow2+fbE9RrarJ3NnRMpgq1ZhxehWGNHG1+Ax7267go0nY+RriogsitrYDRDJSacT8dHv4fjq6G2DxwTWdsZX49rA10N6600i+pvKSsCioc3g5miN1YejJY95f8c1FGtFvNjFX+buiMjcMcSSxSgo1uLNny5i95V7Bo/p0bgmlo9qBSdb/tUgKg9BEDCrbxM4WKvx2f4IyWMW7LyOEq0Ok7rXl7k7IjJn/E5NFiEjrwgTN5zDudh0g8c8164eFgzm/FeiypjWuyHUKgGL996UrH/0+w3oROCVHgyyRFQ1GGLJ7N3LLMC4dacRkZRj8JgZTzfGK93rQzC0vyYRPdKrPRvARmWFhbvDJesf77kBW7UVJnBqARFVAYZYMmvRKTkYu/YMEjPyJes2KissfrY5NzAgqiIvdQuAWiUg7LfrkvX5O6/D1toKo9trZO6MiMwNQyyZrauJmRi/7gzu50ovoeVip8ZX49qgfYD0epdEVDkvdPaHWmWFOduvStbf23YVtmoVhrX2kbkzIjInDLFklk7cSsXLG/9CTmGJZN3bzR4bJrRFAy9nmTsjsgxjO2hgJfwdWKXM2HIJtmorDAypK3NnRGQuGGLJ7Oy5eg+v/3ABRVqdZL1RLSdsnNAetV3tZO6MyLKMbq9BYbEO83fqTy3QicAbmy/CRm2FpwxsmkBEVBY+hk1m5aez8Zjy3V8GA2yrem74aVJHBlgimUzo4o8ZTzeWrGl1Il77/jyORabK3BURmQOGWDIbG0/GYMYvl6ETpevdG9XEtxPbw83BRt7GiCzclB4N8PoTDSVrxVoRL286h0vxGfI2RUSKxxBLZmHNkVt4f8c1g/VBIXXx1bg2cLDhDBoiY5jeuyEmdQuQrOUVafHC+rOISja8DB4R0b8xxJKiiaKI//4ZiQ933zB4zPiOGnw+ogVs1LzciYxFEATM7BuI5zv5SdbTcoswft0Z3M2UXg6PiOjf+F2dFEsURSzeexOf7pPe6hIA3ujdEPMGNYWVFTcxIDI2QRDw/oAgPNNSel3mxIx8jFt7Bhl50sviERH9E0MsKZIoiliwMxwrD90yeMzs/k3wRu9G3IWLyIRYWQn4eFhz9Ar0kqxHJufghfVnkVckvTweEdEDDLGkODqdiNnbr2Ld8dsGj1kwuCkmdpWef0dExmWtssKKUa3QWuMuWb8Ql4HXvr+AEgOrjBARAQyxpDA6nYh3t13Bd6fjJOuCAHwytDnGdvSTtzEiqhB7GxXWjW+LxrWkNxw5cCMZ8367BlE0sNwIEVk8kwuxqampmDBhAkJCQuDp6Qk7OzsEBATgueeew6VLl4zdHhmRTifive1X8ePZeMm6ykrA5yNaYHhbX5k7I6LKcHWwxsYX28HbzV6y/u2pOHx1NFrmrohIKSq93lBycjK8vKTnND2OjIwM3LhxA71794ZGo4GjoyNiYmKwfv16tG3bFrt27UKfPn2q/Pcl0yaKIt7/9Sp+OCN9B9ZaJeC/I1uib7M6MndGRI+jlosdvp3YHsO+PIH7ufoPdH24+wa83RzQvzn/bhPRwwSxkp/V2NraIjQ0FJMmTUKvXr2qui89iYmJ0Gg06NatGw4cOFDh1wcE/D0/MjqaP9UrjSiKmPfrNWw4GStZt1FZ4csxrfBEk1oyd0ZEVeVCXDqe++oUCor158HaqK3ww0vt0VrjYYTOiEhOFclrlZ5O0KhRI/z888/o06cPGjVqhKVLl+L+/fuVfbtHql27Nuzt7ZGenl5tvweZHlEUMX/ndcMBVm2Fr8a3YYAlUriW9dzx+YiWkFpMpKhEh5c2/oWY1Fz5GyMik1XpEHvlyhUcPXoUo0ePRkJCAt5++234+PhgzJgxOHr06GM3VlxcjNTUVNy7dw9nzpzB6NGjkZOTg/79+z/2e5MyiKKID3eH45vjMZJ1G5UVVo9pje6NasrbGBFVi6eDa2N2/yDJWlpuEZ7/5gzSJKYcEJFlqvR0gn/KyMjA+vXrsWbNGty4cQOCIKBx48aYPHkyxo0bBzc3twq/56FDh9CzZ8/S/3ZxccGkSZOwcOFCWFtbS77mwS1oKfHx8fD19eV0AoUQRRGL9tzA6sPS/7+sVQJWjWnNO7BEZkYURYT9dh3rT8RI1jsEeGDTi+1hrTK555KJqArIMp3gn9zc3PDGG2/g+vXrOHToEJ577jncvn0b06dPh7e3N1544QWcO3euQu8ZEhKCffv2YefOnfj000/RsGFD5OXloaiIP4Vbgs/2RxoMsGorAStGcQ4skTkSBAFzBgSht4G/36ei0zDv12syd0VEpqhK7sT+U2RkJFavXo3169cjLS0NVlZW0Ol0EAQBoaGhWLt2baXuzKanpyMkJARBQUHYs2dPhV/PB7uU46sj0Vi4O1yyprYSsHxUKzwdXFvmrohITnlFJRi55hQuJ2RK1hcMbsr1oInMkOx3YouLi7F582Y88cQTCAwMxKeffoqaNWvis88+Q2pqKvbv348+ffpg27ZtePXVVyv1e7i7u2PQoEHYu3cvYmJiqqJtMkE/nIkzGGBVVgK+eK4lAyyRBXCwUePr8W1Q19VOsj7vt+s4EZUqc1dEZEoeK8RGRUVhxowZ8PHxwahRo3D06FEMGTIE+/fvR3h4OKZNmwY3Nzf06tULe/bswaBBg7B79+5K/375+fkAwBUKzNSvl+7g3W1XJGtWArBsZAuuA0tkQbyc7bBmXBvYW6v0alqdiFe+O88VC4gsWKVDbO/evdG4cWMsWbIE1tbWeP/99xETE4MtW7YYXDe2devWyMrKKvN9k5KSJMdjYmKwfft2uLq6okmTJpVtm0zUn+FJeHPzRRia3LJ4WAgGNK8rb1NEZHTB3q74dHiIZC0zvxgTN55DVkGxzF0RkSmo9I5dBw4cQM+ePTFlyhSEhoZCpdL/SfnfQkNDUa9evTKP+eijj7Bv3z7069cPfn5+EAQB4eHh2LhxI3JycrBhwwbY2Ul/vETKdOJWKl757jxKdNIJdv7gphja2kfmrojIVPRtVgfTezfCZ/sj9GpRyTmY9sMFfD2+LVRWEovMEpHZqnSIDQ8PR+PGjSv0mmbNmqFZs2ZlHjNgwAAkJiZiy5YtSE5ORklJCerUqYMBAwZg2rRpaNeuXWVbJhN0IS4dEzecQ1GJ/i49APD2U40xjg9vEFm8159ogIikbOy6clevdvBmCpbtj8CbT1bsexIRKVuVr05gqrg6gem5cS8LI1afQma+9EeBk7vXx8y+gTJ3RUSmKr9Ii2dXn8DVROlpaV+Pa4PeQVx6j0jJZF+dgKiiEtLzMH7dGYMBdkyHenjnad5VIaL/sbdRYc3YNqjhZCtZn775Im7zQS8ii8EQS7JLzy3CuHVnkJRVKFkf0tIb8wcFQ5DaRJ2ILFpdN3usHtsK1ir9rw/ZhSWYvOkv5BWVGKEzIpIbQyzJKq+oBBM2nEV0ivTdkieDamHxsOaw4gMaRGRAa40H5gwIkqzdTMrGO79cgYXMlCOyaAyxJJtirQ6vfX8BF+IyJOud6nviv8+1hJp7ohPRI4ztoMEzrbwla79duoN1x2PkbYiIZMe0QLIQRRHvbr2CAzeSJetBdVywemxr2Eksak5E9G+CIODDIc0QVMdFsv7h7nCcjr4vc1dEJCeGWJLF4r038fNfCZI1Xw97rJ/QFs521jJ3RURKZmetwuqxreFqr/+1Q6sT8er3F5CcXWCEzohIDgyxVO3WH7+NlYduSdY8HW2wcUJ7eDlzAwsiqjhfDwcsG9kCUs+BpuYU4o0fL0JrYCMVIlI2hliqVrsu30XYzuuSNQcbFb55oS38azjK3BURmZMejb0wvXcjydqJW/ex/ECUzB0RkRwYYqnanItJw/SfLkLqIWG1lYBVY1qjuY+b7H0Rkfl5rWcDPBHoJVn7/M8InIhKlbkjIqpuDLFULWJSc/HSRsPbyS55NgTdGtWUuSsiMldWVgKWDg+Bt5u9Xk0UgWmbLyIlW3ptaiJSJoZYqnLpuUV4Yf1ZpOdJ78b1Xr8mCG0pvTQOEVFluTnY/L1Mn8Q60ynZhZi+mfNjicwJQyxVqYJiLV7edM7g1o8TOvvjpW4BMndFRJaitcYdMwxsWX0sKhUrDnJ+LJG5YIilKqPTiXh7y2WcjUmXrD8ZVAvv9W8ic1dEZGkmdgkwPD92fwRO3uL6sUTmgCGWqszSfTfx26U7krUQH1csG9kSKm4nS0TVzMpKwJJnQ1DXVX/pPp0IvLH5AtJzi4zQGRFVJYZYqhI/nonDioPSa8H6uNvj6/FtYW/D3biISB7ujjb4YpT0D85JWYV455fLEKWWTiEixWCIpcd2NDIF722/KllztlPjm+fboqazrcxdEZGla63xwIynpOfH/nE9CT+ciZe5IyKqSgyx9FiiknMw5bvzkk/8qq0ErB7TGg1rORuhMyIi4KWuAehuYDm/+TuvISo5R+aOiKiqMMRSpWXkFWHihrPILiiRrC8a2hydGtSQuSsiov95MD/W09FGr1ZQrMO0Hy+gsERrhM6I6HExxFKlFGt1mPLdecTcz5Osv/5EQwxr7SNzV0RE+mo622Lxs80la9fuZGHpHxEyd0REVYEhliol7LdrOGFgmZrBLepieu+GMndERGRYr8BaGN9RI1lbcyQaRyNTZO6IiB4XQyxV2KaTMfj2VJxkrYWvGz4e2hyCwKW0iMi0zOrXBI0NzNF/66dLSOOyW0SKwhBLFXIsMhXzfrsuWavjaoc141rDzppLaRGR6bGzVmHZcy1go9b/1pecXYhZW7nsFpGSMMRSud1OzcWU7/6SXInA3lqFr8a1gZez/uLiRESmIrC2C2b1DZSs7b2WhG0XEmXuiIgqiyGWyiUzvxgvbjiLLAMrEXw6PATB3q4yd0VEVHHPd/JDj8bSy27N/fUa7mTky9wREVUGQyw9UolWh9e+P4/olFzJ+pt9GqFvszoyd0VEVDmCIGDxsBB4SCy7lV1Qwt28iBSCIZYeafEfN3E0MlWyNjCkLqb2aiBzR0REj6emsy0+HBIsWTsamYpvT8XK3BERVRRDLJXpt0t3sPpwtGStuY8rFg/jSgREpExPB9fBkJbekrUPd99ATKr0p09EZBoYYsmg8LtZmLHlsmStlostvhrXhisREJGizRvUFLVd9B9IzS/W4q2fL0k+yEpEpoEhliRl5BXh5U3nkF+svx2jjcoKq8a0Ri2JL/xEREriam+NT4ZJ7+b1V2w61hyR/iSKiIyPIZb0aHUipv5wAfFp0k/ofhAajJb13GXuioioenRrVBNjOtSTrH22LwI37mXJ3BERlQdDLOlZvNfwg1xjO2gwvK2vzB0REVWvd/s1gcbTQW+8SKvDf36+hBKtzghdEVFZGGLpITsv38Gqw7cka2393DFnQJDMHRERVT8HGzWWPhsCqedUryZmYc1RTisgMjUMsVTqxr0svP2z4Qe5VoxuJbldIxGROWjj54GXuwVI1j7fH4lbKTkyd0REZWEiIQBAZl4xXt74V5kPcnFLWSIyd2/2aYQGXk5640UlOszYcpmrFRCZEIZYgk4n4o3NFxCXlidZXxDalA9yEZFFsFWr8PHQ5pLTCv6KTcfGkzGy90RE0hhiCV8evoWDN1Mka6Pb18OIttJP7RIRmaPWGne80MlfsvbJnpuIN/ADPxHJiyHWwh2PSsXSP25K1lpr3DF3YFOZOyIiMr7/PNUI9Tz0VyvIL9Zi5tbLEEVOKyAyNoZYC3YvswCv/3ABUlO8ajrb4ks+yEVEFsrBRo1FzzSTrB2Puo/NZ+Nl7oiI/s3kEkpERATmzp2LTp06wcvLC05OTmjWrBlmzZqF9PR0Y7dnNoq1Orz2/Xnczy3Sq6msBCx/riW8uCMXEVmwTg1q4Ll20tOpFu4Kx73MApk7IqJ/MrkQu27dOixduhQajQazZ8/GkiVLEBQUhEWLFqFFixZISkoydotmYdHvN3AuVvqHghlPNUb7AE+ZOyIiMj2z+gWitsQP9NmFJZiz46oROiKiB0wuxA4bNgwJCQn44Ycf8Prrr2Py5MnYvHkz3nvvPcTFxWHJkiXGblHxdl+5i7XHbkvWngyqZXCdRCIiS+NiZ40PnwmWrO27noS91+7J3BERPWByIbZNmzZwc3PTGx8+fDgA4MqVKzJ3ZF6iU3IwY4v0hgYaTwcsfjYEgtTaMkREFqpXYC0MaektWZv36zXkFJbI3BERASYYYg1JTEwEAHh5eRm5E+XKL9LilW/PS37BtVVbYeXoVnC1tzZCZ0REpu39AUHwcLTRG7+bWWBwhRciql5qYzdQHlqtFh988AEA4Pnnnzd4XECA4Y/B4+Pj4evrW9WtKYYoinhv+xXcTMqWrC8YHIymdV1l7oqISBncHW3wXr8meOvnS3q1DSdiMKSlN5r7uMnfGJEFU8Sd2GnTpuHEiROYNGkSevXqZex2FOnnvxKw9XyiZG14Gx8Mb2u5AZ+IqDyeaeWNjhIPvepE4N1tV1Ci1RmhKyLLZfJ3YmfPno0VK1bgmWeewfLly8s8Njo62mCtrLu05i4yKRvvG3iKNqiOC+YPln5ogYiI/kcQBCwcEoynlx1FUcnDgfVqYhY2nIzFi12kd/oioqpn0ndi582bh4ULF2LIkCH48ccfoVabfOY2OflFWrz2/QUUFOvfIXC2U+PLMa1gZ60yQmdERMoTUNMJr/VsIFlb+sdNJGbky9wRkeUy2RAbFhaGsLAwDB06FD/99BOsrfnAUWXM33nN4DzYxcNCoPF0lLkjIiJlm9Q9APVr6n/tzCvSYu6Oq9ySlkgmJhli58+fj3nz5mH48OG8A/sYdlxMxA9npLdGHN9Rg6eDa8vcERGR8tmqVfhwiPSWtPvDk7l2LJFMTC4drlixAnPnzoWvry/69euHH3/88aG6k5MTQkNDjdOcgsSk5uLdrdJr6jat64JZ/ZrI3BERkfloH+CJEW18sfmc/o2CsN+uo1ujmnCwMblvsURmxeT+hp09exbA30tiSS2npdFoGGIfobBEi9d+OI/cIq1ezdFGheWjOA+WiOhxzeoXiP3hSbifW/TQ+N3MAnxxIArvPB1opM6ILIPJTSdYv349RFE0+CsmJsbYLZq8j3bfwNXELMnah880g38NzoMlInpcbg42mD1A+lOtr49G41ZKjswdEVkWkwux9Hj2XruH9SdiJGsj2vhicAvprROJiKjiQlt4o72/h954sVbEvF+v8SEvomrEEGtGEtLz8LbEbjIA0NDLCfMGNZW5IyIi8yYIAuYPDobKStCrHY1Mxe9X+ZAXUXVhiDUTJVod3vjxIrIKSvRqdtZWWDG6FextOA+WiKiqNa7tjBc6+UnWFuy8jrwi/a/LRPT4GGLNxPKDUTgXmy5ZCxvUFI1qOcvcERGR5ZjWuyFqOtvqjT94yIuIqh5DrBn4KzYN//0zUrI2uEVdDG/jK3NHRESWxdnOGrP78yEvIjkxxCpcVkExpv14ETqJZwc0ng5YOKQZBEF/rhYREVWtQSF1+ZAXkYwYYhVuzvarSEjX36tbbSVg2ciWcLI1uaWAiYjMEh/yIpIXQ6yCbbuQgB0X70jWpvdphBa+bvI2RERk4cp6yOuDndeRL7EJDRFVDkOsQsXdz8Oc7dcka+39PTC5e32ZOyIiIsDwQ153Mguw5ki0EToiMk8MsQpUotXhjc0XkFOov2yLi50an41oIflxFhERVb+yHvL68nAU7mToTwEjoopjiFWg/x6Iwvm4DMnaoqHNUdfNXt6GiIjoIYNC6qKNxl1vvKBYh4/33DBCR0TmhyFWYc7GpGH5AenltIa38UG/ZnVk7oiIiP5NEATMHdgUUovD7Lh4B3/FpsnfFJGZYYhVkMz8YrxhYDkt/xqOmDuQ28oSEZmKZj6uGNbKR7IW9tt16KS+mBNRuTHEKsj7O64iUWIuldpKwOcjWsCRy2kREZmUt59uDEeJLb8vJ2Ri64VEI3REZD4YYhVi5+U7BpfTeuvJxgjhclpERCbHy9kOr/ZqIFn7ZM8NyQd0iah8GGIVICmrAO9tuypZ6xjgiUndAmTuiIiIymtCZ3/U83DQG0/OLsTKg1FG6IjIPDDEmjhRFPH2lsvIzC/Wq7nYqfHpiBBYcTktIiKTZWetwrv9pJfc+vrYbcSn5cncEZF5YIg1cd+ejsORiBTJ2oLQYNRx5XJaRESm7qmmtdAxwFNvvKhEhw93hxuhIyLlY4g1YbdTc/HhLukvbgOa18HgFt4yd0RERJUhCALeHxgEqQ/Ofr96D2duc8ktoopiiDVRJVod3vzpIvKL9ffZruViiw9Cg43QFRERVVaTOi54rl09ydrC3eEQRS65RVQRDLEmatXhW7hgYFeuT4aFwM3BRt6GiIjosb3ZpxGc7fSXQ7wUn4Gdl+8aoSMi5WKINUFXEzPx+X7pXbnGdtCge6OaMndERERVwdPJFq/1lF5y6+M9N1BYov/pGxFJY4g1MQXFWkzffBElEju5+NdwxKx+gUboioiIqsr4Tn7wdtN/KDchPR+bTsYaoSMiZWKINTGL995EZHKO3riVACwdHgIHG+7KRUSkZHbWKsx4urFk7YsDUcjIK5K5IyJlYog1ISdupWLtsduStVd7NkCreu4yd0RERNVhYPO6aObtqjeemV+M5Qe4AQJReTDEmoicwhK8/fNlyVqwtwtef6KhzB0REVF1sbISDG6AsPFkLOLucwMEokdhiDURH+0OR2JGvt64jdoKnw1vAWsV/1cREZmTjvU90buJl954kVaHT/beMEJHRMrCZGQCjkel4rvTcZK1GU81RsNazjJ3REREcpjZNxAqiR0Qdl6+i4vxGfI3RKQgDLFGllNYghlbpKcRtPP3wITO/jJ3REREcmng5YyRbX0lax/u4gYIRGVhiDWyDw1MI7C3VmHxsOawktqjkIiIzMYbvRvB0UalN34mJg37ricZoSMiZWCINaJjkan43sA0gneebgyNp6PMHRERkdxqOtticvf6krUlf9yEVmLdcCJiiDWa7IJivPOL9DSC9v4eGNfRT96GiIjIaCZ2DUAtF1u98YikHGy/kGiEjohMH0OskXy4+0YZ0whCOI2AiMiC2NuoML13I8nap/siuB0tkQSGWCM4GpmCH85ITyOY2TcQ9TwdZO6IiIiMbVhrHwTU0J9GlpiRb3DqGZElY4iVWXZBMd4xsBpBhwAPjO2gkbkjIiIyBWqVFf7zlPR2tMsPRCGnsETmjohMG0OszD7cHY47mQV64w42KnwylNMIiIgsWd/g2pLb0d7PLcLao9LbkhNZKoZYGR2JSMEPZ+Ila5xGQEREgiDgnacDJWtfHY3G/ZxCmTsiMl0mGWIXLVqEESNGoGHDhrCysoJarTZ2S48tp7AEs7Zekax1CPDAmPacRkBERECXhjXQuYGn3nhOYQlWHrplhI6ITJNJhthZs2bhjz/+gK+vL2rVqmXsdqrE4j3SqxE42HA1AiIietjbT0nfjd10MlbyewmRJTLJEBsVFYX09HQcOHAAjRtLT3JXknMxadh4KlayNqtvIHw9OI2AiIj+p4WvG55uWltvvEirw+f7IozQEZHpMckQW7++9M4lSlRQrMU7v1yG1PbX7f09MJrTCIiISMJ/nmoEqQ/pfjmfgMikbPkbIjIxJhlizcnyA1G4lZKrN26rtsKioc05jYCIiCQ18HLGsNY+euM68e8NEIgsnfKfmPqHgIAAg7X4+Hj4+vrK2A1w/U4WVh2WnoQ/vU8j+Essak1ERPTAtN6NsP3iHRSV6B4a//3qPVy7k4mmdfWX4yKyFLwTW01KtDq888tllOj05xEEe7tgYhd/I3RFRERK4u1mb3ATnM/2RcrcDZFpMas7sdHR0QZrZd2lrQ7rjt/GlcRMvXG1lYBPhoZAreLPD0RE9Giv9KiP70/HIb9Y+9D4/vAkXIrPQIivm3EaIzIyJqlqEJOai6V/SM9XmtQ9AEF1XWTuiIiIlKqGky3Gd/KTrH22n3NjyXIxxFYxURQxc+tlFP5r/hIABNR0xNReDY3QFRERKdmkbgFwstX/8PTQzRT8FZtuhI6IjI8htor9eDYep6LT9MYFAfhkaHPYWauM0BURESmZu6MNJnT2k6x9uu+mvM0QmQiTnBO7adMmxMb+vTlAbGwsRFHEBx98UFqfPXu2sVor073MAny4K1yyNraDBm38PGTuiIiIzMWLXQOw/kQMsgpKHho/HnUfp6Lvo0OA/la1ROZMEEWpZfiNq0ePHjh8+LDBemVafvBgV1kPfz0OURTx0sa/sD88Sa9W19UOf7zZXfKjICIiovL64s9ILJVYI7advwc2v9wBgsC1x0nZKpLXTHI6waFDhyCKosFfpmjP1XuSARYAFj7TjAGWiIge2/Od/eDmYK03fuZ2Go5H3TdCR0TGY5IhVmmyCoox99drkrUhLb3Rs7GXzB0REZE5crazxqRu0luzL91302Rv9BBVB4bYKrBk700kZxfqjXs62mDOgCAjdEREROZqfCcNajjZ6I1fiMvAoYgUI3REZBwMsY/pQlw6Np2KlazNGRAED0f9LzRERESV5WCjxuTu0ndjP9sXwbuxZDEYYh9DsVaHWVuvQOrrRdeGNTC4RV35myIiIrM3poMGXs62euOXEzJ5N5YsBkPsY1h37DZu3MvWG7dVW+GD0GA+JUpERNXCzlqFV3s2kKwt2x/Ju7FkERhiKyk+Lc/gdn+vP9EQGk9HmTsiIiJLMrKdL2q72OmNX4zPwLGoVCN0RCQvhthKEEURs7dfRUGx/tayjWo54aWuAUboioiILImtWoXJ3aW/3/BuLFkChthK2HXlLg4bmHP00TPNYKPmaSUiouo3sl091HDSnxt7LjYdJ6O5biyZN6atCsrML0bYb9cla6Pa10NrDbeWJSIiedhZG74b+98/I2XuhkheDLEV9MmeG0iRWBO2hpMt3nkq0AgdERGRJRvVvh48JZZzPBWdhjO304zQEZE8GGIr4K/YNHx3Ok6yNndgEFwltgIkIiKqTg42arzUTfpu7BcHeDeWzBdDbDkVa3V4d+tVyVr3RjUxoHkdmTsiIiL629gOGrhL3Eg5GpmKv2LTjdARUfVjiC2nr45G42aS/pqwdtZcE5aIiIzL0VaNF7v4S9Z4N5bMFUNsOSSk5xmcIP9G70bw9XCQuSMiIqKHjevkBxc7td74oZspuBSfIX9DRNWMIbYcwn67LrkmbGBtZ4M/+RIREcnJxc4aE3g3liwIQ+wj/BmehH3XkyRrC4c0g7WKp5CIiEzDC5384Wyrfzd2f3gyriZmGqEjourDBFaGgmIt5v12TbL2XDtftNa4y9wRERGRYa4O1ni+s59k7cvDt+RthqiaMcSWYeXBKMSn5euNuztYYwbXhCUiIhM0obM/HG1UeuO/X7mL26m5RuiIqHowxBpwOzUXqw5HS9Zm9g2Eu8TC0kRERMbm7miD0R00euM6EVjNu7FkRhhiJYiiiPd3XEWRVv9hrlb13PBsa18jdEVERFQ+L3bxh43EMxu/nE/AvcwCI3REVPUYYiXsvnIPRyNT9catBGBBaDCsrLgmLBERma5aLnYY2tpHb7xYK+Lro9KfMhIpDUPsv+QUlmDBzuuStXEd/dC0rqvMHREREVXc5O4BkLrn8v2ZOKTnFsnfEFEVY4j9l//+GYl7WfoftdR0tsWbTzYyQkdEREQVp/F0RP/mdfXG84q02HAyRv6GiKoYQ+w/3LyXjbXHbkvWZvdvAhc7/X2piYiITNWUHvUlx9efiEFuYYnM3RBVLYbY/yeKIuZsvwqtTtSrdQzwxKAQ/Z9miYiITFmTOi7oFeilN56RV4wfzsQZoSOiqsMQ+/+2nk/EmZg0vXFrlYAFoU0hCHyYi4iIlMfQ3divjkajsEQrczdEVYchFkBmfjE++j1csjaxawAaeDnL3BEREVHVaOPngXZ+HnrjSVmF2HY+0QgdEVUNhlgAn+2LQGqO/pOa3m72mNqrgRE6IiIiqjqv9JS+G7v6SLTkNDoiJbD4EHvzXjY2nYqVrL0/MAgONmqZOyIiIqpaPRrVRFAdF73x26m5+P3qXSN0RPT4LDrEiqKIub9KP8zVo3FNPBlUywhdERERVS1BEDDFwN3YVYdvQRR5N5aUx6JD7K4rd3EqWv9hLhuVFeYO5MNcRERkPvoG14F/DUe98auJWTh5674ROiJ6PBYbYvOKSrBwl/TDXC929Zf8i05ERKRUKisBk7oFSNZWH+FWtKQ8FhtiVx68hbuZ+jtz1Xaxw2s9+TAXERGZn9CW3qjhZKs3fjgiBTfuZRmhI6LKs8gQG3s/F2sM/NQ5q18gHG35MBcREZkfO2sVXujsJ1kz9H2RyFRZZIhdsPM6irQ6vfF2/h7cmYuIiMza6Pb14GCj0hv/9eId3M3MN0JHRJVjcSH24M1k7A9P1hu3EoB5fJiLiIjMnJuDDYa38dUbL9GJ+OZ4jPwNEVWSRYVYEcD8365L1sZ00CCorv4aekRERObmxS7+UFnp37T5/nQcsgqKjdARUcWZbIjdunUrOnToAEdHR7i7u2PgwIG4fPnyY71ndkEJbqfm6o27O1jjzT6NHuu9iYiIlMLXwwH9mtXRG88pLMEPp+OM0BFRxZlkiF27di2GDh2K3NxcfPzxx5g9ezauXr2Kzp0749KlS5V+36x86Z8u334qEG4ONpV+XyIiIqUxtNzWN8djUFSi/9wIkakxuRCbkZGBN998Ez4+Pjh+/Dhee+01vPXWWzh69CgEQcDrr79e6feW2pEk2NsFI9rqzw0iIiIyZ8HeruhU31Nv/F5WAX69dMcIHRFVjMmF2O3btyMrKwsTJ06Ei8v/5qj6+Phg+PDhOHLkCGJiYqrs9wsbFCw5L4iIiMjcvWzgbuxXR6K5FS2ZPJMLsadPnwYAdOrUSa/2YOzMmTNV8ns908obrTXuVfJeREREStO9UU0E1nbWG7+ZlI1DESlG6Iio/ExuVf+EhAQAf995/bcHYw+O+beAAOmfKAHg9u3bgKBC4qoXAQCCIOAXVztsn8m7sEREZLlyi7RIyynUGx+4VgUvZ/3dvYiqU3x8PNTq8sVTkwuxeXl5AABbW/2/OHZ2dg8dU1ECdPD1cKh8c6QnPj4eAODry3nFVY3ntnrwvFYPntfqIcd5dbRRwdHCvjfyeq0eVXFe1Wq1ZAaUPLbSv0s1cXD4+y9SYaH+T4X5+fkPHfNv0dGGt8x7cJe2rGOo4nheqw/PbfXgea0ePK/Vg+e1evC8Vg+5z6vJzYkta8pAYmLiQ8cQERERkWUyuRDbrl07AMCJEyf0ag/G2rZtK2tPRERERGRaTC7EhoaGwtnZGV9//TWysrJKxxMSEvDTTz+hS5cu8Pf3N2KHRERERGRsJhdi3d3dsWTJEiQkJKBz585Yvnw5Pv30U3Tt2hU6nQ7//e9/jd0iERERERmZyT3YBQAvv/wyPDw8sHjxYsyYMQM2Njbo0qULFi5ciJCQEGO3R0RERERGZpIhFgCGDRuGYcOGGbsNIiIiIjJBgsh95YiIiIhIYUxuTiwRERER0aMwxBIRERGR4jDEEhEREZHiMMQSERERkeIwxBIRERGR4jDEEhEREZHiWESI3bp1Kzp06ABHR0e4u7tj4MCBuHz5srHbUjRBEAz+ysnJMXZ7Jm/RokUYMWIEGjZsCCsrK6jVZS/ZnJeXh5kzZ8LPzw+2trbw8/PDzJkzkZeXJ1PHylCR87p+/XqD1zDXqH5YREQE5s6di06dOsHLywtOTk5o1qwZZs2ahfT0dL3jeb2WT0XOK6/X8ktNTcWECRMQEhICT09P2NnZISAgAM899xwuXbqkdzyv1/KpyHmV63o12c0OqsratWsxceJEBAcH4+OPP0ZhYSGWL1+Ozp0749ixY9wB7DF07doVL7/8st64nZ2dEbpRllmzZsHNzQ0tW7ZETk4OUlJSDB6r1WrRr18/HD58GGPHjkW3bt1w5coVLFmyBKdPn8b+/fuhUqlk7N50VeS8PvDuu++iSZMmD41pNJrqalGR1q1bh+XLl2PgwIEYOXIkbGxscPDgQSxatAjff/89zpw5g1q1agHg9VoRFTmvD/B6fbSMjAzcuHEDvXv3hkajgaOjI2JiYrB+/Xq0bdsWu3btQp8+fQDweq2IipzXB6r9ehXNWHp6uuji4iL6+PiImZmZpePx8fGis7Oz2K1bNyN2p2wAxPHjxxu7DcWKiooq/ffu3buLKpXK4LFr164VAYhTp059aPzzzz8XAYjr1q2rtj6VpiLn9ZtvvhEBiAcPHpShM2U7e/asmJ6erjf+3nvviQDE//znP6VjvF7LryLnldfr40tISBBVKpXYs2fP0jFer49P6rzKdb2a9XSC7du3IysrCxMnToSLi0vpuI+PD4YPH44jR44gJibGeA2agaKiImRnZxu7DcWpX79+uY/duHEjAOCtt956aHzSpElwdHQsrVPFzus/5eTkoKioqIq7MR9t2rSBm5ub3vjw4cMBAFeuXCkd4/VafhU5r//E67VyateuDXt7+4emavB6fXxS5/WfqvN6NesQe/r0aQBAp06d9GoPxs6cOSNrT+Zky5YtcHBwgIuLCzw9PTFx4kQkJSUZuy2zIooizp07h7p16+p9BGNnZ4dWrVrh7NmzELl7dKUNHjwYzs7OsLW1RVBQEL788kuez3JKTEwEAHh5eQHg9VpV/n1e/4nXa/kVFxcjNTUV9+7dw5kzZzB69Gjk5OSgf//+AHi9Vtajzus/Vff1atZzYhMSEgD8fef13x6MPTiGKqZt27YYNmwYGjVqhJycHOzbtw/r1q3D/v37cfr0ab15XFQ5aWlpyM3NRdOmTSXrPj4+OHr0KNLT0+Hh4SFzd8rm4OCAkSNH4oknnkDt2rURGxuL1atXY8qUKbh06RJWrVpl7BZNmlarxQcffAAAeP755wHweq0KUucV4PVaGcePH0fPnj1L/9vFxQVvv/025s6dC4DXa2U96rwC8l2vZh1iHzxZaGtrq1d78PARnz6snH/fwR4zZgw6dOiAKVOmICwsDCtXrjRSZ+alrGsYePg65hfZihk+fHjpx7YPTJo0CT169MDq1avxwgsvoH379kbqzvRNmzYNJ06cwKRJk9CrVy8AvF6rgtR5BXi9VkZISAj27duHwsJCRERE4LvvvkNeXh6KiopgbW3N67WSHnVeAfmuV7OeTuDg4AAAKCws1Kvl5+c/dAw9vldeeQU1a9bErl27jN2K2SjrGgZ4HVc1tVqNWbNmAQB2795t5G5M1+zZs7FixQo888wzWL58eek4r9fHY+i8GsLrtWzu7u7o3bs3+vfvj+nTp2Pfvn349ddfMXToUAC8XivrUefVkOq4Xs06xJY1ZeDBnCOpqQZUeRqNplzLGlH5eHh4wMHBweC0l8TExNL1j6lq+Pn5AQCvYwPmzZuHhQsXYsiQIfjxxx8fWouX12vllXVey8Lrtfzc3d0xaNAg7N27FzExMbxeq8i/z2tZqvp6NesQ265dOwDAiRMn9GoPxtq2bStrT+ZMp9MhOjoatWvXNnYrZkMQBLRp0wZ37txBbGzsQ7WCggKcP38ebdq0gSAIRurQ/ERGRgIAr2MJYWFhCAsLw9ChQ/HTTz+VfnT4AK/XynnUeS0Lr9eKeXB3NT09nddrFfrneS1LlV+v1bqAl5GlpaWJzs7OBteJ7dKlixG7U6579+5Jji9cuFAEIE6bNk3ehhTuUeuZfvXVV5LrGC5btkwEIH799dfV3aIiPeq8Sl3Hubm5YosWLUQA4vnz56uzPcUJCwsTAYjDhw8Xi4uLDR7H67Viynteeb2Wn6HvUbdv3xY9PDxEV1dXMT8/XxRFXq8VUZHzKtf1Koiiea8dsWbNGkyaNAnBwcGYNGkSioqK8MUXXyAlJQVHjx5Fy5Ytjd2i4rzxxhv4888/MWDAAGg0GuTl5WHfvn3Ys2cPAgMDcfz4cU6Cf4RNmzaV/uS/du1axMXFISwsrLQ+e/bs0n/XarXo2bMnjh49inHjxqFbt264fPkyVqxYgU6dOuHgwYPcUeb/VeS81qxZE127dkXr1q1Rq1YtxMXFYcOGDYiLi8Pbb7+NTz75RPb+TdWKFSvw2muvwdfXFwsWLNC73pycnBAaGgqA12tFVOS88notvzfeeAP79u1Dv3794OfnB0EQEB4ejo0bNyInJwcbNmzAmDFjAPB6rYiKnFfZrtcqicIm7ueffxbbtWsn2tvbi66urmL//v3FixcvGrstxdqxY4f41FNPid7e3qKtra1ob28vBgcHi7NnzxazsrKM3Z4idO/eXQRg8Ne/ZWdni2+//bZYr1490draWqxXr5749ttvi9nZ2Ubo3nRV5Ly++eabYuvWrUVPT09RrVaL7u7uYu/evcUtW7YYqXvTNX78+DLPq0ajeeh4Xq/lU5Hzyuu1/Pbt2ycOGzZM9PPzEx0cHEQbGxtRo9GIo0aNEk+fPq13PK/X8qnIeZXrejX7O7FEREREZH7M+sEuIiIiIjJPDLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxRERERKQ4DLFEREREpDgMsURERESkOAyxREQKEhoaCkEQ8MUXX+jV5syZA0EQMGnSJCN0RkQkL0EURdHYTRARUfmkpaWhZcuWSEpKwsmTJ9GyZUsAwJ9//oknn3wSQUFBOHPmDOzt7Y3cKRFR9WKIJSJSmBMnTqB79+7w9/fH+fPnkZeXh5CQEGRlZeHs2bMICgoydotERNWO0wmIiBSmU6dOWLBgASIjIzFp0iSMGTMG9+7dwxdffMEAS0QWg3diiYgUSBRF9O3bF3v37gUAPPfcc/j++++N3BURkXx4J5aISIEEQcCQIUNK//uNN94wXjNEREbAO7FERAoUGRmJVq1awdraGpmZmQgODsbp06dhZ2dn7NaIiGTBO7FERApTWFiIESNGIDc3F5s3b8asWbNw+fJlTJ8+3ditERHJhiGWiEhh/vOf/+DChQt455130KdPH4SFhaFz585YtWoVtmzZYuz2iIhkwekEREQKsn37dgwZMgQdO3bEkSNHoFarAQDx8fFo0aIFtFotLl68CD8/P+M2SkRUzRhiiYgUIi4uDi1atIAoirh48SI0Gs1D9R07diA0NBTt27fH0aNHYW1tbaROiYiqH0MsERERESkO58QSERERkeIwxBIRERGR4jDEEhEREZHiMMQSERERkeIwxBIRERGR4jDEEhEREZHiMMQSERERkeIwxBIRERGR4jDEEhEREZHiMMQSERERkeIwxBIRERGR4jDEEhEREZHiMMQSERERkeL8H1G4qfiOXwAAAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 4))\n", "x3n = r[:, 0]\n", "y3n = r[:, 1]\n", "plt.plot(x3n, y3n, lw=4)\n", "plt.xlim(0, 36)\n", "plt.ylim(0, 6)\n", "plt.title('Ball trajectory')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### All numerical solutions plotted together"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 4))\n", "plt.plot(x1n, y1n, lw=4, c='g', label='$g$')\n", "plt.plot(x2n, y2n, lw=4, c='b', label='$g+bv$')\n", "plt.plot(x3n, y3n, lw=4, c='r', label='$g+bv^2$')\n", "plt.xlim(0, 36)\n", "plt.ylim(0, 6)\n", "plt.title('Ball trajectory')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.legend(loc='best', frameon=False)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "- Read the chapter 0 of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about mechanics  \n", "- Read pages 478-494 of the chapter 10 of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about 1D dynamics  \n", "- Read the chapter 13 of the [<PERSON><PERSON><PERSON>'s book](https://drive.google.com/file/d/1sDLluWCiBCog2C11_Iu1fjv-BtfVUxBU/view) (available in the Classroom)  \n", "- See [What is the fastest possible volleyball serve?](https://uio-ccse.github.io/computational-essay-showroom/essays/exampleessays/volleyball/Volleyball.html) for a nice investigation about the ball movement during a volleyball serve"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the internet\n", "\n", "- Khan Academy: [Forces and Newton's laws of motion](https://www.khanacademy.org/science/ap-physics-1/ap-forces-newtons-laws) (Em português: [Forças e as Leis do Movimento de Newton](https://pt.khanacademy.org/science/physics/forces-newtons-laws)).  "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "R8JH_WGNM8ja", "slideshow": {"slide_type": "slide"}}, "source": ["## Problems\n", "\n", "1. (Example 13.1, <PERSON><PERSON><PERSON>'s book) A $50 kg$ crate rests on a horizontal surface for which the coefficient of kinetic friction is $\\mu_k = 0.3$. If the crate is subjected to a $400 N$ towing force applied upward with a $30^o$ angle w.r.t. the horizontal, determine the velocity of the crate after $3 s$ starting from rest.  \n", "\n", "2. (Example 13.2, <PERSON><PERSON><PERSON>'s book) A 10 kg projectile is fired vertically upward from the ground, with an initial velocity of 50 $m/s$. Determine the maximum height to which it will travel if (a) atmospheric resistance is neglected; and (b) atmospheric resistance is measured as $F_D=0.01v^2$ $N$, where $v$ is the speed of the projectile at any instant, measured in $m/s$.  \n", "\n", "3. (Exercise 13.6, <PERSON><PERSON><PERSON>'s book) A person pushes on a $60 kg$ crate with a force $F$. The force is always directed down at $30^o$ from the horizontal as shown, and its magnitude is increased until the crate begins to slide. Determine the crate’s initial acceleration if the coefficient of static friction is $\\mu_s=0.6$ and the coefficient of kinetic friction is $\\mu_k=0.3$.  \n", "\n", "4. (Exercise 13.46, <PERSON><PERSON><PERSON>'s book) The parachutist of mass $m$ is falling with a velocity of $v_0$ at the instant she opens the parachute. If air resistance is $F_D = Cv^2$, show that her maximum velocity (terminal velocity) during the descent is $v_{max}=\\sqrt{{mg}/{C}}$. Solve the problem numerically considering $m=100 kg$, $g=10 m/s^2$, and $C=10 kg/m$. Plot a simulation of this numerical solution and show that indeed the parachutist approaches the terminal velocity.\n", "\n", "5. Consider a block with mass of $1 kg$ attached to a spring hanging from a ceiling (the spring constant $k = 100 N/m$). At $t = 0 s$, the spring is stretched by $0.1 m$ from the equilibrium position of the block + spring system and then it's released (the initial velocity is not specified). Find the motion of the block.\n", "\n", "6. <PERSON><PERSON> exercises 12.1.16, 12.1.19, 12.1.24, 12.1.29, 12.1.30, 12.1.31(a, b, d) and 12.1.32 from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book (2019).  \n", "\n", "7. (https://youtu.be/N6IhkTjWrd4) A $15 kg$ box rests on a frictionless horizontal surface attached to a $5 kg$ box as shown below.  \n", "  a. What is the acceleration of the system?  \n", "  b. What is the tension in the rope?  \n", "  c. Now consider a coefficient of kinetic friction of $0.25$ between the horizontal surface and the box. What are the acceleration of the system and the tension in the rope? \n", "  \n", "<figure><center><img src=\"../images/boxes_pulley_rope.png\" alt=\"free-body diagram of the ball\" width=\"300\"/></center></figure>\n", "\n", "8. Consider a moving particle in a fluid with viscosity proportional to the cube of the velocity. This particle moves only vertically and never reaches the ground. Consider the magnitude of the acceleration due to gravity as $10 m/s^2$.  \n", "   a. Draw the free body diagram.  \n", "   b. What is the differential equation that describes the particle's motion?  \n", "   c. If the viscosity coefficient is $5 Ns^3/m^3$, what is the maximum speed of the particle?\n", "\n"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "3l-W0R08M8jb"}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON> (2010) [Engineering Mechanics Dynamics](https://drive.google.com/file/d/1sDLluWCiBCog2C11_Iu1fjv-BtfVUxBU/view). 12th Edition. Pearson Prentice Hall.\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  \n", "- <PERSON> (2005) [Classical Mechanics](https://books.google.com.br/books?id=P1kCtNr-pJsC). University Science Books."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"collapsed_sections": [], "name": "Copy of newtonLawForParticles.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}