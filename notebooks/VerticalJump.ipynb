{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Biomechanical analysis of vertical jumps\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Introduction\" data-toc-modified-id=\"Introduction-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Introduction</a></span></li><li><span><a href=\"#Center-of-gravity\" data-toc-modified-id=\"Center-of-gravity-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Center of gravity</a></span><ul class=\"toc-item\"><li><span><a href=\"#Measurement-of-the-jump-height-from-flight-time\" data-toc-modified-id=\"Measurement-of-the-jump-height-from-flight-time-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>Measurement of the jump height from flight time</a></span><ul class=\"toc-item\"><li><span><a href=\"#Beware:-the-flight-time-you-measure-not-always-is-the-flight-time-you-want\" data-toc-modified-id=\"Beware:-the-flight-time-you-measure-not-always-is-the-flight-time-you-want-3.1.1\"><span class=\"toc-item-num\">3.1.1&nbsp;&nbsp;</span>Beware: the flight time you measure not always is the flight time you want</a></span></li></ul></li><li><span><a href=\"#Measurement-of-the-jump-height-using-a-force-platform\" data-toc-modified-id=\"Measurement-of-the-jump-height-using-a-force-platform-3.2\"><span class=\"toc-item-num\">3.2&nbsp;&nbsp;</span>Measurement of the jump height using a force platform</a></span></li><li><span><a href=\"#Force-platform\" data-toc-modified-id=\"Force-platform-3.3\"><span class=\"toc-item-num\">3.3&nbsp;&nbsp;</span>Force platform</a></span><ul class=\"toc-item\"><li><span><a href=\"#Ground-reaction-force-during-vertical-jump\" data-toc-modified-id=\"Ground-reaction-force-during-vertical-jump-3.3.1\"><span class=\"toc-item-num\">3.3.1&nbsp;&nbsp;</span>Ground reaction force during vertical jump</a></span></li></ul></li><li><span><a href=\"#Jump-height-from-the-impulse-measurement\" data-toc-modified-id=\"Jump-height-from-the-impulse-measurement-3.4\"><span class=\"toc-item-num\">3.4&nbsp;&nbsp;</span>Jump height from the impulse measurement</a></span></li><li><span><a href=\"#Kinetic-analysis-of-a-vertical-jump\" data-toc-modified-id=\"Kinetic-analysis-of-a-vertical-jump-3.5\"><span class=\"toc-item-num\">3.5&nbsp;&nbsp;</span>Kinetic analysis of a vertical jump</a></span></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-internet\" data-toc-modified-id=\"Video-lectures-on-the-internet-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Video lectures on the internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.552800Z", "start_time": "2021-02-09T21:13:02.348546Z"}}, "outputs": [], "source": ["import numpy as np\n", "from scipy.integrate import cumulative_trapezoid\n", "import matplotlib.pyplot as plt\n", "from IPython.display import YouTubeVideo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction\n", "\n", "A vertical jump is the act of raising (in the vertical direction) the body center of gravity using your own muscle forces and jumping into the air ([Wikipedia](https://en.wikipedia.org/wiki/Vertical_jump)).\n", "\n", "Besides being an important element in several sports, the vertical jump can be used to train and evaluate some physical capacities (e.g., strength and power) of a person.\n", "\n", "In this notebook we will study some aspects of the biomechanics of vertical jumps."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Center of gravity\n", "\n", "Center of gravity or center of mass is a measure of the average location in space of the body considering all body segments, their mass and their position.   \n", "From Mechanics, the exact definitions of these quantities are:   \n", "\n", "- **Center of mass (CM)**: The center of mass (or barycenter) is the unique point at the center of a distribution of mass in space that has the property that the weighted position vectors relative to this point sum to zero. SI unit: $m$ (vector).   \n", "- **Center of gravity (CG)**: Center of gravity is the point in an object around which the resultant torque due to gravity forces vanishes. Near the surface of the earth, where the gravity acts downward as a parallel force field, the center of gravity and the center of mass are the same. SI unit: $m$ (vector).\n", "\n", "The mathematical definition for the center of mass or center of gravity of a system with N objects (or particles), each with mass $m_i$ and position $r_i$ is:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l} \n", "\\vec{r}_{cm} = \\dfrac{1}{M}\\sum_{i=1}^N m_{i}\\vec{r}_i \\quad\\quad \\text{where} \\quad M = \\sum_{i=1}^N m_{i} \\\\\n", "\\vec{r}_{cg} = \\dfrac{1}{Mg}\\sum_{i=1}^N m_{i}g_{i}\\vec{r}_i \\quad \\text{where} \\quad Mg = \\sum_{i=1}^N m_{i}g_{i}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "If we consider $g$ constant:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l} \n", "\\vec{r}_{cg} = \\dfrac{g}{Mg}\\sum_{i=1}^N m_{i} \\: \\vec{r}_i = \\dfrac{1}{M}\\sum_{i=1}^N m_{i}\\:\\vec{r}_i \\\\\n", "\\\\\n", "\\vec{r}_{cg} = \\vec{r}_{cm}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "This means that how much a person jumps is measured by the displacement of his or her center of gravity, and not by how much the feet was raised in space. For instance, the following Youtube video entitled \"Highest Vertical jump 62 inches\" (157.5 cm!) shows a great vertical jump, but in fact its height is 'just' about half of that:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.651415Z", "start_time": "2021-02-09T21:13:02.553778Z"}}, "outputs": [{"data": {"image/jpeg": "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", "text/html": ["\n", "        <iframe\n", "            width=\"400\"\n", "            height=\"300\"\n", "            src=\"https://www.youtube.com/embed/NUyql5IFTNY\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7f46569b7cb0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["YouTubeVideo('NUyql5IFTNY')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So, the true measurement of the height of a vertical jump is the displacement of the center of gravity in the vertical direction. A problem is that to measure the center of gravity position is usually too complicated because we would have to estimate the position of each body segment.    \n", "Instead, there are alternative methods with varying degrees of accuracy for measuring the height of vertical jump ([see here for a list](http://www.topendsports.com/testing/equipment-verticaljump.htm)):   \n", "\n", " - [**Sargent Jump Test**](http://www.brianmac.co.uk/sgtjump.htm). How high you can reach an object.   \n", " - **Flight time measurement**. From <PERSON>'s laws of motion, the height of a jump is related to the flight time under controlled conditions. One can measure the flight time using a [contact mat](http://www.probotics.org/JustJump/verticalJump.htm), light sensor, [accelerometer](http://www.jssm.org/vol11/n1/17/v11n1-17pdf.pdf), video (the number of frames the jumper is in the air), attaching a cable to the jumper's waist and measuring the displacement of this cable, etc.      \n", " - **Force platform measurements**. A device called [force platform](https://en.wikipedia.org/wiki/Force_platform) which measures the forces applied on the ground as a function of time can also be used to measure the flight time and then the height of vertical jump. But the real utility of the force platform is that it can actually measure the force produced by the jumper to find the displacement of the center of gravity using <PERSON>'s laws of motion.   "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Measurement of the jump height from flight time\n", "\n", "We can estimate the height of a jump measuring for how long the jumper stayed in the air during the jump (flight time). At the vertical direction and ignoring the air resistance, the only external force that acts on the body in the air is the (constant) gravitational force, with magnitude $P = -mg$, where $g$ is the acceleration of gravity (about $9.8 m/s^2$ on the surface of the Earth). Using the equation of motion for a body with constant acceleration, the height of the center of gravity of a body in the air at a certain time is:   \n", "\n", "\\begin{equation}\n", "h(t) = h_0 + v_0 t - \\frac{gt^2}{2}\n", "\\label{}\n", "\\end{equation}\n", "\n", "At the maximum height ($h$, the jump height), the vertical velocity of the body is zero. We can take use this property to calculate the jump height from the time of falling:\n", "\n", "\\begin{equation}\n", "h = \\frac{gt_{fall}^2}{2}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Because the time of falling is equal to the time of rising, $t_{flight} = t_{rise} + t_{fall} = 2 t_{fall}$, the jump height as a function of the flight time is:\n", "\n", "\\begin{equation}\n", "h = \\frac{gt_{\\text{flight}}^2}{8}\n", "\\label{}\n", "\\end{equation}\n", "\n", "This simple equation is the principle of measurement of the jump height any an instrument that measures the time the feet is not in contact with ground (using a pressure mat or a photocell sensor).  \n", "There are (were) even shoes using this principle of measurement:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.740049Z", "start_time": "2021-02-09T21:13:02.652681Z"}}, "outputs": [{"data": {"image/jpeg": "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", "text/html": ["\n", "        <iframe\n", "            width=\"400\"\n", "            height=\"300\"\n", "            src=\"https://www.youtube.com/embed/QXnQ4ENjTuY\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7f46e39db050>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["YouTubeVideo('QXnQ4ENjTuY')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Beware: the flight time you measure not always is the flight time you want\n", "\n", "However, the flight time, measured as the time without contact with the ground, during a jump is not necessarily equal to the flight time of the body center of gravity (which is the measure we need to estimate the actual height jump).  \n", "\n", "For example, if the jumper flexes knees and hips at the landing phase, the measured flight time will be larger but not the flight time of the body center of gravity. Because that, a more accurate method is to use a force platform as we will see now."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Measurement of the jump height using a force platform\n", "\n", "Let's draw the [free body diagram](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/FreeBodyDiagram.ipynb) for a person performing a jump:\n", "\n", "<div class='center-align'><figure><img src=\"./../images/FBDjump.png\" alt=\"Vertical jump FBD\"/><figcaption><center><i>A person during the propulsion phase of jumping and the corresponding free body diagram drawn for the center of mass. GRF(t) is the time-varying ground reaction force.</i></center></figcaption></figure></div>\n", "\n", "So, according to the <PERSON><PERSON><PERSON>'s version of the Newton's second law (for the motion of the body center of mass), the dynamics (as a function of time) for the body center of mass during a jump is given by:\n", "\n", "\\begin{equation}\n", "GRF(t) - mg = ma(t)\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $GRF(t)$ is the ground reaction force applied by the ground on the jumper, $m$ is the subject mass, and $a(t)$ the center of mass acceleration."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Force platform  \n", "\n", "Force platform or force plate is an instrument for measuring the forces generated by a body acting on the platform. A force plate is an electromechanical transducer that measures force typically by measuring the deformation (strain) on its sensors and converting that to electrical signals. These electrical signals are converted back to force and moment of force using calibration factors. \n", "\n", "<div class='center-align'><figure><img src=\"./../images/KistlerForcePlate.png\" alt=\"A force plate and its coordinate system convention\"/><figcaption><center><i>Figure. A force plate and its coordinate system convention (from <a href=\"http://isbweb.org/software/movanal/vaughan/kistler.pdf\">Kistler Force Plate Formulae</a>).</i></center></figcaption></figure></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Usually the force platform is placed on the floor and we are interested not in the force applied on the force platform, but in its reaction, the force that the force platform applied on the jumper, which, according to <PERSON>'s third law of motion, has the same magnitude and line of action but opposite direction. Because of that, usually the forces measured by the force platform are referred as ground reaction forces. \n", "\n", "Most of the commercial force platforms are able to measure the vectors force, $[F_X,\\, F_Y,\\, F_Z]$, and moment of force, $[M_X,\\, M_Y,\\, M_Z]$, from which the [center of pressure](https://en.wikipedia.org/wiki/Center_of_pressure_(terrestrial_locomotion) (COP, the point of application of the resultant force on the force plate) can be calculated $[COP_X,\\, COP_Y]$. Because of that, these force platforms are known as six-components. Force platforms that can measure only the vertical force component and two moments of force (or the two COPs) are known as three-components.\n", "\n", "Read more about force platforms in chapter 5 or <PERSON>'s book and in Cross (1999). "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Ground reaction force during vertical jump\n", "\n", "Here is a video from Youtube of a person jumping and a plot of the ground reaction force measured with a force platform:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.824992Z", "start_time": "2021-02-09T21:13:02.741306Z"}}, "outputs": [{"data": {"image/jpeg": "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", "text/html": ["\n", "        <iframe\n", "            width=\"400\"\n", "            height=\"300\"\n", "            src=\"https://www.youtube.com/embed/qN3apht8zRs\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7f4656a1ba10>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["YouTubeVideo('qN3apht8zRs')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is a plot of the vertical component of the ground reaction force measured with a force platform during a vertical jump with countermovement:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.946896Z", "start_time": "2021-02-09T21:13:02.825938Z"}}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load data file\n", "GRFv = np.loadtxt('https://raw.githubusercontent.com/BMClab/BMC/master/data/GRFZjump.txt',\n", "                  skiprows=0, unpack=False)\n", "#GRFv = np.loadtxt('./../data/GRFZjump.txt', skiprows=0, unpack=False)\n", "\n", "# sampling frequency (Hz)\n", "freq = 600\n", "time = np.arange(0, len(GRFv)/freq, 1/freq)\n", "# gravitational acceleration, m/s2\n", "g = 9.8\n", "# subject's mass\n", "m = np.mean(GRFv[0:int(freq/10)])/g\n", "# plot GRF data\n", "fig, ax = plt.subplots(1, 1, figsize=(10, 4))\n", "ax.plot(time, GRFv, 'b', linewidth='3')\n", "# plot subject's weight\n", "ax.plot([time[0], time[-1]], [m*g, m*g], 'k', linewidth='2')\n", "ax.set_xlabel('Time [s]', fontsize=16)\n", "ax.set_ylabel('Vertical GRF [N]', fontsize=16)\n", "ax.set_xticks(np.arange(time[0], time[-1], .2))\n", "ax.grid()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This graph of the vertical component of the ground reaction force shown above has very interesting things: \n", "\n", " 1. At the beginning the jumper was standing still and the GRF is equal to the jumper's body weight;  \n", " 2. After the jumper started the movement to jump there is a phase where GRF is lower than the body weight even with the jumper completely on the ground;   \n", " 3. The GRF increases to about two times the body weight while the jumper is still on the ground;   \n", " 4. When the jumper is in the air is clearly indicated by the zero values of GRF;   \n", " 5. After landing the GRF reaches a peak of about six times the jumper's body weight.  \n", " \n", "These are all interesting things but we will discuss only some of them next.  \n", "We can detect the takeoff and landing instants by detecting the first and last time GRF is under a certain threshold (close to zero):"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.950674Z", "start_time": "2021-02-09T21:13:02.947801Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Takeoff instant 0.907 s\n", "Landing instant 1.323 s\n"]}], "source": ["# make sure GRFv has no offset\n", "GRFv -= np.min(GRFv)\n", "\n", "limiar = 10\n", "\n", "inds = np.where(GRFv < limiar)\n", "i1 = inds[0][0]\n", "i2 = inds[0][-1]\n", "print('Takeoff instant {:.3f} s'.format(time[i1]))\n", "print('Landing instant {:.3f} s'.format(time[i2]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The jump height can be calculated from these instants using the kinematic expression:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.954099Z", "start_time": "2021-02-09T21:13:02.951775Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jump height 0.213 m\n"]}], "source": ["h = g*(time[i2]-time[i1])**2/8\n", "print('Jump height {:.3f} m'.format(h))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can detect the beginning of movement by detecting when GRFv starts to go below the body weight:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.958053Z", "start_time": "2021-02-09T21:13:02.955615Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jump start 0.188 s\n"]}], "source": ["inds = np.where(GRFv < 0.95*m*g)\n", "i0 = inds[0][0]\n", "print('Jump start {:.3f} s'.format(time[i0]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Jump height from the impulse measurement\n", "\n", "We know that the [mechanical impulse](http://nbviewer.ipython.org/github/BMClab/bmc/blob/master/notebooks/KineticsFundamentalConcepts.ipynb#Impulse) is equal to the change in linear momentum of a body, which for a body with constant mass is simply the body mass times the change in velocity. For a time-varying force, the impulse is:\n", "\n", "\\begin{equation}\n", "\\vec{Imp} = \\int_{t_0}^{t_f} \\vec{F}(t) \\mathrm{d}t = m [\\vec{v}_{t_f} - \\vec{v}_{t_0}]\n", "\\label{}\n", "\\end{equation}\n", "\n", "For the analysis of a vertical jump, in the vertical direction $F(t) = GRF(t) - mg$ and $F(t)$ is discrete in time (sampled at intervals $\\Delta t$):\n", "\n", "\\begin{equation}\n", "Imp = \\sum_{t_0}^{t_f} F(t)\\Delta t = m [v_{t_f} - v_{t_0}]\n", "\\label{}\n", "\\end{equation}\n", "\n", "For a jump starting from a rest position, the initial velocity is zero. This means that the change in velocity is equal to the final velocity (at the moment of takeoff). Therefore, we can calculate the final velocity of the propulsion phase as:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\sum_{t_0}^{t_f} F(t)\\Delta t = m[v_f-0] \\implies \\\\\n", "v_f = \\dfrac{\\sum_{t_0}^{t_f} F(t)\\Delta t}{m}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "This final velocity is the initial velocity of the aerial phase of the jump.  \n", "If we know the initial velocity of the jump, it's straightforward to calculate the jump height, for example, using the [<PERSON><PERSON><PERSON>'s equation](http://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>'s_equation): \n", "\n", "\\begin{equation}\n", "v_f^2 = v_0^2 - 2gh\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $v_i$ and $v_f$ are the initial and final velocities and $h$ is the change in the position of the body center of gravity (the height of the vertical jump) and we considered just the first part of the jump, the upward phase.\n", "\n", "From Mechanics we know that the velocities at takeoff and at landing are equal in magnitude and that the time of the upward phase is equal to the time of the downward phase of this ballistic flight.\n", "\n", "For the upward phase of the vertical jump:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l} \n", "0 = v_0^2 - 2gh \\implies \\\\\n", "h = \\dfrac{v_0^2}{2g}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "We could also have calculated the jump height from the velocity using the [principle of conservation of the mechanical energy](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KineticsFundamentalConcepts.ipynb#Principles-of-conservation). The sum of the potential and kinetic energies at the instants beginning of the jump ($t_0$) and highest point of the jump trajectory ($t_{hmax}$) are equal:\n", "\n", "\\begin{equation}\n", "mgh(t_0) + \\frac{mv^2(t_0)}{2} = mgh(t_{hmax}) + \\frac{mv^2(t_{hmax})}{2}\n", "\\label{}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "0 + \\frac{mv^2(t_0)}{2} =  mgh(t_{hmax}) + 0\n", "\\label{}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "h(t_{hmax}) = \\frac{v^2(t_0)}{2g}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Same expression as before.   "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Kinetic analysis of a vertical jump\n", "\n", "The force plate can also be used to calculate other mechanical variables to characterize the subject's performance (see <PERSON><PERSON> and <PERSON> 1993) for a list of such variables). For example, \n", "\n", "Resultant vertical force on the subject:\n", "\n", "\\begin{equation}\n", "F(t) = GRF(t) - mg\n", "\\label{}\n", "\\end{equation}\n", "\n", "Vertical acceleration of the center of mass:\n", "\n", "\\begin{equation}\n", "a(t) = \\frac{F(t)}{m}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Vertical velocity of the center of mass (initial velocity equals zero):\n", "\n", "\\begin{equation}\n", "v(t) = \\sum_{t_0}^{t_f} a(t) \\Delta t\n", "\\label{}\n", "\\end{equation}\n", "\n", "Vertical displacement of the center of mass (initial displacement equals zero):\n", "\n", "\\begin{equation}\n", "h(t) = \\sum_{t_0}^{t_f} v(t) \\Delta t\n", "\\label{}\n", "\\end{equation}\n", "\n", "Power due to the resultant vertical force:\n", "\n", "\\begin{equation}\n", "P(t) = GRF(t)v(t)\n", "\\label{}\n", "\\end{equation}\n", "\n", "Note that we used the vertical ground reaction force for the calculation of mechanical power, not the resultant force on the body, to represent the total power the jumper is producing. One could argue that during standing the jumper is not actually 'generating' any force (so we should not use GRF), but in this phase, the jumper's speed is zero, and the power is zero.\n", "\n", "Let's work with the ground reaction force data from a vertical jump and calculate such quantities.  \n", "For now, let's ignore the landing and focus on the propulsion phase of the jump."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:02.962356Z", "start_time": "2021-02-09T21:13:02.959414Z"}}, "outputs": [], "source": ["time = time[i0:i1+1] - time[i0]\n", "# resultant vertical force\n", "F = GRFv[i0:i1+1] - m*g\n", "# vertical acceleration of the center of mass\n", "a = F/m\n", "# vertical velocity of the center of mass\n", "v = cumulative_trapezoid(a, dx=1/freq, initial=0)\n", "# vertical displacement of the center of mass\n", "h = cumulative_trapezoid(v, dx=1/freq, initial=0)\n", "# power due to the resultant vertical force\n", "P = (F+m*g)*v"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And here are the plots for these quantities:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:03.416195Z", "start_time": "2021-02-09T21:13:02.963611Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(5, 1, sharex=True, squeeze=True, figsize=(8, 8))\n", "axs[0].plot(time, F, 'b', lw='4', label='Force')\n", "axs[1].plot(time, a, 'b', lw='4', label='Acceleration')\n", "axs[2].plot(time, v, 'b', lw='4', label='Velocity')\n", "axs[3].plot(time, h, 'b', lw='4', label='Displacement')\n", "axs[4].plot(time, P, 'b', lw='4', label='Power')\n", "axs[4].set_xlabel('Time [$s$]', fontsize=14)\n", "axs[4].set_xticks(np.arange(time[0], time[-1], .1))\n", "axs[4].set_xlim([time[0], time[-1]])\n", "ylabel = ['F [$N$]', 'a [$m/s^2$]', 'v [$m/s$]', 'h [$m$]', 'P [$W$]']\n", "for (axi, ylabeli) in zip(axs, ylabel):\n", "    axi.axhline(0, c='r', linewidth='2', alpha=.6)\n", "    axi.set_ylabel(ylabeli, fontsize=14)\n", "    axi.text(.03, .75, axi.get_legend_handles_labels()[1][0],\n", "             transform=axi.transAxes, fontsize=14, c='r')\n", "    axi.yaxis.set_major_locator(plt.MaxNLocator(4))\n", "    axi.yaxis.set_label_coords(-.1, 0.5)\n", "    axi.grid(True)\n", "plt.tight_layout(h_pad=0)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looking at these graphs, we can see that the instant where the jumper has the lowest position is approximately at 0.44 s, where the velocity is also zero. Up to this instant the jumper was moving down (countermovement phase) and from this instant on the jumper starts to rise until loses contact with the ground. This instant also indicates when the power starts to be positive. Looking at the graph of the force, the total area under the curve (the impulse) up to this instant should be zero because if the jumper started at rest and came to zero velocity again, the impulse is zero.  \n", "\n", "This turning point is used to indicate the change from eccentric (negative power) to concentric (positive power) phase of the jump. Although this instant was detected based on the movement of the center of mass, this roughly indicates the change in the net muscle activation in the body to perform the jump.\n", "\n", "Let's visualize these phases in the plots:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:03.419285Z", "start_time": "2021-02-09T21:13:03.417118Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Instant of the lowest position 0.438 s\n"]}], "source": ["inds = np.where(v > 0)\n", "iec = inds[0][0]\n", "print('Instant of the lowest position {:.3f} s'.format(time[iec]))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-02-09T21:13:03.606781Z", "start_time": "2021-02-09T21:13:03.420151Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1, 2, sharex=True, squeeze=True, figsize=(12, 4))\n", "axs[0].plot(time, F+m*g, 'b', linewidth='2')\n", "axs[0].plot([time[0], time[-1]], [m*g, m*g], 'k', linewidth='1')\n", "axs[0].fill_between(time[0:iec], F[0:iec]+m*g, m*g, color=[1, 0, 0, .2])\n", "axs[0].fill_between(time[iec:-1], F[iec:-1]+m*g, m*g, color=[0, 1, 0, .2])\n", "axs[0].plot([], [], linewidth=10, color=[1, 0, 0, .2], label='Eccentric')\n", "axs[0].plot([], [], linewidth=10, color=[0, 1, 0, .2], label='Concentric')\n", "axs[0].legend(frameon=False, loc='upper left', fontsize=12)\n", "axs[0].set_ylabel('Vertical GRF [$N$]', fontsize=14)\n", "axs[0].set_xlabel('Time [$s$]', fontsize=14)\n", "axs[0].yaxis.set_major_locator(plt.MaxNLocator(5))\n", "axs[1].plot(time, P, 'b', linewidth='2')\n", "axs[1].plot([time[0], time[-1]], [0, 0], 'k', linewidth='1')\n", "axs[1].fill_between(time[0:iec], P[0:iec], color=[1, 0, 0, .2])\n", "axs[1].fill_between(time[iec:-1], P[iec:-1], color=[0, 1, 0, .2])\n", "axs[1].set_ylabel('Power [$W$]', fontsize=14)\n", "axs[1].set_xlabel('Time [$s$]', fontsize=14)\n", "axs[1].set_xticks(np.arange(time[0], time[-1], .1))\n", "axs[1].yaxis.set_major_locator(plt.MaxNLocator(5))\n", "axs[1].set_xlim([time[0], time[-1]])\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "- [The Physics of Nike+ Hyperdunk](https://www.wired.com/2012/09/the-physics-of-nike-hyperdunk-2/)  \n", "- <PERSON><PERSON><PERSON><PERSON> NP (2001) <a href=\"http://www.brunel.ac.uk/~spstnpl/Publications/VerticalJump(Linthorne).pdf\" target=\"_blank\">Analysis of standing vertical jumps using a force platform</a>. American Journal of Physics, 69, 1198-1204. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the internet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- [Physics breakdown of a jump](https://youtu.be/ZaGreqCFOJM)  \n", "- [Recap of the force plate analysis](https://youtu.be/u05pLtC-T1s)  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Problems\n", "\n", "1. The mechanical work ($W$) by the vertical force is defined as:  \n", "$$ W = \\int_{h_0}^{h_f} F(h) \\mathrm{d}h $$  \n", "Which can also be calculated as the variation in the kinetic energy or the integral of mechanical power:  \n", "$$ W = \\Delta E_K = \\int_{t_0}^{t_f} P(t) \\mathrm{d}t $$  \n", " a) Use the data of the vertical jump in this text to plot the graph for the ground reaction force versus displacement.  \n", " b) Calculate the mechanical work produced in the entire jump and in the eccentric and concentric phases using the two methods described above.  \n", "  \n", "2. For the dataset in this text, calculate all the variables described in <PERSON>ling, <PERSON><PERSON> (1993).\n", "\n", "3. A Biomedical Engineering student (m = 50 kg) performs a vertical jump. The ground reaction force during the jump is measured with a force platform. Use g = 10 m/s2. The values of the vertical ground reaction force over time are presented in the graph below.\n", "\n", "<div class='center-align'><figure><img src=\"./../images/verticaljump_fz.png\" width=500px alt=\"Vertical jump Fz\"/><figcaption><center><i>Hypothetical vertical ground reaction force during a jump.</i></center></figcaption></figure></div>\n", "\n", "Considering these data:  \n", "   a) Model the phenomenon and draw a free body diagram for the student’s center of gravity.  \n", "   b) Sketch a graph of the student's vertical acceleration over time.  \n", "   c) Calculate the speed of the center of gravity at the instant the student loses contact with the ground (when the aerial phase of the jump begins).  \n", "   d) Calculate the height of the student's jump (the displacement of her center of gravity).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON> R (1999) [Standing, walking, running, and jumping on a force plate](https://pdfs.semanticscholar.org/1ad0/3557c2bc1ac8e02d89946a17eeadfb7d8a78.pdf). American Journal of Physics, 67, 4, 304-309.  \n", "- <PERSON><PERSON>, <PERSON><PERSON><PERSON> (1993) [Identification of Kinetic and Temporal Factors Related to Vertical Jump Performance](./../refs/Dowling93JABvert_jump.pdf). Journal of Applied Biomechanics, 9, 95-110.   \n", "- <PERSON><PERSON><PERSON><PERSON> NP (2001) <a href=\"http://www.brunel.ac.uk/~spstnpl/Publications/VerticalJump(Linthorne).pdf\" target=\"_blank\">Analysis of standing vertical jumps using a force platform</a>. American Journal of Physics, 69, 1198-1204.  \n", "- Winter DA (2009) [Kinetics: Forces and Moments of Force](https://elearning2.uniroma1.it/pluginfile.php/92521/mod_folder/content/0/Biomechanics%20and%20Motor%20Control%20of%20Human%20Movement%20-%20ch5.pdf). Chapter 5 of [Biomechanics and motor control of human movement](http://books.google.com.br/books?id=_bFHL08IWfwC). 4 ed. Hoboken, EUA: Wiley."]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}, "nbTranslate": {"displayLangs": ["en", "pt"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "pt", "targetLang": "en", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}