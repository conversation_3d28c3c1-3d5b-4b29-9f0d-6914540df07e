{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/BMClab/BMC/blob/master/notebooks/Transformation2D.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "SJ4fNMif2p42"}, "source": ["# Rigid-body transformations in a plane (2D)\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"toc": true, "id": "12HQW4Zj2p46"}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Affine-transformations\" data-toc-modified-id=\"Affine-transformations-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Affine transformations</a></span></li><li><span><a href=\"#Kinematics-of-a-rigid-body\" data-toc-modified-id=\"Kinematics-of-a-rigid-body-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Kinematics of a rigid body</a></span></li><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Translation\" data-toc-modified-id=\"Translation-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Translation</a></span></li><li><span><a href=\"#Rotation\" data-toc-modified-id=\"Rotation-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Rotation</a></span><ul class=\"toc-item\"><li><span><a href=\"#Using-trigonometry\" data-toc-modified-id=\"Using-trigonometry-5.1\"><span class=\"toc-item-num\">5.1&nbsp;&nbsp;</span>Using trigonometry</a></span></li><li><span><a href=\"#Using-direction-cosines\" data-toc-modified-id=\"Using-direction-cosines-5.2\"><span class=\"toc-item-num\">5.2&nbsp;&nbsp;</span>Using direction cosines</a></span></li><li><span><a href=\"#3.-Using-a-basis\" data-toc-modified-id=\"3.-Using-a-basis-5.3\"><span class=\"toc-item-num\">5.3&nbsp;&nbsp;</span>3. Using a basis</a></span></li><li><span><a href=\"#Local-to-Global-and-Global-to-local-coordinate-systems'-rotations\" data-toc-modified-id=\"Local-to-Global-and-Global-to-local-coordinate-systems'-rotations-5.4\"><span class=\"toc-item-num\">5.4&nbsp;&nbsp;</span>Local-to-Global and Global-to-local coordinate systems' rotations</a></span></li><li><span><a href=\"#The-orthogonality-of-the-rotation-matrix\" data-toc-modified-id=\"The-orthogonality-of-the-rotation-matrix-5.5\"><span class=\"toc-item-num\">5.5&nbsp;&nbsp;</span>The orthogonality of the rotation matrix</a></span></li><li><span><a href=\"#Rotation-of-a-Vector\" data-toc-modified-id=\"Rotation-of-a-Vector-5.6\"><span class=\"toc-item-num\">5.6&nbsp;&nbsp;</span>Rotation of a Vector</a></span></li><li><span><a href=\"#Calculation-of-rotation-matrix-using--a-basis\" data-toc-modified-id=\"Calculation-of-rotation-matrix-using--a-basis-5.7\"><span class=\"toc-item-num\">5.7&nbsp;&nbsp;</span>Calculation of rotation matrix using  a basis</a></span></li><li><span><a href=\"#Determination-of-the-unknown-angle-of-rotation\" data-toc-modified-id=\"Determination-of-the-unknown-angle-of-rotation-5.8\"><span class=\"toc-item-num\">5.8&nbsp;&nbsp;</span>Determination of the unknown angle of rotation</a></span></li><li><span><a href=\"#Joint-angle-as-a-sequence-of-rotations-of-adjacent-segments\" data-toc-modified-id=\"Joint-angle-as-a-sequence-of-rotations-of-adjacent-segments-5.9\"><span class=\"toc-item-num\">5.9&nbsp;&nbsp;</span>Joint angle as a sequence of rotations of adjacent segments</a></span></li><li><span><a href=\"#Kinematic-chain-in-a-plain-(2D)\" data-toc-modified-id=\"Kinematic-chain-in-a-plain-(2D)-5.10\"><span class=\"toc-item-num\">5.10&nbsp;&nbsp;</span>Kinematic chain in a plain (2D)</a></span></li></ul></li><li><span><a href=\"#Translation-and-rotation\" data-toc-modified-id=\"Translation-and-rotation-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Translation and rotation</a></span><ul class=\"toc-item\"><li><span><a href=\"#Transformation-matrix\" data-toc-modified-id=\"Transformation-matrix-6.1\"><span class=\"toc-item-num\">6.1&nbsp;&nbsp;</span>Transformation matrix</a></span></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "uZuPs4NM2p46"}, "source": ["## Affine transformations\n", "\n", "Translation and rotation are two examples of [affine transformations](https://en.wikipedia.org/wiki/Affine_transformation). Affine transformations preserve straight lines, but not necessarily the distance between points. Other examples of affine transformations are scaling, shear, and reflection. The figure below illustrates different affine transformations in a plane. Note that a 3x3 matrix is shown on top of each transformation; these matrices are known as the transformation matrices and are the mathematical representation of the physical transformations. Next, we will study how to use this approach to describe the translation and rotation of a rigid-body.  \n", "<br>\n", "<figure><img src='https://upload.wikimedia.org/wikipedia/commons/thumb/2/2c/2D_affine_transformation_matrix.svg/360px-2D_affine_transformation_matrix.svg.png' alt='Affine transformations'/> <figcaption><center><i>Figure. Examples of affine transformations in a plane applied to a square (with the letter <b>F</b> in it) and the corresponding transformation matrices (<a href=\"https://en.wikipedia.org/wiki/Affine_transformation\">image from Wikipedia</a>).</i></center></figcaption> </figure>"]}, {"cell_type": "markdown", "metadata": {"id": "RS6kbJZY2p47"}, "source": ["## Kinematics of a rigid body"]}, {"cell_type": "markdown", "metadata": {"id": "U8wkcJr02p47"}, "source": ["The kinematics of a rigid body is completely described by its pose, i.e., its position and orientation in space (and the corresponding changes are translation and rotation). The translation and rotation of a rigid body are also known as rigid-body transformations (or simply, rigid transformations).\n", "\n", "Remember that in physics, a [rigid body](https://en.wikipedia.org/wiki/Rigid_body) is a model (an idealization) for a body in which deformation is neglected, i.e., the distance between every pair of points in the body is considered constant. Consequently, the position and orientation of a rigid body can be completely described by a corresponding coordinate system attached to it. For instance, two (or more) coordinate systems can be used to represent the same rigid body at two (or more) instants or two (or more) rigid bodies in space.\n", "\n", "Rigid-body transformations are used in motion analysis (e.g., of the human body) to describe the position and orientation of each segment (using a local (anatomical) coordinate system defined for each segment) in relation to a global coordinate system fixed at the laboratory. Furthermore, one can define an additional coordinate system called technical coordinate system also fixed at the rigid body but not based on anatomical landmarks. In this case, the position of the technical markers is first described in the laboratory coordinate system, and then the technical coordinate system is calculated to recreate the anatomical landmarks position in order to finally calculate the original anatomical coordinate system (and obtain its unknown position and orientation through time).\n", "\n", "In what follows, we will study rigid-body transformations by looking at the transformations between two coordinate systems. For simplicity, let's first analyze planar (two-dimensional) rigid-body transformations and later we will extend these concepts to three dimensions (where the study of rotations are more complicated)."]}, {"cell_type": "markdown", "metadata": {"id": "yjLzsQ8u2p48"}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:16.387858Z", "start_time": "2021-11-10T19:16:16.298167Z"}, "id": "XWxtVSzy2p48"}, "outputs": [], "source": ["import numpy as np\n", "np.set_printoptions(precision=4, suppress=True)"]}, {"cell_type": "markdown", "metadata": {"id": "wgrTpVdJ2p49"}, "source": ["## Translation\n", "\n", "In a two-dimensional space, two coordinates and one angle are sufficient to describe the pose of the rigid body, totalizing three degrees of freedom for a rigid body. Let's see first the transformation for translation, then for rotation, and combine them at last.\n", "\n", "A pure two-dimensional translation of a coordinate system in relation to other coordinate system and the representation of a point in these two coordinate systems are illustrated in the figure below (remember that this is equivalent to describing a translation between two rigid bodies).  \n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/translation2D.png?raw=1' alt='translation 2D'/> <figcaption><center><i>Figure. A point in two-dimensional space represented in two coordinate systems (Global and local), with one system translated.</i></center></figcaption> </figure>\n", "\n", "The position of point $\\mathbf{P}$ originally described in the local coordinate system but now described in the Global coordinate system in vector form is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{L_G} + \\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "Or for each component:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_X} = \\mathbf{L_X} + \\mathbf{P}_x \\\\\n", "\\mathbf{P_Y} = \\mathbf{L_Y} + \\mathbf{P}_y\n", "\\end{equation}\n", "</span>\n", "\n", "And in matrix form is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y}\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\mathbf{L_X} \\\\\n", "\\mathbf{L_Y}\n", "\\end{bmatrix} +\n", "\\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Because position and translation can be treated as vectors, the inverse operation, to describe the position at the local coordinate system in terms of the Global coordinate system, is simply:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_l} = \\mathbf{P_G} -\\mathbf{L_G}\n", "\\end{equation}\n", "</span>\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y}\n", "\\end{bmatrix} -\n", "\\begin{bmatrix}\n", "\\mathbf{L_X} \\\\\n", "\\mathbf{L_Y}\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "From classical mechanics, this transformation is an example of [Galilean transformation](http://en.wikipedia.org/wiki/Galilean_transformation).   \n", "\n", "For example, if the local coordinate system is translated by <span class=\"notranslate\">$\\mathbf{L_G}=[2, 3]$</span> in relation to the Global coordinate system, a point with coordinates <span class=\"notranslate\">$\\mathbf{P_l}=[4, 5]$</span> at the local coordinate system will have the position <span class=\"notranslate\">$\\mathbf{P_G}=[6, 8]$</span> at the Global coordinate system:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:16.677394Z", "start_time": "2021-11-10T19:16:16.666773Z"}, "id": "n9sFFPcR2p4-", "outputId": "22751938-18d2-4745-ae6c-42b376fe8cef", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([6, 8])"]}, "metadata": {}, "execution_count": 2}], "source": ["LG = np.array([2, 3])  # (Numpy 1D array with 2 elements)\n", "Pl = np.array([4, 5])\n", "PG = LG + Pl\n", "PG"]}, {"cell_type": "markdown", "metadata": {"id": "v5vyZLzg2p4-"}, "source": ["This operation also works if we have more than one data point (NumPy knows how to handle vectors with different dimensions):"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:16.941485Z", "start_time": "2021-11-10T19:16:16.937290Z"}, "id": "pH442juC2p4_", "outputId": "353aac07-702c-421d-98a0-35e849d8fdde", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 6,  8],\n", "       [ 8, 10],\n", "       [10, 12]])"]}, "metadata": {}, "execution_count": 3}], "source": ["Pl = np.array([[4, 5], [6, 7], [8, 9]])  # 2D array with 3 rows and two columns\n", "PG = LG + Pl\n", "PG"]}, {"cell_type": "markdown", "metadata": {"id": "Pu-5o8kA2p4_"}, "source": ["## Rotation\n", "\n", "A pure two-dimensional rotation of a coordinate system in relation to other coordinate system and the representation of a point in these two coordinate systems are illustrated in the figure below (remember that this is equivalent to describing a rotation between two rigid bodies). The rotation is around an axis orthogonal to this page, not shown in the figure (for a three-dimensional coordinate system the rotation would be around the $\\mathbf{Z}$ axis).  \n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/rotation2D.png?raw=1' alt='rotation 2D'/> <figcaption><center><i>Figure. A point in the two-dimensional space represented in two coordinate systems (Global and local), with one system rotated in relation to the other around an axis orthogonal to both coordinate systems.</i></center></figcaption> </figure>\n", "\n", "Consider we want to express the position of point $\\mathbf{P}$ in the Global coordinate system in terms of the local coordinate system knowing only the coordinates at the local coordinate system and the angle of rotation between the two coordinate systems.   \n", "\n", "There are different ways of deducing that, we will see three of these methods next.     "]}, {"cell_type": "markdown", "metadata": {"id": "MyaTJdX32p4_"}, "source": ["### Using trigonometry\n", "\n", "From figure below, the coordinates of point $\\mathbf{P}$ in the Global coordinate system can be determined finding the sides of the triangles marked in red.   \n", "\n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/rotation2Db.png?raw=1' alt='rotation 2D'/> <figcaption><center><i>Figure. The coordinates of a point at the Global coordinate system in terms of the coordinates of this point at the local coordinate system.</i></center></figcaption> </figure>\n", "\n", "Then:   \n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_X} = \\mathbf{P}_x \\cos \\alpha - \\mathbf{P}_y \\sin \\alpha \\\\\n", "\\\\\n", "\\mathbf{P_Y} = \\mathbf{P}_x \\sin \\alpha + \\mathbf{P}_y \\cos \\alpha\n", "\\end{equation}\n", "</span>\n", "\n", "The equations above can be expressed in matrix form:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y}\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix} \\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Or simply:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{R_{Gl}}\\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "Where <span class=\"notranslate\">$\\mathbf{R_{Gl}}$</span> is the rotation matrix that rotates the coordinates from the local to the Global coordinate system:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl}} = \\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "So, given any position at the local coordinate system, with the rotation matrix above we are able to determine the position at the Global coordinate system. Let's check that before looking at other methods to obtain this matrix.  \n", "\n", "For instance, consider a local coordinate system rotated by $45^o$ in relation to the Global coordinate system, a point in the local coordinate system with position <span class=\"notranslate\">$\\mathbf{P_l}=[1, 1]$</span> will have the following position at the Global coordinate system:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:17.448995Z", "start_time": "2021-11-10T19:16:17.443425Z"}, "id": "I8zCs8CA2p4_", "outputId": "98d48196-d602-4853-f307-66119d8cb4b7", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[0.    ],\n", "       [1.4142]])"]}, "metadata": {}, "execution_count": 4}], "source": ["α = np.pi / 4\n", "RGl = np.array([[np.cos(α), -np.sin(α)],\n", "                [np.sin(α),  np.cos(α)]])\n", "Pl = np.array([[1, 1]]).T  # transpose the array for correct matrix multiplication\n", "PG = RGl @ Pl  # the operator @ is used for matrix multiplication of arrays\n", "PG"]}, {"cell_type": "markdown", "metadata": {"id": "HkycrbMN2p5A"}, "source": ["And if we have the points [1,1], [0,1], [1,0] at the local coordinate system, their positions at the Global coordinate system are:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:17.689501Z", "start_time": "2021-11-10T19:16:17.685092Z"}, "id": "__F7OaIY2p5A", "outputId": "0f83131e-9387-49a5-8fee-8cc0aebece23"}, "outputs": [{"data": {"text/plain": ["array([[ 0.    , -0.7071,  0.7071],\n", "       [ 1.4142,  0.7071,  0.7071]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Pl = np.array([[1, 1], [0, 1], [1, 0]]).T  # transpose array for matrix multiplication\n", "PG = RGl@Pl\n", "PG"]}, {"cell_type": "markdown", "metadata": {"id": "8cA7o2kG2p5A"}, "source": ["### Using direction cosines\n", "\n", "Another way to determine the rotation matrix is to use the concept of direction cosine.   \n", "\n", "> Direction cosines are the cosines of the angles between any two vectors.   \n", "\n", "For the present case with two coordinate systems, they are  the cosines of the angles between each axis of one coordinate system and each axis of the other coordinate system. The figure below illustrates the directions angles between the two coordinate systems, expressing the local coordinate system in terms of the Global coordinate system.  \n", "\n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/directioncosine2D.png?raw=1' alt='direction angles 2D'/> <figcaption><center><i>Figure. Definition of direction angles at the two-dimensional space.</i></center></figcaption> </figure>  \n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl}} = \\begin{bmatrix}\n", "\\cos\\mathbf{X}x & \\cos\\mathbf{X}y \\\\\n", "\\cos\\mathbf{Y}x & \\cos\\mathbf{Y}y\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cos(\\alpha) & \\cos(90^o+\\alpha) \\\\\n", "\\cos(90^o-\\alpha) & \\cos(\\alpha)\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "The same rotation matrix as obtained before.\n", "\n", "Note that the order of the direction cosines is because in our convention, the first row is for the <span class=\"notranslate\">$\\mathbf{X}$</span> coordinate and the second row for the <span class=\"notranslate\">$\\mathbf{Y}$</span> coordinate (the outputs). For the inputs, we followed the same order, first column for the <span class=\"notranslate\">$\\mathbf{x}$</span> coordinate, second column for the <span class=\"notranslate\">$\\mathbf{y}$</span> coordinate."]}, {"cell_type": "markdown", "metadata": {"id": "UOZQQkvz2p5A"}, "source": ["### 3. Using a basis\n", "\n", "Yet another way to deduce the rotation matrix is to view the axes of the rotated coordinate system as unit vectors, versors, of a <a href=\"http://en.wikipedia.org/wiki/Basis_(linear_algebra)\">basis</a> as illustrated in the figure below.\n", "\n", "> A basis is a set of linearly independent vectors that can represent every vector in a given vector space, i.e., a basis defines a coordinate system.\n", "\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/basis2D2.png?raw=1' alt='basis 2D'/> <figcaption><center><i>Figure. Definition of the rotation matrix using a basis at the two-dimensional space.</i></center></figcaption> </figure>\n", "\n", "The coordinates of these two versors at the local coordinate system in terms of the Global coordinate system are:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{e}_x = \\hphantom{-}\\cos\\alpha\\:\\mathbf{e_X} + \\sin\\alpha\\:\\mathbf{e_Y} \\\\\n", "\\mathbf{e}_y = -\\sin\\alpha\\:\\mathbf{e_X} + \\cos\\alpha\\:\\mathbf{e_Y}\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Note that as unit vectors, each of the versors above should have norm (length) equals to one, which indeed is the case.\n", "\n", "If we express each versor above as different columns of a matrix, we obtain the rotation matrix again:  \n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl}} = \\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "This means that the rotation matrix can be viewed as the basis of the rotated coordinate system defined by its versors.   \n", "\n", "This third way to derive the rotation matrix is in fact the method most commonly used in motion analysis because the coordinates of markers (in the Global/laboratory coordinate system) are what we measure with cameras.   \n", "\n", "Probably you are wondering how to perform the inverse operation, given a point in the Global coordinate system how to calculate its position in the local coordinate system. Let's see this now."]}, {"cell_type": "markdown", "metadata": {"id": "6oQZudpe2p5A"}, "source": ["### Local-to-Global and Global-to-local coordinate systems' rotations"]}, {"cell_type": "markdown", "metadata": {"id": "3JDhp3Dg2p5B"}, "source": ["If we want the inverse operation, to express the position of point $\\mathbf{P}$ in the local coordinate system in terms of the Global coordinate system, the figure below illustrates that using trigonometry.  \n", "\n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/rotation2Dc.png?raw=1' alt='rotation 2D'/> <figcaption><center><i>Figure. The coordinates of a point at the local coordinate system in terms of the coordinates at the Global coordinate system.</i></center></figcaption> </figure>\n", "\n", "Then:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P}_x = \\;\\;\\mathbf{P_X} \\cos \\alpha + \\mathbf{P_Y} \\sin \\alpha\n", "\\end{equation}\n", "</span>\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P}_y = -\\mathbf{P_X} \\sin \\alpha + \\mathbf{P_Y} \\cos \\alpha\n", "\\end{equation}\n", "</span>\n", "\n", "And in matrix form:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\hphantom{-}\\cos\\alpha & \\sin\\alpha \\\\\n", "-\\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix} \\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y}\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_l} = \\mathbf{R_{lG}}\\mathbf{P_G}\n", "\\end{equation}\n", "</span>\n", "\n", "Where <span class=\"notranslate\">$\\mathbf{R_{lG}}$</span> is the rotation matrix that rotates the coordinates from the Global to the local coordinate system (note the inverse order of the subscripts):\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{lG}} = \\begin{bmatrix}\n", "\\hphantom{-}\\cos\\alpha & \\sin\\alpha \\\\\n", "-\\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "If we use the direction cosines to calculate the rotation matrix, because the axes didn't change, the cosines are the same, only the order changes, now $\\mathbf{x, y}$ are the rows (outputs) and <span class=\"notranslate\">$\\mathbf{X, Y}$</span> are the columns (inputs):\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{lG}} = \\begin{bmatrix}\n", "\\cos\\mathbf{X}x & \\cos\\mathbf{Y}x \\\\\n", "\\cos\\mathbf{X}y & \\cos\\mathbf{Y}y\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cos(\\alpha) & \\cos(90^o-\\alpha) \\\\\n", "\\cos(90^o+\\alpha) & \\cos(\\alpha)\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\hphantom{-}\\cos\\alpha & \\sin\\alpha \\\\\n", "-\\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>    \n", "\n", "\n", "And defining the versors of the axes in the Global coordinate system for a basis in terms of the local coordinate system would also produce this latter rotation matrix.\n", "\n", "The two sets of equations and matrices for the rotations from Global-to-local and local-to-Global coordinate systems are very similar, this is no coincidence. Each of the rotation matrices we deduced, <span class=\"notranslate\">$\\mathbf{R_{Gl}}$</span> and <span class=\"notranslate\">$\\mathbf{R_{lG}}$</span>, perform the inverse operation in relation to the other. Each matrix is the inverse of the other.   \n", "\n", "In other words, the relation between the two rotation matrices means it is equivalent to instead of rotating the local coordinate system by $\\alpha$ in relation to the Global coordinate system, to rotate the Global coordinate system by $-\\alpha$ in relation to the local coordinate system; remember that <span class=\"notranslate\">$\\cos(-\\alpha)=\\cos(\\alpha)$</span> and <span class=\"notranslate\">$\\sin(-\\alpha)=-\\sin(\\alpha)$</span>."]}, {"cell_type": "markdown", "metadata": {"id": "MzFRtFsw2p5B"}, "source": ["### The orthogonality of the rotation matrix\n", "\n", "**[See here for a review about matrix and its main properties](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Matrix.ipynb)**.\n", "\n", "A nice property of the rotation matrix is that its inverse is the transpose of the matrix (because the columns/rows are mutually orthogonal and have norm equal to one).   \n", "This property can be shown with the rotation matrices we deduced:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{R}\\:\\mathbf{R^T} & =\n", "\\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\hphantom{-}\\cos\\alpha & \\sin\\alpha \\\\\n", "-\\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix} \\\\\n", "& = \\begin{bmatrix}\n", "\\cos^2\\alpha+\\sin^2\\alpha & \\cos\\alpha \\sin\\alpha-\\sin\\alpha \\cos\\alpha\\;\\; \\\\\n", "\\sin\\alpha \\cos\\alpha-\\cos\\alpha \\sin\\alpha & \\sin^2\\alpha+\\cos^2\\alpha\\;\\;\n", "\\end{bmatrix} \\\\\n", "& = \\begin{bmatrix}\n", "1 & 0 \\\\\n", "0 & 1\n", "\\end{bmatrix} \\\\\n", "& = \\mathbf{I} \\\\\n", "\\mathbf{R^{-1}} = \\mathbf{R^T}\n", "\\end{array}\n", "\\end{equation}\n", "</span>   \n", "\n", "This means that if we have a rotation matrix, we know its inverse.   \n", "\n", "The transpose and inverse operators in NumPy are methods of the array:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:18.859300Z", "start_time": "2021-11-10T19:16:18.853767Z"}, "id": "15heEVbw2p5B", "outputId": "b564b0c5-f97b-4b7c-b42d-e8e401c57f52", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Orthogonal matrix (RGl):\n", " [[ 0.7071 -0.7071]\n", " [ 0.7071  0.7071]]\n", "Transpose (RGl.T):\n", " [[ 0.7071  0.7071]\n", " [-0.7071  0.7071]]\n", "Inverse (RGl.I):\n", " [[ 0.7071  0.7071]\n", " [-0.7071  0.7071]]\n"]}], "source": ["α = np.pi/4\n", "RGl = np.array([[np.cos(α), -np.sin(α)],\n", "                [np.sin(α),  np.cos(α)]])\n", "\n", "print('Orthogonal matrix (RGl):\\n', RGl)\n", "print('Transpose (RGl.T):\\n', RGl.T)\n", "print('Inverse (RGl.I):\\n', np.linalg.inv(RGl))"]}, {"cell_type": "markdown", "metadata": {"id": "-JoS4ZIN2p5B"}, "source": ["Using the inverse and the transpose mathematical operations, the coordinates at the local coordinate system given the coordinates at the Global coordinate system and the rotation matrix can be obtained by:   \n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{P_G} = \\mathbf{R_{Gl}}\\mathbf{P_l} \\implies \\\\\n", "\\mathbf{R_{Gl}^{-1}}\\mathbf{P_G} = \\mathbf{R_{Gl}^{-1}}\\mathbf{R_{Gl}}\\mathbf{P_l} \\implies \\\\\n", "\\mathbf{R_{Gl}^{-1}}\\mathbf{P_G} = \\mathbf{I}\\:\\mathbf{P_l} \\implies \\\\\n", "\\mathbf{P_l} = \\mathbf{R_{Gl}^{-1}}\\mathbf{P_G} = \\mathbf{R_{Gl}^T}\\mathbf{P_G} \\quad \\text{or}\n", "\\quad \\mathbf{P_l} = \\mathbf{R_{lG}}\\mathbf{P_G}\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Where we referred the inverse of <span class=\"notranslate\">$\\mathbf{R_{Gl}}\\;(\\:\\mathbf{R_{Gl}^{-1}})$</span> as <span class=\"notranslate\">$\\mathbf{R_{lG}}$</span> (note the different order of the subscripts).  \n", "\n", "Let's show this calculation in NumPy:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:19.073874Z", "start_time": "2021-11-10T19:16:19.067264Z"}, "id": "ydRRMMLK2p5B", "outputId": "9cf8a201-f5a4-48be-dd25-f56a337aaca8", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Rotation matrix (RGl):\n", " [[ 0.7071 -0.7071]\n", " [ 0.7071  0.7071]]\n", "Position at the local coordinate system (Pl):\n", " [[1]\n", " [1]]\n", "Position at the Global coordinate system (PG=RGl*Pl):\n", " [[0.    ]\n", " [1.4142]]\n", "Position at the local coordinate system using the inverse of RGl (Pl=RlG*PG):\n", " [[1.]\n", " [1.]]\n"]}], "source": ["α = np.pi/4\n", "RGl = np.array([[np.cos(α), -np.sin(α)],\n", "                [np.sin(α),  np.cos(α)]])\n", "print('Rotation matrix (RGl):\\n', RGl)\n", "\n", "Pl  = np.array([[1, 1]]).T # transpose the array for correct matrix multiplication\n", "print('Position at the local coordinate system (Pl):\\n', Pl)\n", "\n", "PG = RGl@Pl\n", "print('Position at the Global coordinate system (PG=RGl*Pl):\\n', PG)\n", "\n", "Pl = RGl.T@PG\n", "print('Position at the local coordinate system using the inverse of RGl (Pl=RlG*PG):\\n', Pl)"]}, {"cell_type": "markdown", "metadata": {"id": "8-chnmGi2p5C"}, "source": ["### Rotation of a Vector\n", "\n", "Another use of the rotation matrix is 'to rotate a vector by a given angle around an axis of the coordinate system as shown in the figure below.   \n", "\n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/rotation2Dvector.png?raw=1' alt='rotation 2D of a vector'/> <figcaption><center><i>Figure. Rotation of a position vector <span class=\"notranslate\">$\\mathbf{P}$</span> by an angle $\\alpha$ in the two-dimensional space.</i></center></figcaption> </figure>\n", "\n", "We will not prove that we use the same rotation matrix, but think that in this case the vector position rotates by the same angle instead of the coordinate system. The new coordinates of the vector position <span class=\"notranslate\">$\\mathbf{P'}$</span> rotated by an angle $\\alpha$ is simply the rotation matrix (for the angle $\\alpha$) multiplied by the coordinates of the vector position <span class=\"notranslate\">$\\mathbf{P}$</span>:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P'} = \\mathbf{R}_\\alpha\\mathbf{P}\n", "\\end{equation}\n", "</span>\n", "\n", "Consider for example that <span class=\"notranslate\">$\\mathbf{P}=[2,1]$</span> and <span class=\"notranslate\">$\\alpha=30^o$</span>; the coordinates of <span class=\"notranslate\">$\\mathbf{P'}$</span> are:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:19.321083Z", "start_time": "2021-11-10T19:16:19.316749Z"}, "scrolled": true, "id": "EXEfmhUY2p5C", "outputId": "ae0b5754-578d-4e0b-faa4-2a29d08a5674", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["P':\n", " [[1.2321]\n", " [1.866 ]]\n"]}], "source": ["α  = np.pi/6\n", "R  = np.array([[np.cos(α), -np.sin(α)],\n", "               [np.sin(α),  np.cos(α)]])\n", "P  = np.array([[2, 1]]).T\n", "Pl = R@P\n", "print(\"P':\\n\", Pl)"]}, {"cell_type": "markdown", "metadata": {"id": "jGaGKGwv2p5C"}, "source": ["**In summary, some of the properties of the rotation matrix are:**  \n", "1. The columns of the rotation matrix form a basis of (independent) unit vectors (versors) and the rows are also independent versors since the transpose of the rotation matrix is another rotation matrix.\n", "2. The rotation matrix is orthogonal. There is no linear combination of one of the lines or columns of the matrix that would lead to the other row or column, i.e., the lines and columns of the rotation matrix are independent, orthogonal, to each other (this is property 1 rewritten). Because each row and column have norm equal to one, this matrix is also sometimes said to be orthonormal.\n", "3. The determinant of the rotation matrix is equal to one (or equal to -1 if a left-hand coordinate system was used, but you should rarely use that). For instance, the determinant of the rotation matrix we deduced is <span class=\"notranslate\">$\\cos\\alpha \\cos\\alpha - \\sin\\alpha(-\\sin\\alpha)=1$</span>.\n", "4. The inverse of the rotation matrix is equals to its transpose.\n", "\n", "**On the different meanings of the rotation matrix:**  \n", "- It represents the coordinate transformation between the coordinates of a point expressed in two different coordinate systems.  \n", "- It describes the rotation between two coordinate systems. The columns are the direction cosines (versors) of the axes of the rotated coordinate system in relation to the other coordinate system and the rows are also direction cosines (versors) for the inverse rotation.  \n", "- It is an operator for the calculation of the rotation of a vector in a coordinate system.\n", "- Rotation matrices provide a means of numerically representing rotations without appealing to angular specification.\n", "\n", "**Which matrix to use, from local to Global or Global to local?**  \n", "- A typical use of the transformation is in movement analysis, where there are the fixed Global (laboratory) coordinate system and the local (moving, e.g. anatomical) coordinate system attached to each body segment. Because the movement of the body segment is measured in the Global coordinate system, using cameras for example, and we want to reconstruct the coordinates of the markers at the anatomical coordinate system, we want the transformation leading from the Global coordinate system to the local coordinate system.\n", "- Of course, if you have one matrix, it is simple to get the other; you just have to pay attention to use the right one."]}, {"cell_type": "markdown", "metadata": {"id": "MKEIPZfa2p5C"}, "source": ["### Calculation of rotation matrix using  a basis\n", "\n", "A typical scenario in motion analysis is to calculate the rotation matrix using the position of markers placed on the moving rigid body. With the markers' positions, we create a local basis, which by definition is the rotation matrix for the rigid body with respect to the Global (laboratory) coordinate system. To define a coordinate system using a basís, we also will need to define an origin.\n", "\n", "Let's see how to calculate a basis given the markers' positions.   \n", "Consider the markers at $m1=[1,1]$, $m2=[1,2]$ and $m3=[-1,1]$ measured in the Global coordinate system as illustrated in the figure below:  \n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/transrot2Db.png?raw=1' alt='translation and rotation 2D'/> <figcaption><center><i>Figure. Three points in the two-dimensional space, two possible vectors given these points, and the corresponding basis.</i></center></figcaption> </figure>\n", "\n", "A possible local coordinate system with origin at the position of m1 is also illustrated in the figure above. Intentionally, the three markers were chosen to form orthogonal vectors.   \n", "The translation vector between the two coordinate system is:\n", "\n", "<span class=\"notranslate\">\n", "$$\\mathbf{L_{Gl}} = m_1 - [0,0] = [1,1]$$\n", "</span>\n", "\n", "The vectors expressing the axes of the local coordinate system are:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\hat{\\mathbf{x}} = m_2 - m_1 = [1,2] - [1,1] = [0,1] $$\n", "\n", "$$ \\hat{\\mathbf{y}} = m_3 - m_1 = [-1,1] - [1,1] = [-2,0] $$\n", "</span>\n", "\n", "Note that these two vectors do not form a basis yet because they are not unit vectors (in fact, only *y* is not a unit vector). Let's normalize these vectors:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\begin{array}{}\n", "\\hat{\\mathbf{e_x}} = \\frac{x}{||x||} = \\frac{[0,1]}{\\sqrt{0^2+1^2}} = [0,1] \\\\\n", "\\\\\n", "\\hat{\\mathbf{e_y}} = \\frac{y}{||y||} = \\frac{[-2,0]}{\\sqrt{2^2+0^2}} = [-1,0]\n", "\\end{array} $$\n", "</span>\n", "\n", "Beware that the versors above are not exactly the same as the ones shown in the right plot of the last figure, the versors above if plotted will start at the origin of the coordinate system, not at [1,1] as shown in the figure.\n", "\n", "Since the markers $m1$, $m2$ and $m3$ were carefully chosen, the versors <span class=\"notranslate\">$\\hat{\\mathbf{e_x}}$</span> and <span class=\"notranslate\">$\\hat{\\mathbf{e_y}}$</span> are orthogonal.\n", "\n", "We could have done this calculation in NumPy (we will need to do that when dealing with real data later):"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:19.735194Z", "start_time": "2021-11-10T19:16:19.728964Z"}, "id": "eGKB03fN2p5C", "outputId": "09d22921-5e8f-4bb0-88bd-f94b5fa93577", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["x = [0. 1.] , y = [-2.  0.] \n", "ex= [0. 1.] , ey= [-1.  0.]\n"]}], "source": ["m1 = np.array([1.,1.])    # marker 1\n", "m2 = np.array([1.,2.])    # marker 2\n", "m3 = np.array([-1.,1.])   # marker 3\n", "\n", "x = m2 - m1               # vector x\n", "y = m3 - m1               # vector y\n", "\n", "vx = x/np.linalg.norm(x)  # versor x\n", "vy = y/np.linalg.norm(y)  # verson y\n", "\n", "print(\"x =\", x, \", y =\", y, \"\\nex=\", vx, \", ey=\", vy)"]}, {"cell_type": "markdown", "metadata": {"id": "s3jt0YmU2p5C"}, "source": ["Now, both <span class=\"notranslate\">$\\hat{\\mathbf{e}_x}$</span> and <span class=\"notranslate\">$\\hat{\\mathbf{e}_y}$</span> are unit vectors (versors) and they are orthogonal, a basis can be formed with these two versors, and we can represent the rotation matrix using this basis (just place the versors of this basis as columns of the rotation matrix):\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl}} = \\begin{bmatrix}\n", "0 & -1 \\\\\n", "1 & \\hphantom{-}0\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "This rotation matrix makes sense because from the figure above we see that the local coordinate system we defined is rotated by 90$^o$ in relation to the Global coordinate system and if we use the general form for the rotation matrix:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R} = \\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cos90^o & -\\sin90^o \\\\\n", "\\sin90^o & \\hphantom{-}\\cos90^o\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "0 & -1 \\\\\n", "1 & \\hphantom{-}0\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "So, the position of any point in the local coordinate system can be represented in the Global coordinate system by:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{P_G} =& \\mathbf{L_{Gl}} + \\mathbf{R_{Gl}}\\mathbf{P_l} \\\\\n", "\\\\\n", "\\mathbf{P_G} =& \\begin{bmatrix} 1 \\\\ 1 \\end{bmatrix} + \\begin{bmatrix} 0 & -1 \\\\ 1 & \\hphantom{-}0 \\end{bmatrix} \\mathbf{P_l}\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "For example, the point <span class=\"notranslate\">$\\mathbf{P_l}=[1,1]$</span> has the following position at the Global coordinate system:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:19.928001Z", "start_time": "2021-11-10T19:16:19.922326Z"}, "id": "eTAaz43e2p5D", "outputId": "f9f67b9e-ea73-4824-9c0b-c989fc306721", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Translation vector:\n", " [[1]\n", " [1]]\n", "Rotation matrix:\n", " [[ 0 -1]\n", " [ 1  0]]\n", "Position at the local coordinate system:\n", " [[1]\n", " [1]]\n", "Position at the Global coordinate system, PG = LGl + RGl*Pl:\n", " [[0]\n", " [2]]\n"]}], "source": ["LGl = np.array([[1, 1]]).T\n", "print('Translation vector:\\n', LGl)\n", "\n", "RGl = np.array([[0, -1],\n", "                [1,  0]])\n", "print('Rotation matrix:\\n', RGl)\n", "\n", "Pl  = np.array([[1, 1]]).T\n", "print('Position at the local coordinate system:\\n', Pl)\n", "\n", "PG = LGl + RGl@Pl\n", "print('Position at the Global coordinate system, PG = LGl + RGl*Pl:\\n', PG)"]}, {"cell_type": "markdown", "metadata": {"id": "-ciYrf1v2p5D"}, "source": ["### Determination of the unknown angle of rotation\n", "\n", "If we didn't know the angle of rotation between the two coordinate systems, which is the typical situation in motion analysis, we simply would equate one of the terms of the two-dimensional rotation matrix in its algebraic form to its correspondent value in the numerical rotation matrix we calculated.\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R} = \\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "So the angle $\\alpha$ can be found by:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\alpha = \\arctan\\left(\\frac{R[1,0]}{R[0,0]}\\right)\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "r56m-u342p5D"}, "source": ["For instance, taking the first term of the rotation matrices above: <span class=\"notranslate\">$\\cos\\alpha = 0$</span> implies that $\\alpha$ is 90$^o$ or 270$^o$, but combining with another matrix term, <span class=\"notranslate\">$\\sin\\alpha = 1$</span>, implies that $\\alpha=90^o$. We can solve this problem in one step using the tangent <span class=\"notranslate\">$(\\sin\\alpha/\\cos\\alpha)$</span> function with two terms of the rotation matrix and calculating the angle with the `arctan2(y, x)` function:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:20.295114Z", "start_time": "2021-11-10T19:16:20.291522Z"}, "id": "iUfxcUsM2p5D", "outputId": "b3c97539-7882-439a-bc2f-357d98abe903", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The angle is: 90.0\n"]}], "source": ["RGl = np.array([[0, -1],\n", "                [1,  0]])\n", "ang = np.arctan2(RGl[1, 0], RGl[0, 0])*180/np.pi\n", "print('The angle is:', ang)"]}, {"cell_type": "markdown", "metadata": {"id": "_z1_tWOz2p5D"}, "source": ["And this procedure would be repeated for each segment and for each instant of the analyzed movement to find the rotation of each segment."]}, {"cell_type": "markdown", "metadata": {"id": "U7-IL-L02p5D"}, "source": ["### Joint angle as a sequence of rotations of adjacent segments\n", "\n", "In the notebook about [two-dimensional angular kinematics](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KinematicsAngular2D.ipynb), we calculated segment and joint angles using simple trigonometric relations. We can also calculate these two-dimensional angles using what we learned here about the rotation matrix.\n", "\n", "The segment angle will be given by the matrix representing the rotation from the laboratory coordinate system (G) to a coordinate system attached to the segment and the joint angle will be given by the matrix representing the rotation from one segment coordinate system (l1) to the other segment coordinate system (l2).\n", "\n", "So, we have to compute two basis now, one for each segment and the joint angle will be given by the product between the two rotation matrices obtained from both basis."]}, {"cell_type": "markdown", "metadata": {"id": "Rd5IiLct2p5D"}, "source": ["The description of a point P in the global basis given the description of this point in the l1 basis is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\begin{bmatrix}\n", "        x_p\\\\\n", "        y_p\n", "    \\end{bmatrix} =R_{Gl1}\n", "    \\begin{bmatrix}\n", "        x_{p_1}\\\\\n", "        y_{p_1}\n", "    \\end{bmatrix}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "h_HDf2Ln2p5E"}, "source": ["The description of the same point P in the global basis given the description of this point in the l2 basis is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\begin{bmatrix}\n", "        x_p\\\\\n", "        y_p\n", "    \\end{bmatrix} =R_{Gl_2}\n", "    \\begin{bmatrix}\n", "        x_{p_2}\\\\\n", "        y_{p_2}\n", "    \\end{bmatrix}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "KUfSkDqf2p5E"}, "source": ["So, to find the description of this point P in the l1 basis given the description of this point in the l2 basis is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    R_{Gl_1}\n", "    \\begin{bmatrix}\n", "        x_{p_1}\\\\\n", "        y_{p_1}\n", "    \\end{bmatrix} =\n", "    R_{Gl_2}\n", "    \\begin{bmatrix}\n", "        x_{p_2}\\\\\n", "        y_{p_2}\n", "    \\end{bmatrix}\n", "\\rightarrow \\begin{bmatrix}\n", "        x_{p_1}\\\\\n", "        y_{p_1}\n", "    \\end{bmatrix} =\n", "    \\underbrace{R_{Gl_1}^{-1}R_{Gl_2}}_{R_{l_1l_2}}\n", "    \\begin{bmatrix}\n", "        x_{p_2}\\\\\n", "        y_{p_2}\n", "    \\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "The rotation matrix from $l_2$ to $l_1$ is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "   R_{l_1l_2} = R_{Gl_1}^{-1}R_{Gl_2} = R_{Gl_1}^{T}R_{Gl_2}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "UCbeUS352p5E"}, "source": ["The rotation matrices of both basis are:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "   R_{Gl_1} = \\begin{bmatrix}\n", "    \\cos(\\theta_1) &-\\sin(\\theta_1)\\\\\n", "    \\sin(\\theta_1) &\\hphantom{-}\\cos(\\theta_1)\n", "    \\end{bmatrix}, \\,\n", "    R_{Gl_2} = \\begin{bmatrix}\n", "    \\cos(\\theta_2) &-\\sin(\\theta_2)\\\\\n", "    \\sin(\\theta_2) &\\hphantom{-}\\cos(\\theta_2)\n", "    \\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "So, the rotation matrix from $l_2$ to $l_1$ is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "   R_{l_1l_2} =& \\begin{bmatrix}\n", "    \\cos(\\theta_1) &\\sin(\\theta_1)\\\\\n", "    -\\sin(\\theta_1) &\\cos(\\theta_1)\n", "    \\end{bmatrix}.\\begin{bmatrix}\n", "    \\cos(\\theta_2) &-\\sin(\\theta_2)\\\\\n", "    \\sin(\\theta_2) &\\hphantom{-}\\cos(\\theta_2)\n", "    \\end{bmatrix} =\\\\\n", "    =&\\begin{bmatrix}\n", "    \\cos(\\theta_1)\\cos(\\theta_2)+\\sin(\\theta_1)\\sin(\\theta_2) &\\cos(\\theta_2)\\sin(\\theta_1)-\\cos(\\theta_1)\\sin(\\theta_2)\\\\\n", "    \\cos(\\theta_1)\\sin(\\theta_2)-\\cos(\\theta_2)\\sin(\\theta_1)&\\cos(\\theta_1)\\cos(\\theta_2)+\\sin(\\theta_1)\\sin(\\theta_2)\n", "    \\end{bmatrix}=\\\\\n", "    =&\\begin{bmatrix}    \\cos(\\theta_2-\\theta_1) &-\\sin(\\theta_2-\\theta_1)\\\\\n", "    \\sin(\\theta_2-\\theta_1) &\\hphantom{-}\\cos(\\theta_2-\\theta_1)\n", "    \\end{bmatrix}\n", "\\end{align}\n", "</span>\n", "\n", "The angle $\\theta_{l_1l_2} = \\theta_2-\\theta_1$ is the angle between the two reference frames. So to find the $\\theta_{l_1l_2}$ is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "   \\theta_{l_1l_2} = \\arctan\\left(\\frac{R_{l_1l_2}[1,0]}{R_{l_1l_2}[0,0]}\\right)\n", "\\end{align}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "H21lXJjq2p5E"}, "source": ["Below is an example: To define a two-dimensional basis, we need to calculate vectors perpendicular to each of these lines. Here is a way of doing that. First, let's find three non-collinear points for each basis:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:21.623759Z", "start_time": "2021-11-10T19:16:21.620165Z"}, "id": "hlMhiWZ-2p5E"}, "outputs": [], "source": ["x1, y1, x2, y2 = 0, 0, 1, 1      # points at segment 1\n", "x3, y3, x4, y4 = 1.1, 1, 2.1, 0  # points at segment 2\n", "\n", "#The slope of the perpendicular line is minus the inverse of the slope of the line\n", "xl1 = x1 - (y2-y1); yl1 = y1 + (x2-x1)  # point at the perpendicular line 1\n", "xl2 = x4 - (y3-y4); yl2 = y4 + (x3-x4)  # point at the perpendicular line 2"]}, {"cell_type": "markdown", "metadata": {"id": "zxTH7L-v2p5E"}, "source": ["With these three points, we can create a basis and the corresponding rotation matrix:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:21.803645Z", "start_time": "2021-11-10T19:16:21.795892Z"}, "id": "E3f1i15T2p5E", "outputId": "561b966d-45d2-4ca5-8547-dd089975f159", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["rotation matrix from segment 1 to global:\n", " [[ 0.7071 -0.7071]\n", " [ 0.7071  0.7071]]\n", "rotation matrix from segment 2 to global:\n", " [[-0.7071 -0.7071]\n", " [ 0.7071 -0.7071]]\n"]}], "source": ["b1x = np.array([x2-x1, y2-y1])\n", "b1x = b1x/np.linalg.norm(b1x)    # versor x of basis 1\n", "b1y = np.array([xl1-x1, yl1-y1])\n", "b1y = b1y/np.linalg.norm(b1y)    # versor y of basis 1\n", "b2x = np.array([x3-x4, y3-y4])\n", "b2x = b2x/np.linalg.norm(b2x)    # versor x of basis 2\n", "b2y = np.array([xl2-x4, yl2-y4])\n", "b2y = b2y/np.linalg.norm(b2y)    # versor y of basis 2\n", "\n", "RGl1 = np.array([b1x, b1y]).T    # rotation matrix from segment 1 to the laboratory\n", "RGl2 = np.array([b2x, b2y]).T    # rotation matrix from segment 2 to the laboratory\n", "\n", "print('rotation matrix from segment 1 to global:\\n', RGl1)\n", "print('rotation matrix from segment 2 to global:\\n', RGl2)"]}, {"cell_type": "markdown", "metadata": {"id": "jm93fdIi2p5E"}, "source": ["Now, the segment and joint angles are simply matrix operations:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:21.962360Z", "start_time": "2021-11-10T19:16:21.955973Z"}, "id": "b7u9z4In2p5F", "outputId": "7d1dc893-f30b-46ff-eeb5-c1e861eb6298", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Rotation matrix for segment 1:\n", " [[ 0.7071 -0.7071]\n", " [ 0.7071  0.7071]]\n", "\n", "Rotation angle of segment 1: 45.0\n", "\n", "Rotation matrix for segment 2:\n", " [[-0.7071 -0.7071]\n", " [ 0.7071 -0.7071]]\n", "\n", "Rotation angle of segment 2: 135.0\n", "\n", "Joint rotation matrix (Rl1l2 = Rl1G*RGl2):\n", " [[ 0. -1.]\n", " [ 1. -0.]]\n", "\n", "Joint angle: 90.0\n"]}], "source": ["print('Rotation matrix for segment 1:\\n', RGl1)\n", "print('\\nRotation angle of segment 1:', np.arctan2(RGl1[1,0], RGl1[0,0])*180/np.pi)\n", "print('\\nRotation matrix for segment 2:\\n', RGl2)\n", "print('\\nRotation angle of segment 2:', np.arctan2(RGl1[1,0], RGl2[0,0])*180/np.pi)\n", "\n", "Rl1l2 = RGl1.T@RGl2  # Rl1l2 = Rl1G*RGl2\n", "\n", "print('\\nJoint rotation matrix (Rl1l2 = Rl1G*RGl2):\\n', Rl1l2)\n", "print('\\nJoint angle:', np.arctan2(Rl1l2[1,0], Rl1l2[0,0])*180/np.pi)"]}, {"cell_type": "markdown", "metadata": {"id": "rIvvyEp72p5F"}, "source": ["Same result as obtained in [Angular kinematics in a plane (2D)](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KinematicsAngular2D.ipynb)."]}, {"cell_type": "markdown", "metadata": {"id": "MSxnbHd52p5F"}, "source": ["### Kinematic chain in a plain (2D)\n", "\n", "The fact that we simply multiplied the rotation matrices to calculate the rotation matrix of one segment in relation to the other is powerful and can be generalized for any number of segments: given a serial kinematic chain with links 1, 2, ..., n and 0 is the base/laboratory, the rotation matrix between the base and last link is: <span class=\"notranslate\">$\\mathbf{R_{n,n-1}R_{n-1,n-2} \\dots R_{2,1}R_{1,0}}$</span>, where each matrix in this product (calculated from right to left) is the rotation of one link with respect to the next one.  \n", "\n", "For instance, consider a kinematic chain with two links, the link 1 is rotated by $\\alpha_1$ with respect to the base (0) and the link 2 is rotated by $\\alpha_2$ with respect to the link 1.  \n", "Using Sympy, the rotation matrices for link 2 w.r.t. link 1 $(R_{12})$ and for link 1 w.r.t. base 0 $(R_{01})$ are:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:22.472345Z", "start_time": "2021-11-10T19:16:22.266249Z"}, "id": "DdAROZOA2p5F"}, "outputs": [], "source": ["from IPython.display import display, Math\n", "from sympy import sin, cos, Matrix, simplify, latex, symbols\n", "from sympy.interactive import printing\n", "printing.init_printing()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:22.613666Z", "start_time": "2021-11-10T19:16:22.473442Z"}, "id": "9G-0eOEg2p5F", "outputId": "8ad31d1c-153e-4645-9d27-58f0f63229e9", "colab": {"base_uri": "https://localhost:8080/", "height": 98}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Math object>"], "text/latex": "$\\displaystyle \\mathbf{R_{12}}=\\left[\\begin{matrix}\\cos{\\left(\\alpha_{2} \\right)} & - \\sin{\\left(\\alpha_{2} \\right)}\\\\\\sin{\\left(\\alpha_{2} \\right)} & \\cos{\\left(\\alpha_{2} \\right)}\\end{matrix}\\right]$"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Math object>"], "text/latex": "$\\displaystyle \\mathbf{R_{01}}=\\left[\\begin{matrix}\\cos{\\left(\\alpha_{1} \\right)} & - \\sin{\\left(\\alpha_{1} \\right)}\\\\\\sin{\\left(\\alpha_{1} \\right)} & \\cos{\\left(\\alpha_{1} \\right)}\\end{matrix}\\right]$"}, "metadata": {}}], "source": ["a1, a2 = symbols('alpha1 alpha2')\n", "\n", "R12 = Matrix([[cos(a2), -sin(a2)], [sin(a2), cos(a2)]])\n", "display(Math(r'\\mathbf{R_{12}}=' + latex(R12)))\n", "R01 = Matrix([[cos(a1), -sin(a1)], [sin(a1), cos(a1)]])\n", "display(Math(r'\\mathbf{R_{01}}=' + latex(R01)))"]}, {"cell_type": "markdown", "metadata": {"id": "lQoQAQ4E2p5F"}, "source": ["The rotation matrix of link 2 w.r.t. the base $(R_{02})$ is given simply by $R_{01}*R_{12}$:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:22.620712Z", "start_time": "2021-11-10T19:16:22.614511Z"}, "id": "cjYeMarJ2p5F", "outputId": "1b28c432-51e8-4f8a-9ffe-dc2c3983cbce", "colab": {"base_uri": "https://localhost:8080/", "height": 58}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Math object>"], "text/latex": "$\\displaystyle \\mathbf{R_{02}}=\\left[\\begin{matrix}- \\sin{\\left(\\alpha_{1} \\right)} \\sin{\\left(\\alpha_{2} \\right)} + \\cos{\\left(\\alpha_{1} \\right)} \\cos{\\left(\\alpha_{2} \\right)} & - \\sin{\\left(\\alpha_{1} \\right)} \\cos{\\left(\\alpha_{2} \\right)} - \\sin{\\left(\\alpha_{2} \\right)} \\cos{\\left(\\alpha_{1} \\right)}\\\\\\sin{\\left(\\alpha_{1} \\right)} \\cos{\\left(\\alpha_{2} \\right)} + \\sin{\\left(\\alpha_{2} \\right)} \\cos{\\left(\\alpha_{1} \\right)} & - \\sin{\\left(\\alpha_{1} \\right)} \\sin{\\left(\\alpha_{2} \\right)} + \\cos{\\left(\\alpha_{1} \\right)} \\cos{\\left(\\alpha_{2} \\right)}\\end{matrix}\\right]$"}, "metadata": {}}], "source": ["R02 = R01*R12\n", "display(Math(r'\\mathbf{R_{02}}=' + latex(R02)))"]}, {"cell_type": "markdown", "metadata": {"id": "-Ftvgfnd2p5F"}, "source": ["Which simplifies to:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2021-11-10T19:16:22.755875Z", "start_time": "2021-11-10T19:16:22.626965Z"}, "id": "7nTArTbs2p5G", "outputId": "3c66c739-a645-4b7e-d670-68b92bcb9065", "colab": {"base_uri": "https://localhost:8080/", "height": 58}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Math object>"], "text/latex": "$\\displaystyle \\mathbf{R_{02}}=\\left[\\begin{matrix}\\cos{\\left(\\alpha_{1} + \\alpha_{2} \\right)} & - \\sin{\\left(\\alpha_{1} + \\alpha_{2} \\right)}\\\\\\sin{\\left(\\alpha_{1} + \\alpha_{2} \\right)} & \\cos{\\left(\\alpha_{1} + \\alpha_{2} \\right)}\\end{matrix}\\right]$"}, "metadata": {}}], "source": ["display(Math(r'\\mathbf{R_{02}}=' + latex(simplify(R02))))"]}, {"cell_type": "markdown", "metadata": {"id": "eTcXOxjr2p5G"}, "source": ["As expected.\n", "\n", "The typical use of all these concepts is in the three-dimensional motion analysis where we will have to deal with angles in different planes, which needs a special manipulation as we will see next."]}, {"cell_type": "markdown", "metadata": {"id": "Nb13kzGO2p5G"}, "source": ["## Translation and rotation\n", "\n", "Consider now the case where the local coordinate system is translated and rotated in relation to the Global coordinate system and a point is described in both coordinate systems as illustrated in the figure below (once again, remember that this is equivalent to describing a translation and a rotation between two rigid bodies).  \n", "<br>\n", "<figure><img src='https://github.com/BMClab/BMC/blob/master/images/transrot2D.png?raw=1' alt='translation and rotation 2D'/> <figcaption><center><i>Figure. A point in two-dimensional space represented in two coordinate systems, with one system translated and rotated.</i></center></figcaption> </figure>\n", "\n", "The position of point $\\mathbf{P}$ originally described in the local coordinate system, but now described in the Global coordinate system in vector form is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{L_G} + \\mathbf{R_{Gl}}\\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "And in matrix form:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y}\n", "\\end{bmatrix} =\n", "\\begin{bmatrix} \\mathbf{L_{X}} \\\\\\ \\mathbf{L_{Y}} \\end{bmatrix} +\n", "\\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha\n", "\\end{bmatrix} \\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "This means that we first *disrotate* the local coordinate system and then correct for the translation between the two coordinate systems. Note that we can't invert this order: the point position is expressed in the local coordinate system and we can't add this vector to another vector expressed in the Global coordinate system, first we have to convert the vectors to the same coordinate system.\n", "\n", "If now we want to find the position of a point at the local coordinate system given its position in the Global coordinate system, the rotation matrix and the translation vector, we have to invert the expression above:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{P_G} = \\mathbf{L_G} + \\mathbf{R_{Gl}}\\mathbf{P_l} \\implies \\\\\n", "\\mathbf{R_{Gl}^{-1}}(\\mathbf{P_G} - \\mathbf{L_G}) = \\mathbf{R_{Gl}^{-1}}\\mathbf{R_{Gl}}\\mathbf{P_l} \\implies \\\\\n", "\\mathbf{P_l} = \\mathbf{R_{Gl}^{-1}}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right) = \\mathbf{R_{Gl}^T}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right) \\quad \\text{or} \\quad \\mathbf{P_l} = \\mathbf{R_{lG}}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right)\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "The expression above indicates that to perform the inverse operation, to go from the Global to the local coordinate system, we first translate and then rotate the coordinate system."]}, {"cell_type": "markdown", "metadata": {"id": "pNGNJmvE2p5G"}, "source": ["### Transformation matrix\n", "\n", "It is possible to combine the translation and rotation operations in only one matrix, called the transformation matrix (also referred as homogeneous transformation matrix):\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y} \\\\\n", "1\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cos\\alpha & -\\sin\\alpha & \\mathbf{L_{X}} \\\\\n", "\\sin\\alpha & \\hphantom{-}\\cos\\alpha  & \\mathbf{L_{Y}} \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix} \\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y \\\\\n", "1\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Or simply:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{T_{Gl}}\\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "The inverse operation, to express the position at the local coordinate system in terms of the Global coordinate system, is:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_l} = \\mathbf{T_{Gl}^{-1}}\\mathbf{P_G}\n", "\\end{equation}\n", "</span>\n", "\n", "However, because $\\mathbf{T_{Gl}}$ is not orthonormal, which means its inverse is not its transpose. Its inverse in matrix form is given by:\n", "\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y \\\\\n", "1\n", "\\end{bmatrix} =\n", "\\underbrace{\\begin{bmatrix}\n", "\\mathbf{R^{-1}_{Gl}} & \\cdot & - \\mathbf{R^{-1}_{Gl}}\\mathbf{L_{G}} \\\\\n", "\\cdot & \\cdot  & \\cdot \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}}_{ \\mathbf{T_{Gl}^{-1}}} \\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y} \\\\\n", "1\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "2qf29WfX2p5G"}, "source": ["## Further reading\n", "\n", " - Read pages 751-758 of the 16th chapter of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about rotation matrix  \n", " - Read section 2.8 of <PERSON><PERSON>'s book about using the concept of rotation matrix applied to kinematic chains  \n", " - [Rotation matrix](https://en.wikipedia.org/wiki/Rotation_matrix) - Wikipedia  "]}, {"cell_type": "markdown", "metadata": {"id": "WH5lF_GZ2p5G"}, "source": ["## Video lectures on the Internet\n", "\n", "- [Linear transformation examples: Rotations in R2](https://www.khanacademy.org/math/linear-algebra/matrix-transformations/lin-trans-examples/v/linear-transformation-examples-rotations-in-r2) - Khan Academy   \n", "- [Linear transformations and matrices | Chapter 3, Essence of linear algebra](https://youtu.be/kYB8IZa5AuE) - 3Blue1Brown  \n", "- [Linear Transformations and Their Matrices](https://youtu.be/Ts3o2I8_Mxc) - MIT OpenCourseWare  \n", "- [Rotation Matrices](https://youtu.be/4srS0s1d9Yw)"]}, {"cell_type": "markdown", "metadata": {"id": "g7_hN_e_2p5G"}, "source": ["## Problems\n", "\n", "1. A local coordinate system is rotated 30$^o$ clockwise in relation to the Global reference system.   \n", "  <PERSON><PERSON> Determine the matrices for rotating one coordinate system to another (two-dimensional).   \n", "  B. What are the coordinates of the point [1, 1] (local coordinate system) at the global coordinate system?   \n", "  C. And if this point is at the Global coordinate system and we want the coordinates at the local coordinate system?   \n", "  D. Consider that the local coordinate system, besides the rotation is also translated by [2, 2]. What are the matrices for rotation, translation, and transformation from one coordinate system to another (two-dimensional)?   \n", "  <PERSON><PERSON> and C considering this translation.\n", "  \n", "2. Consider a local coordinate system U rotated 45$^o$ clockwise in relation to the Global reference system and another local coordinate system V rotated 45$^o$ clockwise in relation to the local reference system U.  \n", "  <PERSON><PERSON> Determine the rotation matrices of all possible transformations between the coordinate systems.   \n", "  B. For the point [1, 1] in the coordinate system U, what are its coordinates in coordinate system V and in the Global coordinate system?   \n", "  \n", "3. Using the rotation matrix, deduce the new coordinates of a square figure with coordinates [0, 0], [1, 0], [1, 1], and [0, 1] when rotated by 0$^o$, 45$^o$, 90$^o$, 135$^o$, and 180$^o$ (always clockwise).\n", "  \n", "4. Solve the problem 2 of [Angular kinematics in a plane (2D)](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KinematicsAngular2D.ipynb) but now using the concept of two-dimensional transformations.  \n", "\n", "5. Write a Python code to solve the problem in [https://leetcode.com/problems/rotate-image/](https://leetcode.com/problems/rotate-image/).  \n", "\n", "6. Rotate a triangle placed at A(0,0), B(1,1) and C(5,2) by an angle $45^o$ with respect to origin. From https://studyresearch.in/2019/12/14/numerical-example-of-rotation-in-2d-transformation/.  \n", "\n", "7. Rotate a triangle placed at A(0,0), B(1,1) and C(5,2) by an angle $45^o$ with respect to point P(-1,-1). From https://studyresearch.in/2019/12/14/numerical-example-of-rotation-in-2d-transformation/.  \n"]}, {"cell_type": "markdown", "metadata": {"id": "RgwyRlwL2p5G"}, "source": ["## References\n", "\n", "- <PERSON><PERSON> (2017) [Cinemática e Dinâmica para Engenharia](https://www.grupogen.com.br/e-book-cinematica-e-dinamica-para-engenharia). Grupo GEN.  \n", "- <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> (2013) [Research Methods in Biomechanics](http://books.google.com.br/books?id=gRn8AAAAQBAJ). 2nd Edition. Human Kinetics.      \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2013) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  \n", "- Winter DA (2009) [Biomechanics and motor control of human movement](http://books.google.com.br/books?id=_bFHL08IWfwC). 4 ed. Hoboken, EUA: Wiley.  \n", "- <PERSON><PERSON><PERSON><PERSON> (1997) [Kinematics of Human Motion](http://books.google.com.br/books/about/Kinematics_of_Human_Motion.html?id=Pql_xXdbrMcC&redir_esc=y). Champaign, Human Kinetics."]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": [], "include_colab_link": true}}, "nbformat": 4, "nbformat_minor": 0}