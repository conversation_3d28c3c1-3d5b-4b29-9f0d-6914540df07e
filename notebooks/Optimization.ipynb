{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Optimization\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div style=\"text-align: right\">\n", "<i>If there occur some changes in nature, the amount of action necessary for this change must be as small as possible.</i> \n", "<br><PERSON><PERSON><PERSON><PERSON><PERSON> (sec XVIII)\n", "</div>\n", "  \n", "**Optimization is the process of finding the best value from possible alternatives with regards to a certain criteria** ([Wikipedia](http://en.wikipedia.org/wiki/Mathematical_optimization)).  \n", "\n", "Typically, such best value is the value that maximizes or minimizes the criteria. In this context, to solve a (mathematical) optimization problem is to find the maximum or minimum (a.k.a., a stationary point) of a function (and we can use maximum or minimum interchangeably because the maximum of a function is the minimum of the negative of that function).  \n", "To solve an optimization problem, we first have to model the problem and define the objective, the variables, and the constraints of the problem. In optimization, these terms are usually defined as:\n", "\n", "1. Objective function (or also, cost, loss, utility, or fitness function): a function describing what we want to optimize.  \n", "2. Design variable(s): variables that will be manipulated to optimize the cost function.  \n", "3. Constraint functions: a set of constraints, equalities or inequalities that constrains the possible solutions to possible values of the design variables (candidate solutions or feasible solutions or feasible set).\n", "\n", "A feasible solution that minimizes (or maximizes) the objective function is called an optimal solution.\n", "\n", "The optimization problem is the calculation of the minimum or maximum values of an objective function over a set of **unknown** possible values of the design variables.  \n", "Even in case of a finite number of possible values of the objective function and design variables (e.g., after discretization and a manual or a grid search), in general the evaluation of the objective function is computationally expensive and should be avoided.  \n", "Of note, even if there is no other option, a random search is in fact more efficient than a manual or a grid search! See [<PERSON>, <PERSON><PERSON> (2012)](http://jmlr.csail.mit.edu/papers/volume13/bergstra12a/bergstra12a.pdf).\n", "\n", "A typical problem of optimization: [Knapsack problem](https://en.wikipedia.org/wiki/Knapsack_problem).\n", "\n", "Read more about that in [Introduction to Optimization](http://neos-guide.org/content/optimization-introduction) from the  [NEOS Guide](http://neos-guide.org/)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Some jargon in mathematical optimization\n", "\n", " - **Linear versus nonlinear optimization**: linear optimization refers to when the objective function and the constraints are linear mathematical functions. When the objective function is linear, an optimal solution is always found at the constraint boundaries and a local optimum is also a global optimum. See [Wikipedia 1](https://en.wikipedia.org/wiki/Linear_programming) and [Wikipedia 2](https://en.wikipedia.org/wiki/Nonlinear_programming).  \n", " - **Constrained versus unconstrained optimization**: in constrained optimization there are no constraints.  \n", " - **Convex optimization**: the field of optimization that deals with finding the minimum of convex functions (or the maximum of concave functions) over a convex constraint set. The convexity of a function facilitates the optimization because a local minimum must be a global minimum and first-order conditions (the first derivatives) are sufficient conditions for finding the optimal solution. Note that although convex optimization is a particular case of nonlinear optimization, it is a relatively simple optimization problem, with robust and mature methods of solution. See [Wikipedia](https://en.wikipedia.org/wiki/Convex_optimization).   \n", " - **Multivariate optimization**: optimization of a function of several variables.\n", " - **Multimodal optimization**: optimization of a function with several local minima to find the multiple (locally) optimal solutions, as opposed to a single best solution.  \n", " - **Multi-objective optimization**: optimization involving more than one objective function to be optimized simultaneously.  \n", " - **Optimal control**: finding a control law for a given system such that a certain optimality criterion is achieved. See [Wikipedia](https://en.wikipedia.org/wiki/Optimal_control).  \n", " - **Quadratic programming**: optimization of a quadratic function subject to linear constraints. See [Wikipedia](https://en.wikipedia.org/wiki/Quadratic_programming).   \n", " - **Simplex algorithm**: linear optimization algorithm that begins at a starting vertex and moves along the edges of the polytope (the feasible region) until it reaches the vertex of the optimum solution. See [Wikipedia](https://en.wikipedia.org/wiki/Simplex_algorithm).   "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Maxima and minima\n", "\n", "In mathematics, the maximum and minimum of a function are the largest and smallest values that the function takes at a point either within a neighborhood (local) or on the function entire domain  (global) ([Wikipedia](http://en.wikipedia.org/wiki/Maxima_and_minima)).  \n", "\n", "For a function of one variable, if the maximum or minimum of a function is not at the limits of the domain and if at least the first and second derivatives of the function exist, a maximum and minimum can be found as the point where the first derivative of the function is zero. If the second derivative on that point is positive, then it's a minimum, if it is negative, it's a maximum.\n", "\n", "<div class='center-align'><figure><img src='./../images/maxmin.png' width=350 alt='minima and maxima of a function'/> <figcaption><center><i>Figure. Maxima and minima of a function of one variable.</i></center></figcaption> </figure></div>\n", "\n", " - Note that the requirement that the second derivative on the extremum to be positive for a minimum or negative for a maximum is sufficient but not a necessary condition. For instance, the function $f(x)=x^4$ has an extremum in $x=0$ since $f'(x)=4x^3$ and $f'(0)=0$, but its second derivative at $x=0$ is also zero: $f''(x)=12x^2;\\: f''(0)=0$. In fact, the requirement is that the first non-zero derivative on that point should be positive for a minimum or negative for a maximum: $f''''(0)=24$; the extremum is a minimum."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now apply optimization to solve a problem with a univariate function."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:36.429519Z", "start_time": "2018-08-06T04:49:33.446990Z"}}, "outputs": [], "source": ["# import Python libraries\n", "import numpy as np\n", "%matplotlib inline\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import sympy as sym\n", "from sympy.plotting import plot\n", "import pandas as pd\n", "from IPython.display import display\n", "from IPython.core.display import Math"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example 1: Maximum volume of a cardboard box\n", "\n", "We want to make a box from a square cardboard with side $a$ such that its volume should be maximum.  \n", "What is the optimal distance where the square cardboard should be cut and folded to make a box with maximum volume?\n", "\n", "<div class='center-align'><figure><img src='./../images/box.png' width=450 alt='box optimization'/> <figcaption><center><i>Figure. A box to be made from a cardboard such that its volume should be maximum. Where we should cut?</i></center></figcaption> </figure></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If the distance where to cut and fold the cardboard is $b$, see figure above, the volume of the box will be:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "V(b) = b(a-2b)(a-2b) \\\\\n", "\\\\\n", "V(b) = a^2b - 4ab^2 + 4b^3\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "In the context of optimization:  \n", "**The expression for $V$ is the cost function, $b$ is the design variable, and the constraint is that feasible values of $b$ are in the interval $]0, \\dfrac{a}{2}[$, i.e., $b>0$ and $b<\\dfrac{a}{2}$.**  \n", "\n", "The first and second derivatives of $V$ w.r.t. $b$ are:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\dfrac{\\mathrm{d}V}{\\mathrm{d}b} = a^2 - 8ab + 12b^2 \\\\\n", "\\\\\n", "\\dfrac{\\mathrm{d}^2 V}{\\mathrm{d}b^2} = - 8a + 24b\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "We have to find the values for $b$ where the first derivative of $V$ is zero (the extrema) and then use the expression for the second derivative of $V$ to find whether each of these extrema is a minimum (positive value) or a maximum (negative value).  \n", "Let's use Sympy for that:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:36.802884Z", "start_time": "2018-08-06T04:49:36.430501Z"}, "scrolled": true}, "outputs": [{"data": {"text/latex": ["$$Roots:\\left [ \\frac{a}{6}, \\quad \\frac{a}{2}\\right ]$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["a, b = sym.symbols('a b')\n", "V = b*(a - 2*b)*(a - 2*b)\n", "Vdiff = sym.expand(sym.diff(V, b))\n", "roots = sym.solve(<PERSON><PERSON><PERSON>, b)\n", "display(Math(sym.latex('Roots:') + sym.latex(roots)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Discarding the solution $b=\\dfrac{a}{2}$ (where $V=0$, which is a minimum), $b=\\dfrac{a}{6}$ results in the maximum volume.  \n", "We can check that by plotting the volume of the cardboard box for $a=1$ and $b: [0,\\:0.5]$:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:36.949338Z", "start_time": "2018-08-06T04:49:36.803862Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$V_{a=1}^{max}(b=0.1667)=0.0741$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(V.subs({a: 1}), (b, 0, .5), xlabel='b', ylabel='V')\n", "display(Math(sym.latex('V_{a=1}^{max}(b=%s)=%s'\n", "                       %(roots[0].evalf(n=4, subs={a: 1}), V.evalf(n=3, subs={a: 1, b: roots[0]})))))"]}, {"cell_type": "markdown", "metadata": {}, "source": [" - Note that although the problem above is a case of nonlinear constrained optimization, because the objective function is univariate, well-conditioned and the constraints are linear inequalities, the optimization is simple. Unfortunately, this is seldom the case."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Curve fitting as an optimization problem\n", "\n", "Curve fitting is the process of fitting a model, expressed in terms of a mathematical function, that depends on adjustable parameters to a series of data points and once adjusted, that curve has the best fit to the data points.\n", "\n", "The general approach to the fitting procedure involves the definition of a merit function that measures the agreement between data and model. The model parameters are then adjusted to yield the best-fit parameters as a problem of minimization (an optimization problem, where the merit function is the cost function).  \n", "\n", "A classical solution, termed least-squares fitting, is to find the best fit by minimizing the sum of the squared differences between data points and the model function (the sum of squared residuals as the merit function).\n", "\n", "For more on curve fitting see the video below and the notebook [Curve fitting](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/CurveFitting.ipynb)."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:37.746290Z", "start_time": "2018-08-06T04:49:36.950336Z"}}, "outputs": [{"data": {"image/jpeg": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEABALDA4MChAODQ4SERATGCgaGBYWGDEjJR0oOjM9PDkzODdASFxOQERXRTc4UG1RV19iZ2hnPk1xeXBkeFxlZ2MBERISGBUYLxoaL2NCOEJjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY//AABEIAWgB4AMBIgACEQEDEQH/xAAbAAEAAQUBAAAAAAAAAAAAAAAABAECAwUGB//EAEgQAAEDAgQCBgUKBAUDBAMBAAEAAgMEEQUSITETQRciUVSS0hQyYXGBBiMzcpGhsbLB0RVCUvBiY3Ph8SRTgiU0NUMWosIH/8QAGQEBAAMBAQAAAAAAAAAAAAAAAAECAwQF/8QALhEBAAICAQMCAwcFAQAAAAAAAAECAxEhBBIxQVETImEFFCNxgaGxFTIzQvA0/9oADAMBAAIRAxEAPwDz9ERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBF2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/yoOPRdh0cYx3mh8b/ACp0cYx3mh8b/Kg49F2HRxjHeaHxv8qdHGMd5ofG/wAqDj0XYdHGMd5ofG/yp0cYx3mh8b/Kg9QRFovlNimJ4YKX+GYeavivIk0Jy7WGm19dfYg3qKjSS0EixtsqoKItDibHQYlNVV1PLUULo2hroyTwCL5jlGuumo10V7Z6p/yjiZBUMdSupOI1puQRmGu+/tQbxFpn4w6OvihMlJI2SbglkbyXs3sez3hZIayuqpqxsbII4qeQxhziSXaA7DbdBtFVc1RVdQPk5h01XkqDJJC0El19XAXJvqVJmxatBxN8UEBioHW6zjeQBgcR7N90G8RaesxaVkskdOIGcOmFQXTuIDgb6C3u1PtCnU1TI7DI6mZnXMXELWi3K9kEpFpaLFaqpqqNrvRTFUwmb5txcWAW0v8AHf2K92LSNwmmrOG3NNOyIt5AOky3QbdFoarHnRuq3RCFzaVxbw3OPEkIGtuzsHapMuJyDE20w4MTXBhZxiQZb7hp2uOzVBtUWrhxKSXFZKV3BiyPIEbyRI9tvWbyI1W0QEREBERAREQEREBERAREQEREBERAREQEREBEVEFUREBERARUQODtiDy0QVRUJABJIAG5KpnaXZb62v8ABBcioiCqKiIKoqKqAiIgIiICIiAqKqIKKqIgg1VDLUOlDa6aOKUWcxoabdtiRcXVP4WxlVTz08r4TDFwcoAIcy4Ntfcp6INTHggjMLRVzcGGbjMis217k6m1zuVOpqRtOaizi7jyGQ35EgC33KQiDVx4K1lDFRmpkdDDIx8YIF25XXA21WT+Fx8PEGcR1q4kv/w3YG6fALYIg5utw6b+JvkyVBjbBGyN0McUmYtvvnBsdeVvetrlrZMDc2ZjDWOhILb2BdbQXGynog53CKWtpaimbEybhuH/AFJnhiZYBuliwXJvbt0vqpZwJpjjhNZPwIpxMyLq2BDs1r2uRf8AFbdEGvOGvZNM+mrJYGzOzuY1rSA7mRcaXSsw01rsstVIYM7XmLK3dpB0Nr7gLYIggPw4y1cU01TJIyKQyMjLWixsRva9hdT0RAREQEREBERAREQEREBERAREQEREBERAREQU2UGlq3+jxSS3c18YkLratv2qVUG1PJbfKVr6Z9qdrHixLI4weR/u6iWlY3DZtcHNDmkEHYhVUPK5krnROyl0lsp9U6a/qssVS15DXjI83sDzt2Hmm1Zr7JCKiqpVRqlj3yxZASAddbD97qJ6JUejcNgyEhxcXSE9bt/H7ls1Dqa3LJ6PSs41R/TyZ7XHkrRMomFktLLLHNxC92Y9VoeR1dL8/erjBOXNDXFjA4X65PVym/3lTGZsjc9s1tbbXVVHcaaxwldPFE+Rpc0tFg45tDcm3tHNZMQLnvETZALiwbcgkk7jt/RT0U7NIL4qvK8Bw2cBZ2urr/heypwJhlDWyABhb64uN9Rrvqp6J3GmOna5kDWvADgNQFlVFVVSIiICIiAiIgLX4rjVBg4h9Pn4XGNmaE3tvty1WwULEMKocUEQrqZk3CdmZmvof75IJgIIBGoKqqAWFgqoNTPUV8uMy0dJNTwsjgZKTLCZCS5zhyc23qrO6tbBUTMqJ4yIKcSyBsZBG93bnTTbdR5MLZU4/NUVVMyWE00bGOeAesHPJ/ELFXYfPJU4iYYhw5cPEEdiBd3W0+8IJ1Ni1HVzNihlJc9uZhLHBrx2tJFnfBQcVxuFsIZSTuEvpEUeYRnKfnGhzQ4ixNr7FZn0c5qMHcI+rT5uKbjq3jI/FQDR4h/CocMFESYaiNxnMjcr2tkDrjW97DUED4oNtjWJDCsMlqzG+Qs2a1pNz7bA2HtKi1OLtjr6B2aWOCZkt2OhcHuItbqkZr78lKxyllrcGq6eAAyyRkNBNrlYjHPU4lQVbqZ8LY2Sh7XlpLSbW2J7DsgkNxSjdRelib5oOyatIdmvbLl3vfS1rq+krYK1rjCXXYbOa9jmOb72uAIWoqcMqnsle1jrsr/SGsbJlMjcoGh5Hf7FOwunDZZql1NUQySNa0+kSh7iBe2xIG/agvrcYoqBzmzvkuwAv4cL3hg5Xyg2+KPxejZMyFz5OM9jZGxiJ5dlOgNrXHt7Oa0tYHzVmMACZ9LdrJ2wlguAwXHW1vY7jlbmtpRxmbF/4hGw+jTUcQjcdDu42tuNCEEgYrSelNpy57XudlaXRODHO7A4ixPxULHMbgpcPr2wTObUwxOs8RktY+1wC62UHbQqGaXE556f0iGd8kVWJHyGVvCyZjbK0G+gtuB8VWspMQbhmKYfDRGY1JldHNxGhpD9bG5vcXttbbUINwysaKuSOSeMNjgbK5paQWgk9Yu2tp9yU2KUlVLwonvD8pcA+NzMwHNtwLj2ha6vwuorJsRa0BjaigZAx5OmcF9x94+1ZaKne+pY+ajq43xNID5pw9tyLHKA4/eAgkUuOYfVuhEEznCf6N3DcGuO9sxFr+zdW4bjDK+rqqcQzMMEhYC6J4BAAOpIsDrt8VFo8PqYsDwinfFaWnfGZG3HVsDdScOiqKbEq9kkDuFPNxmTAty2ytFrXvfQ8rINoiIgIiICIiAiIgIiICIiAiIgIiICIiDDU34VmgElwAB96g0bmS00cbwLucLtPYG/7KZUlwMWVuY57kX5WKiUjWPpqcE6ta51wbFp/sqsta+F7A+N0ZbeRmZ7st9QNRvz3VGvZJBfQ5YSXNPImx/RW55IYhxBmDIPWA2v2/Yq1ULJopC15aQ1jQ5h/vtSOVp45W1VY3DWuLpczW5Q2Nxu5x9h+xVbjlEW6vPE5saM5v2aXCiSUcM7WtqmmUh2kv8AOBZSqV0EbYoeI0kZz1rAn3j3FX1pl5Xk1tdoAaOA873kcPwapdNTRUsfDhYGjc9pPaTzUVsZjj+ac6NzY2nqnS59myy8WdriOpIA/IP5TtdV2ns9kpVUVtX/AFxSDfUWI032VwrIeZcNvWaQm0dspCLB6XT/APeZ8SnplPyladL6a6Js7Z9mdUWD0uO9miRxvb1Dv8VY6qkIuyG2hN3utt7rps7ZSlbJKyMXe8Nvtc7qI8zuNnS5RdgswW3OuvuVrRHD86bDLndmcbnQ23PvTa0UZX1T3NPBZplzBz9Pu/4V1I5zn1DXPLskmW5+qD+qjZ3OAZE0kDhszHQaG/xWfDwbTlxBJlJJA30CQm0ahdWzPhZHwy0Fz7XcL8if0UU1dQ0XzxHXbIf3WTF3ZYYj/mf/AMlawy3sO0j8V5XV58tM9a0njj+VseOLVmW3rG1TnM4DrMsc1vW203/dKIVQL/SewW1563/RSS4NF3ED3oHNdexBtvZeswXIiICIiAixvmijNpJGMOUus5wGg3PuF1eCCLg6IKoqKqAqK0yxtkbG57RI4EtaTqQN7D4hWsqYJHNbHPG9zgSA14JIBsT8DogyKqorWSxyOe1kjXOjOV4BuWm17Hs0IQYJ8NoaicTT0dPLKNnvjBcPipNlVEFLJZVRBRVREBFjdPExxa6VjSLAguGlzYfaVWSWOINMj2sDnBozG1ydh70F6IiAiIgIrGSMkzcN7X5SWnKb2PYkkjIY3SSvaxjBdznGwA7SUF6LHx4czm8VmZrc5GYXDe33e1UiqIZnFsU0byGhxDXA6HY+4oMqIiAiIgK172xtLnuDWjmVcsNT9EPrt/MEGRrg5oc03B2KuVFVARFRBGqZDHK14YXBjHE23GyhwMz0UbmutI2maMw5k9v2KTUvLXy9W7C1rCRuCfZ8VHp2Z6dr43Wc4sHsdYA6/eqy2rxDK6TK57ZRlJLGXHqnX7t1SaMDM9hLc8uw2Nh2e8K5koe8Mkblc6U3B2NgRoeeyjyQTXi4ErWx2dIWO23/AN1MeSeIYHyOhrITK7LDIC32A7g/opTqeGSMvLGl4hFnDtP/AAsdUBLSOhkaWPbCMhd/VfTX3hXU5ZVwiZgMb3OY1wabWI3Ht5q0zMxwrFYieWZ8cge8MkuM7GgPF9rHdGySB7C6InrucS032uOdkaJg9tssl5HO10OgIVvHtEC5j2kRE7X393uVF1OPHwdSWkQ/zNI1Pv8AcsvGie8hsjT12gWPZqqGWJziGyNJzMba/Ybq4Bj3tLg0gyPcfcLhCdAkaC0kjTiP++36qwuYxmVz2izI2b+3VW8CIw2MTL8Efyjdyy8ONsmjGgGUDbsbdDiFvpEJkDg8OvKT1ddm25LHxS6KzYnm8QFzp6x9qq6eNjM3EaDw3ka8ydFcZQXlscb3DOxo6ttteaJiPoWmfLq5rAZP5dSLN7T+ys4UYizP1PDHWedsx+5VJnLc3VjGWR4/mJ1VTDEx95Dmyva3M87WF/goFeKXy3ibmHELsx0bo2yzUAIidfe4v78oUbiOez5ltxlecztBqdLdqmwCz5h2PH5QphS/HCDj/wD7OO9/pRsbfylaRrg6WPU+uNie1bj5SvDKCNziAOKNSfYVz0UrRUQguAJe21zvqF5nV1mc9ZdOCInFLrqujFS5js5a5l7cwqUdGKUus/NdoG1tr/usk9TFTgGUkA31sTsqw1MU5cI3XygE6dq9Vwsy0XylxPFcOdSDC6D0sSPIk0Jy7WGm19dfYt6iCguQL6FVREGuq8JZVzyyvnkHEEbS0WsGtdmI/wDLn7lAdHTyY9iAqKJ1VlbFlswOy9U9q6BR4aRsVZUVIcS6fLmB2GUWQaqpxqRtRVx0whApCG5JA7NI7KHWFttwOavmxpzqpsEBihPAZM507XH1r2bYe43Ut+GkVM01NVTU5nsZGsDSC4C2bUGxsB9irNh2aqFTDUSwTGMRvc0NOcDa9xuLn7UGuZUursTwqcsML5Kae4/pN2DmtjRYXBRyNewucWRCJubWwvcn3k6lXCgb6VTVD5pHyU7HMBdbrZrXJsPZyUxBpsRfWj5QYfHTzxsjfHKS1zSQbZd7EX307NVDZU1lJPj1RTshdHDNxHB5N32iYSBbbQb6rc1lAKqop52zPhlgzZXMANw4ag3B7B9isOFxGLEGZ32riS/bq3YG6fAIItRjJ9MbTQOiiPAbM58wJHWvYAD3HVTaCqkrcOZUcPhyuaeqb2Dhp7NLhY3YWGyxzQVEkErYhE5zQ052ja4I5XP2qZDGYoWsdI+Ugavfa7vfbRBpW4+938MPBbao0qDf6I3DLeM2+BWf+KSOppZ+JTQR+kOijfKTqBpe3MlwOnYq/wABpwK60ko9LN9x82bk9X/yJPvWQ4PEKaiijmljfRm8cgsSTYg3uLG9ygiwY1K5sL5GxGP0o00r23tcjquF9rkgW9q2GH1b6x1S7K0QxzGOMjd2XRxP/lcfBQqzC3RYJXwQulqZ5yZGlxAdxLCx7BYgFbDD6UUVDDTg34bQCTzPM/EoIpwuOOo9KfPK7LO6oc02sTkyge4DZaurq6yto8Lq3xwtp56uB7WgnOwFwIvyP3W9q6YgOBB1BWpbgMbY6eL0uoMFNK2SGK7bNym4G1yOWqA/EK2WOrnpY4DDTPcwNkvmkLd9f5dbjmrJMbc1tQ5sQI9GZPTX0z5tMp9ua32hZ5cHDzUNjq54oal2aWJmWxJ3sbXF+dlfV4RT1M9HKS5nop6rW7OFwQD7AWg/BBgpMWlqTRNETQ+SF8k/MRlthbxE/YVgpMYrZKbDaqoigbDWuazKwnM0kEg3+G3t3U2jwmCjqqueNzy6pNyDszUkge8uJ+KMwiFlDQ0okflontcw6XdlBAv9qDVw1lXQ02KVUbITBDVyOcHE5ni4vbs++/sVccqA+HHYRE0FuHh+fm64fofdb71tJMJikoaykMj8lW9z3EWuM3YranB4ak1pfJIPS6cU77W0Azaj29YoITIPScYqYS9zBJh8TSW72LnXW1pKGKkklfGNZLD3NaLNaPYP1KpDQsirXVQc4vdC2Gx2s0k3+9S0BERAREQFgqyGwXOwc38wWdYan6IfXb+YIMjXBzQ4bFXKiqgKiqqFBBe9zZC5wvG+Xcbiw/2WGjjPApnxuAcbvIOxG36rIx7ojG9wLo8rpDYatv8AjuVipw70SOWIhrxTguH8pJ/4VG7IyVjmMErbHhueWu53sdO3mjopImOZG7M0RBtncr9hVXlj88UjMpysYGn38vtVS2Rj3BhztMgAa7fQA7/AonaplZI97HDK50jWhrudrH91EhidDibomOMccrnSMA2BFwRb7CpbJI5nNZILEvc4seN7XHxUOtgcKaOeB7g+CPjBp1Gu4+y6tVS3CTnmZGHZA60TnXabG512KudNGM7TdgGRnWFufb7irGyuczM1gexwY0Fh5b7H2FXioiMgzOy3lJOYEbC3P2hQt550uaY53ggteDISeegFli9HhMWYRMB4JO3MqpZDJEHljHkROedAdTr+6uNNEHODW2sWM0Nvb+qhO9LnQRF7gGD12tHuGv7qzgwizjEw/SP27CrhFGXNPW9Z7vWPLRY/RojFZzATwmjXXVxQifquJigGQuY3qxtte3PVV9IaX5mMe7rudo22wtzQOgiks0saDLsLcm/uFZxs8XUje4mPe1rZj7UNbLTOZkdlYMsbNNTqdVUiKJwmldchz3Znns0V1pnydZzWNMn8upFm9p/ZYyIY2DMeu6Mb6uOY8kF7C4lrGsOUcNhLtNjc2Clw/ST/AFx+UKPE58lQCWZWGQu1OugspEJ+eqB/jH5QrQzu1nyoNsNYf80fgVzNM+9VCP8AMb+K6P5XOLcKYQCfnRt7iuUpXn02nFibyt/ELh6iu8sS7um/wy9CkgZK5rnZrt2s4iypBTRU9+E3LcAbk7LFWVRpiyzWuzX3Nrab+5KKsdVFwLMmVrTv23/b713vNS0REBERBjE0ZmMIe3ihocWX1AOx+4q9aIUkX/5dPKTLnFLG8DjPAJzOG17W202URsjzgsWKCrnNc+RoLOMcpcX2MeT1fZtdB0s0scEZkle1jG7ucbALFJWMjr4KMtcXzMe9pGwDct7+IKD8qYWTYFPnLxkyuGV5bzG9jqotXRRnHcLp2SztiFPUXImcXOF2aZyc33oOgRczNUVMFC2nZLI9n8QNOZHS2cI7EgZ997C++q2eEw1MFRURyu+Ys0xsdOZXtOt7k622t8UEuKsZLXVFKGuD4Gsc4nY5r2t9ikLnzBDUfKPE2TVMsVoIXBscpjP82txY6fZqsdFPNiLsOp6yolbHJSOlJY8xumcHAA3bY7a2Hag6GaaOBodK9rGlwaC421JsB9qcaPjiEvbxS3MGX1t2rla5vpOFysmllkhp8Sijhk4rgSzOy9yDrYki57O1TX0FO35UUwzz2bSEi9TJqWvb/i19o580HQIuXklkdgtVinpU4ro5JAxglcGhzXkNZkvY3sBtc3UqWGSrxDEhJU1MbYoo3MZHKWhrspN9Pw2Qb5FzlDJOwYFVuqZpJa5o44e8lpvEX6N2FiOQXSICIiAqKqIKIqogIiIKKqIgIiICIiAsFWQ2C50Ac38wWdYar6IfXZ+YIMjXBzQ5puCrlRVQFiqHZKeR17WaVlUerdlYwWJu8aAX2N/0RNfKNI10Qla0l8eVsdty2/Z27hYqZofTB8LrOJY23I6Am/3rIwEZXwEOjfKTk9g7PsWOiY18NOWOySi7nfZbUfFUbs2dsjhHMyxfLsdjYcj8EYJIzGWuzt68mVx1tft+KsEobGz0hgA4bnnm031+HNVdFJExzYnXAiDQHe3TQ/Yhr0U4kbogyRoDxD6rxuT2duyufCWl7Y5CBdjMp1H96pJIx7pGStyXextnDSwsd9uZVWw2eDE8svI423GmiCJQvdBO+j4ZdklL25Tu3Xt5AiykipjEYcSW2Y53WBGpN+ai1T56YwVgY13Dac5adS1x7PfqpbpWNzNLXtADI9W+****************************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\n", "text/html": ["\n", "        <iframe\n", "            width=\"480\"\n", "            height=\"360\"\n", "            src=\"https://www.youtube.com/embed/Rxp7o7_RxII?rel=0\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x1f44decc208>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import YouTubeVideo\n", "YouTubeVideo('Rxp7o7_RxII', width=480, height=360, rel=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Gradient descent\n", "\n", "Gradient descent is a first-order iterative optimization algorithm for finding the minimum of a function ([Wikipedia](https://en.wikipedia.org/wiki/Gradient_descent)).  \n", "In the gradient descent algorithm, a local minimum of a function is found starting from an initial point and taking steps proportional to the negative of the derivative of the function (gradient) at the current point and we evaluate if the current point is lower than then the previous point until a local minimum in reached (hopefully).  \n", "\n", "It follows that, if\n", "\n", "\\begin{equation}\n", "x_{n+1} = x_n - \\gamma \\nabla f(x)\n", "\\label{}\n", "\\end{equation}\n", "\n", "for $\\gamma$ small enough, then $f(x_{n}) \\geq f(x_{n+1})$.\n", "\n", "This process is repeated iteratively until the step size (which is proportional to the gradient!) is below a required precision (hopefully the sequence $x_{n}$ converges to the desired local minimum)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example 2: Minimum of a function by gradient descent\n", "\n", "From https://en.wikipedia.org/wiki/Gradient_descent:  \n", "Calculate the minimum of $f(x)=x^4-3x^3+2$."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:37.760299Z", "start_time": "2018-08-06T04:49:37.747290Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True local minimum at 2.25 with function value -6.54296875.\n", "Local minimum by gradient descent at 2.2499646074278457 with function value -6.542968737317345.\n"]}], "source": ["# From https://en.wikipedia.org/wiki/Gradient_descent\n", "# The local minimum of $f(x)=x^4-3x^3+2$ is at x=9/4\n", "\n", "cur_x = 6               # The algorithm starts at x=6\n", "gamma = 0.01            # step size multiplier\n", "precision = 0.00001\n", "step_size = 1           # initial step size\n", "max_iters = 10000       # maximum number of iterations\n", "iters = 0               # iteration counter\n", "\n", "f  = lambda x: x**4 - 3*x**3 + 2  # lambda function for f(x)\n", "df = lambda x: 4*x**3 - 9*x**2    # lambda function for the gradient of f(x)\n", "\n", "while (step_size > precision) & (iters < max_iters):\n", "    prev_x = cur_x\n", "    cur_x -= gamma*df(prev_x)\n", "    step_size = abs(cur_x - prev_x)\n", "    iters+=1\n", "\n", "print('True local minimum at {} with function value {}.'.format(9/4, f(9/4)))\n", "print('Local minimum by gradient descent at {} with function value {}.'.format(cur_x, f(cur_x)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The gradient descent optimization algorithm is one of the methods for optimization implemented in the [scipy.optimize.minimize](https://docs.scipy.org/doc/scipy/reference/generated/scipy.optimize.minimize.html#scipy.optimize.minimize) function, see [minimize(method='CG')](https://docs.scipy.org/doc/scipy/reference/optimize.minimize-cg.html), which deals with minimization of a scalar function of one or more variables using the conjugate gradient algorithm.  \n", "Let's run the example above but using the scipy function:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:37.885213Z", "start_time": "2018-08-06T04:49:37.761280Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: -6.542969\n", "         Iterations: 3\n", "         Function evaluations: 49\n", "         Gradient evaluations: 15\n"]}, {"data": {"text/plain": ["     fun: array([-6.54296875])\n", "     jac: array([0.])\n", " message: 'Optimization terminated successfully.'\n", "    nfev: 49\n", "     nit: 3\n", "    njev: 15\n", "  status: 0\n", " success: True\n", "       x: array([2.24999999])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy.optimize import minimize\n", "import numpy as np\n", "\n", "minimize(f, 6, args=(), method='CG', jac=None, tol=precision, callback=None,\n", "         options={'gtol': 1e-05, 'norm': np.inf, 'eps': 1.4901161193847656e-08,\n", "                  'maxiter': max_iters, 'disp': True, 'return_all': False})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multivariate optimization\n", " \n", "When there is more than one design variable (the cost function depends on more than one variable), it's a multivariate optimization. The general idea of finding minimum and maximum values where the derivatives are zero still holds for a multivariate function. The second derivative of a multivariate function can be described by the Hessian matrix:\n", "\n", "\\begin{equation}\n", "\\mathbf{H} = \\begin{bmatrix}{\\dfrac  {\\partial ^{2}f}{\\partial x_{1}^{2}}}&{\\dfrac  {\\partial ^{2}f}{\\partial x_{1}\\,\\partial x_{2}}}&\\cdots &{\\dfrac  {\\partial ^{2}f}{\\partial x_{1}\\,\\partial x_{n}}}\\\\[2.2ex]{\\dfrac  {\\partial ^{2}f}{\\partial x_{2}\\,\\partial x_{1}}}&{\\dfrac  {\\partial ^{2}f}{\\partial x_{2}^{2}}}&\\cdots &{\\dfrac  {\\partial ^{2}f}{\\partial x_{2}\\,\\partial x_{n}}}\\\\[2.2ex]\\vdots &\\vdots &\\ddots &\\vdots \\\\[2.2ex]{\\dfrac  {\\partial ^{2}f}{\\partial x_{n}\\,\\partial x_{1}}}&{\\dfrac  {\\partial ^{2}f}{\\partial x_{n}\\,\\partial x_{2}}}&\\cdots &{\\dfrac  {\\partial ^{2}f}{\\partial x_{n}^{2}}}\n", "\\end{bmatrix}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Let's see now a classical problem in biomechanics where optimization is useful and there is more than one design variable."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The distribution problem in biomechanics\n", "\n", "Using the inverse dynamics approach in biomechanics, we can determine the net force and torque acting on a joint if we know the external forces on the segments and the kinematics and inertial properties of the segments. But with this approach we are unable to determine the individual muscles forces that  created such torque, as expressed in the following equation:\n", "\n", "\\begin{equation}\n", "M_{total} = M_1 + M_2 + \\dots + M_n = r_1F_1 + r_2F_2 + \\dots + r_nF_n\n", "\\label{}\n", "\\end{equation}\n", "\n", "where $r_i$ is the moment arm of the force $F_i$ that generates a torque $M_i$, a parcel of the (known) total torque $M_{total}$.  \n", "\n", "Even if we know the moment arm of each muscle (e.g., from cadaveric data or from image analysis), the equation above has $n$ unknowns. Because there is more than one muscle that potentially created such torque, there are more unknowns than equations, and the problem is undetermined. So, the problem is how to find how the torque is distributed among the muscles of that joint.\n", "\n", "One solution is to consider that we (biological systems) optimize our effort in order to minimize energy expenditure, stresses on our tissues, fatigue, etc. The principle of least action, stated in the opening of this text, is an allusion that optimization might be ubiquitous in nature. With this rationale, let's solve the distribution problem in biomechanics using optimization and find the minimum force of each muscle necessary to complete a given task.\n", "\n", "The following cost functions have been proposed to solve the distribution problem in biomechanics:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\displaystyle\\sum_{i=1}^N F_i \\quad &\\text{e.g., <PERSON><PERSON><PERSON> and <PERSON><PERSON> (1973)}\n", "\\\\\n", "\\displaystyle\\sum_{i=1}^N F_i^2 \\quad &\n", "\\\\\n", "\\displaystyle\\sum_{i=1}^N \\left(\\dfrac{F_i}{pcsa_i}\\right)^2 \\quad &\\text{e.g., Crowninshield and Brand (1981)}\n", "\\\\\n", "\\displaystyle\\sum_{i=1}^N \\left(\\dfrac{F_i}{M_{max,i}}\\right)^3 \\quad &\\text{e.g., <PERSON><PERSON><PERSON> (1987)}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $pcsa_i$ is the physiological cross-sectional area of muscle $i$ and $M_{max,i}$ is the maximum torque muscle $i$ can produce.  \n", "Each muscle force $F_i$ is a design variable and the following constraints must be satisfied:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "0 \\leq F_i \\leq F_{max}\n", "\\\\\n", "\\displaystyle\\sum_{i=1}^N r_i \\times F_i = M\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Let's apply this concept to solve a distribution problem in biomechanics."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Muscle force estimation\n", "\n", "Consider the following main flexors of the elbow joint (see figure below): biceps long head, biceps short head, and brachialis. Suppose that the elbow net joint torque determined using inverse dynamics is 20 Nm (flexor). How much each of these muscles contributed to the net torque?\n", "\n", "<div class='center-align'><figure><img src='./../images/elbowflexors.png' alt='Elbow flexors'/> <figcaption><center><i>Figure. A view in OpenSim of the arm26 model showing three elbow flexors (Biceps long and short heads and Brachialis).</i></center></figcaption> </figure></div>\n", "\n", "For the optimization, we will need experimental data for the moment arm, maximum moment, and *pcsa* of each muscle. Let's import these data from the OpenSim arm26 model:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:37.901201Z", "start_time": "2018-08-06T04:49:37.886211Z"}}, "outputs": [], "source": ["# time elbow_flexion BIClong BICshort BRA\n", "r_ef = np.loadtxt('./../data/r_elbowflexors.mot', skiprows=7)\n", "f_ef = np.loadtxt('./../data/f_elbowflexors.mot', skiprows=7)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The maximum isometric force of these muscles are defined in the arm26 model as: Biceps long head: 624.3 N, <PERSON><PERSON>ps short head: 435.56 N, and Brachialis: 987.26 N. Let's compute the mamimum torques that each muscle could produce considering a static situation at the different elbow flexion angles:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:37.906198Z", "start_time": "2018-08-06T04:49:37.902200Z"}}, "outputs": [], "source": ["m_ef = r_ef*1\n", "m_ef[:, 2:] = r_ef[:, 2:]*f_ef[:, 2:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And let's visualize these data:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.429686Z", "start_time": "2018-08-06T04:49:37.907197Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsgAAAEYCAYAAABBfQDEAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi4yLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvhp/UCwAAIABJREFUeJzs3Xd4FdXWwOHfSq+EFhJKIEACSJcqAipNQAUUC1gQEeRa0Kvoh+1eKzYsYMdGFQsqICqICGIFhFCEUEPvBEhCejvr+2Mm3hATCJBkTpL9PuQ550xdczh7Zs3M3ntEVTEMwzAMwzAMw+LhdACGYRiGYRiG4U5MgmwYhmEYhmEY+ZgE2TAMwzAMwzDyMQmyYRiGYRiGYeRjEmTDMAzDMAzDyMckyIZhGIZhGIaRj0mQDbcnIi+IyP3nOO99IvJiScdkGEURkRQRaeR0HAWJiL+IfCMiSSLyhcOxhIrIVhHxO4t5/hSRFqUZl1H+uWv5K49E5HcRubAUl+/WZdokyKVIRHaLSJaI1CwwfJ2IqIhEOhNZ4URkmoiMdzqO/EQkFLgVeO8cF/E+cIuI1Cq5qIzyqizKpKoGqerO811OKbgOCANqqOr1DsfyCDBVVTMARGSZiGSISETeBCLSW0R255vnFeCZsg3TKEmVufy54/H1dERkAJCsqmvtz0/Z/0fX55vG6zz/39y6TJsEufTtAm7M+yAirQB/58IpOyLiVQKLuQ1YoKrp5zKzfQBeiJVkGwZU3jLZANimqjlnO2MJleW8ZfkCw4GPC4xKBf57mlnnAz1EpHZJxWI4orKWv/MiIp5lvMo7gZkFhp0AninBWNy6TJsEufTN5NTkbDgwI/8EIhIiIjNEJF5E9ojIf0TEwx53m32bY6KIJIrIThG52B6+T0SOisjwfMvyFZFXRGSviBwRkcki4m+Pu0xE9ovIg/Z8h0RkhD1uNHAzMM6+RfVNYRsjIq/b6z0pIjEi0j3fuKdE5EsR+VhETgK32cO+sIcli8gGEWkiIo/aMewTkctP8/31B37Ot468bRiXbxuuFpErRGSbiJwQkccKLGMZcOVp1mFULsUpk1eKyFr7d75PRJ7KN26IXQ6r2J/7i8hh+24H9hWVKPv9NBF5R0QW2uXqdxEJF5FJIpIgIlsk3y3M/PPmm3+8/f5cfvt5y3kaeAIYYscxUkQ87H3NHnt5M0QkxJ4+0o5lpIjsBZbaw7uJyB/2vmifiNxmDy9yv1OIzkCiqu4vMPwN4Mb825+ffbIbA5xuf2G4v8pY/go9vorIBWLdPUkUkVgRGVhg3e+KyAIRScVKJGuIyHz7e/lTRJ4Vkd/s6fPKrFe+ZSwTkVH5Pt8uIpvtbV8kIg2KiNcH6Em+Y6/teyALuKWI+c7q+3b3Mm0S5NK3AqhiFwRPYAj/vHLyJhACNAIuxdp5jMg3vjPwF1AD+AT4DOgIRGH9UN8SkSB72peAJkBbe3xdrANjnnB7XXWBkcDbIlJNVd8HZgET7FtUA4rYnlX2sqvbsXwhp9YjHAR8CVS1lwcwAGunWA1YCyzC+u3Vxbq9crrqE62ArQWGhQN++bbtA/t7aA90B56QU+ugbQbanGYdRuVSnDKZilUOq2KdXN0lIlcDqOrnwHLgDRGpAXwEjFLV+CLWdwPwH6AmkGnPu8b+/CXw2lnEfra/feyYnwSeBz63y/dHWHdnbgN6YO17goC3Csx6KXAB0FdE6mPdjXkTCMXaD6yzpzvTfie/wso0wAF7e54qevNNWa4AKmP5+8fxVUS8gW+AH4BawL3ALBFpmm/Wm4DngGDgN+BtIAOoDdxu/xWL/f09BgzGKr+/Ap8WMXk04CrkJFax7vI8acdfmLP9vt22TJsEuWzknTH3AbZgHQiAv2+bDAEeVdVkVd0NvAoMyzf/LlWdqqq5wOdABPCMqmaq6g9YZ3RRIiLAHcADqnpCVZOxDopD8y0r2543W1UXAClA/gJ5Wqr6saoeV9UcVX0V8C0w/3JVnaeqrnzVIn5V1UX2rd0vsArni6qajZXsR4pI1SJWWRVILjAsG3gu3/w1gdft7y8WiAVa55s+GeukwDDyFFkmAVR1mapusH/Hf2EdSC7NN8k9WFdYlgHfqOq3p1nXXFWNsa+WzAUyVHVGvvJ8No1gzva3fzo3A6+p6k5VTQEeBYbKqdUpnlLVVLss3wz8qKqf2vuP46q6rpj7nfwKK9N5XgAGSNENd5Lt+Y3yzZQ/uAjrpPRFVc1S1aXAt+SrfgJ8raq/q6rLXve1wBN2mdwITD+L2P8FvKCqm+1j8fNA2yKuIhdZRlV1PhAPjCpsPGf/fbttmS6xemXGac0EfgEaUuBWElYB8wH25Bu2B+sMNc+RfO/TAVS14LAgrMQzAIixjlkACJC/vtDxAnUQ0+x5i0VEHsQqGHWwziar2NuQZ18hsxWM9ZhdWP7eHjuGxELmTcA6e87veCHzF/Z95AkGkgpZtlF5na5MIiKdgReBlljl0xfr5A4AVU0UqyeIsVgHrdMp+Ns83W/1TM72t386dfjnfscLqyFfnvzlOQLYUchyirPfya+wMg2AqsaLyFtYd5beLWSSYArfTxjliyl/VvnbZye/eQoe+/OXv1Cs8rmvwPTF1QB4XURezTdM7PUVXE6RZdT2H2Aq/6yjDGf/fbttmTZXkMuAqu7BaphwBTCnwOhjWGeG+c/i6lPgjLqYjmH9AFuoalX7L0RVi1tg9XQjxapv/DDWLZRqqloVK/GUfJOddhnn4C+sW7fn4wJgfQnEYlQQZyiTYFUfmg9EqGoIMJl8v3MRaYt1e/NTrLqzJSUNK9nME16Cyy7oIP/c7+Rw6gEtf3neBzQuZDlnu985U5l+GavaR/tCxpmyXAFU0vJX8Nh4EIgQu72RreCxP/888VjlM6LA9HlS7dei4t8H/CtfGa2qqv6q+kchsW4HRETqFjIOVV0MxAF3Fzb+LLltmTYJctkZCfRU1dT8A+2z0dnAcyISbN/uGMs/62SdkX0m+gEwUexuzUSkroj0LeYijmDVRSxKMFYBjQe8ROQJrCvIpWkBp95aOxeXYtWdNIz8Ci2TtmDghKpmiEgnrLqAANh17j/Gqs83AqgrIiVxoACrTu9NIuIpIv04/9/+6XwKPCAiDe02DHl1lIvq5WIW0FtEbhCre6caItL2HPY7fwJVT3PwTcSqZjYu/3Cxer9oDyw+y+003FNlK38Fj68rsZLacSLiLSKXYbXX+aywme1cYQ7wlIgEiEhzrAaOeePjsZLrW+z4b+fUE9rJwKN51ZfE6hyg0O4e7SokP3L67X+cAmX0bLl7mTYJchlR1R2qurqI0fdiFZSdWBXxPwGmnOOqHsY6s1shVk8SP1L8OsYfAc3tFrXzChm/CCvR3IZ1SyaDwqtUlKQZwBVSdIv407J3pldwdnW1jErgDGXybqzujJKxGuPMzjfuBWC/qr6rqplYjXTGi0h0CYT1b6yDZCJWnd/CymFJmcL/bnXvwirP9xY1saruxSpLD2J197SO/zWuKfZ+R1WzgGkU0RLe9jqQW2DYQGCZqh483UYZ5UMlLH+nHF/tcjAQq6emY8A7wK2quuU0yxiDVUXhMFYZmlpg/B3A/wHHgRbA31eHVXUuVmPaz+wyutFed1He49S2UKdQ1d+xTnbPh1uXaVEt6TvihlGyROR54KiqTjqHee/Fuk13Xme6hmGUHLG65PoVuFCL2ce5iKwERtqNkwyj0hOrm8VRqtqtlJb/G3Cv2g8LKYXlu3WZNgmyYRiGYRhGOVPaCXJlZ6pYGIZhGIZhGEY+5gqyYRiGYRiGYeRjriAbhmEYhmEYRj7l6kEhNWvW1MjISKfDMIwyExMTc0xVQ52O42yZsmpUNqasGkb5UNyyWq4S5MjISFavLqpXGMOoeETkbJ6U5DZMWTUqG1NWDaN8KG5ZNVUsDMMwDMMwDCMfkyAbhmEYhmEYRj7FSpBFpJ+IbBWROBF5pJDxviLyuT1+pYhE2sMjRSRdRNbZf5PzzdNeRDbY87whIlJwuYZhnDsRmSIiR0VkY75h1UVksYhst1+r2cPFLodxIvKXiLTLN89we/rtIjK8sHUZhmEYRkVyxgRZRDyBt7EeSdgcuNF+Bnh+I4EEVY0CJmI9zjDPDlVta//dmW/4u8BoINr+63fum2EYRiGm8c9y9QiwRFWjgSX2Z7DKd15ZHI1VPhGR6sCTQGegE/BkXlJtGIZhGBVVca4gdwLiVHWn/ezwz4BBBaYZBEy3338J9DrdFWERqQ1UUdXlanXEPAO4+qyjNwyjSKr6C3CiwOD8ZXU6/yt3g4AZalkBVLXLaV9gsaqeUNUEYDHmZNYwDMOo4IqTINcF9uX7vN8eVug0qpoDJAE17HENRWStiPwsIt3zTb//DMsEQERGi8hqEVkdHx9fjHANwziNMFU9BGC/1rKHF1XOi1P+AVNWDcMwjIqjOAlyYVeCCz5+r6hpDgH1VfVCYCzwiYhUKeYyrYGq76tqB1XtEBpa7rqYNIzyoqgyacqqYRiGUekUpx/k/UBEvs/1gINFTLNfRLyAEOCEXX0iE0BVY0RkB9DEnr7eGZZpGGct6UQ8+2L/IGX/RuTETrzTjuKXnYC3KwMPzSVHvMnyDCDDL5ScoLp4125OaHQn6ke3RjwqRacuR0SktqoesqtQHLWHF1XO9wOXFRi+7EwriU+PZ1LMJDJyM8jIySAzN5PM3EyycrPIdmWT7com15VLjuaQ68rFpS4UxdplWEQEQRARvMQLTw9PPMUTbw9vfDx9/n719fTFx9MHfy9//Lz88PfyJ8ArgEDvQOvVJxB/L3+8PbzxFE88xANP8fx7eV4eXqcsy9vDG9Nm2DCM8iYpPZtVu06w+dBJ4uJTiE/OJDEtGwW8PIQaQT7UDvGjaVgwrepVpU29ELw8K8Vx75wUJ0FeBUSLSEPgADAUuKnANPOB4cBy4DpgqaqqiIRiJcq5ItIIqwHQTlU9ISLJInIRsBK4FXizZDbJqEwyM9LY8se3ZGz6nvATf9LAtY8Qe1yK+nPMsyapXlVJ86qKenjh4crCNyeV0KS11Ez8Ea8DLlgNxwlhd3B7uGAgF1wymICgkNOutxzLK6sv2q9f5xs+RkQ+w2qQl2Qn0YuA5/M1zLscePRMKzmadpTpsdPx9/LH18sXP08/fDx9rD8PH7w8vPDy8MLPw+/vhFUQrH9WcqqqKEquWgl0ruaS48ohLSeNxMxEsl3ZZOVmkZmbSUZuBpk51uv5EgQ/Lz/8PP0I8A6wEm7vAIK8gwj0DiTIO4hgn2CCfYIJ8Q0hxCeEEN8QqvpVpZpvNar5VcPfy/+84zAMwziToyczmL/+IN9tOMT6fYm47GsMdav6Ex7iR+0QPzw8hOxcF8dTsli/L5FP/7RqzQX7enFJ01CuaVuXS5uG4m2S5VOcMUFW1RwRGQMsAjyBKaoaKyLPAKtVdT7wETBTROKwGgUNtWe/BHhGRHKAXOBOVc1rNHQXVit7f2Ch/WcYZ+TKzWXT8u9IX/UxFyT+QhtJJ019ifNvxYrwgQQ26kztJu2oUasuQae5KpyVmcHO7es5tnU5Hnt+o9HJlVT/cylpKx9mZY2+1OxxD41bXVSGW1ayRORTrKu/NUVkP1ZvFC8Cs0VkJLAXuN6efAFwBRAHpAEjAOyT2WexTpQBnslXhovUvEZzYm6NKcGtKR6XusjIySAtJ4307HRSslNIzU4lLSeNHFcOuZqLqv6dbOdqLtmubHJcOWTlZpGVm/V3sp2ek056TjppOWmkZaeRkpXC4dTDpGSlkJydTHpOepFx+Hv5U92vOjX8axDqH0qofyhhgWGEBYQRHhhOnaA6hAWE4eVRrh5mahiGm1i7N4EPf9vF9xsPk+tSWtSpwpie0VzcuAYt64YQ5Fv4vkVVOXIyk5g9Cfy6PZ4fNh3hu78OEVbFlxFdG3JT5/pU8fMu461xT5L/lqa769Chg5pHYlZeKScT2Pjt29TdPosIPUiy+rO5Wg98W19Ds4uvwtcv4LyWn5uTw5aVi0hd9TGtEn7EX7JY798Z/z6P0aTdZSWzEWdJRGJUtYMjKz8PlaGsZudmczLrJElZSSRlJpGYkUhCZgIJGQkczzjOiYwTHEs/xrG0YxxNP0pyVvIp83uKJ7UDa1O/Sn0igiNoGNKQhlUa0qhqI8ICwkw1j3LGlFWjLMQeTOKVRVv5aWs8wX5eDO0YwdBO9WkcGnROy8vOdfHz1nim/rGL3+OOUy3AmzE9oxl2UQN8vCrmFeXillWTIBtuL+n4ETbPfYnm+z+jCqls8W5OSqvhtOx9C34B57ZTOOM6T8Szaf6rNNv9MdVIZnWV3kTcMIGweo1LZX1FMQfdiiM9J50jqUc4mHqQgynW377kfexL3sfek3tJzv5fAh3kHURU1SiaVm9K0+pNaV6jOU2qNsHb01zZcVemrBqlKTEti5cXbeWTP/dSxc+bf13aiOFdIgks4krxufhrfyITvt/Kb3HHiKoVxAuDW9ExsnqJLd9dmATZKPfSU5NZ98VztNg9nSqksTawG4G9/q9Mr+amnExgw+dP0W7/LLLxIrbFWDpe+yAenp5lsn5z0K0cVJXjGcfZlbSLHYk7iEuMY3vCdrYlbCMlOwUAbw9vmlVvRuvQ1rQNbUvbWm0JDwx3OHIjjymrRmlZvOkIj87ZQEJaFrd2acD9vZsQ4l96J8tLtxzhv/NiOZCYzu1dG/Jw/6b4epXNMa8smATZKLfU5SJmwYdErH6RMI6zNuBiQq54ikYtOzsW08FdWzj+2V20ylzDRt+2hI+YSc3w+qW+XnPQrdxc6uJA8gE2ndhE7LFYNhzbQOzx2L/rP9cNqkuHsA50rt2ZzrU7Uyug1hmWaJQWU1aNkpaelctT82P5fPU+LqhdhVeub02LOmXTgDw1M4eXvt/CjOV7aFGnCu/c3I4GNQLLZN2lzSTIRrm0d9s6kr+8jxZZ64nzbExW7+do3qW/02EBVuK+as4kWm14gTTx51Cft2nZdUCprtMcdI2Csl3ZbDuxjTVH1xBzJIbVR1aTlJkEQHS1aLrV6Ub3et1pW6st3h6mSkZZMWXVKEk74lO4++M1bDuazF2XNub+3k0cqRP846YjPPTlelThrZsupHt0+e/j3iTIRrmSk53Fqk+epv3OyWSID5ubj6XD4Afw9HK/Vv67N69GvriNurkHiGnxKJ1vGFdq6zIHXeNMXOpi64mtLD+0nD8O/EHM0RhyXDkEewfTvV53etbvSfe63QnwPr9GrMbpmbJqlJSft8Uz5pM1eHt6MHFIWy5t4mxSuvd4GqNnrmbbkWSevbolN3du4Gg858skyEa5sW/7etI/H0WTnG2sCexO/WHvlEn1hfORnHSCnZOH0iZ9JSvCbqTzv94plQeNmIOucbZSs1NZcXAFP+37iV/2/0JCZgJ+nn50r9edfpH9uKTeJfh5+TkdZoVjyqpREj5esYcnvt5I0/AqfHBre+pVc48T29TMHMZ8soaftsZzf+9o/t0rutz2tFPcsup+l+eMSkNdLlbNfYOWfz1PtngR0/FV2vW/vVw80S44pDotH1zAysmjuejIp6x6PYELx8zEy9vH6dCMSi7QO5BeDXrRq0Evcl25rDm6hh92/8DiPYtZvGcxQd5B9GnQh4GNB9I+rH25PcgZRkWiqry2eBtvLo2jV7NavHHjhSXaQ8X5CvT14v1bO/DonA1M+nE7aVm5PNq/WYXef7jPt29UKiknE9jy4Sg6nfyRjX5tqXXrNNrXbeh0WGfF08uLTnd/yPJpNeiy931i3hhCm/s+N0my4TY8PTzpGN6RjuEdebjTw6w6vIrvdn7Hot2LmBs3l/rB9bkm+hqujrqamv41nQ7XMColl0t56ptYZizfw5AOETx3TUu3fAS0t6cHL1/XmgAfT97/ZSe5LuU/V15QYZNk9/sfMCq8PVvXcXxSNy5MWsLyyLtoPm4ptcpZcpxHPDzocvvLLG90H+2Tl7LuzZvIzclxOizD+AcvDy+61OnC+G7j+emGn3i+2/OEBYbx+prX6fNFH8YuG8uqw6soT9XuDKO8c7mUx+ZuYMbyPYy+pBEvXtvKLZPjPCLC0wNbcNvFkXz02y5eW7zN6ZBKjbmCbJSptT98TPTvD5El3my+fCZdSrkXiLLS5dZnWT4tmy6732Xl5NF0uvvDclFVxKicArwDGNB4AAMaD2B30m6+3PYlc+PmsnjPYppUa8ItF9zCFY2uwNfT1+lQDaPCUlUen7eBz1bt496eUYzt06RcXI0VEZ4c0JyM7FzeXBpHiL83o7o3cjqsEmeO4EaZUJeLFdMe48I/7uGgdwTZo5aVehdpZa3LbS9aDfaOfcWKmf91OhzDKJbIkEge6vgQS65fwtMXP42iPPHHE/T9si8f/PUBJ7NOOh2iYVQ4qsrT32zi0z/3cU+Pxjx4edNykRznERGeu6YVV7aqzfjvNvPN+oNOh1TiTIJslLrMjDRWvz6Ei3a/zeoqvan/4LIyf2RzWek02trGLrveImbBR06HYxjF5uflx+DowXw14Cve7/M+zWo04421b3D5l5czMWYix9OPOx2iYVQYbyyJY9ofuxnZrSEPXd7U6XDOiaeH8NqQNnSMrMaDX6xn9e4TTodUokyCbJSqpBPxxL3Wl45JP7C8wZ20v/8L/PwrxtN4CuPh6Umru2eyxbs5zVc+Qtz6350OyTDOiojQpU4XJveezJcDvuSSupcwLXYa/b7qx8urXjaJsmGcp1kr9zDxx21c265euW/k5uvlyfvDOlC3qj+jZ8awPyHN6ZBKjEmQjVJz9MAuEt7qSXRmLKvbvUSXES9Vinq5vn4B1Bw5m5MSTNDcW0mIP+R0SIZxTppWb8qESycwb9A8+jTow8ebP6b/nP5Mipn099P7DMMoviWbj/DfeRvp2awWL17bqlwnx3mqBfrw0fAOZOe6+NfMGNKzcp0OqURU/GzFcMS+7etxfdCHmrnxbOs9lQ4D73Q6pDJVMzyCk1dPp7omsvejYbhyK8YOw6icGoY05PnuzzNv0Dwui7iMKRun0H9Ofz7a8BEZORlOh2cY5cLGA0nc++laWtQJ4a2bLsTbjXurOFuNQoN4Y+iFbDp0kkfn/FUhesMp1v+OiPQTka0iEicijxQy3ldEPrfHrxSRyALj64tIiog8lG/YbhHZICLrRMQ8xqcC2bFhBYGzrsKHTA4P/pKW3Qc5HZIjott2Z23zcbTJWMXKj02jPaP8axjSkAmXTOCLAV/QNrQtk9ZMYsC8AXy781tc6nI6PMNwW0dPZjBq+mqq+nvz4fAOBPhUvE7EejSrxdjeTZi37iCf/LnX6XDO2xkTZBHxBN4G+gPNgRtFpHmByUYCCaoaBUwEXiowfiKwsJDF91DVtuXx8ZxG4bauXkroV4PJxpuUm74hqk03p0NyVKfr/4+Y4B503Pku29YsczocwygRTas35Z3e7/DR5R9Rzbcaj/76KLcsuIX18eudDs0w3E5Gdi7/+jiGpPRsPhzekbAqFfdR7/f0iOKSJqE8/c0mNh4o39WwinMFuRMQp6o7VTUL+AwoeElwEDDdfv8l0EvsijUicjWwE4gtmZANd7Vl5Q/U/eYmkiUI14iF1G/S1umQHCceHkTd/iHHpDr+39xFWop77DBE5N8islFEYkXkfntYdRFZLCLb7ddq9nARkTfsO0R/iUg7Z6M33EWn2p347KrPGN91PIdTD3PLglt4/LfHOZZ+zOnQDMMtqCr/nbeRtXsTee2GNjSvU8XpkEqVh4cwaUhbqgf4MOaTNaRmlt8HZxUnQa4L7Mv3eb89rNBpVDUHSAJqiEgg8DDwdCHLVeAHEYkRkdFFrVxERovIahFZHR8fX4xwDSdsWfkD9RfcQoJHNbxHLaJ2g/LZbU1pCKlWk2N9JlHXdYgNU+9zOhxEpCVwB9bJbxvgKhGJBh4BlqhqNLDE/gzW3aNo+2808G6ZB224LQ/xYFDUIL655htub3k7C3YtYMDcAXyy+RNyXabuvVG5zVq5ly9i9nNfzyj6t6rtdDhlonqgD5OGtmXPiTSe/qb8XhstToJcWBPLgrWvi5rmaWCiqqYUMr6rqrbDOvjeIyKXFLZyVX1fVTuoaofQ0NBihGuUtS2rfiRiwTCOe9TA746F5fax0aWpZdcB/Bk+lM7H5xH7+3dOh3MBsEJV0+wT2p+Bazj1TtB04Gr7/SBghlpWAFVFpHLs6Y1iC/QO5IH2DzB34Fxa1WzFC3++wE0LbmLT8U1Oh2YYjlizN4Gnv4nlsqah3N+7idPhlKmLGtXg7ssaM3v1fhZsKJ89ORUnQd4PROT7XA8o+MiUv6cRES8gBDgBdAYmiMhu4H7gMREZA6CqB+3Xo8BcrKtZRjmzfe0v1P3WunLsO2oBoXUinQ7JbbW59WX2SzghPz5Iemqyk6FsBC4RkRoiEgBcgVV+w1T1EID9Wsuevjh3kczdHgOwnsz3Xp/3ePmSlzmSeoQbv7uRV1a9QnpOutOhGUaZOXoyg7s+jiE8xI9JQ9ri4VH+u3M7W/f3bkKbeiE8PncDR5PLX283xUmQVwHRItJQRHyAocD8AtPMB4bb768DltpXm7qraqSqRgKTgOdV9S0RCRSRYAC7GsblWAdtoxzZFbuS0K9vJNkjGO+R35krx2fgHxhMYu9XqKeHWD/zYcfiUNXNWA1pFwPfA+uB01UUK85dJHO3x/ibiNCvYT/mXzOfwdGDmb5pOtfOv5ZVh1c5HVqFICIRIvKTiGy22xH82x7+lIgcsHuHWiciVzgda2WUmZPLnR/HcDI9h/eHdaBqgI/TITnC29ODV29oQ2pWLo/N2Vjuun47Y4Js34IdAywCNgOzVTVWRJ4RkYH2ZB9h1TmOA8byv7qLRQkDfhOR9cCfwHeq+v25boRR9vbHbST4ixvIwgcdNr/CPjq6pLXsOoA/q11Jh0OfsmuTc8mCqn6kqu1U9RKsuz3bgSN5VSfs16P25MW5i2QY/1DFpwpPdnmSKX2nAHD7otsZv2I8adkV52lbDskBHlTVC4CLsKop5vUuNdHuHaqtqi5wLsTK68mvY1mzN5FXrm/DBbUBJLQXAAAgAElEQVQrdqO8M4mqFcz/Xd6UHzcfYc6aA06Hc1aK1Q+yqi5Q1Saq2lhVn7OHPaGq8+33Gap6vapGqWonVd1ZyDKeUtVX7Pc7VbWN/dcib5lG+RB/cDces67Bk1zSh35F3UYXOB1SuRJ906ukSABp8x5AXc70HSsitezX+sBg4FNOvRM0HPjafj8fuNXuzeIiICmvKoZhFEfH8I58NfArhjUfxuyts7l2/rXEHIlxOqxyS1UPqeoa+30y1sWrf1R7Msre56v28tmqfdx9WWOubG2aagDc3q0h7RtU45lvNxGfnOl0OMVWcR7jYpSJpIRjpHw4iBDXSeIHzqJBM9Pj19mqFlqbrS3G0iJrAzHffeBUGF+JyCbgG+AeVU0AXgT6iMh2oI/9GWABVleNccAHwN0OxGuUc/5e/ozrOI4pfaegKCO+H8FrMa+RlZvldGjlmv1grguBlfagMXZ3jFPyumosZB7TXqAUbDyQxH+/jqVbVE0evNz05JTH00N46dpWpGfllqteLUyCbBRbRnoqB969mrq5+9jV+32atLvU6ZDKrQ7X/JvtXtHUj3nRkQZ7dvuA5vZdnCX2sOOq2ktVo+3XE/ZwVdV77DtIrVTVPPnSOGcdwjswZ+AcBkcPZurGqdy84GZ2Jv7jpqNRDCISBHwF3K+qJ7G6YGwMtAUOAa8WNp9pL1DyktKzuWtWDDUDfXjjxgvxrISN8k4nqlYwY3pG8e1fh/hx0xGnwykWkyAbxZKbk8Omt4bSPGsDf3V6qdI+PrqkeHp5kdPnOWpxgnWzxzsdjmGUqQDvAJ66+Cne7PkmR1KPMOTbIczeOrvcNeJxkoh4YyXHs1R1DoCqHlHVXFV1Yd3tMb1DlQFV5ZGv/uJQYgZv3tSO6oGVs1Hemdx5aWOahAXx5PxY0rLc/wEiJkE2imXVB/fQLvUXVkSPpcOVdzgdToVwQee+rAm6hDa7pxJ/cLfT4RhGmbss4jLmDJpD+7D2PLviWR78+UGSMt3jaZPuzH5S7UfAZlV9Ld/w/JVer8H0DlUmZizfw8KNhxnXryntGxRaq8UAfLw8eO6aVhxITOf1JdudDueMTIJsnNGKT5/noiOfsSL0ejrf+F+nw6lQwgZPwItcdn3xmNOhGIYjavrX5J3e7/Bg+wf5ae9PXP/N9ayPX+90WO6uKzAM6FmgS7cJIrJBRP4CegAPOBplJbD50EmeW7CZHk1DGdWtkdPhuL2OkdUZ0iGCj37dxZbDJ50O57RMgmyc1vqln9FxywTWBlxMx39NRjzMT6Yk1W10AWvCBtPuxEL2blvndDiG4QgP8eC2lrcx84qZ1vuFtzE9drqpclEEVf1NVUVVW+fv0k1Vh9ntBFqr6kDT20zpSs/K5d5P1xLi780r17eplA8DOReP9G9GkJ8XT34d69Zl3GQ7RpF2bFhB9M/3scurEU3v/gxPLy+nQ6qQoq99kiy8iZ//pNOhGIajWtZsyewBs7k04lJeWf0KDyx7gJNZ7n2Vyai8xn+3iR3xKUwa0pYaQb5Oh1NuVAv04aHLm7Jy1wm+/ct9z+FMgmwU6tjhfQR+dTMpEkjIyDkEBIU4HVKFVSOsHusjbqZ9yjLi1v/mdDiG4agqPlWYeNlEHurwEMv2LWPot0PZemKr02EZximWbD7CrJV7uaN7I7pG1XQ6nHLnxk71aVGnCs8v2Exqpns22DMJsvEPGempHPvwekL0JCevmUlonUinQ6rwWlz3H5IIJGWR6dHCMESE4S2GM6XvFDJyMrhlwS18u/Nbp8MyDACOpWQy7su/aBYezIOXN3E6nHLJ00N4emALDiVl8N7PO5wOp1AmQTZOoS4XGybfTrOczWy+aAJRbbo5HVKlUKVqDTY1GEbbtOXmKrJh2NqFtWP2gNk0r9GcR399lJf+fIlsV7bTYRmVmKry6JwNJGfk8PrQC/H18nQ6pHKrQ2R1rmpdm/d/3cnBxHSnw/kHkyAbp1j52fN0TPqe5RGjaNd/hNPhVCotrhnHSQJJXmSevG4YeWr61+TDvh9y8wU38/Hmj/nX4n+RkJHgdFhGJfXVmgMs3nSEh/o2oWl4sNPhlHuP9G+GS2HC91ucDuUfTIJs/G3jr1/TYeurrA3oSufbJjgdTqVTpWoNYuvfzIVpf7Djrz+cDscw3Ia3hzePdHqE57s9z/qj6029ZMMRBxPTeXp+LJ0iqzPSdOlWIupVC2B090bMW3eQdfsSnQ7nFCZBNgA4tGcrdZfcw37PukTfOQsPT3PbyAnNrx5HivqTuNicoBhGQQMaD2BG/xnkaA7DFg5j8Z7FTodkVBKqyiNzNpCrysvXtzaPki5Bd17WmJpBPjy/YLNbdftmEmSDjPRUUmbchJfm4HnjLIKqmCcBOSWkeigbaw+m7cllHNgZ63Q4huF2WtRswedXfU50tWjGLhvLu+vedauDqlExzV69j1+2xfNI/2Y0qBHodDgVSpCvF//u3YQ/d51gyeajTofzt2IlyCLST0S2ikiciDxSyHhfEfncHr9SRCILjK8vIiki8lBxl2mUnb/eG0V0bhw7ur1GRHQbp8Op9BoPHEcunuz/zlxFNozC1PSvydS+UxnYeCDvrH+Hcb+MIyMnw+mwjArqUFI647/dzEWNqnNL5wZOh1MhDe0YQaPQQF5YuJmcXJfT4QDFSJBFxBN4G+gPNAduFJHmBSYbCSSoahQwEXipwPiJwMKzXKZRBv78aiKdEhewvO4I2va5yelwDCC0TiTrqvelzbHvnA7FMNyWj6cP47uO54H2D7Bo9yJuX3Q7x9KPOR2WUcGoKo/P3UiOS5lwrXlaXmnx9vRgXN9m7IhPZc6aA06HAxTvCnInIE5Vd6pqFvAZMKjANIOA6fb7L4FeIiIAInI1sBPIf7+4OMs0Slnc+t9o89dzbPBtR6cRrzgdjpFPeP9xuEwNKMM4LRHh9pa3M7HHROIS47jxuxtN4z2jRH297iBLtxzlob5NqV8jwOlwKrS+LcJoG1GViT9uIyM71+lwinUErgvsy/d5vz2s0GlUNQdIAmqISCDwMPD0OSzTKEVJCcfwn3c7iVKFuiM/No+RdjP1m7Qld6z7dXtjGO6oV/1eTOs3DZfLxfDvh/PbAdOXuHH+jqdk8vQ3sVxYvyq3XRzpdDgVnogwrl9TDiVlMHP5HqfDKVaCXNj9hIItIoqa5mlgoqqmnMMyrQlFRovIahFZHR8ff8ZgjTNTl4udHw6nlusYCVe8R/Va5tzEHQWHVC+1ZYvIAyISKyIbReRTEfETkYZ2G4LtdpsCH3va07YxMAx30LxGc2ZdOYuI4AjGLBnD7K2znQ7JKOee/XYTKZk5vHSt6bWirFzcuCbdo2vy9rI4kjOcfShQcRLk/UBEvs/1gINFTSMiXkAIcALoDEwQkd3A/cBjIjKmmMsEQFXfV9UOqtohNDS0GOEaZ7Lys+e5MPU3Ypr8m2ad+jgdjlHGRKQucB/QQVVbAp7AUKy2AxNVNRpIwGpbAGduY2AYbiE8MJzp/aZzcZ2LeXbFs7y6+lVc6h4Nfozy5edt8cxbd5C7LouiSZh5IEhZ+r++TUlMy2bKb7sdjaM4CfIqINq+uuSDdSCdX2Ca+cBw+/11wFK1dFfVSFWNBCYBz6vqW8VcplEKtq35mXZbX2NtwMV0vvG/TodjOMcL8LdPaAOAQ0BPrDYEYLUpuNp+X2QbA8NwNwHeAbzR8w2GNB3CtNhpjPtlHJm5mU6HZZQjaVk5PD53A41DA7mnR2Onw6l0WteryuXNw/jw150kpmU5FscZE2S7TvEYYBGwGZitqrEi8oyIDLQn+wirznEcMBY4bbdtRS3z3DfDKI6TiccJ+uYOTkh1Go2chniYRmCVkaoeAF4B9mIlxklADJBol004tV1AoW0MCi7XVIcy3IWXhxePd36cse3Hsmj3Ikb/MJqkzCSnwzLKideXbGd/QjrPX9MKXy/z0CwnjL28CSlZOXzw607HYihWhqSqC1S1iao2VtXn7GFPqOp8+32Gql6vqlGq2klV/7FFqvqUqr5yumUapUddLrZ/eDu1XPEkXvEuITXCnA7JcIiIVMO6KtwQqAMEYnW5WFBeu4BitRkw1aEMdyIijGg5ggmXTGDDsQ0MWziMAynu0X2U4b42HzrJh7/uYkiHCDo3+sd1AKOMNAuvwlWt6zD1992cSHXmKrK5hFhJrJr7Ou1TlrGq0V2m3rHRG9ilqvGqmg3MAS4GqtpVLuDUdgFFtTEwDLfXv2F/3uvzHsfSj3HLglvYcsL0DmMUzuVSHpu7gar+3jx6RTOnw6n0/t0rivTsXMeuIpsEuRLYs3Udrf56no2+bel8y7NOh2M4by9wkYgE2HWJewGbgJ+w2hCA1abga/t9oW0MyjBewzgvHcM7MqPfDDzFk9u+v40Vh1Y4HZLhhj5btY+1exN5/MoLqBrg43Q4lV5UrWAGtK7D9D+cuYpsEuQKLjMjjZzZI8gQP8KHz8DD09SnquxUdSVWY7s1wAas/cD7WH2Wj7XbEtTAalsAZ9nGwDDcUVS1KD6+4mNqB9bmrh/vYsHOBU6HZLiRYymZvPT9Fi5qVJ1rLjRdn7qL++yryO//UvZXkU2CXMGtnTqWxrk72dNtAjXrmGfIGxZVfVJVm6lqS1UdpqqZ9pMtO9ltCa5X1Ux72jO2MTCM8iA8MJzp/afTJrQND//6MDNiZzgdkuEmXly4hdTMHMZf3RLTSY/7iKoVzFWt6zBj+W4SyvgqskmQK7ANv8zloiOfsrLmYNr2vtHpcAzDMBxXxacK7/V5jz4N+vDy6pd5bfVrpq/kSm717hN8GbOfUd0bEVXL9Hnsbu7tGUVaVi5Tft9Vpus1CXIFlXjsMGFLx7LHI4I2t7/pdDiGYRhuw9fTl5cveZkhTYcwNXYq//ntP2S7nH1ql+GMnFwX/5m3kTohftzXK8rpcIxCNAkLpn/LcKb9vpuk9LIrpyZBroDU5WLn1DuoqklkD3oPv4Agp0MyDMNwK54enjze+XHuaXsP3+z8hvuW3kdadprTYRllbOaKPWw5nMwTA5oT4ON15hkMR4zpGUVyZg7T/9hdZus0CXIFtHr+u7RL/YWYxvcQ1aar0+EYhmG4JRHhzjZ38kSXJ/jj4B/csfgO80CRSiQ+OZPXftjGJU1C6dsi3OlwjNNoUSeEXs1qMeX3XaRm5px5hhJgEuQK5uDurVyw9lk2ebek001POh2OYRiG27u+yfW8eumrbDm+heELh3M49bDTIRll4KXvt5CRk8tTA5qbhnnlwN09okhMy+bTP/eWyfpMglyBuHJzSfhkJABVb56Cp5e5XWQYhlEcvRv0ZnKfyRxOO8ywhcPYmWQ6a6nIYvYk8GXMfkZ2a0SjUFMNsTxo36AaFzWqzge/7iQzJ7fU12cS5Arkz8+eo0XWBja1fYw6kU2dDscwDKNc6Rjekal9p5Kdm83whcPZEL/B6ZCMUpDrUp6aH0tYFV/u7Wka5pUn9/SI4sjJTOasKf3HxptLjBXEni1ruHDbG6wL7ELHQWOcDscwDKNcuqDGBczsP5PRi0cz8oeRTOoxiYvrXOx0WEYJ+mL1PjYcSOL1oW0J9DVpUJ6MnAwOpBzgUOohEjISOJl1khxXDqqKr5cvgd6B1PCrQVhAGBFVIvD19C3zGLtF1aRV3RDe+3kHN3SIwNOj9KrGmF9GBZCTnUXWl6NJEz/q3fo+4mFuDBiGYZyriCoRzOg/gzt/vJN7ltzDC91foF9kP6fDMkpAUno2ExZtpWNkNQa2qeN0OI5RVXYk7mDVkVWsPbKWzSc2s+fkHhQt1vwe4kFEcAQtarSgdWhrutTuQsOQhqVel1tEuPPSxtzzyRoWxR7mila1S21dJkGuAFbPeoqLcrYT02kS7cPrOx2OYRhGuRcaEMrUflO5d8m9jPt5HEkZSQxpNsTpsAAQkQhgBhAOuID3VfV1EakOfA5EAruBG1Q1wak43dHrP24nIS2LJwd0qnQN81SV9fHr+X739yzdu5RDqYcAqBVQi1Y1W9G/YX8aVGlAnaA6VPerTrBPMD4ePogImbmZpGSlcCz9GIdTD7P75G62JWxj9ZHVLNhlPba9dmBtetXvRd/IvrQJbVNq32+/luFE1ghg8s876N8yvNTWYxLkcm5X7Era7ZpMTPBltL9ihNPhGIZhVBh5T937v5//j/Erx3Mi4wR3trnTHRKrHOBBVV0jIsFAjIgsBm4DlqjqiyLyCPAI8LCDcbqVuKPJzFi+m6Ed69OybojT4ZSZhIwE5sbN5attX7E3eS8+Hj50rduV0a1H06VOF+oE1jnjbzrQO5DqftWpX+WfF+H2J+9nxaEV/Lz/Z2Zvnc3Hmz+mQZUGDI4ezLXR1xLiW7LftaeHMPqSxjw2dwN/7DhO16iaJbr8PMVKkEWkH/A64Al8qKovFhjvi3U22x44DgxR1d0i0gl4P28y4ClVnWvPsxtIBnKBHFXtcP6bU7lkZ2WSO+cukiWIRsMnOx2OYRhGhePn5cdrPV7jqT+e4p3173Ai4wSPdn4UD3GuKpuqHgIO2e+TRWQzUBcYBFxmTzYdWIZJkAHr6ukz327G38eThy5v4nQ4ZeJAygGmbpzK13Ffk5GbQbta7RjdejS96vciyKfkeu6oF1yP64Kv47om15GancriPYuZu30uE2Mm8u66dxkUNYgRLUdQN6huia1zcLu6vLZ4G+/9stO5BFlEPIG3gT7AfmCViMxX1U35JhsJJKhqlIgMBV4ChgAbgQ6qmiMitYH1IvKNqub18txDVY+V5AZVJqtnPUGX3B2s6fIW7UJLrx6OYRhGZebt4c34ruOp7ledabHTSMxM5Pluz+Pt6e10aIhIJHAhsBIIs5NnVPWQiNQqYp7RwGiA+vUrR7W8n7Ye5Zdt8fz3qubUCCr7xmVl6UjqESb/NZl52+chIlzV6CqGNR9GdLXoUl93oHcgV0ddzdVRV7MtYRuzNs/iq+1f8dW2rxgUNYi72txFWGDYea/Hz9uTEV0jeXnRVjYfOskFtauUQPSnKs4pcCcgTlV3qmoW8BnWWWp+g7DOVgG+BHqJiKhqWr5k2A+KWfvbOKNdm1bRfvcHxAT3pF3fYU6HYxiGUaGJCA92eJCx7cfy/e7vGbN0jOOPphaRIOAr4H5VPVnc+VT1fVXtoKodQkNDSy9AN5GV42L8t5tpFBrIrV0aOB1OqUnPSeftdW8zYN4A5sXN47om17Fw8EKe6fpMmSTHBTWp1oSnL36ahYMXcl2T6/h6x9dcNfcq3l73Nuk56ee9/Fs6NyDAx5MPfimdPsuLkyDXBfbl+7zfHlboNHZCnATUABCRziISC2wA7syXMCvwg4jE2GezRjHlZGeRM+cuUiSQhsPecjocwzCMSmNEyxE8c/EzrDy0klE/jCIhw5k2cCLijZUcz1LVOfbgI/bdWuzXo44E52ZmLN/NzmOp/PfK5nh7Vsxenn7a+xNXz7uayesnc2m9S5l/9Xwev+jxErlae77CA8N5/KLHmX/1fHpE9GDy+skMmjeIn/b+dF7LDQnwZkjHCOavP8jBxPNPuAsqzi+lsJrbBa8EFzmNqq5U1RZAR+BREfGzx3dV1XZAf+AeEbmk0JWLjBaR1SKyOj4+vhjhVnyrP32W6Jzt7Or0FNVrlVydHsMwDOPMrom+hkk9JrEtYRu3LryVgykHy3T9YrWo+gjYrKqv5Rs1Hxhuvx8OfF2mgbmhE6lZvL5kO5c0CeWyphXvavmJjBM89PND3PfTfQR4BzC171RevvRlIoIjnA7tHyKCI5hw6QSm9p1KoHcg9/10H2OXjeV4+vFzXubIbg1RYNofu0sszjzFSZD3A/m/6XpAwb3B39OIiBcQApzIP4GqbgZSgZb254P261FgLlZVjn+obLeCzmTf9vVcuONd1gZ2o10/02uFYRiGEy6LuIz3+7zP8YzjDFtY5tXcugLDgJ4iss7+uwJ4EegjItux2g29eLqFVAYTF28jLSuX/155gTv0PlKilu1bxjVfX8PSvUu598J7mT1gNh3C3b+/gw7hHZg9YDb/bvdvft73M4PnD2bJniXntKx61QLo3zKcT1fuJSUz58wznIXiJMirgGgRaSgiPsBQrLPU/PKftV4HLFVVtefxAhCRBkBTYLeIBNpd0yAigcDlWA36jNNw5eaSPPtuMsWHiJvfMQ8EMc6JiDTNd1BdJyInReR+EakuIotFZLv9Ws2eXkTkDRGJE5G/RKSd09tgGO6gXVg7pvWbVuata1T1N1UVVW2tqm3tvwWqelxVe6lqtP164sxLq7i2H0nmkz/3clOn+kSHBTsdTonJzM3k2eXPcu/Se6kVUIvZV81mdOvReHs432i0uLw9vBnVahSfX/U5YQFh3L/sfp5e/vQ51U0e1b0RyZk5fL5q35knPgtnzLDsOsNjgEXAZmC2qsaKyDMiMtCe7COghojEAWOx+l4E6IbVc8U6rKvEd9u9VoQBv4nIeuBP4DtV/b4kN6wiWvXVqzTP3siWNo9Ss07FbWhglC5V3Zp3UMXqmjENq3w+gtWHajSwhP+V4/5AtP03Gni37KM2DPfUpFoTZl05y+kwjEI8t2AzAT6ePNCn4nTrtvfkXm5ZcAuzt83mtha3MeuKWURVi3I6rHMWVS2KWVfOYmTLkXy57Utu+u4mdiXtOqtltI2oSsfIakz9fRc5ua4Si61YlyDtM9MmqtpYVZ+zhz2hqvPt9xmqer2qRqlqJ1XdaQ+fqaot7INxO1WdZw/fqapt7L8Wecs0inZ4XxwtYl9jg287Og66x+lwjIqjF7BDVfdwam8004Gr7feDgBlqWQFUzWsIZBiG1QjJcC+/bItn2dZ47usZTfVAH6fDKRG/7P+Fod8O5WDKQd7u9TYPdngQH8/yv23eHt7c3/5+3uv9HsfTj3PjdzeedZWLkd0asT8hnUWxR0osLnOPvhxQl4vDn9yDBy5qDH3XVK0wStJQ4FP7/Sl9qAJ5fagWpycb06DWMAy3kOtSnvtuM/WrB3DrxeX/bquqMmXjFMYsGUPd4LrMHjCbS+oV2q9BuXZx3Yv5/KrPaVilIfcvu59317+LavHqL/VpHkb96gFM+f3srj6fjsm0yoE130+lbfoK/oq+mzoNmzkdjlFB2G0KBgJfnGnSQob9Y69lGtQahuEOZq/ex9YjyTzavxm+Xp5Oh3NesnOzefKPJ5kYM5G+kX2Z0X9GiT6Rzt3UDqrNtP7TGNh4IO+se4dxv4wjMzfzjPN5eggjukYSsyeBdfsSSyQWkyC7uaQT8TT48xm2e0bRYchjTodjVCz9gTWqmndPqqg+VIvTk41hGIbjUjJzePWHbXSMrEa/luW76ktKVgp3L7mbuXFzubPNnUy4ZAL+Xv5Oh1XqfD19Gd91/N8P5bnjhztIzDhz0nt9hwiCfb346LeSuYpsEmQ3t3Xm/VTVk8jAN/DyLv91jQy3ciP/q14BRfehOh+41e7N4iIgKa8qhmEYhjt57+cdHEvJ5PErm5frbt2OpR9jxKIRrDq8ivFdx3NP23vK9facLRFhRMsRvHrpq8Qei2XYwmFn7G88yNeLIR0jWLDhUIk8OMTrvJdglJpNyxfSKeFbVtS+mYvadHU6HKMCEZEArH5S/5Vv8IvAbBEZCewFrreHLwCuAOKwerwwHXAbhuF2DiWl88GvOxnYpg5tI6o6Hc45O5hykDt+uIP49Hje7Pkm3et1/9/I7AxI2g/JByE9ATKTITfbGuftDz5BEFQLgsOhSl3wKN9VTC6PvJya/jUZs3QMwxYO473e7522147hF0cy5fddzFyxh4f7nV+VVJMgu6nMjDQCFz/EQalF61tecDoco4JR1TTsx8HnG3Ycq1eLgtMqYLpOMQzDrb2yaBsuhf/r29TpUM7Z7qTdjPphFGk5aXzQ6x3aZGTALy/DgbVwNBYS9lDsjrc9faFGY6jdFuq2g8juENoUytmV6HZh7Zjadyp3/XgXIxaN4L0+79G8RvNCp42oHsDlzcP59M+93NczGn+fcz9BMAmym1rzyVN0ce1n/aUfUicoxOlwDMMwDMNtxR5MYs7a/Yzu3oiI6gFOh3NOdibtZOT3I3BlpzOVMJp+eCXkZFgja0RBnQuh9VCoFglV6kBADfANgryu3rLTrSvKqfFw8gAc3wHxWyHuR1j/iTVNUDg06QsXDIRGl4Fn+UgDm1ZvyvR+0xn1wyhGLRrFO73foW2ttoVOO6JrJN/HHmbeugPc2Kn+Oa+zfHwzlcy+uA202zOFmCo9aN/j+jPPYBiGYRiVlKrywoItVPX35u4e5fChGTlZ7Fw3jdtj34HcLKYcOkLjoExofxs0vBQadAH/aue+fFVI3AO7frWS5Y1fwZrpEBgKrW6ADiOgZnSJbU5piagSwfT+VpJ85493Mrn35EKT5E4Nq9O8dhWm/r6LoR0jzrnutmmk52bU5SLxi3vJwosGN77udDiGYRiG4dZ+3hbPb3HHuLdnNCH+5edxy6TEw7IX2fNGC0aunwSuHKbU7k/jUb/Afeug/0vQ7IrzS47BqlJRLRLaDYMbpsO4nTD0E6jfBf58H97qADOvgZ3LrGTajYUHhjOl7xRq+tfkX4v/xbqj6/4xjYjV5du2Iyn8seP4Oa/LJMhuJua7D2iVuZZNzR8wj5M2DMMwjNPIdSkvLtxCgxoB3HJROTlmJh2ABeNgUksO/DaBkTWCcPmF8NGgOTTq/yqEtSjdesJevtDsShgyEx6IhR7/gcMbYcYg+LA37Fjq1olyrYBaTOk7hf9n77zjqqr/P/78cC97D0EUFTduRFBxoubMlXuUlZVWapplafub/jJtWJlZ5s5KzcxRmnvkFkVz4kBciIAoQ+blfn5/nKuZoV4QuAif5+NxHvdyzufzOS/gjvd5n/co41CGlza+xLGEY/8Z061BOafKiEQAACAASURBVDwcbZi/Kzrf51EGcjEi6XoC/gc+4pS+BsG9X7O0HIVCoVAoijW/HrzEydgUxnWsiY2+mJs0aYmw7m34KhDC5xBfpzsv1GxEuo0DszrPp6pHjaLX5OwDrcfBmCPQdRqkxGre5IXd4crfRa/HTLwdvJndYTautq4M2zCMU9dP/eu4nbWOgY0rsPHEVS4mpuXrHMX81VS6OPnjONxlElbdvkCnV+HhCoVCoVDci/SsHD5ff4oGFdx4vJ6vpeXcG0MW7J4BXwbCnm+gXj+Shm9nuO4aCVlJzHxsJjU9LFx5w9oOgofCKweh81TNo/xdK1g9RisnVwwp61iW2R1mY6e3Y/iG4VxMvviv4082rYSVEPyw53y+1lcGcjHh1MFthMT/xn6fvlRTNY8VCoVCobgvc3eeIzY5g7c6BxTfJhrRO+DbFrDuLfALhpd2kdntc145OJVzSef4ss2X1C9T39Iq/0FvC02GwysR0PQlOLgQpgfDkWXFMuzCz9mPWe1nYTAaeGHDC8Slxd0+5utqT6c6ZVm87wJpWYY8r60M5GJAjsGA+GMs14QbdQZPsbQchUKhUCiKNYk3s/h261keq+VNkyqeD55Q1KRfh5UjYP7jYEiHgUvgyV/J8arB+O3jiYiLYHKLyYSWC7W00tyxd4NOk2H4NnCvBL8+B0uehNS4B88tYqq6VWXmYzNJzEjkpY0vkZKVcvvY0838Sc4wsPLQ/bvw5YYykIsB4b9+RvWcM1wIeQdnVw9Ly1EoFAqFolgzffNpbmYZHrpbWqFwah3MaAqHfobmY+DlvVCzEwjBp+GfsvHCRt4IeYNOlTtZWumDKVsPhq6H9hPh9AaY2Uz7/YoZdb3q8kXYF0TdiGL0ltFk5WQBEOLvTi1fFxbsikbm0QNuloEshOgkhIgUQpwRQozP5bitEGKJ6fheIYS/aX9jIcQh03ZYCPGEuWuWFhJiL1LrxBccsW1IUOehlpajUCgUCkWx5sK1NBbtOU//kApU93G2tJx/yLoJv78KP/UDBw94YRO0/x/YaI1LFh1fxKITi3iy1pM8WftJC4vNAzo9NH8Fhm0FJx/t91v39j8trosJzco3Y2KLieyP3c87O9/BKI0IIXg6tBInY1PYH523WOoHGshCCB0wA+gM1AYGCiHu7vH3HHBdSlkNmAbcihM4CgRLKQOBTsB3Qgi9mWuWCs79/Dp2MhPX3l8grJRDX6FQKBSK+/HJ+kh0VoIxj1mg6sO9iD0Ks8IgfB40G6UZk+Ua3j687eI2pu6fSruK7Xg9+HVLqXw4fGrDC5uh8TDY/TXM6wzJeQ9dKEy6VunK6KDRrD23lukR0wHoEVgeFzs9C3ZH52ktcyyyxsAZKWWUlDILWAz0uGtMD2CB6fkyoJ0QQkgp06SUtyKj7fingbg5a5Z4TuxdR0jSnxwo/yQVa+TeMlGhUCgUCoXGkUtJrD4cw/MtquDjYmdpORoHF8LsdpCRBENWQIdJWrKbicjESN7Y/ga1PGvxUYuP0FnpLCj2IdHbQpdPoO98iDsB37WG87strepfPFf3OfrW6MvsI7NZfno59jY6+odU4M+jsVxNzjB7HXNqiZUH7qydcQlocq8xUkqDECIJ8AQShBBNgLlAJeAp03Fz1gRACDEMGAZQsWL+e2oXNwzZWdiue4NYvGgwaKKl5SgUiofAaJQkZ2Rz7WYW129mcT0tmxtpWSSlZ5OcYSAlI5vUDANpWTmkZ+eQnpVDpiGHrBwjhhxJjlHe9h5YCdBZWWGjE9jqddjZ6HC00eFkq8fV3hp3Rxts9VborQQ2eh22eiuc7PQ42+pxd7TB09EGTydbdFbFNKtfocgnUko+/vME7g7WDG9dxdJyIDsD1rwGEYugShj0+h6cvP815Fr6NUZtHoWTtRNftfkKB2sHi0gtcOo8AWUCYPEgWNANun0JDQdbWhWgddJ7q8lbXE69zMTdE/Fz8uPJpnWYveMcP+29YPY65hjIuX3K3h3pfM8xUsq9QB0hRC1ggRBirZlrYpo/C5gFEBwcXPxqjOST8GWf0NQYTUToV5R1crW0HIVCkQvZOUbiUzKJTc4gLjmDq8mZXE3OID4lk/jUTO0xJZPEm1kYjPf+eHK21eNoq8fBVoejjR47ayscbfW46TRDV2clbjfOkhKycyTZOUYyDTkkpWURcyOHlIxsktMNpGfnPFC3lQBvZzv83O2p4OFAFS9Hqnk7EeDrQiUPB6yU8ax4BPnrdAI7z1zjva61cbazcEvp5BhYPBhiDkKrcRA2Ae7yDGfnZDN261gSMxJZ0GkBPo4+FhJbSHjX0kIulj4NK1+Ga6eh7XtQDMJF9VZ6Pm39KU+teYpXt77KT4//ROsaZfh5X8EayJeACnf87AfcHXRya8wlIYQecAUS7xwgpTwhhLgJ1DVzzRJLQuxF6pyczhG7IALbP2VpOQpFqSQty0BsUgaxSRlcScogNvmf51eTtZ8TUjP/U/pTbyXwcrKljLMtPi521CnngpeTLZ5Otng62uDuaIO7gzXuDja42FvjbKsvUIM005BDpkHzPGcZjGRk53Azy0ByuoEbaVkk3MwiPjmDmKQMLl1PY0/UNX6LuHx7vqONjjrlXWlY0Y1GFd1pXNkDNwebAtOnUBQGRlNLaT93ewY3tfDd5Evhmuc06yb0/xFqdc112Mf7PuZg3EGmtJxCHa86RSyyiLB3hyd/hTWvw45pWhvtHjNAb/nPFGcbZ75u9zWD/hjEqM2jGBY8jZE/xps93xwDeT9QXQhRGbgMDAAG3TVmFfA0sBvoA2yWUkrTnIumsIpKQE0gGrhhxpollnOLx9GALFx7T1OJeQpFAWPIMZKQmsXVZM3QvZqSSZzJ+I017buSlEFKxn8Lx7vaW1PWxY6yrnbU9nWhrKsdPi52lHW1xdtZ2+/hYGNRD6ytXoetPm8xjGlZBs7EpXLySgrHYpI4fCmJuTvO8V1OFEJAbV8XWlYvQ+saZQj2d8dapz6XFMWL1X/HcPxKMl/0D8zz679AObocVrykVXMYslLzoubC8tPLWXpqKc/WfZYuVboUscgiRmcNXb8A1wqweSLcjNMuHGydLK0MP2c/Pgv7jGHrh/F77CeUd++DuX31Hmggm4zbkcA6QAfMlVIeE0J8CIRLKVcBc4AfhBBn0DzHA0zTWwDjhRDZgBF4WUqZAJDbmub/yo8uJ8M3EXJjLbvLDSFUJeYpLIQQwg2YjXZHRwJDgUhgCeCPdiHbT0p5XWgtqr4EugBpwDNSyoNFqTcjO4drN7NITM3i2k0tpCEhNZNrqVn/CnWIT8kkMS3rP15fKwFlnG0p62KHv6cjTat4UtbV7rYx7OtqT1kXO+xtHuHkmfvgYKOnvp8b9f3cuHXzLiM7h8MXb7D3XCI7zyQw+68ovt12Fld7a9oGeNOlni+tanhZ1hhRKNDumnyyLpJavi50b1DOMiKkhJ1fwMYPoGKoZgA65t6g5Ej8ESbtmUSobyijG44uWp2WQgho9To4+8KqUbCwBwz+RSt3Z2FCyoYwockEJu6ZSNPavuwyc545HmSklGuANXfte++O5xlA31zm/QD8YO6aJZ0cgwHd2jeIw4P6KjFPYVm+BP6UUvYRQtgADsBbwCYp5cem2uTjgTfRyjFWN21NgJncI6k2N6SUZGQbuZllIC0zh9RMg2nLJiXDcDuJLSldi7FNTs/mRnoW129qiW7X07LvGXdro7OijLMtXk42+Lk70LCiG97Odni72OJjeizrYqeS1nLBzlpHkyqeNKniySvtqpOSkc3OMwlsOB7HxhNX+S3iMs52eh6v50uvID9C/N2LbztfRYnmp70XuHQ9nQVD61nm7o0xB9aMg/A5ULcP9PzmX1Uq7uR6xnXGbhuLt4M3U1tNfbQrVuSHhoPBzhWWPQvzu2pedqcyllZFv5r9OJl4kl9O/WL2HLMMZEXBcGDldBrnnCE8+BOCnd0sLUdRShFCuACtgGcATKUWs4QQPYAw07AFwFY0A7kHsFBqbYj2CCHchBC+Usor9zpHZGwKwZM2kp6lJZXdJ3/tNjY6K1zsrXG11+PmYIOvqx21fF1wc7DGw9Hm9uZpevRytsXZVq+MtgLC2c6aTnV96VTXl+wcIzvPJLDqcAyrDseweP9Fqng50j+kAv2CK+DuaPn4QkXpIDXTwNebzxBaxZNW1b2KXkB2Bix/Hk6shuajod0H90xCyzHmMP6v8SSmJ/JDlx9wsyul3/O1usKgpfDzQK3V9tOrwLmspVUxofEEzt44y1GOmjVeGchFRNL1BKof+Zzj1nVp1OV5S8tRlG6qAPHAPCFEA+AAMBrwuWX0SimvCCFu1SvKrSxjeeCeBrKDrY72tb2xt9bjYKPTKjiYHp1sdTjZWuNoq8PZzhoXOz0u9tbYWZcyT0sxxlpnRVhNb8JqejOpp4E1R2JZvO8Ck9ee5LMNp+jRoBxDW1Smlq+LpaUqSjjfb4/i2s0sxncOKPqL4cwUzciL/gs6TobQl+87/Lu/v2NXzC7eD32f2p6lsvfZP1RtA08ugx/7aZ7kZ363uJFsrbPms7DPWMhCs8YrA7mIOPHTeEJkCondPlGJeQpLoweCgFFSyr1CiC/RwinuhVllGe+uWT65V/2C0KqwMA42evo08qNPIz8iY1P4YU80vx64zC8HLtGyuhcvta5KaFVP5clXFDgJqZnM/iuKLvXK0qBCEXtj0xLhxz4QcwiemAUN+t93+O6Y3Xx7+Fu6V+1O7+q9i0hkMce/hVbhYlFvrVby07+Ds2VL3XnZm38XQllqRcC54/sJjvuVcK8eVK3fzNJyFIpLwCVTjXLQul8GAVeFEL4Apse4O8Y/sCyjlHKWlDJYShlcpozlY84UBU/Nss5M6lmP3RPa8kanmpy4ksKg2XvpNXMXWyPjkHdnRyoUD8HXm8+QYTDyWoeaRXvi1HjNoIs9Cv0XPdA4jk+LZ/xf46niWoW3m7ytLhbvpFKo5klOugQ/9NQuPB4RlIFcyEijkZsrX+emsKfGgI8tLUehQEoZC1wUQtz61mkHHOefco2YHleanq8ChgiNpkDS/eKPFSUfNwcbXg6rxo432zCpZ13ikjN5Zt5+es3cxa6zCZaWVyoQQswVQsQJIY7ese8DIcRlIcQh0/bI1he7mJjGj3vP0y+4AlXLFGG5sJRYLW722lkYtAQC7v8nvBV3nG5I57Owz0pOp7yCpFIzGLhY+5v+8ITWkvsRQBnIhUzEhh+pm3mIkwGjcC/ja2k5CsUtRgE/CiH+BgKBj4CPgfZCiNNAe9PPoFWbiQLOAN8D9w/EU5Qa7Kx1PNm0ElteD+OjJ+oRm5TBoO/38tScvRyPSba0vJLOfKBTLvunSSkDTdsjWynq8w2nsBKCMY9VL7qTJl/RjOOkS1poQNU2D5zy/ZHv2Re7jwmNJ1DVrWoRiHxEqdIa+v8AV49qcd3Z6ZZW9EBUDHIhkpF+k7J7JhJtVZFGvV+ztByF4jZSykNAcC6H2uUyVgIjCl2U4pHFRm/FoCYV6RVUnkV7zvP1ljM8Pv0v+gT5Ma5jTbxd7CwtscQhpdwuhPC3tI7C4MSVZFYcuszwVlXxKarXTvIVWNBV8yA/tRwqNn3glANXDzDz8Ewer/I4Pav1LAKRjzg1OsIT38Gvz8OyodDvB9AVXzNUeZALkUNL/o9y8iopYZPQW6uySAqFomRjZ63j+ZZV2DauDcNaVmHFocuEfbqVmVvPkmUwWlpeaWGkEOJvUwiGe24DhBDDhBDhQojwUwmnWHV2FRmGjKLWeU8+WReJs62eF1tXKZoTpsZpMccpsZrn2AzjOCkzifF/jae8U3nebfquijs2l3p9oMsnELkG/niV/3R1KkYoA7mQiI+Jpv652UQ4NKdeqx6WlqNQKBRFhqu9NRO61GLDq61pVtWLKX+epNOX29l1RsUnFzIzgapoYVNXgM9yG3RnQq2V3oq3d7xN21/aMnnvZE5dP1WUev/D/uhENp+M48Wwqrg5FIFj6eY1retb8mWt85sZxrGUkol7JpKQlsDUVlNxtHYsfJ0licYvQKtxcHAhbC2+uVnKQC4kope8gZ4cvPt8YmkpCoVCYRH8vRyZ/XQw854JIccoGTR7L68uOcS11ExLSyuRSCmvSilzpJRGtHyBxg+aU82tGnM6zKFF+Rb8cuoXeq/qzeA/BrP89HLSstMKX/QdSCmZsvYk3s62PNuscuGfMP2GVlkhMUpLIqtkXpWplWdXsi56HSMbjqSuV91CFllCafM2BA6GbR/DwVwbLlscZSAXAqcObiUkaR0Hyg2ifJU6lpajUCgUFqVNgDfrxrRiVNtq/P53DI99vo3lBy+psnAFzK0yjSaeAPNahjX2bczUVlPZ1HcT44LHkZqdyvu73qftL22ZuHsixxKOFcn/aktkHOHnrzOqXXXsbQq5cVDWTfipH8Sd0Eq5VWlt1rSLyReZvHcyIWVDeKbOM4WrsSQjBHT7Eqq0gd/HwNnNllb0H5SBXMBIoxHjmvEk4EbdAf+ztByFQqEoFthZ63itQ03+eKUl/l6OjF16mKHz9xObVHxiXx8lhBA/A7uBmkKIS0KI54CpQogjpuo0bYBX87Kmu507Q+oMYUWPFSzsvJB2Fdux6uwqBvwxgL6r+/LzyZ9Jziqc6iRGo2Tqn5FU8nRgQEiFB094GAyZsHgwXNoPfeZA9fbmTTMamLBjAjqh46MWH6GzUt0/HwqdNfRbAF41YenT2sVKMUIZyAXMgbVzCDCcIKreqzi7elhajkKhUBQravg4s+zFZrzXtTa7o67RfpryJucHKeVAKaWvlNJaSuknpZwjpXxKSllPSllfStk9v/XKhRA09G7I/7X4Pzb328w7Td7BSljx0d6PaLu0LW/99RbhseEF+j9b/XcMJ2NTGNu+Bta6QjRNjDmwfBhEbYHu06G2+TlCc47M4XD8Yd5p+g5lHS3bNrnEYOcKg5eCtb3m0b9ZfPIUlIFcgGSkpeK3/2PO6qrQqMdIS8tRKBSKYonOSjC0RWX+HN2Kmj7OjF16mBcXHSDxZpalpSnuwtnGmf4B/VnabSmLuy6mZ7WebLm4hWfXPUv3Fd2Zc2QO8WnxD3WOLIORz9afopavC93qlysg5bkgJfzxGhxfAR0mQcMnzZ56/Npxvj38LZ39O9OlyiPbf6V44uoHA37WqoksHqx5+IsBykAuQCKWTKQsCWS0+z90+uJb20+hUCiKA/5ejiwZHsqEzgFsORlPxy+2szUy7sETFRahjmcd3mn6Dpv7bWZS80l42HnwxcEvaL+sPSM3jWTT+U1k52Tned0l4Re5kJjGGx1rYmVViOXStk2BA/Og+RhoNsrsaZk5mbz111t42HnwdtO3C09facavEfScCRf3wO9ji0X5N7MMZCFEJyFEpBDijBBifC7HbYUQS0zH994qXi6EaC+EOGCKiToghGh7x5ytpjVvtcT0LqhfyhLEx0TTIHoeEY4tqNNMXV0qFAqFOeisBMNbV2XlyOZ4ONjwzLz9fLj6OJmGHEtLU9wDe709Par1YEHnBazuuZpn6jzD8WvHGbN1DI8te4yp+6cSmRhp1lrpWTlM33SaEH93wmqWKTzR4fNg62QIfBIe+yBPU7+O+JqzSWf5X/P/4WrrWijyFEDdXtDqDTi0CPbMtLSaBxvIQggdMAPoDNQGBgohat817DngupSyGjANmGLanwB0k1LWA54G7q7lMfiOlpiPtNvg3NIJ6DHg3XuqpaUoFArFI0ctXxdWjmzO06GVmLvzHE/M2EVUfKqlZSkegL+rP2MajWF9n/XMaDeDIO8gfj75M31W96Hf6n4sOr6IxIzEe86fvyuauJRM3ugUUHjNNiLXwh9joXoHrXJCHs4TERfBgmML6FujLy3KtygcfYp/CJsAAV1h/dsQtdWiUszxIDcGzkgpo6SUWcBi4O6o9h7AAtPzZUA7IYSQUkZIKWNM+48BdkII24IQXpw4c3gnwdfXcrBsP1XWTaFQKPKJnbWO//Woy+whwcQkpdN1+g5WRFy2tCyFGeit9LTya8W0NtPY3Hcz4xuPRwjBlP1TaLe0HaM2jWJ99Hoyc/6JL01Kz2bm1jO0qVmGEP9CSmq/FA6/PAu+gdB3fp5aG6dlp/H2jrcp51SO14JfKxx9in9jZQVPfAteNbT/2/XzlpNixpjywMU7fr5k2pfrGCmlAUgCPO8a0xuIkFLeGX09zxRe8a64x6XjnS0x4+MfLhGgMJBGI5l/TCBJOFNrwCRLy1EoFIpHnsdq+7B2dEvqlnNlzJJDTFj+NxnZKuTiUcHdzp3BtQazpOsSlndfzlO1n+LYtWO8tu012ixtwwe7PiA8Npzvtp0mOcPA6x1rFo6QxHPwU39w9oFBS8Embx3vvor4iospF5nYfKLqlleU2DrDgJ+0iiNLBkNW0TasuYU5BnJuhuvd0dP3HSOEqIMWdjH8juODTaEXLU3bU7md/M6WmGXKFGJ8Uj45vGkxdbIOc6rWSFzdvSwtR6FQKEoEvq72/PRCE14Kq8rP+y7S65tdnL9209KyFHmkunt1xgaPZUOfDXz32He09mvNmnNreHbdsyy8NJzadbaBbSGU+UtLhB/7gsyBwb+CU97sh/DYcH488SODAgYRUjakYLUpHoxnVeg9G2KPauExFkjaM8dAvgTcWbXbD4i51xghhB5wBRJNP/sBvwFDpJRnb02QUl42PaYAP2FGS8ziRnZWJp67JnLeqgKNeuWpHrtCoVAoHoBeZ8WbnQKY90wIl29oIRcbjl+1tCxFPtBZ6WhWvhmTW05ma7+tBDuMwJjpS4xcz4DfB9BtRTemR0zn9PXTD28sGzJhyZNw47xWPsyrWp6mp2Wn8d6u9/Bz8mN00OiH06LIPzU6QNh4OPwz7J9d5Kc3x0DeD1QXQlQWQtgAA4BVd41ZhZaEB9AH2CyllEIIN+APYIKUcuetwUIIvRDCy/TcGuiKmS0xixMHfv2MCjKGGy3eQ29tY2k5CoVCUSJpE+DN76Na4O/pyAsLw/lk3UlyjJYvA6XIH9dSYMehivTwfZdt/bfxfuj7+Dj4MPvIbHqt6kX3Fd2ZdmAah+IOYZTGvC0uJaweDed3Qo9voFJonvV9fehrLqZc5MPmH+Jg7ZDn+YoCpNUbUL0j/DkBLh0o0lM/MFpdSmkQQowE1gE6YK6U8pgQ4kMgXEq5CpgD/CCEOIPmOR5gmj4SqAa8K4R417SvA3ATWGcyjnXARuD7Avy9Cp2kxHgCImdwxLYh9cP6WFqOIp8YpZG4tDhib8YSmxZLYnoiNzJvkJqdSlp2GtnGbIzSiJWwQm+lx05nh5ONE642rnjae+Lt4E05p3KUdSir2o4qFIVIBQ8HfnkxlPdXHmPGlrMcuZzMVwMCcXNQzolHjS83nUYIweh21XG1taNPjT70qdGHhPQENp3fxKYLm1h4bCFzj87Fw86DVn6tCPMLo2m5pg+OBf7rU83jGPYW1O+bZ22H4w+z6Pgi+tfsr0IrigO3kvZmtYZfnobh28GhaLoUi0epvWdwcLAMDw+3tAwA9sx8kcaxiznX50+q1mtqaTkKM0jKTOJYwjGOJx7nVOIpziSd4ULyhX9lVd/CQe+Avd4eG50NVsIKKSUGo4F0Qzo3DTf/49XQW+nxd/Gnmls1annWoq5nXeqVqYe93v6hNAshDkgpgx9qEQtQnN6ripLHT3sv8P6qo/i62jNrSCMCyrpYWpJ6r5rJ6aspdPxiO8+1qMzbj99dMfYfkrOS2XFpB1submHn5Z2kZKegt9IT7BNMi/ItCCkbQhXXKtjp7f6ZdGyFZkTV6we9ZuWpnBtAVk4WfVf3Jd2Qzm89flOJecWJywdhbkeo3FpLuLTKf587c9+rqt1bPrgcdYKg2F8Id+9MY2UcF1sSMxLZd2Ufe2P3cvDqQaKSom4fK+dYjmru1Qj1DaWic0XKOZXDx9EHTztP3Gzd7usNNkojqdmpJKQnEJcWx6WUS1xIuUDUjSj+jv+bP6P/BEAv9NT2qk1T36Y0L9ecBmUaFBsvsxAiGkgBcgCDlDJYCOEBLAH8gWign5TyuqnCzJdAFyANeEZKedASuhUKgEFNKlKzrDMvLTpAr2928WnfBnSp52tpWQoz+HzDKeytdbwUdv+4YBcbF7pU6UKXKl3INmZzKO4Q2y9t569Lf/Fp+KcACAQ13GvQ0q8lLW19qP/bGPQVmkD36Xk2jgFm/T2LqKQoZj42UxnHxY3yQdBpstYqfOc0aFn4ZfeUBzkfHPisJ7WSd5E6bC/e5StbWo7ChJSSU9dPsfnCZrZd2sbxa8eRSJysnWjo3ZCG3g2pV6YetT1r42JTeB6n6xnXOZJwhIi4CPbF7uNowlGM0oi7rTutK7Smk38nGvs2xtrK+oFrFZZXymQgB0spE+7YNxVIlFJ+bOqY6S6lfFMI0QUYhWYgNwG+lFI2ud/6xeW9qijZxCVnMHzRASIu3OCVttUY81iNwm1VfB+UB/nB/H3pBt2/3skr7aoztn2NfK8TkxrD0YSjnLlxhv2x+4mIO0iONOJslDTxa0VoxTBCfUPxc/Yzu/nIqeun6L+6P50qd2Jyy8n51qYoRKSEZUPh+Ap4ejX4569xi/IgFxInwzfRKGULuys+T6gyjosFp6+fZs25NayLXsfFlIsIBPXL1GdE4AialWtGLc9a6K2K7qXubudOK79WtPJrBWi3CnfF7GLLhS1sPL+RFWdW4GnnSdcqXelVvRdV3KoUmbYH0AMIMz1fAGwF3jTtXyi1q+k9Qgg3IYSvlPKKRVQqFCa8XexYPKwp7644ylebz3AiNoVp/QNxslVfbcWRT9efws3BmhdaPtx3ZzmncpRzKkcHOkB2OsnzOrE39Tw763dn540TbNzzFwDlncrTxLcJjcs2JtgnGB9Hn1zX1vPcZAAAIABJREFUy8rJ4p0d7+Bs48wbIW88lDZFISIEdP8KrhyGX5+HF3eAY+GV11WfInlAGo3Ide+QgBv1+7374AmKQiMpM4nfo35nxZkVnEw8iU7oaOLbhKF1h9KmQhs87e/uU2M5XGxc6OTfiU7+ncjMyWTHpR2sjlrNjyd+ZMHxBTQu25hBAYMIqxBWlCEYElgvhJDAd1LKWYDPLaNXSnlFCOFtGnuvZkH/MpCFEMOAYQAVK1YsZPkKhYatXseU3vWp5evCpD9O0PubXcx+OpgKHqr6QHFiT9Q1tp+K560uATjbPfjumVlICatG4RJziPb9f6R9ra5IKYlOjmbPlT3sidnDhvMbWH56OQAVnSvSyKcRQT5BBHkHUcG5AkIIph2YxonEE3zZ5kvc7dwLRpuicLB11joizn4MfnvxoeOR74cykPNAxIYfCco+zt4679HE2c3SckodUkoOxx9mSeQS1kevJ8uYRW3P2oxvPJ7OlTvjYVc0ma0Pg63OlnaV2tGuUjuupV9jxZkVLI1cypitY6joXJFn6j5Dj6o9sNEVemZ+cylljMkI3iCEOHmfseY0C8JkZM8C7bZtwchUKB6MEIJnm1emurczL/94gO5f7+DbJxvRpErxuVAuzUgp+XRdJD4utgwJ9S+4hXdMgyO/QNt3oFZXQHstVHatTGXXygwMGEiOMYfI65Hsj91P+NVwNl/czG9nfgPAw86DAI8AdsXsYnCtwbSt2LbgtCkKD9/60PH/YM3rsPtraP5KoZxGxSCbSXZWJrGTAzEKHeUnHFR1j4uQbGM266LXsej4Io5dO4aTtRPdqnajd/Xe1PQopBalRYjBaGDThU3MPzqfo9eO4uPgw7D6w3ii+hPY6GwKPa5RCPEBkAq8AISZvMe+wFYpZU0hxHem5z+bxkfeGnevNVUMssJSnEu4yXML9nMxMY3/61mPfiEVHjypAFAxyPdmy8k4np2/n4k96/JU00oFs+jJNbB4ENR5AvrMNTspzyiNRN2IIiI+gkNxhzgcfxgvey9mtZ9VFI4JRUEhJSx9CiLXwnProXwjs6eqGOQC5uBv02giYzjU4jsqKeO4SEg3pPPrqV9ZeHwhV25ewd/Fn3eavEO3qt1KVPF2vZWejv4d6VCpA7tjdjPz8Ewm7pnIvKPzCuV8QghHwEpKmWJ63gH4kH8a/nxselxpmrIKGCmEWIyWpJek4o8VxZXKXo789nJzRv50kDd+/Zsz8am82SkAnYWS90o7RqPk0/WRVPRwoH9wAV2sXD0Gy1+AcoHQ85s8VaywElZUc69GNfdq9K2R9zrJimKCENDtK7jcEpY9p9VHtivY5HtlIJtBSlIi1U/M4JhNfRq06WdpOSWetOw0fj75MwuPLyQxI5Eg7yDebvI2Lf1aYiUKJ9aoOCCEoFn5ZoSWC+Wvy3/xzaFvCutUPsBvpuxuPfCTlPJPIcR+YKkQ4jngAnDr22MNWgWLM2hl3p4tLGEKRUHgam/NvGdC+PD348zaHsXZuFS+HNhQJe9ZgLVHYzkWk8zn/Rpgoy+Az++b1+DnAWDjBAN+AuuHqzWveIRx8IDes2F+F1gzDnp9V6DLq08LMzj6y0RCSSah0yREIQWDKyDDkMGSyCXMOTKH65nXaV6uOcPqDyPIJ8jS0ooUIQSt/FrRsnxLlrCkwNeXUkYBDXLZfw1ol8t+CYwocCEKRSGi11nxYY+6VPd24oPVx+kzU0ve83MvOXefijuGHCOfbYikurcTPQLLP/yCOdlaI5CUqzB0LbiUe/g1FY82lUKh9ZuwdTJUawf1C86JqQzkBxAfE03gxUUccGlLo6DWlpZTIskx5rDy7EpmHJpBXFocob6hjGg4ggZl/mPDlSrMrd+pUCjuzVOh/lTydGTEjwfpOWMX3w9pRMOKqlJBUbA84jJR8Tf59slGBRPi8ucEiP4LnpiVp5hTRQmn5esQtRV+Hwt+IeBRMCV4lTv0AZz75W105FD2iY8sLaVEsvPyTvqs7sP7u96nrENZ5nacy6wOs0q9caxQKAqOVjXK8NuIZjjY6Og/aw+rD8dYWlKJJ9OQw5cbT1Pfz5WOdXKvP5wnDiyA/d9Ds1HQoP/Dr6coOej0ptbiVrB8GOQYCmRZZSDfh/MnDtAo8Q8O+vShfJValpZToohOimbEphG8uPFFMgwZfNb6MxZ1WURI2RBLS1MoFCWQat7OrBjRnAZ+roz6OYIvN57mUari9KixZP9FLt9I5/UONR/+btjFfVqL4apt4bH/FYxARcnCrSJ0/Rwu7YO/Pi2QJVWIxX24vvptPLCjZl/1hiwo0rLT+O7v71h4fCF2Ojtea/Qag2oNUuV1FApFoePhaMOi55swYfkRpm08RVRCKlN618fOusga9JQK0rNymL75DI0re9Cy+kN2Oku+AkueBFc/rZxb0TVTUjxq1OsDp9fDtinaxVSFxg+1nDKQ78HxPX8SmLab3VVGEFrG19JyHnmklGy+uJnJeydzNe0qPar2YEyjMXjZF16bSIVCobgbW72Oz/o2oGoZJz5ZF8nFxDRmDQnGy8nW0tJKDAt2RxOfksk3g4MezntsyISlQyAzFZ5aAfYqdlzxALp8Aud3a6EWL+4AW6d8L6VCLHJBGo3oNr5PHB4E9plgaTmPPFdvXmX0ltGM2TIGF1sXFnZeyKQWk5RxrFAoLIIQghFtqvHN4CCOX0mm54ydRMamWFpWiSA5I5tvt52ldY0yhPg/ZHfTtW9qt8x7fgM+tQtGoKJkY+eqlXu7Hg3rHs5+M8tAFkJ0EkJECiHOCCHG53LcVgixxHR8rxDC37S/vRDigBDiiOmx7R1zGpn2nxFCfCWKUcr+oQ0/UNNwkuh6o7F3dLa0nEcWozSyNHIpPVb2YHfMbsY2GsuSrkto6N3Q0tIUCoWCLvV8WTo8lCyDkd4zd7ElMs7Skh55Zv91jhtp2bze4SG7nB5cCAfmQfMxUKdnwYhTlA4qNYMWY7TX0Mk/8r3MAw1kIYQOmAF0BmoDA4UQd1/KPQdcl1JWA6YBU0z7E4BuUsp6aJ25frhjzkxgGFDdtHXK929RgGRnZeK152OirSoQ1P1lS8t5ZLmYcpEX1r/AxD0TqetVl+U9lvNs3WextrK2tDSFQqG4TX0/N1aObE5FDweem7+fuTvOqeS9fJJ4M4u5O87RuW5Z6vm55n+hywfgj9ehShi0e6+g5ClKE2FvQdl6sOoVSM3fha85HuTGwBkpZZSUMgtYDPS4a0wPYIHp+TKgnRBCSCkjpJS36ukcA+xM3mZfwEVKudvUhGAhUCwuEQ+unE4FGcON0LfQq5bSeUZKydLIpfRe1Zvj147zQegHfN/+eyo4F1CLUYVCoShgfF3tWfZSKO1r+/Dh78d567ejZOcYLS3rkeO7bWe5mWVgbPsa+V/kZgIsGQJO3tBbJeUp8oneBnp9D5kpmpGcj4tec5L0ygMX7/j5EtDkXmOklAYhRBLgieZBvkVvIEJKmSmEKG9a5841c22zI4QYhuZppmLFimbIzT9pqUlUPTadE9a1adBuQKGeqyQSlxbHe7veY+flnYT6hvK/Zv/D10klOJZKsm5C9A6QRjDmgMwBo9H0aLhj/53H73w0ah9o0vjvfcg7jknt53siQAitNqaw0r5ohU57tNKBlTXobEBnbdpsQW8Lejuw0pvmiX/mWulNc6xN40xj9XbaOMUjjYONnpmDG/HZhkhmbDlLdMJNvhkchLujcpSYQ1xyBgt2R9MzsDzVffIZmmjMgWVD4WY8DP0THD0LVKOilOFdCx57H9a9BRGLIOipPE03x0DO7ZP/7m+l+44RQtRBC7vokIc1tZ1SzgJmAQQHBxfqfa+/l02hKTdIeOx71VI6j2w6v4n3d79PpiGTt5q8xYCaA1QnuNJMwimY/7ilVRQRAqwdwMZRy5i2dQZbF7B307Lu7T3AwRMcy4BTGXDyAWdfbZ96jxQrrKwE4zoGULWME+N/PULPb3Yy5+lgqnkXv1wUIcRcoCsQJ6Wsa9rnASwB/IFooJ+U8npR6Pl6yxkMOZIxj1XP/yJb/g/ObYPu06F8UMGJU5RemrwEkWvhz/FQuSW4+5s91RwD+RJw5/1xP+DuNkS3xlwSQugBVyARQAjhB/wGDJFSnr1jvN8D1ixSrsdfoc65uUQ4NqNhkw4PnqAAtLrGU/dP5dfTv1LbszYft/yYyq4F0+ZR8QjjWQ2GzDJ5ba3+7bm99fzO/be9vPo7vL1Wd3l/rdC8wnd4dm95ie/mlnf5trf5lrfaoHmyjQYwZkNONuRkaY+GTMjJhOz0u+bcOdegjTdkmrZ0bXxWGmSlaltmCmQkQcJpSL8OaYnaue5GZwMu5cC1gra5V9I+vD2qgEdV5T2zIL2C/Kjk6cjwHw7Qc8YuvhoYSNuAAugGV7DMB75GC1G8xXhgk5TyY1NC/XjgzcIWcjExjZ/3XaBvcAUqeTrmb5GTa+CvzyBoiLYpFAWBlZVWBeWbZrDiZXj6d7OnmmMg7weqCyEqA5eBAcCgu8asQkvC2w30ATZLKaUQwg34A5ggpdx5a7CU8ooQIkUI0RTYCwwBpputuhCIXPYBIWTg0W2SJWU8UkQmRvLG9jc4l3SOoXWHMjJwJNY6lYSnQPOiVmltaRXFAykhM1mLrUyNg9SrkBILKTGQdBmSLkHUVki5wr9upNl7QJmapq0W+NTRNoeHLJ2lMItGldxZNbI5LywM57kF4bzZKYDhraoUmztjUsrttypG3UEPIMz0fAGwlSIwkKdvPo0QglfaVcvfAolR8NuL4BsInT8pWHEKhVtF6PwxrBwBe781e9oDDWRTTPFIYB2gA+ZKKY8JIT4EwqWUq4A5wA9CiDNonuNbAbwjgWrAu0KId037Okgp44CX0K6A7YG1ps0iXDkfSVDsMg64d6ZxrUaWkvHIIKVk2ellfLz3Y1xtXZnVYRZNfZtaWpZCUTwRQqvNaecKnlXvPc6QCTcuwLWzkHhWC1OJPwXHV0L6/H/GuVYA3wZQriGUb6TdirZ7iIoBintSzs2eZS82Y9yyw3y89iTHY5KZ0rs+9jbFNnHMR0p5BW47orxzG1SQuT1R8an8evAyQ0Ir4etqn/cFstO1ZiBCQL+FYG33UHoUilwJHAwnfodN5ndGNquTnpRyDbDmrn3v3fE8A+iby7xJQK4uWSllOFDXbKWFyKXf3scDQcXeEy0tpdiTlp3GB7s/YO25tTQr14yPWnyEp726FaxQPDR6W/Cqrm13IqXmeb56FGKPwJXD2nby1q1CoSWjVGgMlZprNUBd/f6zvCJ/2NvomD6wIbV8Xfh0fSRn41P57qlG+Lk7WFpavinI3J5pG09jo7Pi5bB8eo/XvK69rgct1cKMFIrCQAjo9iV8c3eNiXtT6ltNR58IJ+j6n+wvO4CmFfL5Bi8lnL1xlle3vsr55POMajiK5+s9j5VQyYwKRaEiBDj7aFu1dv/sT78BMQfh4n6t29jR3+DAfO2Yuz9UbqXVka0cpuKZH5Jbnfdq+Toz+udDdJu+gxmDgmhWrdh1A70qhPA1eY99gULtfHLiSjKrD8fwclhVyjjno1V3xCJta/ka1OhY8AIVijtx9tGM5PHmVRUu9Qby9dXv4okdAX0/sLSUYs2f5/7kvV3v4aB34Pv239PYt7GlJSkeElMToHDgspSyqynPYDHgARwEnpJSZgkhbNESgRoB14D+UspoC8lW3MLeDaq21TbQkgmvHoPzO+HcX3BspdZJCqGFY1RvD9U7as9VlZ580TbAh5UjmzPshwM8OWcv4zsH8ELL4hOXzD/5QB+bHlcW5sk+33AKZzs9w1vdJ3ToXsQehT9e0y7k2rxd8OLuIicnh8TERLKzc0mYVZRIrK2t8fDwQKe7IySq9t1tPO5NqTaQT+7bQMO0Xeyu/BKhXmUtLadYYjAa+PzA5/xw/AcCywTyWdhneDvkGtamePQYDZwAXEw/TwGmSSkXCyG+ReuQOZM7OmUKIQaYxvW3hGDFfbDSgW99bWv6EuQYICYCzm6GMxth+yewbYpWZq5GJwjoqiVS6vPh+SvFVCnjxIoRzRn3y2E+WnOSQxdvMKV3/SLXIYT4GS0hz0sIcQl4H80wXiqEeA64QC6hjwXF4Ys32HD8KmPb18DVIY/J2RnJWtyxnRv0nlMkzUASExOxs7PDy8urOF3QKAoJKSWpqakkJiZSpkyZfK1Rag1kaTRi3Pg/EnCjfu/xlpZTLEnMSGTctnHsi93HwICBjAsep6pUlBBM5RcfB/4PGCu0b4y2/FOhZgHwAZqB3MP0HLROmV+bOmWqfrzFGZ0eKoRoW9ibWrm50xvg1Fo4uhwOLtDqNdfoBHWe0MI3lLFsFk62er4ZHMSs7VFMXRfJydidD55UwEgpB97jULt77C9QPl0fibuDNUNb5LGsp5SwejRcj4anV2sd84qA7OxsZRyXIoQQODk5kZKSku81Sq2B/PfWZTTIOsLegPE0cXaztJxiR2RiJK9sfoWE9AQmNZ9Ej2rm35ZQPBJ8AbwB3OqA4AnckFIaTD/f2d3SnE6ZiuKOgwc06K9thkw4t12rkHHydziyFGxdoVY3qN8X/FuqFr8PQAjB8NZVaVDBjZE/RVhaTpGyN+oaf51O4K0uATjZ5tGM2D8bji2Hxz4A/+aFIe+eKOO4dPGw/+9SGYhmzMnBaedHxAgfGj7xqqXlFDs2nN/AU2ufwiANLOi8QBnHJQwhxK3uWwfu3J3LUGnGsTvXHSaECBdChMfHxxeAUkWhobfVYpJ7fA2vn4bBv0LA45rBvLAHTKsLGz/Qyswp7kvTKp6seaWFpWUUGVJKPlt/Cm9nW55q6p+3yTERWtvf6h2g2ehC0adQFBSl0kA+uHYOVXPOEdNwLDa2qubiLaSUfHv4W8ZuHUt19+osfnwxdb2KRSU+RcHSHOguhIhGS8pri+ZRdjN1woR/d7e83U3z7k6ZdyKlnCWlDJZSBuc35kthAXTWUP0xeGImjDsNfeZB2Xqw8yuYEQJzOsDBHyDrpqWVFlu8XUrP98iOMwnsi05kRJtqeasHnX4Dlj4Njt7wxHelMlFUp9MRGBhIgwYNCAoKYteuXQDExMTQp0+fItfj5ORUqOvPnz+fkSNHFvi60dHR1K1b+LZJqXuFZmdlUvbA50RZ+RP0+AuWllNsyDBk8OZfbzLj0Ay6V+3O3I5zKeOgjJySiJRygpTST0rpj9bUZ7OUcjCwBa0TJvw7A/5WZjzc0SmzCCUrigpre6jbCwYvhbEnoP1ErV32qpHwaU34faxWKUNRKpFS8um6SMq72TOgcYW8TNReQ8mXoe+8UtsN0t7enkOHDnH48GEmT57MhAkTAChXrhzLli2zsDrF3ZQ6A/ngyun4ySskN5+AlU7F2AEkpCfw3PrnWHtuLaODRjOp+SRsdSpZpxTyJlrC3hm0GOM5pv1zAE/T/rGAymotDTj7QPNXYMQ+ePZPLQQjYhHMbAZzO8HRXyFHlcwqTWw8EcfhS0m80q4atvo8fH/umwUnVmtxxxVUiVCA5ORk3N3dgX97RHNycnj99depV68e9evXZ/r06QAcOHCA1q1b06hRIzp27MiVK1cACAsLY8yYMTRr1oy6deuyb98+ALZt20ZgYCCBgYE0bNjwvslqUkrGjRtH3bp1qVevHkuWLAFg69athIWF0adPHwICAhg8eDC3fCNr1qwhICCAFi1a8Morr9C1a9dc146JiaFTp05Ur16dN9544/b+9evXExoaSlBQEH379iU1NRWADz/8kJCQEOrWrcuwYcNun+/AgQM0aNCA0NBQZsyYkb8/eh4pVUl6GWmpVD42g5PWtWnQpp+l5RQLzt44y4hNI7iWfo1pYdN4rNJjlpakKEKklFuBrabnUcB/vr3u1SlTUUoQAiqFalunyZqRHD4Hlg0FZ18IeR4aPauakZRwjEbJZ+sj8fd0oFdQHjo1Xj4I69/RqqWEFvzt9vzwv9XHOB6TXKBr1i7nwvvd6tx3THp6OoGBgWRkZHDlyhU2b978nzGzZs3i3LlzREREoNfrb9duHjVqFCtXrqRMmTIsWbKEt99+m7lz5wJw8+ZNdu3axfbt2xk6dChHjx7l008/ZcaMGTRv3pzU1FTs7O4dBrR8+fLbnu2EhARCQkJo1aoVABERERw7doxy5crRvHlzdu7cSXBwMMOHD2f79u1UrlyZgQPvVVAFDh06REREBLa2ttSsWZNRo0Zhb2/PpEmT2LhxI46OjkyZMoXPP/+c9957j5EjR/Lee1qj5qeeeorff/+dbt268eyzzzJ9+nRat27NuHHjHvj/KAhKlYF86NepNCWRhLbfIEph/NPd7LuyjzFbxmCrt2Vep3kq3lihUNwfBw/Nqxw6QisZt/db2DxRq7HcYKBmAHmpjqQlkTVHr3AyNoUv+gdirTPz+zMjCZY9q8Ud95ypXWyVYm6FWADs3r2bIUOGcPTo0X+N2bhxIy+++CJ6vWaeeXh4cPToUY4ePUr79u0Bzcvs6+t7e84tA7VVq1YkJydz48YNmjdvztixYxk8eDC9evXCz+/eFzU7duxg4MCB6HQ6fHx8aN26Nfv378fFxYXGjRvfnhsYGEh0dDROTk5UqVKFypUr3z7/rFmzcl27Xbt2uLq6AlC7dm3Onz/PjRs3OH78OM2ba1VMsrKyCA0NBWDLli1MnTqVtLQ0EhMTqVOnDq1ateLGjRu0bt0a0AzntWvXmvtnzzelxkBOvnGNgLNz+NsuhPqhnS0tx+KsPrua93a9h7+LPzPazaCcUzlLS1IoFI8KVjqo2Unb4k7Anm/g0E9aq+uAx6H5GK3+sqJEYMgx8vmGU9TwcaJbAzO/K27VO75xEZ5dW6zijh/k6S0KQkNDSUhI4O6KP1LK/5Qnk1JSp04ddu/enetad48XQjB+/Hgef/xx1qxZQ9OmTdm4cSMBAQG5zr9fSomt7T/hljqdDoPBcN/x5s5v3749P//887/GZmRk8PLLLxMeHk6FChX44IMPyMjIyPVvUhSUGjfq8WX/hxupOHT+wNJSLIqUktlHZvPWjrcI8g5iQecFyjhWKBT5x7sWdJ8Orx6DVuO0VtdzHoN5XeD0Rs1QUjzSrDwUQ1T8Tca2r4HOykxD5cA8OPYbtHsXKjYpXIGPICdPniQnJwdPz3+HJnXo0IFvv/0Wg0ErSZ+YmEjNmjWJj4+/bSBnZ2dz7Ng/ybK3YoZ37NiBq6srrq6unD17lnr16vHmm28SHBzMyZMn76mlVatWLFmyhJycHOLj49m+fTuNG987VjwgIICoqCiio6P/dX5zadq0KTt37uTMmTMApKWlcerUKTIyMgDw8vIiNTX1duKim5sbrq6u7NixA4Aff/wxT+fLL6XCg3zt6iXqX1zEQefWBDUoPfUq7ybHmMPkfZNZErmELpW7MLH5RGx0NpaWpVAoSgJOZaDt29B8NBxcCLu/hh97g28gtHodaj5eKkt7Pepk5xj5ctNp6pRzoWOdsuZNij0Ka8dD1Xaq3vEd3IpBBs1ZtWDBAnR3FQt4/vnnOXXqFPXr18fa2poXXniBkSNHsmzZMl555RWSkpIwGAyMGTOGOnU0T7i7uzvNmjUjOTn5dlzyF198wZYtW9DpdNSuXZvOne995/yJJ55g9+7dNGjQACEEU6dOpWzZsvc0qu3t7fnmm2/o1KkTXl5e9zWmc6NMmTLMnz+fgQMHkpmZCcCkSZOoUaMGL7zwAvXq1cPf35+QkH/uQs2bN4+hQ4fi4OBAx44d83S+/CIepWpNwcHBMjw8PM/z9nwzjJCrS7k8eCsVawQWgrLiT2ZOJuO3j2fjhY08W+dZxjQag5VQX1bFHSHEASllsKV15JX8vlcVJQhDFvy9GHZMg8Qo8K4DrcdBrR4l0lAuqe/Vn/Ze4K3fjjD3mWDaBvg8eMGsmzArDDKS4cUd2oVTMSAmJoZy5Ure3dKwsDA+/fRTgoOL9qWXmpqKk5MTUkpGjBhB9erVefXV4td4Lbf/u7nvVbM+pYQQnYQQkUKIM0KI/5R4EkLYCiGWmI7vFUL4m/Z7CiG2CCFShRBf3zVnq2nNQ6atUBqyx144TdDVXzng3rnUGsfJWckM3zCcjRc28mbIm4wNHquMY4VCUbjobSBoCIzYD72+B2M2/PKMVibu2G9gNFpaoeIBZGTnMH3zaYIqutGmpplf0WvGQcJp6DWr2BjHioLn+++/JzAwkDp16pCUlMTw4cMtLanAeWCIhRBCB8wA2qN11NovhFglpTx+x7DngOtSympCiAHAFKA/kAG8C9Q1bXczWEpptpvJKPP+gXrhtw/wACr2+jDPc0sC8WnxvLjxRaKSopjScgpdqnSxtCSFQlGa0Omhfj+o21szjLdN0Qxl7zrQ5i0tqa+UVzcorized4ErSRl81reBeUlSh5fAoR+h1RtQpXXhC1SwdetWi5z31VdfLZYe44LEHDdiY+CMlDJKSpmF1pq2x11jegALTM+XAe2EEEJKeVNKuQPNUH5oTiaeZMjaIXwd8TX7ruwjMyfzvuMvnj5MUOIaDno/QdmK1QtCwiPFxf9v787jqqrWBo7/Hg+oOINgDpE4YiqIiIZWTuXQZJY2erO3bjZq9Vq9Zr2ZdW2wa/NklqbdrOvNsqwcytcpUjNUVDQyB0zFCQcEFWVY7x97QwcEzkHxDPB8P5/z4Zy91977OZuzdLHOs9bK3MXw+cPZlbmLd/u+q41jpZT3VHNA1FB4cBXc+BHknYJZw+DDPrD1/3Qwn485eTqPd5ZsI75lCD1ah7o+4NA2+H40XNQDeo05/wEqdZ65M0ivGbDL6fVuoPiQ1MIyxphcEcnAWokr3cW5PxaRPOBLYEJJy9eKyL3AvQANmzckNz+XDzd+yAcbPiCwWiDRYdF0bdyVbo27ERUaRc2AvybDPjB3PA2F5i7NAAAgAElEQVQJpPWQZ914m5XLliNbuO/H+8jJz2Fq/6lEhUV5OySllLIaytE3QYcbrBzlpRPh0xuh+WVwxTid8cBHfLIylfSsU7z/t1jXhXNPWfMdOwJhyIfWtwZK+Tl3PsUlfa9SvCHrTpnihhlj9ohIXawG8h3AJ2ecxJgpwBSwBhN8ds1nZJ7OZO3+tSTuT2T1vtVM2TCFyesnE1gtkKjQKOIax9HsRG0GZi1hfbPhdG9cjjXjK4H1B9fz4KIHqRlQkxkDZ9CqQStvh6SUUkU5AqDz3yDqJlgzw1psZFp/iLzaaig3utjbEVZZmdk5TF62jV5tw+ga4cb8xYvGw971cOtnUL8cq+wp5cPcaSDvBpxbmBcCaaWU2S0iAUB94HBZJzXG7LF/ZorIZ1ipHGc0kEtSt3pdeoX3ole4leN07PQx1u1fR+L+RBL3JTJ141TyTB7PN7+QyOB9rEh8ldhGsXRu1JkGNRu4cwm/9cveXxi1eBShQaF82P9DmtVp5u2QlFKqdAE14JJ7ofMwWPU+/PymNZCv021WjrI2uDzu459TOXIih9H92rouvGWhtVBMt/usfHKlKgl3cpB/BdqISAsRqQ7cCswtVmYucKf9fCiwuKR0iQIiEiAiofbzQOBaILm08q7Uq16PXuG9eCzuMT6/9nOmRb7A5H0H6E9ralYPYuZvM3l4ycNcPutyBn89mOdWPsfcbXPZdWxXuVaE8XVLdy3lwUUP0qxOM2YMnKGNY6WU/6he25ov+ZH1EP8gbPwC3u4CP46Dk0e9HV2VkXEihw9/2s6VF19Ap3AXHUrH9sLXD8AFUdCvag6ELw+Hw0FMTAydOnUiNjaWFStWANZUZEOHDvVydJaIiAjS011lx5YuKSmJefPmlbhv+vTpjBw58qzPXZrU1FQ6dixpHohz47IH2c4pHgksBBzANGPMJhF5Hkg0xswFpgL/EpGtWD3HtxYcLyKpQD2guogMBvoDO4GFduPYASwCPqyoN1V98StEnKpOzJDp1K7bgOzcbJLTk1l7YC3rDqxj4Y6FzN5irdASUjOEmLAYOjXqRKewTrRv2J6ggKCKCsVjFqQuYOzysUSGRDL5ysmVvqdcKVVJ1QqBAS/AJffBkhfh57eshUd6PgFd77F6nNV58+FP28nMzuWx/i56j/Pz4KsRkHMShk6DwJpll1cEBQWRlJQEwMKFCxk7dizLli2jadOmhavG+bPc3FySkpJITEzk6qv9f1IAtzLpjTHzgHnFto1zep4N3FTKsRGlnLaLeyGWT/LP39LxVBKr2jxGfF2rkVgzoCZxjeOIa2zNC51v8tl6dCtJB5JYf3A9SQeSWLxrMQABEkCb4DZEh0UTHRZNx9CORNSL8Ol5g7/Z+g3jVowjJiyGd694lzrV63g7JKWUOjcNLoIbJkP3h6xe5IVPwS8fwJXjrQF+OjVchTuUdYqPf97BtdFNuLhJvbILJ7wOqT/BoHcgzI1UDFXEsWPHCA4OBqwe0GuvvZbk5GTy8vIYM2YMCxcuREQYMWIEo0aNYs2aNYwePZqsrCxCQ0OZPn06TZo0oXfv3sTExLB69erClfS6devGsmXLeOQRaxVDEWH58uXUrVu38PrHjx/n5ptvZvfu3eTl5fHMM89wyy23APD222/z7bffkpOTwxdffEG7du04fPgwd999N9u3b6dWrVpMmTKF6Ohoxo8fT1paGqmpqYSGhpKQkMDJkydJSEhg7NixhecskJaWxsCBA9m2bRs33HADr7zyCgA//PADzz77LKdOnaJVq1Z8/PHH1KlTh+eff55vv/2WkydP0qNHDz744ANEhDVr1hSurHfZZednheRKNdTU5OcTsOQFDhBCzI2PlVqumlSjbXBb2ga35ebImwE4nH2YDQc3FD6+2/4ds3631hevHVib9g3b06FhB9o3bE/7hu0JrxvuE43m//z+H/6x6h/EN4nnzT5vUiuwlrdDUkqpitM4Cu6YA9sWww/PWLMlrHzX6mW+KN7b0VUqHyzfzsmcPB690kWDd9dqq3e/4xBroKW/mf8k7NtYsedsHAVXvVxmkYKlprOzs9m7dy+LFy8+o8yUKVPYsWMH69atIyAggMOHD5OTk8OoUaP45ptvCAsLY9asWTz99NOFy0ofP36cFStWsHz5cu6++26Sk5OZNGkS7777LpdeeilZWVnUrFm0h3/BggU0bdqU77//HoCMjIzCfaGhoaxdu5b33nuPSZMm8dFHH/Hss8/SuXNnvv76axYvXszw4cMLe8PXrFlDQkICQUFBTJ8+ncTERN55p8jacIWSkpJYt24dNWrUIDIyklGjRhEUFMSECRNYtGgRtWvXZuLEibz22muMGzeOkSNHMm6c1R97xx138N1333Hddddx11138fbbb9OrVy+eeOIJN39J5VOpGsgblv6HTrm/8UuHcVwSVLtcx4bUDKF3eG96h/cGIC8/jx0ZO0g+lExyejKb0jfx2W+fcTr/NGA1miODI2kX0o52Ie2IDImkVYNW1HB47uu/mb/N5OXVL9Pzwp681vs1j15bKaU8qlVfuK8XrP8c/u8fMG0AXDwI+j0HIS29HZ3fO3Asm09WpjI4phmtG5XxLWR2Bnz5d6jfDK59XXvyy8E5xWLlypUMHz6c5OSiw68WLVrE/fffT0CA1TwLCQkhOTmZ5ORk+vXrB0BeXh5NmjQpPOa2224DoGfPnhw7doyjR49y6aWXMnr0aIYNG8aNN97IhRcWHewaFRXF448/zpgxY7j22mu5/PLLC/fdeOONAHTp0oWvvvoKgISEBL788ksA+vbty6FDhwob1YMGDSIoyL3U1CuuuIL69esD0L59e3bu3MnRo0fZvHkzl156KQCnT5+me/fuACxZsoRXXnmFEydOcPjwYTp06EDPnj05evQovXpZEzXccccdzJ8/363rl0elaSDn5+VR++dX2CMXEHv9uSeBO6o5aB3cmtbBrRncejAAOfk5bDu6jd8O/cbmQ5tJOZzCnK1zOJl70jpGHLSo34I2DdrQJrgNrRu0plWDVjSq1ajI/MwVYcamGUxKnETf8L5M6jWJQEdghZ5fKaV8TjWH1WPZ4QZY8Q78/Ab8Pt/KV+75OAQFeztCv/Xe0m3k5BkeubKMRbWMge/+GzL2wN0LoGZ9zwVYkVz09HpC9+7dSU9P5+DBg0W2G2POWLXQGEOHDh1YuXJliecqXl5EePLJJ7nmmmuYN28e8fHxLFq0iHbt2hWWadu2LWvWrGHevHmMHTuW/v37F/bU1qhhdbY5HA5yc3MLYyjturVru98hWXBu5/MbY+jXrx+ff/55kbLZ2dk8+OCDJCYmEh4ezvjx48nOzi7xHp0PlaaBnPTDJ8TmbePXzi/RrPr56UkNrBZY2GN8Q5sbAKuneVfmLlKOpLDl8Ba2HNnC+oPrmZ9a9K+ZetXr0bJ+S1oHt6Zl/ZaFjwtqX1DuVI1pydN4fc3r9Gvej4k9JxJYTRvHyn0iUhNYDtTA+jdgtjHmWRFpgbVSZgiwFrjDGHNaRGpgTcHYBTgE3GKMSfVK8EqBNeNF7zHQ5U5YPMFKuUiaCb3HQtzd1oIVym1pR0/y2S9/cnPchTRvWEZjJ+kzSP4S+v4vhHfzXICVUEpKCnl5eTRs2JATJ04Ubu/fvz+TJ0+md+/ehSkWkZGRHDx4kJUrV9K9e3dycnLYsmULHTp0AGDWrFn06dOHhIQE6tevT/369dm2bRtRUVFERUWxcuVKUlJSijSQ09LSCAkJ4W9/+xt16tRh+vTpZcbbs2dPZs6cyTPPPMPSpUsJDQ2lXr0z89Tr1q1LZmZmue5FfHw8Dz30EFu3bqV169acOHGC3bt306hRI8BK+cjKymL27NkMHTqUBg0aUL9+fRISErjsssuYOXNmua7nrkrRQM7LzaXhr6+ys1o4sdfc69FrO6o5iKgfQUT9CAZGDCzcnnk6k21Ht7EjYweHsg+xN2sv2zK28ePOH8k49VeuT1BAEBfVvYjm9ZrTvF5zwuuGE143nAvrXkijWo2KNJ7zTT5vrn2TacnTuCriKl68/EUCqlWKX6HyrFNAX2NMlj2TTIKIzAdGA68bY/4tIpOBvwPv2z+PGGNai8itwETgltJOrpTH1G0M179j9SD/8L8w/39g9RTo9w+IvEq//nfTO0u2AjCybxm9x+lbYd4TEHE5XDbaQ5FVLgU5yGD1yM6YMQOHw1GkzD333MOWLVuIjo4mMDCQESNGMHLkSGbPns3DDz9MRkYGubm5PProo4UN5ODgYHr06FE4SA/gjTfeYMmSJTgcDtq3b89VV11V5DobN27kiSeeoFq1agQGBvL++++XGfv48eO56667iI6OplatWsyYMaPEcn369OHll18mJiamxEF6JQkLC2P69OncdtttnDp1CoAJEybQtm1bRowYQVRUFBEREXTt2rXwmI8//rhwkN6AAQNcXuNsiD/NAxwXF2cSExPP2J44933i1j7J2kveIPaqu7wQmfuMMRzOPsz2jO3syNhB6rFUUjNS+TPzT/Zk7iHX5BaWrV6tOk3qNKFJ7SY0rdOUAycOkLAngZvb3szYS8Zq47gKEJE1xpi483j+WkAC8ADwPdDYntqxOzDeGDNARBbaz1faCwHtA8LKmuu8tLqq1HljDPzxAyx8Gg79YTXkBrwITaI9cvnzXVfPl+iYWHPi6gncfslFPH99KXPJ5p6GqVfC0T/h/p+t/GM/k5aWRtOmTb0dRoXr3bs3kyZNIi7O7z56HlHS793duur3Layc06dovO4NtjlaEtN/uLfDcUlEaBjUkIZBDenauGuRfTn5OezL2seuzF3sztrN7szd7MnaQ1pWGst2LSMrJ4vHujzGnR3u9Ej+jaq8RMQBrAFaA+8C24CjxhT+hbYbKPhfsBmwCwrnRc8AGgLpxc55L3AvwEUXXXS+34JSRYlA2wHWYL41061ZFj7oCTHDrJSAek1cnqIqOpB5ivrVhIf6tC690OLnraWkb/nULxvHSp0Nv28gr/v2PbqZfST1+IBqxb6q8DeB1QIJrxdOeL3wEvd7KjFdVX7GmDwgRkQaAHOAi0sqZv8s6UN3Ru+xMWYKMAWsHuQKClWp8nEEQrcREHUT/DQJVk2GTV/BpY9Aj1FW/rIqdOTEaUbGN+eCeqUMJN+2GFa8DV3ugouv82xwyqWlS5d6O4RKy/sT+Z6DU9knuGjjO/weEEmnPjd7O5zzThvHqqIZY44CS4F4oIGdQgFwIZBmP98NhIO1TDxQH2vFTKV8V1AD6D8BRv4KbfrD0pespavXfWqtAqcAqCbC/b1blbzzeDrMuR9CI610FaWqEL9uICd9/SaNSed0z7FINb9+K0p5jIiE2T3HiEgQcCXwG7AEGGoXuxP4xn4+136NvX9xWfnHSvmUkBZw8wy4eyHUawbfPGSlXmw7c5GGqqhlWG1C65Qw85Mx1r06eQSGToXqugiVqlr8tlV58ngmrVIms6l6FB0vu97b4SjlT5oAS0RkA/Ar8KMx5jtgDDBaRLZi5RhPtctPBRra20cDT3ohZqXOzUXxcM8iGDIVTh2Df90Anw6B/Zu8HVmFE5FUEdkoIkkiUuZo2aDAUlITf/0ItiyAfs9bq8QpVcX4bQ7y+jmvEs9RDvT9QHuPlSoHY8wGoHMJ27cDZ0xuaozJBm7yQGhKnV8iEDXUyqVdPQWW/xMmXwYxt0Ofp6FepZrloI8xJt11sRLs32xNm9e6H1xyfwWHpZR/8MuW5fHMo0RuncqGml1oHz/Q9QFKKaVUgYAa1oC9h5Mg/kHY8B94KxYWjYeTR70dnXflZMOX90CNujD4PZ1LugI5HA5iYmLo1KkTsbGxrFixokLOO378eCZNmlTivh49erg8vk4da2nxtLQ0hg4d6qJ01eGXDeSNX75CMMeoceX/ejsUpZRS/qpWCAx4wRrId/G1kPA6vBVjzdqQk+3t6M6FAX4QkTX29ItFiMi9IpIoIonFlzpm0bNwYBNc/x7UaeShcKuGoKAgkpKSWL9+PS+99BJjx449o0xeXsUOIC1PI7xp06bMnj27Qq/vz/yugZxxJJ32qdNJqtWdyLi+3g5HKaWUvwuOgCEfwX3LoWlnK73g7S6w9hPIy3V5uA+61BgTC1wFPCQiPZ13GmOmGGPijDFxYWFhf+3440f4ZbKVVtG2v2cjrmKOHTtGcHAwYE3V1qdPH26//Xaioqx878GDB9OlSxc6dOjAlClTCo9bsGABsbGxdOrUiSuuuKJw++bNm+nduzctW7bkrbfeKtxe0DuclZXFFVdcQWxsLFFRUXzzzTcUl5qaSseO1mIxmzZtolu3bsTExBAdHc0ff/xR8TfBx7mVgywiA4E3AQfwkTHm5WL7awCfAF2AQ8AtxphUEWkIzAa6AtONMSOdjukCTAeCgHnAI+6MjN/81Ut05zh1B45zJ3SllFLKPU06wR1zYPsyK91i7ij4+S3o8xS0Hwx+Mt7FGJNm/zwgInOwxhYsL/OgrAPw9QPQqANc+ZwHovSeiasnknI4pULP2S6kHWO6jSmzTMFS09nZ2ezdu5fFi/+aSWX16tUkJyfTokULAKZNm0ZISAgnT56ka9euDBkyhPz8fEaMGMHy5ctp0aIFhw//NdtmSkoKS5YsITMzk8jISB544AECAwML99esWZM5c+ZQr1490tPTiY+PZ9CgQaVOHzt58mQeeeQRhg0bxunTpyu8Z9sfuKzt9opb72L9JdoeuE1E2hcr9nfgiDGmNfA6MNHeng08Azxewqnfx1p1q439cJlMnJebQ9SfM1lbpyetol3n1SillFLl1rIXjFhsrRxXzQGz77KmhkuZZ01/5sNEpLaI1C14DvQHkss8qGBKt+xjVk96YCmLhqhzUpBikZKSwoIFCxg+fDgF/YLdunUrbBwDvPXWW3Tq1In4+Hh27drFH3/8wapVq+jZs2dhuZCQkMLy11xzDTVq1CA0NJRGjRqxf//+Itc2xvDUU08RHR3NlVdeyZ49e84o46x79+68+OKLTJw4kZ07dxIUFFSRt8IvuNOD3A3Yao9wR0T+DVwPbHYqcz0w3n4+G3hHRMQYcxxIEJEia1iKSBOgnjFmpf36E2AwML+sQLKP7KUWpwm55lk3wlZKKaXOkog120Xk1bBxNix7Gf59m5WC0fspaNPPVwewXQDMsXsGA4DPjDELyjzi14/gjx/gqn/CBcX7vyofVz29ntC9e3fS09MpyAGvXfuvFR6XLl3KokWLWLlyJbVq1aJ3795kZ2eXuZpujRp/zWXtcDjIzS2aGjRz5kwOHjzImjVrCAwMJCIiguzs0vPsb7/9di655BK+//57BgwYwEcffUTfvlUrrdWd74uaAbucXu+2t5VYxhiTC2RgzaNa1jl3uzgnUHQwQVDOEdbW60vExXFuhK2UUkqdo2oO6HQLPPQrDHoHThyCz26CD/vC7wt8rkfZGLPdGNPJfnQwxrxQ5gG52VbOdZv+1hLdyiNSUlLIy8ujYcMzm0oZGRkEBwdTq1YtUlJSWLVqFWA1qpctW8aOHTsAiqRYuJKRkUGjRo0IDAxkyZIl7Ny5s8zy27dvp2XLljz88MMMGjSIDRs2lOPdVQ7u9CCX9OdK8X8R3ClzVuWNMVOAKQBxTQNMo+s091gppZSHOQIg9g7odCskfQY/vQqf32ItonF5SVmEfuJIKtRoDNe/66s94pVGQQ4yWCkPM2bMwOE4c6GWgQMHMnnyZKKjo4mMjCQ+Ph6AsLAwpkyZwo033kh+fj6NGjXixx9/dOvaw4YN47rrriMuLo6YmBjatWtXZvlZs2bx6aefEhgYSOPGjRk3ruq1vcTVuDgR6Q6MN8YMsF+PBTDGvORUZqFdZqWIBAD7gLCCQXci8l9AXMEgPTvFYokxpp39+jagtzHmvrJiuTg8xPy2y/2/mJTydyKyxhjjd1+ZxMXFmcTEMhfwUsq/5eVY8ycnvAaHtiLPHfPPutrUYRKXzq/0s1akpaXRtGmlWghGuaGk37u7/6+6k2LxK9BGRFqISHXgVmBusTJzgTvt50OBxWXNSGGM2Qtkiki8WAk1w4Ez5xwppnoD/XArpZTyAY5A6DwMHloNQ6d5O5qzVz+80jeOlTobLlMsjDG5IjISWIg1zds0Y8wmEXkeSDTGzAWmAv8Ska3AYaxGNGCtCQ/UA6qLyGCgvzFmM/AAf03zNh8XA/QAAmvoyFqllFI+pJoDOg7xdhRnr3aotyNQyie5NQ+yMWYe1lzFztvGOT3PBm4q5diIUrYnAh3dDVQppZRS6myVNQuEqnzcWFqjTP4x67lSSiml1FkKDAwkKyvrnBtNyj8YY8jKyiqyWEp5udWDrJRSSinlr0JCQjh8+DCZmZneDkV5SGBgYJHFVMpLG8hKKaWUqtQcDgdhYWHeDkP5EU2xUEoppZRSyok2kJVSSimllHKiDWSllFJKKaWcuFxJz5eISCbwu7fjcBIKpHs7CCe+Fg/4Xkz+Fk9zY4zfJc5pXXWLr8Wk8bhWVkxaVyuOr/3uNR7XfC2mc66r/jZI73dfWspTRBI1nrL5Wkwaj8doXXXB12LSeFzzxZgqgE/VVfC9+6zxuOZrMVVEPJpioZRSSimllBNtICullFJKKeXE3xrIU7wdQDEaj2u+FpPG4xm+9r58LR7wvZg0Htd8MaZz5Yvvyddi0nhc87WYzjkevxqkp5RSSiml1Pnmbz3ISimllFJKnVfaQFZKKaWUUsqJXzSQRWSgiPwuIltF5EkvXD9cRJaIyG8isklEHrG3jxeRPSKSZD+u9nBcqSKy0b52or0tRER+FJE/7J/BHool0uk+JInIMRF51NP3SESmicgBEUl22lbiPRHLW/bnaoOIxHoonn+KSIp9zTki0sDeHiEiJ53u1eSKjud883ZdtWPwufqqdbXEOLSuepm366vWVbfi8Xp9rbJ11Rjj0w/AAWwDWgLVgfVAew/H0ASItZ/XBbYA7YHxwONevDepQGixba8AT9rPnwQmeul3tg9o7ul7BPQEYoFkV/cEuBqYDwgQD/zioXj6AwH284lO8UQ4l/O3hy/UVTsOn6uvWldLvLbWVS8+fKG+al09q9+Zx+trVa2r/tCD3A3YaozZbow5DfwbuN6TARhj9hpj1trPM4HfgGaejKEcrgdm2M9nAIO9EMMVwDZjzE5PX9gYsxw4XGxzaffkeuATY1kFNBCRJuc7HmPMD8aYXPvlKuDCirymF3m9roJf1Vetq1pXvcnr9VXrarl5pb5W1brqDw3kZsAup9e78WIFEpEIoDPwi71ppN2lP82TX7vYDPCDiKwRkXvtbRcYY/aC9Y8P0MjDMQHcCnzu9Nqb9whKvye+8Nm6G+uv7QItRGSdiCwTkcs9HMu58oX7WYQP1Vetq+7Ruuo5vnBPC2lddYsv1ddKX1f9oYEsJWzzytx0IlIH+BJ41BhzDHgfaAXEAHuBVz0c0qXGmFjgKuAhEenp4eufQUSqA4OAL+xN3r5HZfHqZ0tEngZygZn2pr3ARcaYzsBo4DMRqeepeCqAz9RV8Ln6qnX13GhdrXg+U1+1rrrmR/W10tRVf2gg7wbCnV5fCKR5OggRCcSqwDONMV8BGGP2G2PyjDH5wIdYX1l5jDEmzf55AJhjX39/wdcZ9s8DnowJ6x+VtcaY/XZsXr1HttLuidc+WyJyJ3AtMMzYiVLGmFPGmEP28zVY+YFtPRFPBfGJugq+V1+1rrpN66rn+ER91brqNl+rr5W+rvpDA/lXoI2ItLD/groVmOvJAEREgKnAb8aY15y2O+fV3AAkFz/2PMZUW0TqFjzHSlBPxro3d9rF7gS+8VRMtttw+grIm/fISWn3ZC4w3B51Gw9kFHxldD6JyEBgDDDIGHPCaXuYiDjs5y2BNsD28x1PBfJ6XQXfq69aV8tF66rneL2+al0tF1+rr5W/rroaxecLD6xRkVuwWv5Pe+H6l2F9RbABSLIfVwP/Ajba2+cCTTwYU0usUcfrgU0F9wVoCPwf8If9M8SDMdUCDgH1nbZ59B5h/QOyF8jB+kv276XdE6yvgt61P1cbgTgPxbMVK0er4LM02S47xP5drgfWAtd5+rNeAe/Xq3XVjsGn6qvW1VJj0Lrq5Ye366vWVbfj8mp9rap1VZeaVkoppZRSyok/pFgopZRSSinlMdpAVkoppZRSyok2kJVSSimllHKiDWSllFJKKaWcaANZKaWUUkopJ9pAVkoppZRSyok2kJVSSimllHKiDWSlVKUlIv8lIqaUx1GncuNFxBR/LSIB3onctxS/PxV43rdF5NtS9oWIyL9E5HcRSRGRMU77/ltENoiI/h+mlDov9B9/pVRVcBPWikvOcr0RiLKISCvgPqBHKUW+Br40xtxhL/u7WUTmG2M2AJOxlpW9E/jYIwErpaoUbSArpaqCJGPMVm8HoYp4FFhvjEksvkNEegG1jDFvAhhjjovIduAC+/VJEfkEeBxtICulzgP9ekoppUp3sYgsEZETIrJXRJ4v/rW+iAwUkZUiclJEMkTkaxGJdNofZ6drXOa0bZS9bYLTtjb2tqtLC0ZEWttpBzvs620XkfdFJLhYuYIUkTYi8r2IZInIThEZV0L8t9kpDNkislFEBonIUhFZ6urmiEgnEZkrIkfseH4WkcvdOK4G8Dfgs1KKxABripWPBDY6lfk30F5ESuuBVkqps6YNZKVUVeAQkYBiD3f+/fsaWAQMxmrMPQOMK9gpIgOB74Es4BbgAaAjkCAizexia4GjQF+n8/YFTpawLQ/4qYx4mmKlijwKDACeB64A5pVSfg6w2I7/a+A5rLSEgvj7ATOBFGAIMAl4A2hbRgwFx8YCK4AQYIR9/CFgkYh0cXF4PNCA0t/rLqCDiDjs39MrwDxjzD6nMknAMWCgq1iVUqq8NMVCKVUVpJSw7XvgWjj2dXkAAASuSURBVBfHfWiMedl+/oOI1AMeE5E3jDFHgQnAduAqY0wugIisBLYAjwGjjTH5IrIc6AMU9ED3At4HHhaROsaYLHt/ojEms7RgjDHLgeUFr0VkBbAV+ElEOhtj1hU75FVjTEEKwiIR6Qvcxl9pCc8Bm4EbjDHGPudGrN7bLS7uzT+BP4G+xpjT9rELgWSsPyQGl3FsPGCADaXs/xroZ59LgPnAQ84F7Pu6wT6XUkpVKO1BVkpVBTcAXYs9HnXjuP8Ue/1voA7Q0R44FgvMKmgcAxhjdgA/YzWCCywBuotITaz0gQZYvaKngIKUhN5Yvb2lEpHqIvKUnRJxEsjhr17YyBIO+b7Y62TgIvtcDiAOayBc4QwVxpi1wA4XcQTZ7+8LIL+gVx6rMbsI6FnW8Vg94ccKGtbFGWPyjTEPGGMuNsa0M8b8tzHmVAlFD9rnUkqpCqU9yEqpqiD5LAfp7S/ldTMgFatBuLeE4/YBzZ1eLwZqYM3Y0BlrcNp+EUkA+ojIn1gD0Ja4iOclYBRWasUKIBO4EPgKqFlC+cPFXp9yKhcKBAIHSjiu+PsuLgRwYPUUP1NSARGpZozJL+X4mnYs5+okEFQB51FKqSK0gayUUqW7ACuFwvk1wB7gCFaaQOMSjmuMlY9bYCOQjpVn3Jm/eooXAzdj5dyexup5LsutwCfGGOfBfXXceSMlSMfqgW5Uwr4LsNInSnMUyAfeBT4pqUAZjWOw7k1wGfvdFYL1PpRSqkJpioVSSpXu5mKvb8UakJdsjDmOlat7k52uAICINMfqKV5WsM1OYViGlVd7OUUbyJ2xUkB+McaccBFPLaxGrbO7yvOGnGLKAxKBISIiTvF3AVq4OPY4VmpHJ2CtMSax+MPF5VOAQBG58Gxid9IC+P0cz6GUUmfQHmSlVFUQIyKhJWxPdM4fLsEIe1Ddr1izRtwDjLcH6IGVXvA98J2IvIeVn/wckAG8Wuxci7F6XJ1nqliLNRNDH6y0CVcWAHfaA+m2AjdS+kIb7ngW+AGYIyJTsNIuxmOliJTVAwwwGmvA4EIRmYqVahKKlZftMMY8WcaxBQMNu3HmAi5uEZEGWLNtTDqb45VSqizag6yUqgq+AFaW8Gjg4rjrsXp952LN2zsB+EfBTmPMAuAa+zz/wVrh7TfgMmNMWrFzFeQXJxpjjtnH5/NXY9FV/jFY+cdzgReAWUBdrFkpzoox5kdgGHAx1pRwY7Bm39iH1cgv69i1WIMdDwFvYTW03wSicJppo5RjU4HVwHVnGzvWfT9tx62UUhVKnAYvK6WUquLstIetwAvGmH+4Kn8O1/kvrAZ1EzdSS0o6fj6Qboy5o6JjU0opbSArpVQVZU/X9hrW1GzpQEvgf7AG6XUwxpQ0Q0dFXduBNXhxmjGmXGkSIhIDrAI66hLiSqnzQXOQlVKq6srDmnHjHaAhUDD47qbz2TgGa5CgiNyNlbNcXo2Bu7RxrJQ6X7QHWSmllFJKKSc6SE8ppZRSSikn2kBWSimllFLKiTaQlVJKKaWUcqINZKWUUkoppZxoA1kppZRSSikn2kBWSimllFLKiTaQlVJKKaWUcqINZKWUUkoppZz8P4LqrHaL3wYtAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 720x288 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["labels = ['<PERSON>iceps long head', 'Bice<PERSON> short head', '<PERSON><PERSON><PERSON><PERSON>']\n", "fig, ax = plt.subplots(nrows=1, ncols=3, sharex=True, figsize=(10, 4))\n", "ax[0].plot(r_ef[:, 1], r_ef[:, 2:])\n", "#ax[0].set_xlabel('Elbow angle $(\\,^o)$')\n", "ax[0].set_title('Moment arm (m)')\n", "ax[1].plot(f_ef[:, 1], f_ef[:, 2:])\n", "ax[1].set_xlabel('Elbow angle $(\\,^o)$', fontsize=16)\n", "ax[1].set_title('Maximum force (N)')\n", "ax[2].plot(m_ef[:, 1], m_ef[:, 2:])\n", "#ax[2].set_xlabel('Elbow angle $(\\,^o)$')\n", "ax[2].set_title('Maximum torque (Nm)')\n", "ax[2].legend(labels, loc='best', framealpha=.5)\n", "ax[2].set_xlim(np.min(r_ef[:, 1]), np.max(r_ef[:, 1]))\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These data don't have the *pcsa* value of each muscle. We will estimate the *pcsa* considering that the amount of maximum muscle force generated per area is constant and equal to 50N/cm$^2$. Consequently, the *pcsa* (in cm$^2$) for each muscle is:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.434700Z", "start_time": "2018-08-06T04:49:38.430685Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[12.486   8.7112 19.7452]\n"]}], "source": ["a_ef = np.array([624.3, 435.56, 987.26])/50  # 50 N/cm2\n", "print(a_ef)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Static versus dynamic optimization\n", "\n", "In the context of biomechanics, we can solve the distribution problem separately for each angle (instant) of the elbow; we will refer to that as static optimization. However, there is no guarantee that when we analyze all these solutions across the range of angles, they will be the best solution overall. One reason is that static optimization ignores the time history of the muscle force. Dynamic optimization refers to the optimization over a period of time. For such, we will need to input a cost function spanning the entire period of time at once. Dynamic optimization usually has a higher computational cost than static optimization. The term 'Dynamic optimization' used in Biomechanics is not related to the broader method known as '[dynamic programming](https://en.wikipedia.org/wiki/Dynamic_programming)' in mathematical optimization or computer programming.\n", "\n", "<div class='center-align'><figure><img src='./../images/optim_stat_dyn.png' width=550 alt='static versus dynamic optimization' vspace=\"10\"/> <figcaption><center><i>Figure. Static and dynamic optimization approaches in Biomechanics. Figure taken from <PERSON> et al. (2013).</i></center></figcaption> </figure></div>\n", "\n", "The title 'Static and dynamic optimization solutions for gait are practically equivalent' of an article published by <PERSON> and <PERSON> (2001), where they studied normal walking (full gait cycle equals to 1.12 s), summarizes the results of such comparison. The authors reported that the dynamic solution required 1000 times more computation time than the static solution.\n", "\n", "For now, we will solve the present problem using static optimization."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Solution of the optimization problem\n", "\n", "For the present case, we are dealing with a problem of minimization, multidimensional (function of several variables), nonlinear, constrained, and we can't assume that the cost function is convex. Numerical optimization is hardly a simple task. There are many different algorithms and public and commercial software for performing optimization. For instance, look at [NEOS Server](http://www.neos-server.org/neos/), a free internet-based service for solving numerical optimization problems.  \n", "We will solve the present problem using the [scipy.optimize](http://docs.scipy.org/doc/scipy/reference/optimize.html#module-scipy.optimize) package which provides several optimization algorithms. We will use the function `minimize`:\n", "\n", "```python\n", "scipy.optimize.minimize(fun, x0, args=(), method=None, jac=None, hess=None, hessp=None, bounds=None, constraints=(), tol=None, callback=None, options=None)\n", "\"\"\"Minimization of scalar function of one or more variables.\"\"\"\n", "```\n", "\n", "Now, let's write Python functions for each cost function:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.456195Z", "start_time": "2018-08-06T04:49:38.435682Z"}}, "outputs": [], "source": ["from scipy.optimize import minimize"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.469208Z", "start_time": "2018-08-06T04:49:38.457194Z"}}, "outputs": [], "source": ["def cf_f1(x):\n", "    \"\"\"Cost function: sum of forces.\"\"\"  \n", "    return x[0] + x[1] + x[2]\n", "\n", "def cf_f2(x):\n", "    \"\"\"Cost function: sum of forces squared.\"\"\"\n", "    return x[0]**2 + x[1]**2 + x[2]**2\n", "\n", "def cf_fpcsa2(x, a):\n", "    \"\"\"Cost function: sum of squared muscle stresses.\"\"\"\n", "    return (x[0]/a[0])**2 + (x[1]/a[1])**2 + (x[2]/a[2])**2\n", "\n", "def cf_fmmax3(x, m):\n", "    \"\"\"Cost function: sum of cubic forces normalized by moments.\"\"\"\n", "    return (x[0]/m[0])**3 + (x[1]/m[1])**3 + (x[2]/m[2])**3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's also define the Jacobian for each cost function (which is an optional parameter for the optimization):"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.482196Z", "start_time": "2018-08-06T04:49:38.470187Z"}}, "outputs": [], "source": ["def cf_f1d(x):\n", "    \"\"\"Derivative of cost function: sum of forces.\"\"\"\n", "    dfdx0 = 1\n", "    dfdx1 = 1\n", "    dfdx2 = 1\n", "    return np.array([dfdx0, dfdx1, dfdx2])\n", "\n", "def cf_f2d(x):\n", "    \"\"\"Derivative of cost function: sum of forces squared.\"\"\"\n", "    dfdx0 = 2*x[0]\n", "    dfdx1 = 2*x[1]\n", "    dfdx2 = 2*x[2]\n", "    return np.array([dfdx0, dfdx1, dfdx2])\n", "\n", "def cf_fpcsa2d(x, a):\n", "    \"\"\"Derivative of cost function: sum of squared muscle stresses.\"\"\"\n", "    dfdx0 = 2*x[0]/a[0]**2\n", "    dfdx1 = 2*x[1]/a[1]**2\n", "    dfdx2 = 2*x[2]/a[2]**2\n", "    return np.array([dfdx0, dfdx1, dfdx2])\n", "\n", "def cf_fmmax3d(x, m):\n", "    \"\"\"Derivative of cost function: sum of cubic forces normalized by moments.\"\"\"\n", "    dfdx0 = 3*x[0]**2/m[0]**3\n", "    dfdx1 = 3*x[1]**2/m[1]**3\n", "    dfdx2 = 3*x[2]**2/m[2]**3\n", "    return np.array([dfdx0, dfdx1, dfdx2])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's define initial values:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.494191Z", "start_time": "2018-08-06T04:49:38.483194Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["M = 20\n", "x0 = [57.51311369 36.29974032 89.6470056 ]\n", "r * x0 = 6.622004446068322\n"]}], "source": ["M = 20  # desired torque at the elbow\n", "iang = 69  # which will give the closest value to 90 degrees\n", "r  = r_ef[iang, 2:]\n", "f0 = f_ef[iang, 2:]\n", "a  = a_ef\n", "m  = m_ef[iang, 2:]\n", "x0 = f_ef[iang, 2:]/10  # far from the correct answer for the sum of torques\n", "print('M =', M)\n", "print('x0 =', x0)\n", "print('r * x0 =', np.sum(r*x0))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inequality constraints (such as boundaries in our problem) can be entered with the parameter `bounds` to the `minimize` function:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.506166Z", "start_time": "2018-08-06T04:49:38.495172Z"}}, "outputs": [], "source": ["bnds = ((0, f0[0]), (0, f0[1]), (0, f0[2]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Equality constraints (such as the sum of torques should equals the desired torque in our problem), as well as inequality constraints, can be entered with the parameter `constraints` to the `minimize` function (and we can also opt to enter the Jacobian of these constraints):"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.520175Z", "start_time": "2018-08-06T04:49:38.507165Z"}}, "outputs": [], "source": ["# use this in combination with the parameter bounds:\n", "cons = ({'type': 'eq',\n", "         'fun' : lambda x, r, f0, M: np.array([r[0]*x[0] + r[1]*x[1] + r[2]*x[2] - M]), \n", "         'jac' : lambda x, r, f0, M: np.array([r[0], r[1], r[2]]), 'args': (r, f0, M)})"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.537148Z", "start_time": "2018-08-06T04:49:38.521158Z"}}, "outputs": [], "source": ["# to enter everything as constraints:\n", "cons = ({'type': 'eq',\n", "         'fun' : lambda x, r, f0, M: np.array([r[0]*x[0] + r[1]*x[1] + r[2]*x[2] - M]), \n", "         'jac' : lambda x, r, f0, M: np.array([r[0], r[1], r[2]]), 'args': (r, f0, M)},\n", "        {'type': 'ineq', 'fun' : lambda x, r, f0, M: f0[0]-x[0],\n", "         'jac' : lambda x, r, f0, M: np.array([-1, 0, 0]), 'args': (r, f0, M)},\n", "        {'type': 'ineq', 'fun' : lambda x, r, f0, M: f0[1]-x[1],\n", "         'jac' : lambda x, r, f0, M: np.array([0, -1, 0]), 'args': (r, f0, M)},\n", "        {'type': 'ineq', 'fun' : lambda x, r, f0, M: f0[2]-x[2],\n", "         'jac' : lambda x, r, f0, M: np.array([0, 0, -1]), 'args': (r, f0, M)},\n", "        {'type': 'ineq', 'fun' : lambda x, r, f0, M: x[0],\n", "         'jac' : lambda x, r, f0, M: np.array([1, 0, 0]), 'args': (r, f0, M)},\n", "        {'type': 'ineq', 'fun' : lambda x, r, f0, M: x[1],\n", "         'jac' : lambda x, r, f0, M: np.array([0, 1, 0]), 'args': (r, f0, M)},\n", "        {'type': 'ineq', 'fun' : lambda x, r, f0, M: x[2],\n", "         'jac' : lambda x, r, f0, M: np.array([0, 0, 1]), 'args': (r, f0, M)})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Although more verbose, if all the Jacobians of the constraints are also informed, this alternative seems better than informing bounds for the optimization process (less error in the final result and less iterations).  \n", "\n", "Given the characteristics of the problem, if we use the function `minimize` we are limited to the SLSQP (Sequential Least SQuares Programming) solver.  \n", "\n", "Finally, let's run the optimization for the four different cost functions and find the optimal muscle forces:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.555138Z", "start_time": "2018-08-06T04:49:38.538147Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.    (Exit mode 0)\n", "            Current function value: 409.59266009953336\n", "            Iterations: 7\n", "            Function evaluations: 7\n", "            Gradient evaluations: 7\n"]}], "source": ["f1r = minimize(fun=cf_f1, x0=x0, args=(), jac=cf_f1d,\n", "               constraints=cons, method='SLSQP',\n", "               options={'disp': True})"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.568130Z", "start_time": "2018-08-06T04:49:38.556137Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.    (Exit mode 0)\n", "            Current function value: 75657.38479127169\n", "            Iterations: 5\n", "            Function evaluations: 6\n", "            Gradient evaluations: 5\n"]}], "source": ["f2r = minimize(fun=cf_f2, x0=x0, args=(), jac=cf_f2d,\n", "               constraints=cons, method='SLSQP',\n", "               options={'disp': True})"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.581123Z", "start_time": "2018-08-06T04:49:38.569130Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.    (Exit mode 0)\n", "            Current function value: 529.9639777695737\n", "            Iterations: 11\n", "            Function evaluations: 11\n", "            Gradient evaluations: 11\n"]}], "source": ["fpcsa2r = minimize(fun=cf_fpcsa2, x0=x0, args=(a,), jac=cf_fpcsa2d,\n", "                   constraints=cons, method='SLSQP',\n", "                   options={'disp': True})"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.594116Z", "start_time": "2018-08-06T04:49:38.582122Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.    (Exit mode 0)\n", "            Current function value: 1075.1388931664733\n", "            Iterations: 12\n", "            Function evaluations: 12\n", "            Gradient evaluations: 12\n"]}], "source": ["fmmax3r = minimize(fun=cf_fmmax3, x0=x0, args=(m,), jac=cf_fmmax3d,\n", "                   constraints=cons, method='SLSQP',\n", "                   options={'disp': True})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's compare the results for the different cost functions:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2018-08-06T04:49:38.660097Z", "start_time": "2018-08-06T04:49:38.595115Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Comparison of different cost functions for solving the distribution problem\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>B<PERSON><PERSON> long head</th>\n", "      <th>B<PERSON><PERSON> short head</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Error in M</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>$\\text{Moment arm}\\;[cm]$</th>\n", "      <td>4.9</td>\n", "      <td>4.9</td>\n", "      <td>2.3</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$pcsa\\;[cm^2]$</th>\n", "      <td>12.5</td>\n", "      <td>8.7</td>\n", "      <td>19.7</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$F_{max}\\;[N]$</th>\n", "      <td>575.0</td>\n", "      <td>363.0</td>\n", "      <td>896.0</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$M_{max}\\;[Nm]$</th>\n", "      <td>28.1</td>\n", "      <td>17.7</td>\n", "      <td>20.4</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$\\sum F_i$</th>\n", "      <td>215.4</td>\n", "      <td>194.2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$\\sum F_i^2$</th>\n", "      <td>184.7</td>\n", "      <td>184.7</td>\n", "      <td>86.1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$\\sum(F_i/pcsa_i)^2$</th>\n", "      <td>201.7</td>\n", "      <td>98.2</td>\n", "      <td>235.2</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>$\\sum(F_i/M_{max,i})^3$</th>\n", "      <td>241.1</td>\n", "      <td>120.9</td>\n", "      <td>102.0</td>\n", "      <td>-3.552713678800501e-15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          Biceps long head Biceps short head <PERSON><PERSON><PERSON><PERSON>  \\\n", "$\\text{Moment arm}\\;[cm]$              4.9               4.9        2.3   \n", "$pcsa\\;[cm^2]$                        12.5               8.7       19.7   \n", "$F_{max}\\;[N]$                       575.0             363.0      896.0   \n", "$M_{max}\\;[Nm]$                       28.1              17.7       20.4   \n", "$\\sum F_i$                           215.4             194.2        0.0   \n", "$\\sum F_i^2$                         184.7             184.7       86.1   \n", "$\\sum(F_i/pcsa_i)^2$                 201.7              98.2      235.2   \n", "$\\sum(F_i/M_{max,i})^3$              241.1             120.9      102.0   \n", "\n", "                                       Error in M  \n", "$\\text{Moment arm}\\;[cm]$                       -  \n", "$pcsa\\;[cm^2]$                                  -  \n", "$F_{max}\\;[N]$                                  -  \n", "$M_{max}\\;[Nm]$                                 -  \n", "$\\sum F_i$                                    0.0  \n", "$\\sum F_i^2$                                  0.0  \n", "$\\sum(F_i/pcsa_i)^2$                          0.0  \n", "$\\sum(F_i/M_{max,i})^3$    -3.552713678800501e-15  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["dat = np.vstack((np.around(r*100,1), np.around(a,1), np.around(f0,0), np.around(m,1)))\n", "opt = np.around(np.vstack((f1r.x, f2r.x, fpcsa2r.x, fmmax3r.x)), 1)\n", "er = ['-', '-', '-', '-',\n", "      np.sum(r*f1r.x)-M, np.sum(r*f2r.x)-M, np.sum(r*fpcsa2r.x)-M, np.sum(r*fmmax3r.x)-M]\n", "data = np.vstack((np.vstack((dat, opt)).T, er)).T\n", "\n", "rows = ['$\\text{Moment arm}\\;[cm]$', '$pcsa\\;[cm^2]$', '$F_{max}\\;[N]$', '$M_{max}\\;[Nm]$',\n", "        '$\\sum F_i$', '$\\sum F_i^2$', '$\\sum(F_i/pcsa_i)^2$', '$\\sum(F_i/M_{max,i})^3$']\n", "cols = ['<PERSON><PERSON><PERSON> long head', '<PERSON><PERSON><PERSON> short head', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>rror in M']\n", "df = pd.DataFrame(data, index=rows, columns=cols)\n", "print('\\nComparison of different cost functions for solving the distribution problem')\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comments\n", "\n", "The results show that the estimations for the muscle forces depend on the cost function used in the optimization. Which one is correct? This is a difficult question and it's dependent on the goal of the actual task being modeled. <PERSON><PERSON><PERSON> and <PERSON><PERSON> (1997) investigated the effect of different cost functions on the optimization of walking and running and the predicted muscles forces were compared with the electromyographic activity of the corresponding muscles of the lower limb. They found that, among the analyzed cost functions, the minimization of the sum of squared muscle stresses resulted in the best similarity with the actual electromyographic activity.\n", "\n", "In general, one should always test different algorithms and different initial values before settling for the solution found. <PERSON><PERSON> (2011), <PERSON><PERSON> (2013), and <PERSON><PERSON><PERSON><PERSON> (2013) present more examples on numerical optimization. The [NEOS Guide](http://neos-guide.org/) is a valuable source of information on this topic and [OpenOpt](http://openopt.org/) is a good alternative software for numerical optimization in Python."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exercises\n", "\n", "1. Find the extrema in the function $f(x)=x^3-7.5x^2+18x-10$ analytically and determine if they are minimum or maximum.  \n", "2. Find the minimum in the $f(x)=x^3-7.5x^2+18x-10$ using the gradient descent algorithm.  \n", "2. Regarding the distribution problem for the elbow muscles presented in this text:  \n", "    a. Test different initial values for the optimization.  \n", "    b. Test other values for the elbow angle where the results are likely to change.   \n", "    \n", "3. In an experiment to estimate forces of the elbow flexors, through inverse dynamics it was found an elbow flexor moment of 10 Nm.  \n", "Consider the following data for maximum force (F0), moment arm (r), and pcsa (A) of the brachialis, brachioradialis, and biceps brachii muscles: F0 (N): 1000, 250, 700; r (cm): 2, 5, 4; A (cm$^2$): 33, 8, 23, respectively (data from <PERSON> et al. (2013)).  \n", "    a. Use static optimization to estimate the muscle forces.  \n", "    b. Test the robustness of the results using different initial values for the muscle forces.  \n", "    c. <PERSON>mpare the results for different cost functions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON> B, <PERSON><PERSON> (2012) [Random Search for Hyper-Parameter Optimization](http://jmlr.csail.mit.edu/papers/volume13/bergstra12a/bergstra12a.pdf). Journal of Machine Learning Research, 13, 281-305.  \n", "- Crown<PERSON><PERSON>eld R<PERSON>, <PERSON> (1981) [A physiologically based criterion of muscle force prediction in locomotion](http://www.ncbi.nlm.nih.gov/pubmed/7334039). Journal of Biomechanics, 14, 793–801.  \n", "- Downey AB (2014) [Physical Modeling in MATLAB](http://greenteapress.com/wp/physical-modeling-in-matlab-2e/). 2nd edition. Green Tea Press.  \n", "- <PERSON><PERSON><PERSON> (1987) [Individual muscle force estimations using a non-linear optimal design](http://www.ncbi.nlm.nih.gov/pubmed/3682873). J <PERSON>ci Methods, 21, 167-179.  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (1997) [The three-dimensional determination of internal loads in the lower extremity](http://www.ncbi.nlm.nih.gov/pubmed/9456380). Journal of Biomechanics, 30, 1123–1131.  \n", "- <PERSON><PERSON> (2013) [pycse - Python Computations in Science and Engineering](http://kitchingroup.cheme.cmu.edu/pycse/).  \n", "- <PERSON><PERSON><PERSON><PERSON> (2013) [Numerical methods in engineering with Python 3](http://books.google.com.br/books?id=aJkXoxxoCoUC). 3rd edition. Cambridge University Press.  \n", "- <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2006) [Biomechanics of the Musculo-skeletal System](https://books.google.com.br/books?id=hOIeAQAAIAAJ&dq=editions:ISBN0470017678). 3rd Edition. Wiley.  \n", "- <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> (2013) [Research Methods in Biomechanics](http://books.google.com.br/books?id=gRn8AAAAQBAJ). 2nd Edition. Human Kinetics.  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (1973) [A mathematical model for evaluation of forces in lower extremeties of the musculo-skeletal system](http://www.ncbi.nlm.nih.gov/pubmed/4706941). Journal of Biomechanics, 6,  313–322, IN19–IN20, 323–326."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"state": {}, "version": "1.1.2"}}, "nbformat": 4, "nbformat_minor": 1}