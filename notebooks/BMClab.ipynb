{"cells": [{"cell_type": "markdown", "metadata": {"nbpresent": {"id": "b939d778-a63f-4c3d-9bb5-a0c2368e8e50"}, "urth": {"dashboard": {"hidden": true, "layout": {}}}, "id": "MTMMdzcojkBC"}, "source": ["# Laboratório de Biomecânica e Controle Motor\n", "**[BMClab](https://bmclab.pesquisa.ufabc.edu.br/)@[UFABC](https://www.ufabc.edu.br/): Why, How, What For?**\n", "\n", "<br>\n", "<div class='center-align'><figure><img src=\"https://bmclab.pesquisa.ufabc.edu.br//wp-content/uploads/2016/05/cropped-BMClab0.png\" alt=\"BMClab image header\"/></figure></div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-04-16T22:45:02.183391Z", "start_time": "2018-04-16T22:45:02.179391Z"}, "cell_style": "center", "nbpresent": {"id": "f34b8454-e464-41c6-b6ad-aed29e717387"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 4, "row": 0, "width": 4}}}, "id": "2kmrcsMejkBF", "outputId": "543533d3-ed0d-4b2b-888a-742748ea8765"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["01:44 PM, Tuesday, September 20, 2022\n", "<PERSON>, https://bmclab.pesquisa.ufabc.edu.br/\n"]}], "source": ["from datetime import datetime\n", "print(datetime.now().strftime(\"%I:%M %p, %A, %B %d, %Y\"))\n", "print('<PERSON>, https://bmclab.pesquisa.ufabc.edu.br/')"]}, {"cell_type": "markdown", "metadata": {"toc": true, "id": "JRLdbc5bjkBG"}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Stuff-in-this-talk\" data-toc-modified-id=\"Stuff-in-this-talk-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Stuff in this talk</a></span></li><li><span><a href=\"#BMClab-website\" data-toc-modified-id=\"BMClab-website-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> website</a></span></li><li><span><a href=\"#Why-the-BMClab\" data-toc-modified-id=\"Why-the-BMClab-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Why the <a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a></a></span></li><li><span><a href=\"#BMClab-lines-of-research\" data-toc-modified-id=\"BMClab-lines-of-research-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> lines of research</a></span></li><li><span><a href=\"#BMClab-financial-support\" data-toc-modified-id=\"BMClab-financial-support-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> financial support</a></span><ul class=\"toc-item\"><li><span><a href=\"#BMClab-financial-support-(II)\" data-toc-modified-id=\"BMClab-financial-support-(II)-5.1\"><span class=\"toc-item-num\">5.1&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> financial support (II)</a></span></li></ul></li><li><span><a href=\"#BMClab-infrastructure\" data-toc-modified-id=\"BMClab-infrastructure-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> infrastructure</a></span><ul class=\"toc-item\"><li><span><a href=\"#BMClab-equipment\" data-toc-modified-id=\"BMClab-equipment-6.1\"><span class=\"toc-item-num\">6.1&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> equipment</a></span></li></ul></li><li><span><a href=\"#BMClab-services\" data-toc-modified-id=\"BMClab-services-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span><a href=\"https://bmclab.pesquisa.ufabc.edu.br/\" rel=\"nofollow\" target=\"_blank\">BMClab</a> services</a></span></li><li><span><a href=\"#Open-data-science\" data-toc-modified-id=\"Open-data-science-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Open data science</a></span><ul class=\"toc-item\"><li><span><a href=\"#Open-education\" data-toc-modified-id=\"Open-education-8.1\"><span class=\"toc-item-num\">8.1&nbsp;&nbsp;</span>Open education</a></span></li></ul></li><li><span><a href=\"#Literate-programming-and-literate-computing\" data-toc-modified-id=\"Literate-programming-and-literate-computing-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Literate programming and literate computing</a></span><ul class=\"toc-item\"><li><span><a href=\"#Literate-computing-with-Jupyter-Notebook\" data-toc-modified-id=\"Literate-computing-with-Jupyter-Notebook-9.1\"><span class=\"toc-item-num\">9.1&nbsp;&nbsp;</span>Literate computing with <a href=\"http://jupyter.org/\" rel=\"nofollow\" target=\"_blank\">Jupyter Notebook</a></a></span></li></ul></li><li><span><a href=\"#Questions?\" data-toc-modified-id=\"Questions?-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>Questions?</a></span></li><li><span><a href=\"#About-these-slides\" data-toc-modified-id=\"About-these-slides-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>About these slides</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "a1123914-0098-4f4e-a058-0f60c30a4ec4"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 6, "row": 4, "width": 12}}}, "id": "_gZ6FIRqjkBG"}, "source": ["## Stuff in this talk\n", "\n", "- [BMClab](http://demotu.org): why and how\n", "- [BMClab](http://demotu.org) infrastructure, current and future activities\n", "- Open data science and education  \n", "- Literate programming & Literate computing\n", "- Advocacy for Python (the programming language)"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2018-04-16T22:45:04.260557Z", "start_time": "2018-04-16T22:45:04.252552Z"}, "nbpresent": {"id": "2cc4e37c-dfa0-4239-a62b-5517692c56b9"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 21, "row": 10, "width": 4}}}, "id": "tjTZQ7DsjkBH"}, "source": ["## [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) website\n", "\n", "[Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "0cbd523b-0ecc-48e6-b915-61c32b32246f"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 7, "row": 31, "width": 12}}}, "id": "3ay7V3CDjkBH"}, "source": ["## Why the [BMClab](https://bmclab.pesquisa.ufabc.edu.br/)\n", "\n", "**[Biomedical engineering](https://en.wikipedia.org/wiki/Biomedical_engineering)**: the application of engineering principles and design concepts to medicine and biology for healthcare *and well-being* purposes.\n", "\n", "**[Neuroscience of human movement](https://en.wikipedia.org/wiki/Neuroscience)**: the scientific study of the nervous system bases of controlling movement.\n", "\n", "**[Biomechanics](https://en.wikipedia.org/wiki/Biomechanics) and [Motor Control](https://en.wikipedia.org/wiki/Motor_control)**: the study of the structure and function of biological systems using the knowledge and methods of the Mechanics and the study of how the biological systems control their movements.\n", "\n", "**[BMClab](http://demotu.org)**: In a broad sense, we are interested in knowing how living beings control and execute their movements. We also work to improve the quality of life in society by offering evaluation services in our laboratory and in the dissemination of scientific knowledge."]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "217e5f45-bda6-4948-8c2a-a8ecfce99bb9"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 6, "row": 38, "width": 12}}}, "id": "cu41VwSGjkBH"}, "source": ["## [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) lines of research\n", "\n", "- Postural control in humans  \n", "- Clinical gait analysis  \n", "- Biomechanics of long distance running  \n", "- Modeling and simulation of the neuromusculoskeletal system\n", "- ..."]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "c64b1ddf-0a73-4c83-8527-576417da242f"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 6, "row": 44, "width": 12}}}, "id": "Z2CkVo0YjkBH"}, "source": ["## [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) financial support\n", "\n", "The [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) was made possible by the financial support from UFABC and from Brazilian research agencies, nominally:  \n", "- Project \"Controle do equilíbrio e movimento em adultos jovens e idosos sedentários e corredores\" (FAPESP).  \n", "- Project \"Postura e envelhecimento: criação de base de dados pública de sinais de oscilação e simulação computacional de mecanismos de controle\" (FAPESP)."]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "7aceb8ec-4232-4e57-8cf2-10d31c898f65"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 8, "row": 50, "width": 12}}}, "id": "aZxTrejmjkBI"}, "source": ["### [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) financial support (II)\n", "\n", "The [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) was made possible by the financial support from UFABC and from Brazilian research agencies, nominally:  \n", "- Project \"Estudo do equilíbrio de pessoas com deficiências e idosos: uma base de dados aberta\" (CNPq).  \n", "- Project \"Desenvolvimento de simulador de cadeira de rodas e de serviço de avaliação do movimento e postura para deficientes físicos com próteses/órteses e usuários de cadeira de rodas\" (MCTI-SECIS/CNPq).  \n", "- Project \"Análise de atletas corredores: estudo multicêntrico para compreensão do movimento com implicação para prevenção de lesão e melhora do rendimento\" (ME/CNPq).  "]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "a4bc4fb9-42b0-4fa6-b5c3-14f4c7b34831"}, "urth": {"dashboard": {"layout": {"col": 4, "height": 7, "row": 10, "width": null}}}, "id": "bajjeG0BjkBI"}, "source": ["## [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) infrastructure\n", "\n", "180-m$^2$ laboratory organized in spaces for:  \n", "- Data collection with motion capture system, force plates, etc.   \n", "- Data analysis with several computers  \n", "- Subject preparation and evaluation  \n", "- Machine and electronics assembly"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "45213ee3-cb7c-4574-9425-beb4ffec8a8a"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 26, "row": 58, "width": 12}}}, "id": "CnzcwNYYjkBI"}, "source": ["<div class='center-align'><figure><img src=\"https://bmclab.pesquisa.ufabc.edu.br/wp-content/uploads/2016/01/BMClab4.png\" alt=\"BMClab lab\"/></figure></div>"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "21d7855d-792b-4bd7-9365-61f456186b0e"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 9, "row": 84, "width": 12}}}, "id": "qfP6CoaxjkBI"}, "source": ["### [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) equipment\n", "\n", "- 12-camera Motion capture system  \n", "- Six-component instrumented dual-belt treadmill  \n", "- Six-component force plates  \n", "- Pressure distribution transducers  \n", "- Tri-axial accelerometers  \n", "- Wheelchair six-component transducers  \n", "- Six-component torque transducer  \n", "- 10-channel wireless electromyography system  \n", "- ..."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-04-16T22:45:20.331960Z", "start_time": "2018-04-16T22:45:18.775431Z"}, "nbpresent": {"id": "9496bc91-1c35-4722-88d1-f279a91a46c4"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 17, "row": 93, "width": 9}}}, "id": "bsM1fJ34jkBI", "outputId": "3a164ebe-876f-45c0-b4ba-7dce49d520e1"}, "outputs": [{"data": {"image/jpeg": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEABALDA4MChAODQ4SERATGCgaGBYWGDEjJR0oOjM9PDkzODdASFxOQERXRTc4UG1RV19iZ2hnPk1xeXBkeFxlZ2MBERISGBUYLxoaL2NCOEJjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY//AABEIAWgB4AMBIgACEQEDEQH/xAAbAAABBQEBAAAAAAAAAAAAAAAAAQIDBAUGB//EAEkQAAICAgECBAMGAAsFBAsBAAABAgMEEQUSIQYTMUEiUWEUMkJicaEVIzNDUoGRosHh4kVjgoOjJFNysQcWJjQ1VGWEk6TCF//EABkBAQEBAQEBAAAAAAAAAAAAAAABAgMEBf/EACMRAQEAAgICAwEAAwEAAAAAAAABAhEDEiExBBNBURQiMiP/2gAMAwEAAhEDEQA/APPwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAm8j837CeR+b9iYDOxD5P5v2DyfzfsTAN1UXkfm/YPI/N+xMA2Ifs/5v2D7P8Am/YnAbNIPs/5v2DyPzfsT6DQ2IPI/N+weR+b9ibQaGxD5H5v2FWPv8X7EwqG6IHja/F+w3yfzfsWpehExtEmFxyyrFDzujfv07/xOgo8Dq6tTXI6/wCR/qMfjJ9OQj0HiJ9eOS2q43kfCX2GO/tvmf8AK1/iZceL6v57+7/md/zsOqhs5GHw2tFl2l8Mq3j/AC/5zf8AwkP2fv8Af/Y18qPYz32ZSGRwupfyn90ZPF6X9/8AYtVyHWx2TYo/Z/zfsOWN+f8AYm6RUmNiFYm/x/sJLF1+P9i1EVrY2KXkfm/YX7P+b9iz0i6Q2Iq8Hr/nNf8ACTriNr+X/uf5klL7l+H3SueWVjN/gf8A3/8Ac/zD+B/9/wD3P8zUArHesmXE6X8v/c/zIZ4PT/Ob/wCE2p+hTuXZkamVZbo09dX7Grxfh9cg0vtXl7/3e/8AEz7OzOh8NW9NsUS11iw/AGo7/hP/AKH+oxeT4B8e/wD3jzP+DX+J6bH4oL9DlfE9Xwt6Mdq3qOI8jv8Ae/YX7P8Am/Yl9x3sb2zVfyPzfsJ5H5v2J2INogdX1/YVU7/F+xKwQ2qPyPzfsPji9X4/2HofB9xtB/BvwdXm/wB3/Mgli9P4/wBjXh3qKN60xsip5H5v2D7P+b9iYBsRLH/N+wv2b8/7EiH+w2Kzo1+L9hPJ/N+xPL1EGw2nE82fT19P9RuYXhNZcd/buj/lb/xMqj4bEzteCluKQ2sYuR4L8mLf2/q/5Ov/AOjDy+MeNJx83q/4df4npeZHdbOK5qHTayS1u4xigABgoAhQABQCkAUAFQoiB+gQgCe4BSgGtgosAfoRssKPYZKAZGJLpuj+p6D4d6rqdQW+3c4CmKU0z1Lwmqlw9coa6394a2qry9bdEotaaOKsj0ZLR3vOW1OXSpLfT3OFy1/2ptFmNTKor47iZ046Zp2LcTOvTUvQ1cazs2PZk3qit1E1UtmfKmz0hux96aZCmFOchYyGaCPqBKyNtpki7obKIElD+I0a3tGXV2mjSpfYsc8olAAK5kfoVbkWivauxFxZty7mlwFnTkJfUoXom4mfRlIzXfF6fjvqpi/oYniOrdLZr8fLrxoP6FTm6+rHl+hzdHm81qT/AFEJMhatkvqRI6RihiCtCaKhGIhWhNBTkOj6iJD4ruQX6f5Iq5C7lih/DoiyF3KzFQUVoCNEQ/2GjkAyXqNHy9RmgJIPUkdb4fs20chH1Ok8P2asSBHX3R3XL9Dj+er09nZ/er/qOX5+v4GzP66/jkAACuZRUIKioUAQugGgOaDQNkEHBoBuhyiJ6C9RA9RQ5JEXWSVwna9RRrHG3xDZ+tjHBt60WY0OH3h+vkj04/Gt9s2q1dL6ts1MTksjCj00WOKKmg0enHgxxZ3U08q6yTlKyTb9e4zqb9RiFN6kNHbD4X6oaA1KCVNUvYZ9mhHvFj9iORm8eNNoL4dit06L77kFkPkefk4P4sqs12Gjp7XYYeTWmkkWTKO0V4eperjuAFXWpl+h9inNfEW8f0LGMlgAQFciENnoTshsXYhFC9bG4j6MiJLaiCt6ui/qZrvg9K4WzrxY/oScpX148v0M/wAP3LyEmzQzr4+TJJo5uuq84z4dGTJfUrpGjycerJeimoG9r9dqJoFEkaSBJDZ9ZnSHlt+xItImqcZPRm2umPFj+1DGp/IkjRL5F7yHCvq12K7uUXozuu318c91JTS0OnidfuMryG32Jp5HTHZf9mf/ABiOPG9RI+KaQ2nkemfc1sSx5S2kYtyenC8FY0+OaGLBZo8hf9ns6ZIqVZe5+hN5N9OC1BZgTS2ipZU4eqOkr1bHaM3ka1FMuOda5ficXXeNZPobPBWauSMXWzT4eXTkRO/4+PZqvQ6PipX6GHztW6pG3hfFjxZQ5mvdL7GK3PTznQobDZtyKKhoqCHegdQDGgpzYmxNBoKXY5dxqQ+IQySESJWhIwcpaXqJ5DsfHldPUUasKYY9eo+vzJKaVj0JP7z9SKye2fR4OKYzbnlTJvbIwkxNne1Ch6vS7sTZY462FXIUTmtxU1szarqeI8K1WYqszNucltJexR5bwvbj2znjS6qlBz7/AE9jpMTnMJRsrvtjTZVJpxn2MbM8Qfwj4gxMTjpOyvqcbWl2aZ4by5dmtORDZJl1PHzbqX61zaIj2Y3cZpdk1zxJY+JTRJvKsn0z36LZXb0UZwt83zK1JuHxbS9DHJbJ4HptXhHBjieXPcrNd5/U4nk8N4OfdjOSl0P1NzD8dv8Ag7y7a28mMddWuzMHLWVdFZ+RFqORJ6b9zz8fJl28tWeGfbXv0K7jpltkNsfc3zce/MSVHHsy/U9wM8u473E8kapsvvElNmnoit+8NT0ysVpp7QEFNm+xPsOdIxk/QexkvQJFW2I2vH6pbZO1uRexcR3LUTllXv8Aj8XabXeKskoqMWaF1Nk4PeypiU/ZrF1mwrYShpNGdx1uGX5HM24LsvaKfI4EsWKlrsdFGH/ayDxDW542kizSaznuOPlIa2TOib9hPss/ka3HP6uS/iLZa46KsyYxfzI1iz+Rb47HdeVFt+5Lli19HJPcdRnYkIcb1Jd9HF5D1e0jus9741rfscRco+dJiVLhSUSfUizb3h2KamoMV5BrbncDeh9Z2XhepOl9RydSc32Ov8PbhVpkrrjhFTxDiRnkrRlQwZbWjpM+HmZAuHhqdiTRh7frmOO6bx3GtY/VJGLzmLNXqMV6newpUaelIoX8fC23qkhpwvJb4ef1cZdL8Jp8dxNsLouSOvjgVR9kSxprh7GtuFxhMKt10pMg5OClUy25xj7lLPui6mthn08y0KKBpyIKgFAURioRlCAIBFOFTGCoB7l2NHi6FJedJdl6GbBOUlFerOhjX5GNCtey7no4MO1ZtR3SbZWkTTfcikfR9OSGQ1skkRsxVJsO/t6iNd+3cu4XHX5ViSi4x+bRjPOY+28cbl6a2byHEZmNXK/FdmQopNrt3IeO5VYDl/B/HxUn+J92auHwWLXFOS639TUrx6qlqFcV/UeDLmx34ejHh/ricmrMzsmy+VEuub29IauJz36Y8jvFWvZaF6Wif5WU9NfTi4OXDch/8tIkw6uR46yVleNvqXTJSjtNHauMn7sRVfMn+Tmv0YuQpzVi2u3I4mmTf5dEnN85TymDVjxxvJdctrv2R1jx65fegn+qKmXxGJfHUqI/qlpjHn1fMZy4Z+PP2MmtxZ0fKeHvL/jMRvS9Ys562Eq5dM4tNfM9mPLOSaefLjuKoyziy7EE/Umx3pnkymqH3/eGxWx+Su2yGuWmETR3BlmuzZFKPVHaI4S6ZBzq6Nn6BCSkgn6Bj0h69M6HgOmb0zmbexrcDl9FyTZzuL1cXNljNOh5SjUdxMpTsguzZ0GYnZTuPfsZCrblpo5XB9Xi+ZjjjqwmJKTtTZPyOp06ZJ5arhtIz8u2c4uKQmKZ/Jwy8qP2aK7jJwjElhXdL8I/7HbL1RLhXfH5mEniKnQ9bSKk73TfHt7nQVYb1poocnx3SutL0NY4aebm+Vll6aFl0r8B6/onGZLnG+SfzOvwZxeJ0++tGJyeKla5pHSPDbaxdyYqjJllxghF07NM6bPB48Zx+M63Cx4wh8JyfC2LqSOwxboxgk/cxk68eXlTuT880cGpLTKGZLok5E3FZTts6dnKPoc2X+kbctRhtvSM67PqhJpyRHz2Z9mxnp9zgsnkrZ2N9R0ktfN7advbzNUfRopW87H2ZxksuyX4hvnyfuXTNydRdzkn6Mo38tOxa2Y/W9eozrLpNowACsABRApUDARlQAAEUCoaCZRd42tWZla+uzaue5MyuGW8vb9kaVj7s9vxZ+sZIJkTJZkej1WsGSj2G10zusUK4uUmTRhKclGK3J9kjdphj8Pibu1581t/Q4cvJMY1jjscdxVWNqVqU7Pr6I2K4wiu+kcpk83Jy3BlDI5jLt7dctfJHzMsssr5e3CzGPQoOtL78dfqO8yr/vYf2nmM8zNsj0+ZPX0ZEnlN/wApP+0z1LnXq8ZVv7s4v9GK4nnGBLJhZFuyS/rO643JdtEFN7lr1JVm1zQukBR5HNjjUTe9PXYm1Nz+bwcB6unuXyXczJeMsBPSrsa/Q5PLhK2cpym3J/MrxxJS/GjUkYy3+Ott8U4Vku0ZJGfm3YPJp+VNRs9jHXF2yW1JDXx99T6k+6+RqXXpzu/1BkxcJafqmFEviQl6mn8fqMqepI627cau3rcCqtpl1rdZUf3yIs0y7aFsh7ojguksw1OJWKhqm4sst7RVsi4SHQsDNNuRHj3Om5Mls7oqWdmRcXofEZccnGUW++ibIpjF7RyPh/P8uxRbOusyFZR1I513MSUoaK8sWO9tENOZF2dLYuXmeXF6As111R9UiVypS9jnVybm2uor5OZa/uy2NLt0nnVeZpNFHmLIul6MTEvvlcttmjlJyp+L1K1tnU3un1fqTXdOTU9euiC+hut69SHDtdcuiZWNsrIjKu1xZF1M287E85dcF3Mp4lvXrpZosaXBdTtR01uQ6VEzuB46SSk0XOZx5xgukzl5awixkWK7G2n6oseH6mptszMFvy0pG1hXQp2/ocpNPZnydsNMzxdfqDimcRN7Og8TZLtuaTOdOseHK7CZJEYkSQRpIf8AhIvcll6ET9TKgNiAAoABQACFCEF0ApFJoNC7DZRo8P2yJf8AhL1j7lHh/wCXn/4S5N9z3fG/5c8jJDdDvUSfwwb9/Y726jMb3hvB64XZso9SgumC+pm8hRdk5E5X9u/ZHWcTj/YeCqrn2l09Uv1ZzvI29V02vmfK5uTdevjwZX2amn73d/IdFV+8YxX1KuRlqub6IuUx8uLyc3CeQ7Ftfg2co63x6NutxlZ0Rk2/oJCFVk+mEmpfJhg4V93I0Sy49EIaW9eyOi5HHwsm6Lx6NNNbku2y0x3b5ZCxbaZLqNvi7pVySbKjhJPy3Pr12X0LuLQ1o511sb0bVJI5rxJGyd8VDbizcgnFFfNqVle/dA05r7DVSlLJl8T/AAruQ3ZFFE7Ko405eXHqlqP3fqzdwNYdknOqFu3vcvVGbz+JPLy55GO/L82PTNJ+qNyud3+MiOfVKCceuEvdv0LNeRKa1Jp/oRww66cKyqyLlOX09CDEw7o2rXV0/Uvhz8/qPlI9PQ/mjPT7o1+Vqfw7XpEyNaZ0npwy9tKp9VZVt7WFnFXwEGTHU9lc9p61uJJX8MtEeL3giSa0yxipLIKaKk4uDLlb2ht1fUgzPCp17RBYSyj0sjmRuExbXTapbOu4vNV9PQ2cW3pmvwmQ4z1szXWNjy+jKbfzLWZVCeOmRXaep7I7LXKtxRGnO3T8vIlp+477Q9eombjzVzlrsyuoNGkrW4yzzLdv0Rr5Li6+xk8ZQ4Q38y5OXszKqNmTGNnTIhyEpx6oeqKvJxlG5SRJhW+Yul+ppnZaeRcfhmjSwJLIsWo7K0eIldcpr7pvY1ePhVJLW0RqRr4cI1QXZJlDnLUqZNFaXK/GkvQfkVPNpf1RGpWDhZsp2dK+Z0Fbap6mYmHgujI1Lt3NvIfRi6XyJprbluUs6733M5+pZyH1Wyf1IJI3HGmolgRaJoFIJvsRa7kkxqRFIACECgIBQuxdjRQFAAIAAFA0OI7Wzf5S5JlPiF1WWr8pZktPR7/jf8ueR0VtFziMP7fytdbW66vimVE+mtt+x0Pgulum/Ia/lJdv0Nc+eouE8t3Oi5U9K7I5+7j/ADJNt+p01sdopWV/Q+VZt7MXP/wVT7x/YfDjoLt3S/U1nT3BVGdu0kVfs8elJRGfZm38i/06H9KJtvWlGrDjF7S2y2q1CHU0Swgo936FTIyLL7VVTD4feRWKl83b7IWUeuAsaY1x3OcdlmEY9KfZl0dvDIprU7XCfZll4Si/TaG5vwZUZ1JN+6L9FitgtrT90ZYZ08Kt7+BFaWJGuW0jbcV8iG6EdegWuQ56hunrS9DmXrZ2vLw3jzXzRxNi6ZnbC+Hj5J5XcWfbQ3K9SPFl3LkKldbFS9Dbkiwq7Ja6Ytlu2qUV3WjrOJ4/Hhjp9KbKHOU1wTcVoRztjnq5dLJ13RVfaRPXLaKiDIr90UpxNO1biUbY9wuNVJRJ8GXl2p7GuL+QzfS9ma7R2OND7TR6+gz7NKuXf0KvA5e2otnS3UxnX1Ixt0jmM/pXfRTrjXbJJGhm0uTkkijg4tkcnujSVreUqcbq+hj2ZMlf0/M182+MKlD3MxUK2fUl6CBbqI219yDFwG7o9BPJy61E0KEqodTGzTRhWq8bpS76Mq+E3Y9+hcoy/Mn0k9tHXHaRFZEMeU5rXzOmwq+jGUdd9FGilQ127mrjJJdyEZMsGyzKcmtR2N5RxpxmtmlfkfG1FGHzFV16fSnoNVy9j6pt/UY0zVq4i+X4WXKuBsetxN7c7HOKLfsTQqm/SLOsp8PL3gXquEhFfcQ2sxcXHDtn+Fk0OLuf4TuK+Mrj7L+wnjh1R9kTa6jzBoaOY1sMABNhsqlFEFQAGxNC6ANibHdImiDQ4btbY/yl2S3Iz+Nl03S+qNH8R7/j3/VzyVuQsdeLqPrJ6PQPDuOqOHx9Lu4ps4SWNLNynTHv5cerR6Nw+lxuOvyI4c+W8tOmM1NpJogsiXrILRWsieWx2wyU5NLsNGX7jMYrGcq9WKdIckhlctokEKhy7FVQ3rt7mdk8jOFfRhUxsm122y/f8cWn6MyvsKqscq5td9l2vXwwLszm45EldVtJ9467GzTy1iogrI9Evdb9DSilZDU9N/NkcuPpnYptbLax10yo2Z3JZMpY8vIx4LvZNer+htcNK12WRssVnStdS92Q3w8xeSvhr36ItYThRHy4f1mbUq9N6RTutLV3xQ2jKyJtSZD8Z3MW/wARJ/Q5C6O+50XNW6x2vmzBkk4nbB5OT2ZjrUjTpglqRl1tqZo126gdHGtCHM3Y8eiL2RZGbZlL4zJnY5WFul7QjlYZZH3Erlpk8o7RXkullInl3iWuL4a3kLPRqHzK+HHzrYxfuzv+Jprx8SKhrb9SVm5dXMcn4ejjY/UvY5PJq6LGkd54p5OuuryYtORw9m5tv5iRrDK1Jxd3lZC7nbY9srsXa+RwVMZRui0vc7TiLW8dxaM16cVJ3RjkOElvZZnGuqnrUe5UyMaUs1NL3NG7GlLHUdEX9cnyOTKy7t7F7hd2z6X7l3+BHa9tdy5hcd9lkuxTSHN47ytWIoTlZJaUWdZOtXQ0yKvj6/kibVgYVFkZJtG9jw64JSRM8aFfokOoS6gkh1eCl8TG2arekWr7411GVZlwnL1I1U8aYye/cnhjwktSSIapx6OrZVyeWrpk0mQ21FTTH8KFTqi/Y5u3nV7MqT5qTfZlZ7OwldUvxIilm0x/EcdPlrJe5Xnn2y92XVOzsJ8rVH3K8uZr36nIyvsl+JjHZL5jSbUNMa4kgjZUM6Q6R2w2FJoA2BQAIwIF2GxBNASU2uFikjYxrY3uLT/UxNEtFsqZqSfodePPoljsOO4+zHoefWk7p/gfvE6XiclW0qPT0Nfh+RWcVGul1/cda0LSvKtjNfPucM8rctvX1nRtN7iQWEifYjmWuOM8szLX8YiLoetlvIh1tfQbCHw9znY9ONR1JolfaPcdGGiDKnqtpepldoZ27k0hr109yG2SrrTk9Mz8rkOlaiWNTy0ITUW/kMsy+l6UkjAXITuu8uEuqXy2LKi+diU7a4dS7bkXS3KRuQvhJ76lserF1bT7nLZdk8KajO6Mt/0HslxORfq5bQ0zbt19eSnuLKGbLUgxrY5Faa9RmcmobZnTlWJycPOqs766F1L6mJXLaOnxfL+0PzdOLj7mJk0R+22KiO4b7aO2LzZqfT8ZbgtwHRwbZ+kGXsfi7pLTTRtz0xZRasLmOa8eAnKXeJex+CUF3itjaXDbEUW/YZPGsl6RZ1dfFVr1SLUMCmK9CbScbkMLDvhapdLOjx55Ma+lbNGONVH2RIoQXyJtbxSuYu4S7MyPMtbeyeHhyteq2b7srh7jHl1L8SJ2dJjIyVwdNa309y1jY8a10pDsjkqY9upEFGfVOXaSJtVtYkXJS0TWQXl/oNrnv9CnymZ5FLaDSaqyMZaYzJyIQWznquTlZb6js7IbrfcrO2vVyMZS6UySzkFUtnL8fObsb29F66zq7Nk0LtvLOT0tl2rJ/iVIxYVwa2mmTxnJVtewFxZnnOUdmbdVar0190ovN+z5L7liXL19O99wbXr8t14+tmBkWSnPeyPL5B2vSfYpyub9zTK1+rE60U3ZL5idUiotu2KE85FVsAqw7tjXayHYBT9jWO0JogaA4QBBQFANBoA2AaDQbDYAAbADoOG8Q5Fd2PjZM08fajt+qR3Cp3Jae4PumeTvfsek+FM5Z3DVqT3ZT8EiWOmGV9NyP3RlhIvQbNEWe1SXqLFD5R7hFaI67NsWkUbWmaFi6oGbZ2skjGTWHlkclj33vcJaSMa3AunJKdkkvfS9Tq2u5VyauoSusjFr4rD18cJr6xl3LEOM4iFfxVX2S3+KYtvXVvS2VXmT3/JSf9RrbXXG+1ieJhacasZQi3vu9sipxKa/gjHtvYyNmRc30wcV9S/hY0m+qb2S1LJ+NHFrjGtaWiDkP5ItaaitehQ5CbWkZcMvCHAw45uRKMl2gtmtXxVEH91Gdw+RDHldKb11F+zlqoe6Okea1dhhUx/CiVU1RXZIwredgvRlWfO79JF8pt03VXH3SGyyakvvI5C/mLJejKz5GyXuyyVm119ufXD3RTs5eK9GczPLnL3IZXSl7jTPZ0s+cUUVrOffszAbkxrRepto38zdP0ZVfI5Evd/2lbqihFZHei6VJbkXT9ZMt8T1yyEttmdZaoo2/C0IW5acmhYR2OLUlXHfqV+RwIZFMl9C5a1Wl0taM7P5OGOtNmXVzy4p41jbkVcxy30pNmt/CdORLUtEWVbixh1LWwzqKlcoY9G3rZkZedJ2PpfYTOzeuTjAz3LbNM7aNHISi+77FyXLLy9JmE2JsmhYuudk3Ij2/mNTDZUKOGIeGoAQgAKxNgxNBC7DYmg0FSbFbEQ7W0RDRBdAGiAhQCAUBNgABsTYU4EJsEwlKzpPA+b5PKyx5P4b49v1RzZZ4zIeJyeNevwWJv8AQLHrafcd6kTfccpdjLpo2aI9rqa90StjGkpNkrcJ6lHLh0Tc36F7fcbdWrINNbMtS6ZnTtDXV1eo+T02vTQql2MO0qtOiD9URxw4N9olpvbJa9J62WNq0MWEfwEldKi+y0WHJIhnZruGKW2KjExeRmnYl8jQyMhKpttI53JyvNslJPsWR5+TLwp332eY1BtIilZbL1kydVdXcPI37nV5lRxk+7bFii15H1BY31NCu/QItN6LLxlr1I40JS9Qg8oVVE6jpBorF2iVaG2w1En0JJbWgnll277kCbUjVnTB+pH9nqTDWlG2LcR+DnW4VilHZclVW0QSpr2KsdBxXNzypuNjKviSyb6XF9iHh60shKPuavNYf8QpSMtuRVlsfRsbO+2S05MuShDehvl1lYZzUmL0v5F/y6xeiAVnuL+QnQy+4w+gmoFFRRYdLLiUA+AhFWMWP6GWF0CNxDSDoYdLJnKIKUQiLoYdBN1RDcQIekOkm3ETaAgiSL0I0SL0Ipr9RBWIABsAABGOQMoaCQoEACDYAOQJ6BDG9MpHrXH3faeMx7t7c603/YSxnr1Mfwdf53h+qL9a24mrac674+kre+6Eb7FP7Q62CzYN6l2JtrRbb5Rekhv2xxS2tkeTOLXVCSZTts+H10ZUuTfGW2nplZZfR2k+xWvluL7mVkWz36he2m88peqY77at76kcrLKtj27kcsufzZdH2adZLkIp/eIreSra+92OTlk2P8TE65y9ZMsxYy5dtPN5J3Sca38JFjVTtl9CDHolZLUUdDhYbqq20N6cvOTAusnXbKPsmQyy5IucjGMMuafv3KM64y9DcYvg15s/mEc6bfqQ2UPfYiUXFmkacciUo+pC75dfqMpe0JYtSCNSiTlDuPZDhv4SdlcbTPcAkIXTO6gubSKE7pqXqXr0UbYkblSRsbh6layySfqS1+hFd6sNxZ43PePapNmpyHOPIp6Ezmtipk00sO1tvuJ5jIkxSof5svmJ5r+Y1iaAc7GJ1saAVIpsd1MjRIgDqYdTF0BNLsm2HcUTZdGypsXqY3aDY0mzuphsZ1CdY0bTdI5DdipmWiMQViBQAgBCoGAFCAAEACAAHJCdI6PoEnoo7bwDZvCyqv6M0/7To8hdjj/AFr+2ZVe+zgmdlkfdMV1wZ81sr2V7RakNcNnJ6NMyyEktJ6KttVrXabNidW2M8hL1CWOftryNaUdlWeLkS/m5HVOmPqPhGK9hsmMrjXh5H/dS/sI/4PypelTO+jVCcfREWRTGuG9IbsLhHELicqT+6kT1cLdtdbOjjFSkSqBezncYp4HHQpiml3NC2rVXYlorJrY/AzFu1k04bxBW4XQml69jJ81o6XxFjuVDkvw9zlG9o74VwziyrU/UH0Mp9QvWbYXIpL0YklsrxsZIrAi/iy0Wk0zMqs79iaNzTLHLKLkhrI43p+o7rTKxoyxbRQuL8ilkL1DUR1sZekNhPUtDrO6I6RWYJC6ANFQ4aO2EpAAVLYCBoc1pDAmzhVIj2KDZ3UHUN0wUWAvUJ1D41Ni+UyptF1C9yVVD1UBXSbHxrZP5ehyiDaMAAw6gBA2AoDWxAp+wGoUqAAE0RQKhdBoB0QmuwRHP0DLd8DT6Obcf6VTO7yvunAeD3rxBT9Yy/wDI9Aye8SV1wUW+45dxsvUWJxeqBxIp7ROiOXqEqPvoTQ8OnYIfjyUZabIOQuUpRhF7G2QkvRkVNEp2dUgtPrhpEqiSuCS0LVHuRhPVHUUFq+ElS0iO77pBiclUrKZJr1OFyanTfOHyZ6HlRTgzj+XxXLIcoL2OmN045xhtCe5JNdMtPsxmztHGlQqG7DZUXcVJy7suTpjoy67Olk7ynrsw52pbF0+jI/O17kLtcl3GPbZRbV+/cZZJSRWWx/cCPTUh0n2HaGyWyNRExCToDyym0YqTZNGnZNGgJclXpfyHRiy2quwjr6WInZBKDaG+U/kW4pMlVewm2cq+/oTQo6iayrXfQkH0vuDZY4g/7MiVWpIZK5A8k8pRQySihk7iPqbCpOwoyJNBAt0ao7HxrJIxHqJWLky9CoAOb0hiCiAIAohQoqGipgOAQCLChsAAVMWTGoX17BGv4Sf/ALQ439f/AJHo1y3BnI+DuM8u/wC02R+LXwnXWv4WZrrioP1FiMk+7HRZyrvKlSGWQ13HxYs+6DStodFCS7MTr6Som6E0LGpRErti16izsUUQNmh9KK/VKc+3oXKlpEZPIbCVkNr7Cipc1pmFmVrzeo3LfQzMqG2GawOWworFlfGOmjAk9HWeIZ+XxKivWctHJWep3wefOEUhyIhVJxZ1c08e7LMKOpFavJUd7jstYWbFyataivYjFh6xvoOWP9C1GymX3Zpki6H+JFY3VNY6GTq0XpSqj2c47I7OhrakiG6oOInTslnrfZhH1C7R+WL5b+RZjFD1XsHZUSkh6k16lny1oR0p+wPaJWIJNNCyofsN8qSQNGKSTLEJrRTlGSewU5a0VVqyxFeVi3sb8TI3BkDncJ1tkbXcdEqlbBPuDGr1IqWJPWQ1otVxRWKlgiZRGRRLHQYsYewEFRzeoogogAAAVQCAUIBRNjXIaNng2R9QnVsaS1ImbfEcd1SjbdHa9kYDlppo6riM2F1MY7+JexnJ0w8ul4ySrkl6Iv3Wx01sycd7eyz3aMOn6RvbFj6keu4+KM11kTxZIviaS9yvH1LNEJSlGS7pP0EW3StdpZzxItSt1vpXyJXhRi1G2+uFj9ItlGm2C8f5Kk1t40Uv1KU/D+Vlc9l3ZinODlumSl2S9jp1jz/ZlWqseUMlUyaU36Fh4MnNw82DsS30b76KHK5Kr8S8PjQluzurP00aeXKqN+VZi1wlyNVPbfuvYdIXkyRYtcbq5WQsiowepN+xO46rc4zhOC9XF+hzvBUZN3g3PT3O6/qaS9W2WfDOFLifD19mbGVblBuUZv6C4xPsybFad0tR9PmVsyapyFRNrqlHqX1Kfh6yUfCkb8vIjTK2LUbJvWvkHLpS4XGzqciGRPEaU7YPe16Mz0a+zys5WLOnE896lH1evYpPHVmBdl9cVXX3Zc5Llnx8+K3qVGR8Fi+jXqJzNFOP4Y5KWO15VsHNL2RZhGbnXD+JsuNkqKIPaiupss1+CMy7HpseXjwlfFShCT7vts5l2Ss+KT2/Q9aps1Vw3/Y3fuuP8Yl/J/D6nSTTnldvPON8MZefyGVhdcKrcZbl1d9v5FXiuGu5Xlnx8JxrmurqlL0WjuOBhDH8a8vS7vMlOKkm33ZS8L8NnYni7JyL8eVdKc9Tfo9vsaZc9xfhfK5LLzKIXVV/ZJOM5S9BeU8K5XHYDzYX05FCepSrfodN4Rurlnc/a/jpcpSf1Xcj8WWRr8L0fwPXCPH3y3Z0r0KjC4zwll5eHHLyMirDol3jK192Qc3wGRxFEchZVeRjyelOuR1PiLEny3H8RfiVzvwoKPm11PvrsQ+IeH4jH8NWZNVN2NNP+LhZJ7b/AE2Q0yqfBWXZiU5Fudj1K2KklNv3KeB4cyc/lsjj6MmqXkLbsT+F/odzm2uvhuNceM+3/wAVHt076fhRkeDHvxPyTeMsX4P5JfhAxMzwvlcdRHN+0VZGMpJSlW96C3jpwirK/ig++zb8WWqvwvQ+IrhDj7pbs6fVNi8NXG7g6G3t9PcxndOnHjMvbnlFrs0SR9fQ178GLb0ifhOIeVnRlKP8VW9vfuZx5Nmfx9eWRRRO7IrpUWpTaS2dbDwnR9l1KcvO16+yZp53HUSh51VMVfDvBpe47LzZ4XG+bbHdij6Je50lcujz62vyrJ1y+9CTTI2lolulK22dkvWbbYzpK43e0Eqkxn2dFlxHdBSWqqo+grxk130XFX8wcEiLtlXYnfcZRK8qpwXddvma9lPUirOqdXp3XyZWpWexF6k2VV0dM4b6JfsyuiNp633Ldb7FKBcqfYJUyY9SIwQc6yhRAJp6NlANBphNgRseq5P2FWPJ+wNouoVbLMMR+6JFiF0m1LTG9LNGVUK18TWyhfbHeogMl2I3Ma5bGlVIpFnDypY1ynF+hSFT7iwl09B4vkYZMF0vv7mtGW0eaYebZjWKUJNfQ7HieXqyoKLmlP5HHLF6McpW0ltksYkVckyzA5V3hFDQTVsqnCmx1yfpJd9EvsS40V1Sm/SK2WJldTbE47hZYmZbl5F8r77PWUuzLOZj8hNL7LnWURftrY/kMqUb8HLg3HHvXTNPtp+xH4iyL6cXjljS1O2+MP12dNVy7Y2elLj+E+y5f2y++d+T/Tl7Fp8fZLk7M5ZE1OyHQ0n6I1OihZMcd3R85rfRvu0Q1X02ZV1Fc+qdMtSXyMXbW8KqYHG2YPGSxcfJnFt7U/dFW/gsvLXTnclbbU/WPps3bJVU0yuvsjXXH1k3oy/EOZOvD4+eFZ1effGKcfSSZZtm3GGchwsMzBx8LzZRx6fw/MbfxEFxVmDjzdVc18SXubLVfmKh2xV0ltQ33ZUdsZTsrTXVW9SXyJdtzrWTk8XZkQw1ZZKccT07epk+LuQcMRYVdsoqX3oJ9jr4vInxFzwXX9o6tQcvTf1Od8WYcOV5Hi8KmVX26a/jXH0S13OmMrhllPTlOGxsDI8xZ17q0vg0dN5GdPEjTxfNtwitKDa9CN+DsGdmTHH5BTnjwbshvun/AGGPicTc/D9/LQv6fJn0qKfqdHJPT4c5inJ+003fx+99al3ZLnWeLEvs11l8lJekfVk8ac7ja+Gy3mynHLsipQb9Ns6TPy74+N8DFhPVc6m2v7QPNqb83jndVCdlLsXTYvTZG83KWD9i86f2fq6ujfbZ6Dg8Zj8pyHKPMas8q5x2l3QmJwHCR5yuuE1OUV1+XL3/AKgrh8DmeR46LjiZU64v8KfYj5Dk83kpJ5mRO3Xom+yOx/8AVvj8/nORqhJQqpe5OL0oN+xQzPCmJXxmZnYedG+uheifuvVMbRjU+IeWophVVm2RhFaS+SK0OTzq8uzKhkzV9q1Oe+7OlxvCGK8LCycrOVKyUuzf4n6JdijyHhTJx+fp42ias86PVGTfovfYGJ9tyfsf2N3z+z9XV5e+2zqfCWVvAspk+8Jdv0G5/hCiOJfPjs2ORfjd7a+r0Mvwvf0ci699rFoznNxvjvl18lsu8dmxwqvLlH1e9opJd0P6d+x5d6r3de0bFfM0SepNx+WzO5fmuumyimrqUlrqZX8rfsEsdNd0b+xi8MYDi/dBo1rsPt2Rn20SrfdHTHN5c+D+IdBoUT3Ou3ks0dJ6k18hrHS+8/1G6CCMlv4l2G5FWm0nvttEsYb9eyEufVJ69NaRV2zrKt1WQf8AR6l+qM3p0b0ofxVkn7Q0ZFsUvRBuVHAs1leCLFRFqf2FSBDisVnKlsfHHZcjUkSKKC3LSrHG+hJHGXyLSQjaj6hO1qKNCQ9VxRBfmRrT0zPsz5t9mFktall1VfuijdyKXaBnWWyk+7I/UNTFLbfKyW2yLewANgAJKqbLpqNcXJkDEiWjGuvmo1VuTf0Oj4rw6oxV2c1GHybLeRymBxy6cWEZSXukUZuL4Zs0p5Vsak/ZlPlcWvjMmKx7/MTW+3sJncrk5lrlOxpeyRQnuXdvbGljd4zxLbj6heuuJ0+H4gw8iP31F/JnnGhYylF9mzFwldJyWPVoZcJrcZJr9SPleSycXjH/AAfX1ZEnpP1SPNqc/Ipe4WSX9ZoY/iPLq11S6kY6adLyTKarssyzPs8N3VZUI5GQ9ODh26WQZTzcvjeJ1Q1bRbGc238jKxfF6WvOg9fRGvi+KePtSTn0v6ol7ROuK9mZePi8vXlvjbrchx6XbB7SRT4ON8+X5HLtonTC6fVFS+RBleK8WnL8uOpw+aJL/F2BCHwScn8kh5pMZ/Wtyksezj7acrHnfU+/RB92c/n58svH47HwuPux1i3KSU/RJFPJ8Y935MP7TIyfEede3qzoX5TWMq5TH27LleZ4/js6OfZhSszejSkpdkchPxHkfbszKgumWRLfT6qJk3ZFt8uq2cpP6kRvr/XG3V8Ohq8USr8OZPHyrm77ZNq1S1rZS8L5F1PP4+RFqU0+/W/VPsZTH1SdclODakvdFjL0W7NxcLMzpYvE3u7KhLrsT2nJmRwl12DxWRg8nxtt2JdJy+B90YWPzmdjv4bpNfJmli+LMmMl5yUl+g0G87z6yr8GvGwp0Y2FKMoxn6vRJleLI3eJ8XlfsslCitwcOru9p+/9ZrU8vxGdD+OjCMvqgs43hclfA4Jv5MIx+L8RYtHIZ2Xk03wlkWdcXTZ3j9PqPzPF9dviDDz6sSSrx4uLUn8U9/Mbm+FrFJyw5KcP1MfJ4fNx38dMv6kBr8Z4rhi8xnX34zljZrfXBS7onyPE3E1cJm8fx2BbUr12bltNs5OdNkX3hJf1AoSa10v+wDo8vxNTlY3FUeS19jlGU9v10WszxfVZ4ixuQppkoU1uEot+uzjmnF6a0O9uwHZ2eJ+Kx6st8bgThkZafXOT7dyrwkeNx8eMrrI/aN7j9DlhU2mmKsenQipJND1Ag4mxZHHUWp76orZdUTyZe30cb4MhHRKopiaFXYypk61oz8vHTTZqS7lW6K0IxXPWR1IYvUvZdSi9opaPTx14OfDV8BghUKkdXmN2/R+gqjKXoSdhsnJrS9AiLKacPLg+y9X8zPtpbZpOC0N8tBZkzFQySNTRddaQ1xSK1ckCiO0PaECN2/hZRp60tGW8Oab+E7+1Rlja0ZtWJU5tOIdOrj51uHZ+pmcjOVfodtyPEpvqgjk+Zx+jqi13iYt03jxOfnY5MZ7itaYho1oBockOjFv2AZ0iqDfotmpg8TkZckowevmdHieH8fFh5mS129e4Vz3G8FkZrT6XGHzZ0Ucfj+Bx+u3UrdehDyXP1Y0PJw9du2zlsvLty7XO2TbAvcpzd+dJqLcK/ZIy29+vqIS4+PZkWquuLk2ERpOT0ltmtxnAZWc1KUXCHzZ0HEcBTiVq7LUer17kXNeIIY8XThNJrttEEHJcbxnG4LrlJSufucpZ09T6fQlvyLb7HO2bk38yEqkFE9BQAGgAIWK3GXzGd0OUtCrTfcgWFe1tvSE0kE220vZCFUoAJsBBUAuiAFEAocn8hysnH0k/7RgmwjRxeZzMb7lra+TNWnxZZpK+mMzmRQrtYc7xF0U7aEpe/YJcxwlP3aIt/RHEiPuBucr9i5ScZYEOm7fePzKK4POT/kXrZUqslTZGcHqSNuPia9U9LXxfMKzMzisrDUXdW0mVfKs7Polr9DbyfEMsvH8u2CbXuafGcnx32OuORCPVH6Eou+DrHPivLkmnXL3Og0ZuBl4Ntrjh6Ta20jTTPNyTy9fFdw3pE6R4Mw6omyKxJolkiOXoEZ2RX1GdbDpkbFqRRyIbRvG6cOTHcUtBoX0EckemV83KWUAI5L5jXZH5mkOYEbtS9yKV6XuDSaRHJkEslfMilkr5hZjVlyRG5oqTyCN3/UjXV0Ef/SJqHS+L3/8Acf6Rv/8AoKT2uL//AGP9JxIB3dtP/wBITnHT4xf/AJ/9JiZ3iBZkpN4vR1f7zf8AgYgEslamVh8rOp71oOv6DAKymjco+sN/1lvH5Cip7liOf/M1/gZwAdTX4xVNfRTx0YfXzf8ASZ2d4hys3akulP2UjHACV3Nvev3E838v7kYATwvhGSc63JfJS0bWB4kx8CGquMTl/Sd3+k54ANrkfEmXnP08uPyUjKle5PbW3+pEAEnm/T9w8z6EYAP8z6B5n0GABJ5n0E8z6DAAf5n0BWa9hgASeZ9A8z6EYASeZ9A8z6EYASeZ9A836fuRgBJ5v5f3Dzfy/uRgBJ5v5f3Dzfp+5GAEnm/T9w836fuRgBJ5v0/cPN+n7kYAP8z6B5n0GAA/r+geZ9P3GABpcTy0uMzFeqvMWtOPVrf7G+vHfb/4b/1/9JxwGbjL7bxzyx9OyXjzX+zf+v8A6Q/9fP8A6b/1/wDScaBPrxX7c/67B+Ot/wCzv+v/AKRr8b7/ANnf9b/SciA+vE+3P+uql4z6v9n/APW/0kNnixTX/uOv+b/kc2A6Yp9mVbc/EPU+2Lr/AJn+RE+cb/mP7/8AkZIGpNOdkvtqPmZf9z/f/wAhHy8n/Nf3v8jMAqdY0Hykn/N/3hj5Fv8Am/7xSAL1i285v8H7jXlt/h/crADUWPtT/o/uJ9of9H9yAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/2Q==\n", "text/html": ["\n", "        <iframe\n", "            width=\"800\"\n", "            height=\"480\"\n", "            src=\"https://www.youtube.com/embed/1ZwYlaqvCSw?rel=0\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7fe73425a4f0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import YouTubeVideo\n", "YouTubeVideo('1ZwYlaqvCSw', width=800, height=480, rel=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-04-16T22:45:30.374714Z", "start_time": "2018-04-16T22:45:29.257523Z"}, "id": "Z3CyOc4SjkBJ", "outputId": "0536bcd6-f47c-4677-cb99-6ac61dffef8d"}, "outputs": [{"data": {"image/jpeg": "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\n", "text/html": ["\n", "        <iframe\n", "            width=\"800\"\n", "            height=\"480\"\n", "            src=\"https://www.youtube.com/embed/tp_rP9C0ysY?rel=0\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7fe7341ac1f0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import YouTubeVideo\n", "YouTubeVideo('tp_rP9C0ysY', width=800, height=480, rel=0)"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "65e7f85e-289f-4ed1-b23b-3919db13bfd4"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 8, "row": 110, "width": 12}}}, "id": "T5a04Zp0jkBJ"}, "source": ["## [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) services\n", "\n", "The [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) (will) offer services for:\n", "\n", "- Clinical gait analysis  \n", "- Running biomechanics assessment  \n", "- Wheelchair propulsion assessment  \n", "- General motion capture  \n", "- ..."]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2018-04-16T22:46:28.188960Z", "start_time": "2018-04-16T22:46:27.232756Z"}, "nbpresent": {"id": "96e9ec4a-de32-4db9-a847-a9656124e87f"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 22, "row": 118, "width": 9}}}, "id": "wIO3sz9IjkBJ", "outputId": "acab79f2-ffde-4007-965f-239c2939ecd3"}, "outputs": [{"data": {"image/jpeg": "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\n", "text/html": ["\n", "        <iframe\n", "            width=\"800\"\n", "            height=\"480\"\n", "            src=\"https://www.youtube.com/embed/5ZKMVWkOyZA?rel=0\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7fe7341aa2b0>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import YouTubeVideo\n", "YouTubeVideo('5ZKMVWkOyZA', width=800, height=480, rel=0)"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "d2228b4a-2c23-451b-b128-e353bd9d7c64"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 7, "row": 140, "width": 12}}}, "id": "NQCyFqKYjkBJ"}, "source": ["## Open data science\n", "\n", "> Open science data is a type of open data focused on publishing observations and results of scientific activities available for anyone to analyze and reuse. [[wikipedia.org](https://en.wikipedia.org/wiki/Open_science_data)]\n", "\n", "**The [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) is committed to open science data.**  \n", "**[Access our data here](https://bmclab.pesquisa.ufabc.edu.br/datasets/).**"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "adb04f3a-f8af-43fe-87e6-3f0c76eb4eb4"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 7, "row": 147, "width": 12}}}, "id": "o-MXc9j0jkBJ"}, "source": ["### Open education\n", "\n", "> Open education is a collective term to describe institutional practices and programmatic initiatives that broaden access to the learning and training traditionally offered through formal education systems. [[wikipedia.org](https://en.wikipedia.org/wiki/Open_education)]\n", "\n", "**The [BMClab](https://bmclab.pesquisa.ufabc.edu.br/) is committed to open education.**  \n", "**[Access our GitHub repository here](https://github.com/demotu/BMC).**"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "54c30a3e-be3b-4935-a561-48ded4c2fd36"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 8, "row": 154, "width": 12}}}, "id": "WsymxjcojkBJ"}, "source": ["## Literate programming and literate computing\n", "\n", "> Literate programming: Instead of imagining that our main task is to instruct a computer what to do, let us concentrate rather on explaining to human beings what we want a computer to do. [[<PERSON> (1984)](http://www.literateprogramming.com/knuthweb.pdf)]  \n", "\n", "\n", "> Literate computing: A literate computing environment is one that allows users not only to execute commands **interactively** but also to store in a literate document format the results of these commands along with figures and free-form text that can include formatted mathematical expressions. [[<PERSON><PERSON> KJ and <PERSON> (2014)](https://osf.io/h9gsd/?action=download&version=1)]"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "350013a7-5873-4ceb-b956-cb1313b94b24"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 6, "row": 162, "width": 12}}}, "id": "qlQs5A2TjkBJ"}, "source": ["### Literate computing with [Jupyter Notebook](https://jupyter.org/)\n", "\n", "> The Jupyter Notebook is a web application that allows you to create and share documents that contain live code, equations, visualizations and explanatory text. [[jupyter.org](https://jupyter.org/)]\n", "\n", "See examples in [A gallery of interesting Jupyter Notebooks](https://github.com/jupyter/jupyter/wiki)"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "047118f9-96d6-4ad3-8a6c-7eb2940d5daf"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 7, "row": 175, "width": 12}}}, "id": "CIg1MutZjkBJ"}, "source": ["## Questions?\n", "\n", "> [https://bmclab.pesquisa.ufabc.edu.br/)  \n", "> E-mail: <EMAIL>   \n", "> Tel.: +55 11 2320-6435   \n", "> [Location Map](https://www.google.com.br/maps/place/Federal+University+of+ABC+-+UFABC/@-23.6803572,-46.5647898,14z/data=!4m2!3m1!1s0x0:0xf1a53d9732f7a8c6)"]}, {"cell_type": "markdown", "metadata": {"nbpresent": {"id": "5c704362-c2b8-425a-b153-a66533454d1f"}, "urth": {"dashboard": {"layout": {"col": 0, "height": 7, "row": 182, "width": 12}}}, "id": "NEcXgKC4jkBK"}, "source": ["## About these slides\n", "\n", "**This document (the webpage version or the slides version) is a *notebook* written using the [Jupyter Notebook](https://jupyter.org/).**\n", "\n", "> The Jupyter Notebook is a web application that allows you to create and share documents that contain live code, equations, visualizations and explanatory text. [[jupyter.org](https://jupyter.org/)]"]}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Slideshow", "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "urth": {"dashboard": {"cellMargin": 10, "defaultCellHeight": 20, "layout": "grid", "maxColumns": 12}}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}