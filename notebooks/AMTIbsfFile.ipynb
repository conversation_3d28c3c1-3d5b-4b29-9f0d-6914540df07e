{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Open AMTI .bsf file\n", "\n", "<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When using the [NetForce](http://www.amti.biz/NetForce.aspx) software to acquire data from [AMTI](http://www.amti.biz/) force platforms, the data is saved in a proprietary binary format with the extension .bsf, but the NetForce software has an option to export data to ASCII format. The open source software [b-tk Biomechanical ToolKit](https://code.google.com/p/b-tk/) can open a .bsf file, but it works only for the old file style of NetForce (verison 100) and the current style is version 105. In the new version, 105, the acquired data is saved as 8-byte doubles and already converted to engineering units, at least when the new AMTI amplifier is used and the full conditioned mode is selected in the AMTI configuration software. In version 100, the raw data from the analog-to-digital converter was saved as 2-byte integers. In the NetForce manual, AMTI provides the necessary information for anyone to write a code and extract all the data from .bsf files (although the manual still refers to .bsf file version 100).\n", "\n", "`AMTIbsf.py` (code at the end of this notebook) is a Python function to open .bsf files in the new style and extract the file metadata and data from force platforms. The function signature for its typical use is:\n", "```<PERSON>  \n", "from AMTIbsf import loadbsf\n", "data, mh, ih = loadbsf(filename, plot=1, axs=None)\n", "```\n", "Where:  \n", "- filename, plot, axs are: string with path and name of the file to be opened; option to plot the data; and option with the plot axes handle.  \n", "- data, mh, ih are: numpy array with all the data; object Main header; and object with all Instrument headers (iH[0], iH[1], ...).\n", "\n", "Or from command line:\n", "```\n", "python AMTIbsf.py filename\n", "```\n", "This code can also open the 'shfile' memory-mapped file by NetForce, just use `shfile` as the filename. \n", "\n", "Let's see this function in action, first the customary imports and customization for the Python environment:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "import seaborn as sns\n", "sns.set_context(\"notebook\", font_scale=1.2,\n", "                rc={\"lines.linewidth\": 2, \"lines.markersize\": 8, \"axes.titlesize\": 'x-large'})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In my setup, functions are in a specific diretory, which I have to add it to Python's path:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')\n", "\n", "from AMTIbsf import loadbsf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Openning a .bsf file with acquired data of one force platform and with the option to plot the data:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArkAAAGhCAYAAABlM6zfAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x85272e8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data, mh, ih = loadbsf(filename='./../data/AMTIdata.bsf', plot=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The headers (metadata from the .bsf file) are objects of the class AMTIbsf. You can visualize all the information from the headers typing for instance `mh` or ih[0]. Each information can be accessed as a propertiy of the header typing for instance `mh.rate`. The headers can also be acessed as dictionaries typing for instance `mh.__dict__`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["500"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mh.rate"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Main header:\n", "cmnfl : \n", "delayks : 0\n", "height : 0.0\n", "instHeadCount : 1\n", "name : <PERSON><PERSON>\n", "num_of_instrs : 0\n", "num_of_plats : 1\n", "numDatasets : 1000.0\n", "post_trig : 0\n", "pre_trig : 0\n", "protocol : Balance.pro\n", "rate : 500\n", "sex : \n", "size_header : 968\n", "sub_dob : \n", "test_by : <PERSON><PERSON>\n", "test_date : 8/11/2015\n", "test_type : Type:Walking Gait\n", "TNC : 6\n", "trigchan : 2\n", "trigmth : 1\n", "trigval : 0.0\n", "trl_lth : 2.0\n", "trl_num : 5\n", "trldscfl : Unknown\n", "units : 1\n", "version_number : 105\n", "weight : 0.0\n", "wtmth : 0\n", "zmth : 0\n"]}, {"data": {"text/plain": []}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["mh"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["{'MNC': 32,\n", " 'acqrange': array([ 634.9206543,  634.9206543,  634.9206543,  634.9206543,\n", "         634.9206543,  634.9206543]),\n", " 'ampgain': array([ 7086.60009766,  7480.29980469,  7874.        ,  8267.70019531,\n", "         8661.40039062,  9055.09960938]),\n", " 'chans': array([0, 1, 2, 3, 4, 5]),\n", " 'coord': array([ 1.,  1.,  1.,  1.,  0.,  1.,  1.,  1.,  0.,  0.,  1.,  1.,  0.,\n", "         0.,  0.,  1.]),\n", " 'data_end_chan': 5,\n", " 'data_strt_chan': 0,\n", " 'end_time': 0.0,\n", " 'end_val': 0.0,\n", " 'extvoltage': array([ 10.02647972,   9.99201298,  10.01435947,  10.0100193 ,\n", "         10.01329136,  10.02140045]),\n", " 'interdist': array([ 0.,  0.,  0.]),\n", " 'latency_period': 0.0,\n", " 'layout_num': 0,\n", " 'length': 0.5999480213165282,\n", " 'model': 'OPT400600-1000-STT',\n", " 'num_chans': 6,\n", " 'offset': array([ 0.,  0.,  0.]),\n", " 'post_trig_time': 0.0,\n", " 'rate': 0,\n", " 'sens': array([ 1.,  1.,  1.,  1.,  1.,  1.]),\n", " 'ser_num': 9538,\n", " 'size_header': 948,\n", " 'tr_end_chan': 5,\n", " 'tr_strt_chan': 0,\n", " 'trig_val': 0.0,\n", " 'trigger_time': 0.0,\n", " 'width': 0.40004999999999996,\n", " 'zero': array([0, 0, 0, 0, 0, 0]),\n", " 'zero_period': 0.0}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ih[0].__dict__"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function `AMTIbsf.py`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["# %load ./../functions/AMTIbsf.py\n", "\"\"\"Reads .bsf file (version 105) from AMTI NetForce software.\n", "\n", "    This module reads .bsf file (version 105) from AMTI NetForce software and\n", "    extracts the file metadata and data from force platforms.\n", "    The typical use of this module is, for example:\n", "    \n", "        from AMTIbsf import loadbsf\n", "        data, mh, ih = loadbsf(filename, plot=1, axs=None)\n", "    \n", "    Or from command line, for example:\n", "    \n", "        python AMTIbsf.py filename\n", "    \n", "    Where:\n", "    filename, plot, axs are: string with path and name of the file to be \n", "    opened; option to plot the data; and option with the plot axes handle.\n", "    data, mh, ih are: numpy array with all the data; object Main header; \n", "    and object with all Instrument headers (iH[0], iH[1], ...).\n", "    \n", "    This code can also open the 'shfile' memory-mapped file by NetForce.\n", "    \n", "    All the data is converted to SI units.\n", "\"\"\"\n", "\n", "__author__ = \"<PERSON>, https://github.com/demotu/BMC\"\n", "__version__ = \"1.0.1\"\n", "__license__ = \"MIT\"\n", "\n", "import sys\n", "import mmap\n", "from struct import unpack\n", "import numpy as np\n", "\n", "\n", "def loadbsf(filename, plot=1, axs=None):\n", "    \"\"\"Load .bsf file (version 105) from AMTI NetForce software.\n", "\n", "    Parameters\n", "    ----------\n", "    filename  : string\n", "        Path and name of the .bsf file (version 105) to be opened.\n", "    plot  : int or bool, optional (default = 1)\n", "        If 1 (True), plot data in matplotlib figure.\n", "    axs  : a matplotlib.axes.Axes instance, optional (default = None).\n", "\n", "    Returns\n", "    -------\n", "    data  : 2-D numpy array (possibly [Fx, Fy, Fz, Mx, My, Mz])\n", "        Data is expressed in engineering units (N and Nm for a force plate).\n", "    mh  : object\n", "        Object with all the information from the Main header.\n", "    ih  : object\n", "        Object with all the information from the Instrument header(s).\n", "        (ih[0], ih[1], ...)\n", "\n", "    Notes\n", "    -----\n", "    The headers are objects of the class AMTIbsf. You can visualize all the\n", "    information from the headers typing mh or ih[0]. Each information can be\n", "    accessed as a property of the header typing for instance mh.rate\n", "    The headers can also be accessed as dictionaries (useful for saving) typing\n", "    mh.__dict__\n", "\n", "    AMTI .bsf file has two versions: 100 (old) and 105 (new).\n", "    In the new version, 105, the acquired data is saved as 8-byte doubles and \n", "    already converted to engineering units, at least when the new AMTI amplifier \n", "    is used and the full conditioned mode is selected in the AMTI configuration\n", "    software. In version 100, the raw data from the analog-to-digital converter\n", "    was saved as 2-byte integers.\n", "\n", "    This code can also open the 'shfile' memory-mapped file by NetForce.\n", "\n", "    All the data is converted to SI units.\n", "\n", "    See the NetForce Users Manual from AMTI [1]_\n", "    See this IPython notebook [2]_\n", "\n", "    References\n", "    ----------\n", "    .. [1] http://www.amti.biz/NetForce.aspx\n", "    .. [2] https://github.com/demotu/BMC/blob/master/notebooks/AMTIbsfFile.ipynb\n", "    \n", "    Examples\n", "    --------\n", "    >>> from AMTIbsf import loadbsf\n", "    >>> data, mh, ih = loadbsf(filename='./../data/AMTIdata.bsf', plot=1)\n", "\n", "    \"\"\"\n", "\n", "    MNC = 32  # Maximum number of channels defined by AMTI\n", "    lbforce_per_N = 1.0/4.44822162  # AMTI conversion factor (version 105)\n", "    # this constant is derived from:\n", "    # g = 9.80665  # standard acceleration of free fall in m/s2 by ISO 80000-3:2006\n", "    # onelb = 0.45359237  # 1 lb in kg by International yard and pound\n", "\n", "    plot = int(plot)  # in case of command line input\n", "\n", "    if filename == 'shfile':  # memory-mapped file by NetForce\n", "        try:\n", "            # bug in Python mmap: file can't be opened with unknown size\n", "            # read at least up to the first instrument:\n", "            nbytes = 4 + 968 + 948\n", "            f = mmap.mmap(fileno=-1, length=nbytes, tagname='shfile')  \n", "            f.seek(0, 0)\n", "        except <PERSON><PERSON><PERSON><PERSON> as err:\n", "            print('{0} I/O error: {1}'.format(filename, err))\n", "            f.close()\n", "            return\n", "    else:  # file in the hard disk\n", "        try:\n", "            f = open(filename, 'rb')  \n", "        except <PERSON><PERSON><PERSON><PERSON> as err:\n", "            print('{0} I/O error: {1}'.format(filename, err))\n", "            f.close()\n", "            return\n", "\n", "    # read Main header\n", "    mh = ReadMainHeader(f)\n", "    \n", "    if filename == 'shfile':\n", "        try:\n", "            # try to open for all bytes in file:\n", "            nbytes = 4 + mh.size_header + 948*mh.instHeadCount + 8*int(mh.numDatasets*mh.TNC)\n", "            f = mmap.mmap(fileno=-1, length=nbytes, tagname='shfile')\n", "        except <PERSON><PERSON><PERSON><PERSON> as err:\n", "            pass\n", "        try:\n", "            # instrument header may have size < 948, do not try to open for all bytes yet:\n", "            nbytes = 4 + mh.size_header + 948*mh.instHeadCount + 4*int(mh.numDatasets*mh.TNC)\n", "            f = mmap.mmap(fileno=-1, length=nbytes, tagname='shfile')   \n", "        except <PERSON><PERSON><PERSON><PERSON> as err:\n", "            print('{0} I/O error: {1}'.format(filename, err))\n", "            f.close()\n", "            return\n", "\n", "    # read Instrument header\n", "    ih = []\n", "    f.seek(4 + mh.size_header, 0)  # advances file to the first instrument header\n", "    for i in range(mh.instHeadCount):\n", "        ih.append(ReadInstHeader(f, MNC, mh.TNC))\n", "        # go to the next instrument header\n", "        f.seek(4 + mh.size_header + ih[i].size_header - f.tell(), 1)     \n", "\n", "    # check the file size and adjust for the shfile:\n", "    current = f.tell()\n", "    f.seek(0, 2)\n", "    filesize = f.tell()\n", "    if filesize - current != 8*int(mh.numDatasets*mh.TNC):\n", "        if filename == 'shfile':  # open the file for all its bytes\n", "            try:\n", "                nbytes = current + 8*int(mh.numDatasets*mh.TNC)\n", "                f = mmap.mmap(fileno=-1, length=nbytes, tagname='shfile')\n", "            except:\n", "                print('Error: unnexpected number of bytes for data in %s.' %filename)\n", "                f.close()\n", "                return\n", "        else:\n", "            print('Error: unnexpected number of bytes for data in %s.' %filename)\n", "            f.close()\n", "            return\n", "    f.seek(current, 0)\n", "\n", "    # read data\n", "    try:\n", "        data = unpack('<'+int(mh.numDatasets*mh.TNC)*'d', f.read(int(mh.numDatasets*mh.TNC)*8))\n", "    except:\n", "        print('Error reading data in %s.' %filename)\n", "        f.close()\n", "        return\n", "    data = np.array(data).reshape((mh.numDatasets, mh.TNC))\n", "    # In NetForce file, data is always in Imperial units, scale factor for force platform:\n", "    scale = np.array([1, 1, 1, 0.0254, 0.0254, 0.0254]) / lbforce_per_N\n", "    for i in range(mh.num_of_plats):\n", "        # In the NetForce file version 105, raw data is already converted\n", "        data[:, ih[i].chans] = data[:, ih[i].chans] * scale\n", "\n", "    f.close()\n", "\n", "    if plot:\n", "        plotGRF(data, mh, ih, axs=None)\n", "\n", "    return data, mh, ih\n", "\n", "\n", "def deco(b):\n", "    \"\"\"Custom decoder for reading .bsf file from AMTI NetForce software.\"\"\"\n", "    return b.decode('latin1', errors='ignore').partition('\\x00')[0] \n", "\n", "\n", "def print_attr(classe, header='\\nHeader:'):\n", "    \"\"\"print class attributes (variables) and their values.\"\"\"\n", "    attributes = sorted([attr for attr in vars(classe) if not attr.startswith('__')], key=str.lower)\n", "    print(header)\n", "    for attr in attributes:\n", "        print(attr, ':', classe.__dict__[attr])\n", "\n", "\n", "def plotGRF(grf, mh, ih, axs=None):\n", "    \"\"\"Plot ground reaction forces of a .bsf file (version 105) from AMTI NetForce software.\n", "    \"\"\"\n", "    try:\n", "        import matplotlib.pyplot as plt\n", "    except ImportError:\n", "        print('matplotlib is not available.')\n", "        return\n", "\n", "    time = np.linspace(0, (grf.shape[0]-1)/mh.rate, grf.shape[0])\n", "    if axs is None:\n", "        fig, axs = plt.subplots(4, 2, figsize=(10, 6), sharex=True)\n", "        fig.subplots_adjust(left = 0.1, right = 0.96, top = 0.92, wspace = 0.25, hspace = 0.15)\n", "    axs = axs.flatten()\n", "    ylabel = ['Fx (N)', 'Fy (N)', 'Fz (N)',\n", "              'Mx (Nm)', 'My (Nm)', 'Mz (Nm)', 'COPx (m)', 'COPy (m)']  \n", "    for p in range(int(grf.shape[1]/6)):\n", "        cop = np.vstack((-grf[:, 4 + 6*p], grf[:, 3 + 6*p])/grf[:, 2 + 6*p]).T\n", "        for i, axi in enumerate(axs):\n", "            if i < 6:\n", "                axi.plot(time, grf[:, i + 6*p], label='FP %d'%p)\n", "            else:\n", "                axi.plot(time, cop[:, i - 6], label='FP %d'%p)\n", "            if p == 0:\n", "                axi.set_ylabel(ylabel[i])\n", "                axi.yaxis.set_major_locator(plt.MaxNLocator(4))\n", "                axi.yaxis.set_label_coords(-.16, 0.5)\n", "    if p:\n", "        axs[7].legend(loc='best', frameon=True, framealpha=.5)\n", "    axs[6].set_xlabel('Time (s)')\n", "    axs[7].set_xlabel('Time (s)')\n", "    title = 'GRF data'\n", "    if len(mh.name) and len(mh.test_type):\n", "        title = 'GRF data (%s, %s)' %(mh.name, mh.test_type)\n", "    plt.suptitle(title, fontsize='x-large')\n", "    #plt.tight_layout(h_pad=.1)  # doesn't work well with suptitle\n", "    plt.show()\n", "\n", "\n", "class ReadMainHeader:\n", "    \"\"\"Reader of the Main header of a .bsf file (version 105) from AMTI NetForce software.\n", "    \n", "    A class is used for the convenience of introspection of the header metadata.\n", "    \"\"\"\n", "    def __init__(self, f):\n", "\n", "        f.seek(0, 0)  # just in case\n", "        self.version_number = unpack('<i', f.read(4))[0]             # .bsf file version number\n", "        if self.version_number != 105:\n", "            print('Error: this function was written for NetForce file version 105;' \n", "                  ' the version of this file seems to be %d.' %self.version_number)\n", "            sys.exit(1)\n", "        self.size_header = unpack('<i', f.read(4))[0]                # Size of the structure in bytes\n", "        self.num_of_plats = unpack('<i', f.read(4))[0]               # Number of active platforms\n", "        self.num_of_instrs = unpack('<i', f.read(4))[0]              # Number of active instruments\n", "        name = deco(unpack('<100s', f.read(100))[0])                 # Subject's name\n", "        name = ' '.join(name.split(', ')[::-1]).strip()              # name is saved as 'Lastname, Firstname'\n", "        self.name = ' '.join(name.split())                           # remove multiple whitespaces\n", "        self.test_date = deco(unpack('<12s', f.read(12))[0 ])        # Test date\n", "        self.sub_dob = deco(unpack('<12s', f.read(12))[0])           # Subject's date of birth\n", "        self.weight = unpack('<d', f.read(8))[0]                     # Subject's weight\n", "        self.height = unpack('<d', f.read(8))[0]                     # Subject's height\n", "        self.sex = deco(unpack('<4c', f.read(4))[0])                 # Subject's sex\n", "        self.trl_num = unpack('<i', f.read(4))[0]                    # Number of trials\n", "        self.trl_lth = unpack('<d', f.read(8))[0]                    # Length of the trial in seconds\n", "        self.zmth = unpack('<i', f.read(4))[0]                       # Zero method\n", "        self.wtmth = unpack('<i', f.read(4))[0]                      # Weight method\n", "        self.delayks = unpack('<i', f.read(4))[0]                    # Delay after keystroke\n", "        self.trigmth = unpack('<i', f.read(4))[0]                    # Trigger method, 0:keystroke, 1:by chan, 2:external\n", "        self.trigchan = unpack('<i', f.read(4))[0]                   # Triggering platform\n", "        self.pre_trig = unpack('<i', f.read(4))[0]                   # Pre trigger values\n", "        self.post_trig = unpack('<i', f.read(4))[0]                  # Post trigger values\n", "        self.trigval = unpack('<d', f.read(8))[0]                    # Trigger value \n", "        f.seek(4, 1)                                                 # 4 unidentified bytes\n", "        self.rate = unpack('<i', f.read(4))[0]                       # Rate of acquisition\n", "        self.protocol = deco(unpack('<150s', f.read(150))[0])        # Protocol file used\n", "        self.test_type = deco(unpack('<200s', f.read(200))[0])       # Type of test, e.g., eyes open\n", "        self.cmnfl = deco(unpack('<150s', f.read(150))[0])           # A file name which contains comments\n", "        self.trldscfl = deco(unpack('<150s', f.read(150))[0])        # A file name with trial descriptions\n", "        self.test_by = deco(unpack('<100s', f.read(100))[0])         # Examiner's name\n", "        f.seek(2, 1)                                                 # 2 unidentified bytes\n", "        self.units = unpack('<i', f.read(4))[0]                      # Units where 0 is English and 1 is metric\n", "\n", "        self.instHeadCount = self.num_of_plats + self.num_of_instrs  # Instrument header count\n", "        self.numDatasets = self.trl_lth * self.rate                  # Number of data sets in the file\n", "        # Total number of channels:\n", "        TNC = 0                                  \n", "        for i in range(self.instHeadCount):\n", "            instsize_header = unpack('<l', f.read(4))[0]\n", "            f.seek(28, 1)\n", "            TNC += unpack('<l', f.read(4))[0]    # add number of channels in this instrumemt\n", "            if i < self.instHeadCount - 1:\n", "                f.seek(instsize_header - 40, 1)  # go to next instrument header\n", "            else:\n", "                f.seek(4 + self.size_header, 0)  # rewinds file to first instrument header\n", "        self.TNC = TNC\n", "\n", "    def __repr__(self):\n", "        print_attr(self, header='\\nMain header:')\n", "        return '\\n'\n", "\n", "\n", "class ReadInstHeader:\n", "    \"\"\"Reader of the Instrument header of a .bsf file (version 105) from AMTI NetForce software.\n", "    \n", "       In version 105, length is always in inch, do the conversion to meter.\n", "       WARNING: AMTI NetForce version 3.5.3 uses 39.4 instead of 39.37 to convert inch to meter.\n", "\n", "       A class is used for the convenience of introspection of the header metadata.\n", "    \"\"\"\n", "    def __init__(self, f, MNC, TNC):\n", "            \n", "        self.MNC = MNC                                                        # Maximum number of channels\n", "\n", "        self.size_header = unpack('<i', f.read(4))[0]                         # Size of the structure in bytes\n", "        self.ser_num = unpack('<i', f.read(4))[0]                             # Serial number of the instrument or platform\n", "        self.layout_num = unpack('<i', f.read(4))[0]                          # The layout or platform number\n", "        self.model = deco(unpack('<20s', f.read(20))[0])                      # Name of the instrument\n", "        self.num_chans = unpack('<i', f.read(4))[0]                           # Number of channels\n", "        self.tr_strt_chan = unpack('<i', f.read(4))[0]                        # True start channel, depends on hardware setup\n", "        self.tr_end_chan = unpack('<i', f.read(4))[0]                         # True end channel, depends on hardware setup\n", "        self.data_strt_chan = unpack('<i', f.read(4))[0]                      # Start channel in the data file\n", "        self.data_end_chan = unpack('<i', f.read(4))[0]                       # End channel in the data file\n", "        self.length = unpack('<f', f.read(4))[0] * 0.0254                     # Length of the platform\n", "        self.width = unpack('<f', f.read(4))[0] * 0.0254                      # Width of the platform\n", "        self.offset = np.array(unpack('<fff', f.read(3*4))) / 39.4            # Instrument x,y,z offset\n", "        self.sens = np.array(unpack('<'+MNC*'f', f.read(MNC*4))[:TNC])        # Sensitivities (possible 32 channels)\n", "        self.chans = np.array(unpack('<'+MNC*'i', f.read(MNC*4))[:TNC])       # Channel numbers (possible 32 channels)\n", "        self.coord = np.array(unpack('<'+16*'f', f.read(16*4)))               # Coordinate transformation\n", "        self.interdist = np.array(unpack('<fff', f.read(3*4))) / 39.4         # x,y,z distances from the previous platform\n", "        self.ampgain = np.array(unpack('<'+MNC*'f', f.read(MNC*4))[:TNC])     # Amplifier Gains (possible 32 channels)   \n", "        self.extvoltage = np.array(unpack('<'+MNC*'f', f.read(MNC*4))[:TNC])  # Excitation voltage\n", "        self.acqrange = np.array(unpack('<'+MNC*'f', f.read(MNC*4))[:TNC])    # Acquisition card range (bits/volt)\n", "        self.zero_period = unpack('<f', f.read(4))[0]                         # Tz\n", "        self.latency_period = unpack('<f', f.read(4))[0]                      # Tl\n", "        self.trigger_time = unpack('<f', f.read(4))[0]                        # Ttrig\n", "        self.end_time = unpack('<f', f.read(4))[0]                            # Tend\n", "        self.post_trig_time = unpack('<f', f.read(4))[0]                      # Tpost\n", "        self.zero = np.array(unpack('<'+MNC*'i', f.read(MNC*4))[:TNC])        # Zero values\n", "        self.rate = unpack('<i', f.read(4))[0]                                # Data set interval   \n", "        self.trig_val = unpack('<f', f.read(4))[0]                            # Trigger value\n", "        self.end_val = unpack('<f', f.read(4))[0]                             # End value\n", "\n", "    def __repr__(self):\n", "        print_attr(self, header='\\nInstrument header:')\n", "        return '\\n'\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    loadbsf(*sys.argv[1:])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.4.3"}}, "nbformat": 4, "nbformat_minor": 0}