{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Scalar and vector\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}, "toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Scalar\" data-toc-modified-id=\"Scalar-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Scalar</a></span><ul class=\"toc-item\"><li><span><a href=\"#Scalar-operations-in-Python\" data-toc-modified-id=\"Scalar-operations-in-Python-2.1\"><span class=\"toc-item-num\">2.1&nbsp;&nbsp;</span>Scalar operations in Python</a></span></li></ul></li><li><span><a href=\"#Vector\" data-toc-modified-id=\"Vector-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Vector</a></span><ul class=\"toc-item\"><li><span><a href=\"#Magnitude-(length-or-norm)-of-a-vector\" data-toc-modified-id=\"Magnitude-(length-or-norm)-of-a-vector-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>Magnitude (length or norm) of a vector</a></span></li><li><span><a href=\"#Vecton-addition-and-subtraction\" data-toc-modified-id=\"Vecton-addition-and-subtraction-3.2\"><span class=\"toc-item-num\">3.2&nbsp;&nbsp;</span>Vecton addition and subtraction</a></span></li></ul></li><li><span><a href=\"#Dot-product\" data-toc-modified-id=\"Dot-product-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Dot product</a></span></li><li><span><a href=\"#Vector-product\" data-toc-modified-id=\"Vector-product-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Vector product</a></span><ul class=\"toc-item\"><li><span><a href=\"#Gram–Schmidt-process\" data-toc-modified-id=\"Gram–Schmidt-process-5.1\"><span class=\"toc-item-num\">5.1&nbsp;&nbsp;</span>Gram–Schmidt process</a></span></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Python handles very well all mathematical operations with numeric scalars and vectors and you can use [Sympy](http://sympy.org) for similar stuff but with abstract symbols. Let's briefly review scalars and vectors and show how to use Python for numerical calculation.  \n", "\n", "For a review about scalars and vectors, see chapter 2 of [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.148480Z", "start_time": "2020-09-25T18:14:33.145872Z"}}, "outputs": [], "source": ["from IPython.display import IFrame\n", "import math\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## <PERSON><PERSON><PERSON>\n", "\n", ">A **scalar** is a one-dimensional physical quantity, which can be described by a single real number.  \n", "For example, time, mass, and energy are examples of scalars.\n", "\n", "### Scalar operations in Python\n", "\n", "Simple arithmetic operations with scalars are indeed simple:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.156785Z", "start_time": "2020-09-25T18:14:33.152486Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = 2 , b = 3\n", "a + b = 5\n", "a - b = -1\n", "a * b = 6\n", "a / b = 0.6666666666666666\n", "a ** b = 8\n", "sqrt(b) = 1.7320508075688772\n"]}], "source": ["a = 2\n", "b = 3\n", "print('a =', a, ', b =', b)\n", "print('a + b =', a + b)\n", "print('a - b =', a - b)\n", "print('a * b =', a * b)\n", "print('a / b =', a / b)\n", "print('a ** b =', a ** b)\n", "print('sqrt(b) =', math.sqrt(b))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["If you have a set of numbers, or an array, it is probably better to use Numpy; it will be faster for large data sets, and combined with Scipy, has many more mathematical funcions."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.164182Z", "start_time": "2020-09-25T18:14:33.159233Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = 2 , b = [3 4 5 6 7 8]\n", "a + b = [ 5  6  7  8  9 10]\n", "a - b = [-1 -2 -3 -4 -5 -6]\n", "a * b = [ 6  8 10 12 14 16]\n", "a / b = [0.66666667 0.5        0.4        0.33333333 0.28571429 0.25      ]\n", "a ** b = [  8  16  32  64 128 256]\n", "np.sqrt(b) = [1.73205081 2.         2.23606798 2.44948974 2.64575131 2.82842712]\n"]}], "source": ["a = 2\n", "b = [3, 4, 5, 6, 7, 8]\n", "b = np.array(b)\n", "print('a =', a, ', b =', b)\n", "print('a + b =', a + b)\n", "print('a - b =', a - b)\n", "print('a * b =', a * b)\n", "print('a / b =', a / b)\n", "print('a ** b =', a ** b)\n", "print('np.sqrt(b) =', np.sqrt(b))  # use numpy functions for numpy arrays"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<PERSON><PERSON><PERSON> performs the arithmetic operations of the single number in `a` with all the numbers of the array `b`. This is called broadcasting in computer science.   \n", "Even if you have two arrays (but they must have the same size), <PERSON><PERSON>y handles for you:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.169496Z", "start_time": "2020-09-25T18:14:33.165541Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = [1 2 3] , b = [4 5 6]\n", "a + b = [5 7 9]\n", "a - b = [-3 -3 -3]\n", "a * b = [ 4 10 18]\n", "a / b = [0.25 0.4  0.5 ]\n", "a ** b = [  1  32 729]\n"]}], "source": ["a = np.array([1, 2, 3])\n", "b = np.array([4, 5, 6])\n", "print('a =', a, ', b =', b)\n", "print('a + b =', a + b)\n", "print('a - b =', a - b)\n", "print('a * b =', a * b)\n", "print('a / b =', a / b)\n", "print('a ** b =', a ** b)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Vector\n", "\n", ">A **vector** is a quantity with magnitude (or length) and direction expressed numerically as an ordered list of values according to a coordinate reference system.  \n", "For example, position, force, and torque are physical quantities defined by vectors.\n", "\n", "For instance, consider the position of a point in space represented by a vector:  \n", "<br>\n", "<figure><img src=\"./../images/vector3D.png\" width=300/><figcaption><center><i>Figure. Position of a point represented by a vector in a Cartesian coordinate system.</i></center></figcaption></figure>  \n", "\n", "\n", "The position of the point (the vector) above can be represented as a tuple of values:\n", "\n", "$$ (x,\\: y,\\: z) \\; \\Rightarrow \\; (1, 3, 2) $$ \n", "\n", "or in matrix form:\n", "\n", "$$ \\begin{bmatrix} x \\\\y \\\\z \\end{bmatrix} \\;\\; \\Rightarrow  \\;\\; \\begin{bmatrix} 1 \\\\3 \\\\2 \\end{bmatrix}$$\n", "\n", "We can use the <PERSON>umpy array to represent the components of vectors.   \n", "For instance, for the vector above is expressed in Python as:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.172492Z", "start_time": "2020-09-25T18:14:33.170590Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = [1 3 2]\n"]}], "source": ["a = np.array([1, 3, 2])\n", "print('a =', a)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Exactly like the arrays in the last example for scalars, so all operations we performed will result in the same values, of course.   \n", "However, as we are now dealing with vectors, now some of the  operations don't make sense. For example, for vectors there are no multiplication, division, power, and square root in the way we calculated.\n", "\n", "A vector can also be represented as:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} = a_x\\hat{\\mathbf{i}} + a_y\\hat{\\mathbf{j}} + a_z\\hat{\\mathbf{k}} $$  \n", "</span>\n", "<br>\n", "<figure><img src=\"./../images/vector3Dijk.png\" width=300/><figcaption><center><i>Figure. A vector representation in a Cartesian coordinate system. The versors <span class=\"notranslate\"> $\\hat{\\mathbf{i}},\\, \\hat{\\mathbf{j}},\\, \\hat{\\mathbf{k}}\\,$ </span> are usually represented in the color sequence <b>rgb</b> (red, green, blue) for easier visualization.</i></center></figcaption></figure>\n", "\n", "Where <span class=\"notranslate\"> $\\hat{\\mathbf{i}},\\, \\hat{\\mathbf{j}},\\, \\hat{\\mathbf{k}}\\,$ </span> are unit vectors, each representing a direction and <span class=\"notranslate\"> $ a_x\\hat{\\mathbf{i}},\\: a_y\\hat{\\mathbf{j}},\\: a_z\\hat{\\mathbf{k}} $ </span> are the vector components of the vector <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span>.\n", "\n", "A unit vector (or versor) is a vector whose length (or norm) is 1.   \n", "The unit vector of a non-zero vector <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> is the unit vector codirectional with <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span>:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{\\hat{u}} = \\frac{\\overrightarrow{\\mathbf{a}}}{||\\overrightarrow{\\mathbf{a}}||} = \\frac{a_x\\,\\hat{\\mathbf{i}} + a_y\\,\\hat{\\mathbf{j}} + a_z\\, \\hat{\\mathbf{k}}}{\\sqrt{a_x^2+a_y^2+a_z^2}} $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Magnitude (length or norm) of a vector\n", "\n", "The magnitude (length) of a vector is often represented by the symbol $||\\;||$, also known as the norm (or Euclidean norm) of a vector and it is defined as:\n", "\n", "<span class=\"notranslate\">\n", "$$ ||\\overrightarrow{\\mathbf{a}}|| = \\sqrt{a_x^2+a_y^2+a_z^2} $$\n", "</span>\n", "The function `numpy.linalg.norm` calculates the norm:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.178718Z", "start_time": "2020-09-25T18:14:33.173437Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["3.7416573867739413"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1, 2, 3])\n", "np.linalg.norm(a)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["Or we can use the definition and compute directly:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.183016Z", "start_time": "2020-09-25T18:14:33.180543Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["3.7416573867739413"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sqrt(np.sum(a*a))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Then, the versor for the vector <span class=\"notranslate\"> $ \\overrightarrow{\\mathbf{a}} = (1, 2, 3) $ </span> is:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.186129Z", "start_time": "2020-09-25T18:14:33.183859Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["u = [0.26726124 0.53452248 0.80178373]\n"]}], "source": ["a = np.array([1, 2, 3])\n", "u = a/np.linalg.norm(a)\n", "print('u =', u)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["And we can verify its magnitude is indeed 1:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.189322Z", "start_time": "2020-09-25T18:14:33.187221Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(u)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["But the representation of a vector as a tuple of values is only valid for a vector with its origin coinciding with the origin $ (0, 0, 0) $ of the coordinate system we adopted.\n", "For instance, consider the following vector:  \n", "<br>\n", "<figure><img src=\"./../images/vector2.png\" width=260/><figcaption><center><i>Figure. A vector in space.</i></center></figcaption></figure>\n", "\n", "Such a vector cannot be represented by <span class=\"notranslate\">$ (b_x, b_y, b_z) $</span> because this would be for the vector from the origin to the point B. To represent exactly this vector we need the two vectors <span class=\"notranslate\"> $ \\mathbf{a} $ </span> and <span class=\"notranslate\"> $ \\mathbf{b} $ </span>. This fact is important when we perform some calculations in Mechanics."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Vecton addition and subtraction"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["The addition of two vectors is another vector:\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} + \\overrightarrow{\\mathbf{b}} = (a_x\\hat{\\mathbf{i}} + a_y\\hat{\\mathbf{j}} + a_z\\hat{\\mathbf{k}}) + (b_x\\hat{\\mathbf{i}} + b_y\\hat{\\mathbf{j}} + b_z\\hat{\\mathbf{k}}) = \n", "(a_x+b_x)\\hat{\\mathbf{i}} + (a_y+b_y)\\hat{\\mathbf{j}} + (a_z+b_z)\\hat{\\mathbf{k}} $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["<figure><img src=\"http://upload.wikimedia.org/wikipedia/commons/2/28/Vector_addition.svg\" width=300 alt=\"Vector addition\"/><figcaption><center><i>Figure. Vector addition (image from Wikipedia).</i></center></figcaption></figure> "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The subtraction of two vectors is also another vector:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} - \\overrightarrow{\\mathbf{b}} = (a_x\\hat{\\mathbf{i}} + a_y\\hat{\\mathbf{j}} + a_z\\hat{\\mathbf{k}}) + (b_x\\hat{\\mathbf{i}} + b_y\\hat{\\mathbf{j}} + b_z\\hat{\\mathbf{k}}) = \n", "(a_x-b_x)\\hat{\\mathbf{i}} + (a_y-b_y)\\hat{\\mathbf{j}} + (a_z-b_z)\\hat{\\mathbf{k}} $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["<figure><img src=\"http://upload.wikimedia.org/wikipedia/commons/2/24/Vector_subtraction.svg\" width=160 alt=\"Vector subtraction\"/><figcaption><center><i>Figure. Vector subtraction (image from Wikipedia).</i></center></figcaption></figure></div>  "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Consider two 2D arrays (rows and columns) representing the position of two objects moving in space. The columns represent the vector components and the rows the values of the position vector in different instants.   \n", "Once again, it's easy to perform addition and subtraction with these vectors:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.194357Z", "start_time": "2020-09-25T18:14:33.190817Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = [[1 2 3]\n", " [1 1 1]] \n", "b = [[4 5 6]\n", " [7 8 9]]\n", "a + b = [[ 5  7  9]\n", " [ 8  9 10]]\n", "a - b = [[-3 -3 -3]\n", " [-6 -7 -8]]\n"]}], "source": ["a = np.array([[1, 2, 3], [1, 1, 1]])\n", "b = np.array([[4, 5, 6], [7, 8, 9]])\n", "print('a =', a, '\\nb =', b)\n", "print('a + b =', a + b)\n", "print('a - b =', a - b)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<PERSON><PERSON><PERSON> can handle a N-dimensional array with the size limited by the available memory in your computer.\n", "\n", "And we can perform operations on each vector, for example, calculate the norm of each one.   \n", "First let's check the shape of the variable `a` using the method `shape` or the function `numpy.shape`:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.198858Z", "start_time": "2020-09-25T18:14:33.195818Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2, 3)\n", "(2, 3)\n"]}], "source": ["print(a.shape)\n", "print(np.shape(a))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["This means the variable `a` has 2 rows and 3 columns.   \n", "We have to tell the function `numpy.norm` to calculate the norm for each vector, i.e., to operate through the columns of the variable `a` using the paraneter `axis`:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.203908Z", "start_time": "2020-09-25T18:14:33.200760Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["array([3.74165739, 1.73205081])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(a, axis=1)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Dot product\n", "\n", "Dot product (or scalar product or inner product) between two vectors is a mathematical operation algebraically defined as the sum of the products of the corresponding components (maginitudes in each direction) of the two vectors. The result of the dot product is a single number (a scalar).  \n", "The dot product between vectors <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> and $\\overrightarrow{\\mathbf{b}}$ is:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} \\cdot \\overrightarrow{\\mathbf{b}} = (a_x\\,\\hat{\\mathbf{i}}+a_y\\,\\hat{\\mathbf{j}}+a_z\\,\\hat{\\mathbf{k}}) \\cdot (b_x\\,\\hat{\\mathbf{i}}+b_y\\,\\hat{\\mathbf{j}}+b_z\\,\\hat{\\mathbf{k}}) = a_x b_x + a_y b_y + a_z b_z $$\n", "</span>\n", "\n", "Because by definition:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\hat{\\mathbf{i}} \\cdot \\hat{\\mathbf{i}} = \\hat{\\mathbf{j}} \\cdot \\hat{\\mathbf{j}} = \\hat{\\mathbf{k}} \\cdot \\hat{\\mathbf{k}}= 1 \\quad \\text{and} \\quad \\hat{\\mathbf{i}} \\cdot \\hat{\\mathbf{j}} = \\hat{\\mathbf{i}} \\cdot \\hat{\\mathbf{k}} = \\hat{\\mathbf{j}} \\cdot \\hat{\\mathbf{k}} = 0 $$\n", "</span>\n", "\n", "The geometric equivalent of the dot product is the product of the magnitudes of the two vectors and the cosine of the angle between them:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} \\cdot \\overrightarrow{\\mathbf{b}} = ||\\overrightarrow{\\mathbf{a}}||\\:||\\overrightarrow{\\mathbf{b}}||\\:cos(\\theta) $$\n", "</span>\n", "\n", "\n", "Which is also equivalent to state that the dot product between two vectors $\\overrightarrow{\\mathbf{a}}$ and $\\overrightarrow{\\mathbf{b}}$ is the magnitude of $\\overrightarrow{\\mathbf{a}}$ times the magnitude of the component of $\\overrightarrow{\\mathbf{b}}$ parallel to $\\overrightarrow{\\mathbf{a}}$ (or the magnitude of $\\overrightarrow{\\mathbf{b}}$ times the magnitude of the component of $\\overrightarrow{\\mathbf{a}}$ parallel to $\\overrightarrow{\\mathbf{b}}$)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["The dot product between two vectors can be visualized in this interactive animation:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.207047Z", "start_time": "2020-09-25T18:14:33.204984Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"500\"\n", "            src=\"https://www.geogebra.org/classic/ncdf2jsw?embed\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x7fd2740b4ee0>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["IFrame('https://www.geogebra.org/classic/ncdf2jsw?embed',\n", "       width='100%', height=500)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The Numpy function for the dot product is `numpy.dot`:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.211082Z", "start_time": "2020-09-25T18:14:33.208103Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = [1 2 3] \n", "b = [4 5 6]\n", "np.dot(a, b) = 32\n"]}], "source": ["a = np.array([1, 2, 3])\n", "b = np.array([4, 5, 6])\n", "print('a =', a, '\\nb =', b)\n", "print('np.dot(a, b) =', np.dot(a, b))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Or we can use the definition and compute directly:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.215843Z", "start_time": "2020-09-25T18:14:33.212582Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["32"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(a*b)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["For 2D arrays, the `numpy.dot` function performs matrix multiplication rather than the dot product; so let's use the `numpy.sum` function:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.221604Z", "start_time": "2020-09-25T18:14:33.217193Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["array([32, 24])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([[1, 2, 3], [1, 1, 1]])\n", "b = np.array([[4, 5, 6], [7, 8, 9]])\n", "np.sum(a*b, axis=1)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Vector product\n", "\n", "Cross product or vector product between two vectors is a mathematical operation in three-dimensional space which results in a vector perpendicular to both of the vectors being multiplied and a length (norm) equal to the product of the perpendicular components of the vectors being multiplied (which is equal to the area of the parallelogram that the vectors span).   \n", "The cross product between vectors <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> and <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span> is:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} \\times \\overrightarrow{\\mathbf{b}} = (a_x\\,\\hat{\\mathbf{i}} + a_y\\,\\hat{\\mathbf{j}} + a_z\\,\\hat{\\mathbf{k}}) \\times (b_x\\,\\hat{\\mathbf{i}}+b_y\\,\\hat{\\mathbf{j}}+b_z\\,\\hat{\\mathbf{k}}) = (a_yb_z-a_zb_y)\\hat{\\mathbf{i}} + (a_zb_x-a_xb_z)\\hat{\\mathbf{j}}+(a_xb_y-a_yb_x)\\hat{\\mathbf{k}} $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Because by definition:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\begin{array}{l l}\n", "\\hat{\\mathbf{i}} \\times \\hat{\\mathbf{i}} = \\hat{\\mathbf{j}} \\times \\hat{\\mathbf{j}} = \\hat{\\mathbf{k}} \\times \\hat{\\mathbf{k}} = 0 \\\\\n", "\\hat{\\mathbf{i}} \\times \\hat{\\mathbf{j}} = \\hat{\\mathbf{k}}, \\quad \\hat{\\mathbf{k}} \\times \\hat{\\mathbf{k}} = \\hat{\\mathbf{i}}, \\quad \\hat{\\mathbf{k}} \\times \\hat{\\mathbf{i}} = \\hat{\\mathbf{j}} \\\\\n", "\\hat{\\mathbf{j}} \\times \\hat{\\mathbf{i}} = -\\hat{\\mathbf{k}}, \\quad \\hat{\\mathbf{k}} \\times \\hat{\\mathbf{j}}= -\\hat{\\mathbf{i}}, \\quad \\hat{\\mathbf{i}} \\times \\hat{\\mathbf{k}} = -\\hat{\\mathbf{j}}\n", "\\end{array} $$\n", "</span>\n", "\n", "The direction of the vector resulting from the cross product between the vectors <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> and <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span> is given by the right-hand rule."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The geometric equivalent of the cross product is:\n", "\n", "The geometric equivalent of the cross product is the product of the magnitudes of the two vectors and the sine of the angle between them:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} \\times \\overrightarrow{\\mathbf{b}} = ||\\overrightarrow{\\mathbf{a}}||\\:||\\overrightarrow{\\mathbf{b}}||\\:sin(\\theta) $$\n", "</span>\n", "\n", "Which is also equivalent to state that the cross product between two vectors <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> and <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span> is the magnitude of <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> times the magnitude of the component of <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span> perpendicular to <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> (or the magnitude of <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span> times the magnitude of the component of <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> perpendicular to <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span>).\n", "\n", "The definition above, also implies that the magnitude of the cross product is the area of the parallelogram spanned by the two vectors:  \n", "<br>\n", "<figure><img src=\"http://upload.wikimedia.org/wikipedia/commons/4/4e/Cross_product_parallelogram.svg\" width=160 alt=\"Vector subtraction\"/><figcaption><center><i>Figure. Area of a parallelogram as the magnitude of the cross product (image from Wikipedia).</i></center></figcaption></figure> "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The cross product can also be calculated as the determinant of a matrix:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{a}} \\times \\overrightarrow{\\mathbf{b}} = \\left| \\begin{array}{ccc}\n", "\\hat{\\mathbf{i}} & \\hat{\\mathbf{j}} & \\hat{\\mathbf{k}} \\\\\n", "a_x & a_y & a_z \\\\\n", "b_x & b_y & b_z \n", "\\end{array} \\right|\n", "= a_y b_z \\hat{\\mathbf{i}} + a_z b_x \\hat{\\mathbf{j}} +  a_x b_y \\hat{\\mathbf{k}} - a_y b_x \\hat{\\mathbf{k}}-a_z b_y \\hat{\\mathbf{i}} - a_x b_z \\hat{\\mathbf{j}} \\\\\n", "\\overrightarrow{\\mathbf{a}} \\times \\overrightarrow{\\mathbf{b}} = (a_yb_z-a_zb_y)\\hat{\\mathbf{i}} + (a_zb_x-a_xb_z)\\hat{\\mathbf{j}} + (a_xb_y-a_yb_x)\\hat{\\mathbf{k}} $$\n", "</span>\n", "\n", "The same result as before."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["The cross product between two vectors can be visualized in this interactive animation:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.226252Z", "start_time": "2020-09-25T18:14:33.223372Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"500\"\n", "            src=\"https://www.geogebra.org/classic/cz6v2U99?embed\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x7fd28d022850>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["IFrame('https://www.geogebra.org/classic/cz6v2U99?embed',\n", "       width='100%', height=500)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The Numpy function for the cross product is `numpy.cross`:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.230672Z", "start_time": "2020-09-25T18:14:33.227536Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a = [[1 2 3]\n", " [1 1 1]] \n", "b = [[4 5 6]\n", " [7 8 9]]\n", "np.cross(a, b) = [[-3  6 -3]\n", " [ 1 -2  1]]\n"]}], "source": ["print('a =', a, '\\nb =', b)\n", "print('np.cross(a, b) =', np.cross(a, b))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["For 2D arrays with vectors in different rows:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.234848Z", "start_time": "2020-09-25T18:14:33.231839Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["array([[-3,  6, -3],\n", "       [ 1, -2,  1]])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([[1, 2, 3], [1, 1, 1]])\n", "b = np.array([[4, 5, 6], [7, 8, 9]])\n", "np.cross(a, b, axis=1)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["### <PERSON><PERSON><PERSON> process\n", "\n", "The [<PERSON><PERSON><PERSON> process](http://en.wikipedia.org/wiki/Gram%E2%80%93Schmidt_process) is a method for orthonormalizing (orthogonal unit versors) a set of vectors using the scalar product. The <PERSON><PERSON><PERSON> process works for any number of vectors.   \n", "For example, given three vectors, <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}, \\overrightarrow{\\mathbf{b}}, \\overrightarrow{\\mathbf{c}}$</span>, in the 3D space, a basis <span class=\"notranslate\">$\\{\\hat{e}_a, \\hat{e}_b, \\hat{e}_c\\}$</span> can be found using the <PERSON><PERSON> process by: \n", "\n", "The first versor is in the <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> direction (or in the direction of any of the other vectors):  \n", "\n", "<span class=\"notranslate\">\n", "$$ \\hat{e}_a = \\frac{\\overrightarrow{\\mathbf{a}}}{||\\overrightarrow{\\mathbf{a}}||} $$\n", "</span>\n", "\n", "The second versor, orthogonal to <span class=\"notranslate\">$\\hat{e}_a$</span>, can be found considering we can express vector <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span> in terms of the <span class=\"notranslate\">$\\hat{e}_a$</span> direction as:  \n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{b}} = \\overrightarrow{\\mathbf{b}}^\\| + \\overrightarrow{\\mathbf{b}}^\\bot $$\n", "</span>\n", "\n", "Then:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{b}}^\\bot = \\overrightarrow{\\mathbf{b}} - \\overrightarrow{\\mathbf{b}}^\\| = \\overrightarrow{\\mathbf{b}} - (\\overrightarrow{\\mathbf{b}} \\cdot \\hat{e}_a ) \\hat{e}_a $$\n", "</span>\n", "\n", "Finally:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\hat{e}_b = \\frac{\\overrightarrow{\\mathbf{b}}^\\bot}{||\\overrightarrow{\\mathbf{b}}^\\bot||} $$\n", "</span>\n", "\n", "The third versor, orthogonal to <span class=\"notranslate\">$\\{\\hat{e}_a, \\hat{e}_b\\}$</span>, can be found expressing the vector <span class=\"notranslate\">$\\overrightarrow{\\mathbf{C}}$</span> in terms of <span class=\"notranslate\">$\\hat{e}_a$</span> and <span class=\"notranslate\">$\\hat{e}_b$</span> directions as:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{c}} = \\overrightarrow{\\mathbf{c}}^\\| + \\overrightarrow{\\mathbf{c}}^\\bot $$\n", "</span>\n", "\n", "Then:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{c}}^\\bot = \\overrightarrow{\\mathbf{c}} - \\overrightarrow{\\mathbf{c}}^\\| $$\n", "</span>\n", "\n", "Where:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\overrightarrow{\\mathbf{c}}^\\| = (\\overrightarrow{\\mathbf{c}} \\cdot \\hat{e}_a ) \\hat{e}_a + (\\overrightarrow{\\mathbf{c}} \\cdot \\hat{e}_b ) \\hat{e}_b $$\n", "</span>\n", "\n", "Finally:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\hat{e}_c = \\frac{\\overrightarrow{\\mathbf{c}}^\\bot}{||\\overrightarrow{\\mathbf{c}}^\\bot||} $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["Let's implement the <PERSON><PERSON><PERSON> process in Python.\n", "\n", "For example, consider the positions (vectors) <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}} = [1,2,0], \\overrightarrow{\\mathbf{b}} = [0,1,3], \\overrightarrow{\\mathbf{c}} = [1,0,1]$</span>:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.237820Z", "start_time": "2020-09-25T18:14:33.235951Z"}, "slideshow": {"slide_type": "skip"}, "tags": []}, "outputs": [], "source": ["a = np.array([1, 2, 0])\n", "b = np.array([0, 1, 3])\n", "c = np.array([1, 0, 1])"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["The first versor is:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.241126Z", "start_time": "2020-09-25T18:14:33.238846Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.4472136  0.89442719 0.        ]\n"]}], "source": ["ea = a/np.linalg.norm(a)\n", "print(ea)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["The second versor is:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.244435Z", "start_time": "2020-09-25T18:14:33.242231Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.13187609  0.06593805  0.98907071]\n"]}], "source": ["eb = b - np.dot(b, ea)*ea\n", "eb = eb/np.linalg.norm(eb)\n", "print(eb)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["And the third version is:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.248750Z", "start_time": "2020-09-25T18:14:33.245628Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.88465174 -0.44232587  0.14744196]\n"]}], "source": ["ec = c - np.dot(c, ea)*ea - np.dot(c, eb)*eb\n", "ec = ec/np.linalg.norm(ec)\n", "print(ec)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["Let's check the orthonormality between these versors:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.254794Z", "start_time": "2020-09-25T18:14:33.250171Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors: \n", "ea = [0.4472136  0.89442719 0.        ] \n", "eb = [-0.13187609  0.06593805  0.98907071] \n", "ec = [ 0.88465174 -0.44232587  0.14744196]\n", "\n", "Test of orthogonality (scalar product between versors): \n", "ea x eb: 2.0816681711721685e-17 \n", "eb x ec: -2.7755575615628914e-17 \n", "ec x ea: 5.551115123125783e-17\n", "\n", "Norm of each versor: \n", "||ea|| = 0.9999999999999999 \n", "||eb|| = 1.0 \n", "||ec|| = 1.0\n"]}], "source": ["print('Versors:', '\\nea =', ea, '\\neb =', eb, '\\nec =', ec)\n", "print('\\nTest of orthogonality (scalar product between versors):',\n", "      '\\nea x eb:', np.dot(ea, eb),\n", "      '\\neb x ec:', np.dot(eb, ec),\n", "      '\\nec x ea:', np.dot(ec, ea))\n", "print('\\nNorm of each versor:',\n", "      '\\n||ea|| =', np.linalg.norm(ea),\n", "      '\\n||eb|| =', np.linalg.norm(eb),\n", "      '\\n||ec|| =', np.linalg.norm(ec))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["Or, we can simply use the built-in QR factorization function from NumPy:"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.260304Z", "start_time": "2020-09-25T18:14:33.255845Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-0.4472136   0.13187609  0.88465174]\n", " [-0.89442719 -0.06593805 -0.44232587]\n", " [-0.         -0.98907071  0.14744196]]\n"]}], "source": ["vectors = np.vstack((a,b,c)).T\n", "Q, R = np.linalg.qr(vectors)\n", "print(Q)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2020-09-25T18:14:33.267956Z", "start_time": "2020-09-25T18:14:33.261646Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors: \n", "ea = [-0.4472136  -0.89442719 -0.        ] \n", "eb = [ 0.13187609 -0.06593805 -0.98907071] \n", "ec = [ 0.88465174 -0.44232587  0.14744196]\n", "\n", "Test of orthogonality (scalar product between versors):\n", "[[ 1.00000000e+00  4.05428641e-17  1.21687623e-16]\n", " [ 4.05428641e-17  1.00000000e+00 -1.01919308e-16]\n", " [ 1.21687623e-16 -1.01919308e-16  1.00000000e+00]]\n", "\n", "Test of orthogonality (scalar product between versors): \n", "ea x eb: 4.163336342344337e-17 \n", "eb x ec: -1.3877787807814457e-16 \n", "ec x ea: 1.1102230246251565e-16\n", "\n", "Norm of each versor: \n", "||ea|| = 0.9999999999999998 \n", "||eb|| = 0.9999999999999999 \n", "||ec|| = 1.0\n"]}], "source": ["ea, eb, ec = Q[:, 0], Q[:, 1], Q[:, 2]\n", "print('Versors:', '\\nea =', ea, '\\neb =', eb, '\\nec =', ec)\n", "\n", "print('\\nTest of orthogonality (scalar product between versors):')\n", "print(np.dot(Q.T, Q))\n", "      \n", "print('\\nTest of orthogonality (scalar product between versors):',\n", "      '\\nea x eb:', np.dot(ea, eb),\n", "      '\\neb x ec:', np.dot(eb, ec),\n", "      '\\nec x ea:', np.dot(ec, ea))\n", "\n", "print('\\nNorm of each versor:',\n", "      '\\n||ea|| =', np.linalg.norm(ea),\n", "      '\\n||eb|| =', np.linalg.norm(eb),\n", "      '\\n||ec|| =', np.linalg.norm(ec))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["Which results in the same basis with exception of the changed signals."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", " - Read pages 44-92 of the first chapter of the [<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about scalars and vectors in Mechanics.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the Internet\n", "\n", " - Khan Academy: [Vectors](https://www.khanacademy.org/math/algebra-home/alg-vectors)  \n", " - [Vectors, what even are they?](https://youtu.be/fNk_zzaMoSs)  "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Problems\n", "\n", "1. Given the vectors, <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}=[1, 0, 0]$</span> and <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}=[1, 1, 1]$</span>, calculate the dot and cross products between them.  \n", "\n", "2. Calculate the unit vectors for $[2, −2, 3]$ and $[3, −3, 2]$ and determine an orthogonal vector to these two vectors.  \n", "\n", "3. Given the vectors <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$=[1, 0, 0]</span> and <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$=[1, 1, 1], calculate $ \\overrightarrow{\\mathbf{a}} \\times \\overrightarrow{\\mathbf{b}} $</span> and verify that this vector is orthogonal to vectors <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}}$</span> and <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}}$</span>. Also, calculate <span class=\"notranslate\">$\\overrightarrow{\\mathbf{b}} \\times \\overrightarrow{\\mathbf{a}}$</span> and compare it with <span class=\"notranslate\">$\\overrightarrow{\\mathbf{a}} \\times \\overrightarrow{\\mathbf{b}}$</span>.  \n", "\n", "4. Given the vectors $[1, 1, 0]; [1, 0, 1]; [0, 1, 1]$, calculate a basis using the <PERSON><PERSON><PERSON> process.\n", "\n", "5. Write a Python function to calculate a basis using the <PERSON><PERSON> process (implement the algorithm!) considering that the input are three variables where each one contains the coordinates of vectors as columns and different positions of these vectors as rows. For example, sample variables can be generated with the command `np.random.randn(5, 3)`. \n", "\n", "6. Study the sample problems **1.1** to **1.9**, **1.11** (using Python), **1.12**, **1.14**, **1.17**, **1.18** to **1.24** of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book\n", "\n", "7. From <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book, solve the problems **1.1.1** to **1.3.16**. \n", "\n", "If you are new to scalars and vectors, you should solve these problems first by hand and then use Python to check the answers."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}