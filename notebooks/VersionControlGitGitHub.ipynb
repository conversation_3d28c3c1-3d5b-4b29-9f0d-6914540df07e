{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Version control with Git and GitHub\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)   \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Basic-glossary-of-version-control\" data-toc-modified-id=\"Basic-glossary-of-version-control-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Basic glossary of version control</a></span></li><li><span><a href=\"#Git\" data-toc-modified-id=\"Git-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Git</a></span><ul class=\"toc-item\"><li><span><a href=\"#Installing-Git\" data-toc-modified-id=\"Installing-Git-2.1\"><span class=\"toc-item-num\">2.1&nbsp;&nbsp;</span>Installing Git</a></span></li><li><span><a href=\"#Configuring-Git\" data-toc-modified-id=\"Configuring-Git-2.2\"><span class=\"toc-item-num\">2.2&nbsp;&nbsp;</span>Configuring Git</a></span></li><li><span><a href=\"#Initialize-a-Git-repository\" data-toc-modified-id=\"Initialize-a-Git-repository-2.3\"><span class=\"toc-item-num\">2.3&nbsp;&nbsp;</span>Initialize a Git repository</a></span></li></ul></li><li><span><a href=\"#Checkout-a-repository\" data-toc-modified-id=\"Checkout-a-repository-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Checkout a repository</a></span></li><li><span><a href=\"#The-working-directory\" data-toc-modified-id=\"The-working-directory-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>The working directory</a></span></li><li><span><a href=\"#Add-and-commit-changes\" data-toc-modified-id=\"Add-and-commit-changes-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Add and commit changes</a></span></li><li><span><a href=\"#Push-changes\" data-toc-modified-id=\"Push-changes-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Push changes</a></span></li><li><span><a href=\"#Update-and-merge-changes\" data-toc-modified-id=\"Update-and-merge-changes-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Update and merge changes</a></span></li><li><span><a href=\"#Remove-or-delete-files\" data-toc-modified-id=\"Remove-or-delete-files-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Remove or delete files</a></span></li><li><span><a href=\"#Syncing-a-fork\" data-toc-modified-id=\"Syncing-a-fork-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Syncing a fork</a></span></li><li><span><a href=\"#Git-Commands-Cheat-Sheet\" data-toc-modified-id=\"Git-Commands-Cheat-Sheet-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>Git Commands Cheat Sheet</a></span></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-12\"><span class=\"toc-item-num\">12&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-13\"><span class=\"toc-item-num\">13&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Version control has become a powerful tool to back up data, keep your work organized, and collaborate with others, very useful in the academic life.\n", "\n", "> Revision control, also known as version control and source control, is the management of changes to documents, computer programs, large web sites, etc.  \n", "Changes are usually identified by a number or letter code, termed the \"revision number\", \"revision level\", or simply \"revision\". For example, an initial set of files is \"revision 1\". When the first change is made, the resulting set is \"revision 2\", and so on. Each revision is associated with a timestamp and the person making the change. Revisions can be compared, restored, and with some types of files, merged.   \n", "Version control systems (VCS) most commonly run as stand-alone applications, but revision control is also embedded in various types of software such as word processors and spreadsheets. Revision control allows for the ability to revert a document to a previous revision, which is critical for allowing editors to track each other's edits and correct mistakes (even if you work alone, version control is useful for that).\n", "> From [http://en.wikipedia.org/wiki/Revision_control](http://en.wikipedia.org/wiki/Revision_control)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic glossary of version control\n", "\n", "- **Repository**: the core of VCS, a data structure usually stored on a server that contains a set of files and directories, a historical record of changes in the repository, a set of commit objects, and a set of references to commit objects (called heads).\n", "- **Branch, fork, clone**: a branch in a repository, or a fork or clone of an entire repository, are different forms of copies of a repository. The main branch in a repository is called master or trunk. You can work on this copy and then merge it in to the master branch/repository.\n", "- **Checkout**: to checkout the repository is to obtain a local working copy of the files. Changes can be made to these local files and files can be added, removed and updated.\n", "- **Commit**: to commit a file to the repository means that the changes to the local files are saved to the repository (committed).\n", "- **Pull, push**: we can pull and push changesets between different repositories. For example, between a local copy of the repository to a central online repository.\n", "- **Diff**: a diff is the difference in changes between two commits, or saved changes.\n", "- **[Git](https://github.com/)** is a free and open source distributed version control software. Git is a very popular VCS nowadays.   \n", "- **[GitHub](https://github.com/)** is a web-based hosting service for software development projects that use the Git VCS. GitHub offers both paid plans for private repositories, and free accounts for open source projects. GitHub is also very popular."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Git\n", "\n", "Let's see how to use Git for version control. For more on that, see this [Git guide](http://rogerdudler.github.io/git-guide/), the [Pro Git Book](http://git-scm.com/book), and the [GitHub help](https://docs.github.com/en/github/getting-started-with-github).\n", "\n", "\"Git has three main states that your files can reside in: committed, modified, and staged.   \n", "Committed means that the data is safely stored in your local database. Modified means that you have changed the file but have not committed it to your database yet. Staged means that you have marked a modified file in its current version to go into your next commit snapshot.   \n", "This leads us to the three main sections of a Git project: the Git directory, the working directory, and the staging area.\" ([Pro Git Book](http://git-scm.com/book))\n", "\n", "<div class='center-align'><figure><img src=\"./../images/GitLocalOperations.png\"/><figcaption><i>Working directory, staging area, and Git directory ([Pro Git Book](http://git-scm.com/book)).</i></figcaption></figure></div> \n", "\n", "The local repository consists of three \"trees\" maintained by Git:\n", "\n", "1. Working Directory holds the actual files.   \n", "2. Index acts as a staging area.   \n", "3. HEAD points to the last commit you've made.\n", "\n", "The basic Git workflow is ([Pro Git Book](http://git-scm.com/book)):\n", "\n", "- Modify files in the working directory.\n", "- Stage the files, adding snapshots of them to the staging area.\n", "- Do a commit, which takes the files as they are in the staging area and stores that snapshot permanently to the Git directory.\n", "\n", "<div><center><figure><img src=\"./../images/GitFileLifecycle.png\"/><figcaption><i>Figure. Lifecycle of a file using Git ([Pro Git Book](http://git-scm.com/book)).</i></figcaption></figure></center></div> "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installing Git\n", "\n", "See [http://git-scm.com/downloads](http://git-scm.com/downloads) for detailed instructions on how to install Git, in short:   \n", "\n", "On MS Windows, install it using [http://git-scm.com/download/win](http://git-scm.com/download/win).\n", "\n", "On Linux (Debian/Ubuntu):    \n", "\n", ">`$ sudo apt-get install git`  \n", "\n", "On Mac OS X, there are different ways:   \n", "\n", "- Use a Git installer, such as [http://git-scm.com/download/mac](http://git-scm.com/download/mac).\n", "- With [Homebrew](http://brew.sh/), use the command line:    \n", "\n", ">`$ brew install git`    \n", "\n", "- With [MacPorts](http://www.macports.org/), use the command line:     \n", "\n", ">`$ sudo port install git`\n", "\n", "You can also install a graphical user interface (GUI) for Git: [GUI Clients](http://git-scm.com/downloads/guis).    \n", "In MS Windows, if you installed the official Git (cited above), you already installed a GUI (Git GUI), look at the Git folder. Anyway, if you are in MS Windows or Mac OS X and have a GitHub account, you may want to consider to use the [GitHub GUI](http://git-scm.com/downloads/guis) GUI because integrates easily with your GitHub account.\n", "\n", "Let's see now a short tutorial on how to use Git and GitHub for version control with command lines (if you plan to work with a GUI client, just the concepts are important). \n", "\n", "After you installed Git, you can check its version using a terminal window or the command `!` in the IPython Notebook to access the system shell.   \n", "In MS Windows, however, if you installed Git with the recommended default options, the commands below will not work and the only terminal window (command prompt window) that works is the `Git Bash` that was installed with Git. So, open `Git Bash` and run the commands below (always without the `!`)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:25:34.552858Z", "start_time": "2017-12-30T09:25:34.436658Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["git version 2.20.1\n"]}], "source": ["!git --version"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And if you type `git` you get a list of the most common commands in Git:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:25:41.151981Z", "start_time": "2017-12-30T09:25:41.037253Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["usage: git [--version] [--help] [-C <path>] [-c <name>=<value>]\n", "           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n", "           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--bare]\n", "           [--git-dir=<path>] [--work-tree=<path>] [--namespace=<name>]\n", "           <command> [<args>]\n", "\n", "These are common Git commands used in various situations:\n", "\n", "start a working area (see also: git help tutorial)\n", "   clone      Clone a repository into a new directory\n", "   init       Create an empty Git repository or reinitialize an existing one\n", "\n", "work on the current change (see also: git help everyday)\n", "   add        Add file contents to the index\n", "   mv         Move or rename a file, a directory, or a symlink\n", "   reset      Reset current HEAD to the specified state\n", "   rm         Remove files from the working tree and from the index\n", "\n", "examine the history and state (see also: git help revisions)\n", "   bisect     Use binary search to find the commit that introduced a bug\n", "   grep       Print lines matching a pattern\n", "   log        Show commit logs\n", "   show       Show various types of objects\n", "   status     Show the working tree status\n", "\n", "grow, mark and tweak your common history\n", "   branch     List, create, or delete branches\n", "   checkout    Switch branches or restore working tree files\n", "   commit     Record changes to the repository\n", "   diff       Show changes between commits, commit and working tree, etc\n", "   merge      Join two or more development histories together\n", "   rebase     Reapply commits on top of another base tip\n", "   tag        Create, list, delete or verify a tag object signed with GPG\n", "\n", "collaborate (see also: git help workflows)\n", "   fetch      Download objects and refs from another repository\n", "   pull       Fetch from and integrate with another repository or a local branch\n", "   push       Update remote refs along with associated objects\n", "\n", "'git help -a' and 'git help -g' list available subcommands and some\n", "concept guides. See 'git help <command>' or 'git help <concept>'\n", "to read about a specific subcommand or concept.\n"]}], "source": ["!git"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Configuring Git\n", "\n", "After installation, we need to configure Git (this is only needed once and it will be used when we commit changes to the repository).   \n", "Let's do that using the following command lines:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:25:55.345529Z", "start_time": "2017-12-30T09:25:55.116143Z"}}, "outputs": [], "source": ["!git config --global user.name \"your name here\"\n", "!git config --global user.email \"<EMAIL>\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Your email address for G<PERSON> should be the same one associated with your GitHub account in case you plan to have a repository there."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize a Git repository\n", "\n", "To initialize a local Git repository in the current directory:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git init"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You would do that in case you want starting to track an existing local project in Git.   \n", "You can also specify a new local repository, with the command `git init <repository-name>`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkout a repository\n", "\n", "To clone a remote repository, we can use two different protocols to transfer data: `HTTPS` and `SSH`. `HTTPS` and `SSH` are cryptographic network protocols for secure data communication, remote command-line login, remote command execution, and other secure network services between two networked computers that connects, via a secure channel over an insecure network. `HTTPS ` is simpler to setup and `SSH` requires a keypair generated on your computer and attached to your GitHub account. See this [GitHub help](https://help.github.com/articles/which-remote-url-should-i-use) to decide which one to use.   \n", "\n", "For instance, this is the command to clone repo of this notebook using `HTTPS`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git https://github.com/BMClab/BMC.git"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And using `SSH` (after you created your keypair and registered into you GitHub account):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!<NAME_EMAIL>:BMClab/BMC.git"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The local repository will be created inside your current directory.   \n", "You can change the current directory to clone from there and there is no need to create a folder with the name of the repo you are cloning; Git will do that for you.   \n", "And you should not have your local repo inside a Dropbox folder because Dropbox can generate conflict files."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The working directory\n", "\n", "The Git commands will only work once you are in your working directory of your local repository.\n", "\n", "Use the command `cd` to change you current directory. In Linux or Mac OS X (change the directory to your case):"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/DATA/GitHub\n"]}], "source": ["cd /mnt/DATA/GitHub"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/mnt/DATA/GitHub'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[0m\u001b[34;42mBMC\u001b[0m/  \u001b[34;42mci1d\u001b[0m/  \u001b[34;42mspm1d\u001b[0m/  \u001b[34;42mstuff\u001b[0m/\n"]}], "source": ["ls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add and commit changes\n", "\n", "You can propose changes (add it to the Index) using the command `add`.  \n", "For instance, let's change the README.md file, and commit it:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "-"}}, "outputs": [], "source": ["!git add README.md"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The command `add` is a multipurpose command; allows to track files, stage files, and mark merge conflicted files as resolved.  \n", "You can add everything in the current directory using the command \"`git add -A`\".  \n", "  \n", "To commit this change:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git commit -m \"Commit message\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Which commits everything in your staging area and uses inline commit message.\n", "\n", "If the file to commit is not new, only changed, you can skip the `add` command using the command `commit -a` to automatically stages every currently **tracked** file and commits them:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git commit –a –m \"Commit message\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you created/added a new file (untracked so far) you still need to add them to your staging area with the command `add`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Push changes\n", "\n", "Now the file is committed to the HEAD, but not in the remote repository yet.   \n", "\n", "To send this changes to the remote repository, execute (substitute `master` by the branch to push the change to if it's not the same repo you cloned from):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git push origin master"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You should do that in a regular terminal window because you may be prompted to enter your username and password for your GitHub account in case you didn't store the username and password in your OS (if you are using `HTTPS`).   \n", "If you are using `SSH`, by default you will not be prompted to enter your credentials (because you created your keypair and registered into you GitHub account).\n", "\n", "Anyway, if you are committing to my repo, this will nor work for you because, I hope, you don't have my credentials.\n", "\n", "You can create a branch (fork) of the BMC repository (go to its website and use the `Fork` button in the upper-right corner) or create your own repo to experiment with it."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Update and merge changes\n", "\n", "To update the local repository to the newest commit, execute in the working directory (this will fetch and merge remote changes):"]}, {"cell_type": "code", "execution_count": null, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["!git pull"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To merge another branch into your active branch (e.g. master), use `git merge <branch>`.\n", "\n", "These two last commands tries to auto-merge changes.   \n", "This might not be possible because of conflicts between the different branches.   \n", "If that is the case, we will have to merge those conflicts manually by editing the files shown by git.    \n", "After changing, we need to mark them as merged with `git add <filename>` or `git add .`.   \n", "Before merging changes, we can preview them by using `git diff <source_branch> <target_branch>`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Remove or delete files\n", "\n", "You should not manually remove or delete a file inside your repository, for that use the command `rm`:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["usage: git rm [options] [--] <file>...\n", "\n", "    -n, --dry-run         dry run\n", "    -q, --quiet           do not list removed files\n", "    --cached              only remove from the index\n", "    -f, --force           override the up-to-date check\n", "    -r                    allow recursive removal\n", "    --ignore-unmatch      exit with a zero status even if nothing matched\n", "\n"]}], "source": ["!git rm -help"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git rm <filename>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Syncing a fork\n", "\n", "To sync a fork of a repository to keep it up-to-date with the upstream repository (for example, if you forked the BMClab/BMC repo), according to the [GitHub Help](https://help.github.com/articles/syncing-a-fork/), you have to first configure a remote for a fork and then fetch the commits (the changes) from the upstream repository.  \n", "Follow these steps:\n", "\n", "1. Open Terminal (for Mac and Linux users) or the command line (for Windows users).  \n", "2. Change the current working directory to your local project.\n", "3. List the current configured remote repository for your fork:\n", "```\n", "git remote -v\n", "```\n", "4. Specify a new remote upstream repository that will be synced with the fork, type:\n", "```\n", "git remote add upstream https://github.com/BMClab/BMC.git\n", "```\n", "5. Verify the new upstream repository you've specified for your fork:\n", "```\n", "git remote -v\n", "```\n", "6. Fetch the branches and their respective commits from the upstream repository. Commits to master will be stored in a local branch, upstream/master:\n", "```\n", "git fetch upstream\n", "```\n", "7. Check out your fork's local master branch:\n", "```\n", "git checkout master\n", "```\n", "8. Merge the changes from upstream/master into your local master branch. This brings your fork's master branch into sync with the upstream repository, without losing your local changes:\n", "```\n", "git merge upstream/master\n", "```\n", "\n", "If your local branch didn't have any unique commits, git will instead perform a \"fast-forward\".  \n", "If there is a conflict and git couldn't merge, if you don't care for any modification in the local repo of your fork, you may need to git stash, then git pull, and repeat step 8.\n", "\n", "To repeat the sync of a fork in the future, you will have only to change to your local project (step 2) and start from step 6."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Git Commands Cheat Sheet\n", "\n", "From [https://github.com/ashash3/Cookies-and-Code-Git/wiki/Git-Commands-Cheat-Sheet](https://github.com/ashash3/Cookies-and-Code-Git/wiki/Git-Commands-Cheat-Sheet).\n", "\n", "**Basic Commands**   \n", "\n", "- `git status`: check the status of your files\n", "- `git add`: multipurpose command; track files, stage files, and mark merge conflicted files as resolved\n", "- `git diff`: compare working directory to staging area\n", "- `git diff --cached`: compare staged changes to last commit\n", "- `git commit –m \"message\"`: commit everything in your staging area, uses inline commit message\n", "- `git commit –a –m \"message\"`: automatically stage every currently tracked file and commits them (to skip “git add” command)\n", "- `git rm [filename]`: untrack the file and remove it from your working directory\n", "- `git rm --cached [filename]`: untrack the file, but keeps it in your working directory - useful if you forgot to include certain files in your .gitignore\n", "- `git mv [orig_name] [new_name]`: change the file's name\n", "- `git log` show the commit history in reverse chronological order (i.e. most recent first)\n", "\"Undoing Things\" Commands\n", "- `git commit --amend`: overrides your most recent commit - i.e. it \"undoes\" your most recent with what's currently in your staging area\n", "- `git reset HEAD [filename]`: allows you to unstage a particular file; this file returns back to the modified state\n", "- `git checkout -- [filename]`: allows you to discard any changes you've made to the file since the last commit Note: use this command carefully - the discarded changes cannot be recovered\n", "\n", "**Remote Repository Commands**\n", "\n", "- `git pull [remote-name] [branch-name]`: automatically fetch data from the remote server (typically called \"origin\") and attempts to merge it into the code you're working on; branch-name is typically \"master\" if you haven't created your own branch\n", "- `git push [remote-name] [branch-name]`: push your code from the branch you're on (typically \"master\" if you haven't created your own branch) upstream to the remote server (typically called \"origin\")\n", "\n", "**Merging and Branching Commands**\n", "\n", "- `git merge [branch-name]`: merge the specified branch with the current working directory\n", "- `git branch`: view all available branches\n", "- `git branch [branch-name]`: create a new branch\n", "- `git checkout [branch-name]`: set current working directory to branch-name\n", "- `git checkout -b [branch-name]`: create a new branch and set current working directory to it\n", "- `git merge [branch-name]`: merge branch-name into the current branch\n", "- `git branch -d [branch-name]`: delete the specified branch\n", "\n", "**Changing to Previous Commits Commands**\n", "\n", "- `git revert <prev_commit>`: create a new commit with a reverse patch that cancels out everything after that previous commit\n", "- `git checkout -b <branchname> <prev_commit>`: return to a previous commit and create a branch using it"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "There is a lot of good materials on how to use Git for version control, here are a few:   \n", "\n", "- [Getting started with GitHub](https://docs.github.com/en/github/getting-started-with-github)  \n", "- [Git guide](http://rogerdudler.github.io/git-guide/)  \n", "- [Revision control software](http://nbviewer.ipython.org/urls/raw.github.com/j<PERSON><PERSON><PERSON><PERSON>/scientific-python-lectures/master/Lecture-7-Revision-Control-Software.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the Internet\n", "\n", " - [Git and GitHub for Beginners - Crash Course](https://youtu.be/RGOj5yH7evk)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## My workflow"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-05-18T05:43:08.934242Z", "start_time": "2020-05-18T05:43:08.928001Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/DATA/GitHub/BMC\n"]}], "source": ["cd /mnt/DATA/GitHub/BMC"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-05-18T05:43:10.575155Z", "start_time": "2020-05-18T05:43:09.956501Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Already up to date.\r\n"]}], "source": ["!git pull"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-05-18T05:45:59.133652Z", "start_time": "2020-05-18T05:45:59.009936Z"}}, "outputs": [], "source": ["!git add ."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-05-18T05:46:00.463875Z", "start_time": "2020-05-18T05:46:00.045027Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[master ff6f487] comments\n", " 12 files changed, 21422 deletions(-)\n", " delete mode 100644 notebooks/.ipynb_checkpoints/AngularVelocity3D-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/CenterOfMassAndMomentOfInertia-checkpoint.ipynb\n", " delete mode 100644 notebooks/.ipynb_checkpoints/ForcePlateCalibration-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/FreeBodyDiagram-checkpoint.ipynb\n", " delete mode 100644 notebooks/.ipynb_checkpoints/FreeBodyDiagramForRigidBodies-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/GaitAnalysis2D-checkpoint.ipynb\n", " delete mode 100644 notebooks/.ipynb_checkpoints/KinematicsAngular2D-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/KinematicsOfRigidBody-checkpoint.ipynb\n", " delete mode 100644 notebooks/.ipynb_checkpoints/Kinetics3dRigidBody-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/MuscleModeling-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/lagrangian_mechanics-checkpoint.ipynb\n", " delete mode 100755 notebooks/.ipynb_checkpoints/newton_euler_equations-checkpoint.ipynb\n"]}], "source": ["!git commit -a -m \"comments\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-05-18T05:46:09.484845Z", "start_time": "2020-05-18T05:46:05.262119Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enumerating objects: 5, done.\n", "Counting objects: 100% (5/5), done.\n", "Delta compression using up to 12 threads\n", "Compressing objects: 100% (3/3), done.\n", "Writing objects: 100% (3/3), 279 bytes | 279.00 KiB/s, done.\n", "Total 3 (delta 2), reused 0 (delta 0)\n", "remote: Resolving deltas: 100% (2/2), completed with 2 local objects.\u001b[K\n", "To https://github.com/BMClab/BMC.git\n", "   a825b4f..ff6f487  master -> master\n", "Branch 'master' set up to track remote branch 'master' from 'origin'.\n"]}], "source": ["!git push -u origin master "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", " - [GitHub](https://github.com/)  \n", " - [Version control](https://en.wikipedia.org/wiki/Version_control) @ Wikipedia"]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}