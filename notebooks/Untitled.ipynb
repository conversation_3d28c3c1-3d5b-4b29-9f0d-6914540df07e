{"cells": [{"cell_type": "markdown", "id": "56436aea-f6ea-4ff2-80b4-d77d71a84b8f", "metadata": {}, "source": ["# Basic passive models"]}, {"cell_type": "code", "execution_count": 3, "id": "40ca8a13-2d5f-44e1-b92b-7da38a74768d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "f596db3c-64c4-4be2-8e71-ff359cabbabc", "metadata": {}, "source": ["## Maxwell Model"]}, {"cell_type": "markdown", "id": "2d4e1ae7-fa27-49f0-a07b-ffaecc657b4c", "metadata": {}, "source": ["$$F = F_k=F_b$$\n", "\n", "$$x = x_k+x_b$$\n", "\n", "$$\\dot x = \\dot x_k + \\dot x_b$$\n", "\n", "$$F_k = kx_k \\rightarrow \\dot x_k = \\frac{\\dot F_k}{k} = \\frac{\\dot F}{k}$$\n", "\n", "$$F_b = b\\dot x_b \\rightarrow \\dot x_b = \\frac{F_b}{b} = \\frac{F}{b}$$\n", "\n", "$$\\dot x = \\frac{\\dot F}{k} + \\frac{F}{b}$$\n", "\n", "$$\\dot F =   - \\frac{kF}{b} + k\\dot x$$"]}, {"cell_type": "markdown", "id": "96cd57e3-add4-428c-b61a-bf9382a6d9b5", "metadata": {}, "source": ["### Length step"]}, {"cell_type": "markdown", "id": "5093cb23-0f77-4d4a-b18b-940f345d346e", "metadata": {}, "source": ["\n", "\n", "$$\\dot{x_1} = -\\frac{kx_1}{b} + x$$\n", "\n", "$$F = -\\frac{k^2x_1}{b} + kx$$"]}, {"cell_type": "code", "execution_count": 58, "id": "424f855b-8636-4df4-a820-7a84c9962d0e", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x = 0.3\n", "dt = 0.01\n", "\n", "k = 0.5\n", "b = 0.1\n", "\n", "t = np.arange(0, 3,dt)\n", "F = np.zeros_like(t)\n", "x1 = 0\n", "\n", "for i in range(len(t)):\n", "    if t[i]>1:\n", "        x = 0.3\n", "    else:\n", "        x = 0\n", "    x1dot = -k*x1/b + x\n", "    x1 = x1 + dt*x1dot # Euler method\n", "    F[i] = k*x1dot\n", "    \n", "plt.figure()\n", "plt.plot(t, F)\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "0ac9d0ae-526a-4f21-ac93-ae0da652e599", "metadata": {}, "source": ["### Sinusoidal length"]}, {"cell_type": "code", "execution_count": 55, "id": "6eb275a7-b071-4aa3-aef1-89fc9d608829", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dt = 0.01\n", "\n", "k = 0.5\n", "b = 0.1\n", "\n", "t = np.arange(0, 3,dt)\n", "F = np.zeros_like(t)\n", "\n", "\n", "x1 = 0\n", "\n", "for i in range(len(t)):\n", "    if t[i] > 1:\n", "        x = 0.3 + 0.15*np.cos(2*np.pi*t[i])\n", "    else:\n", "        x = 0\n", "    x1dot = -k*x1/b + x\n", "    x1 = x1 + dt*x1dot # Euler method\n", "    F[i] = -k**2*x1/b + k*x\n", "    \n", "plt.figure()\n", "plt.plot(t, F)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "edb4a44c-cbd5-4e25-b219-c04942ca4f14", "metadata": {}, "source": ["## Voight Model"]}, {"cell_type": "markdown", "id": "cfb5f12b-88e8-4e25-a136-eb216225a8de", "metadata": {}, "source": ["$$F = F_k+F_b$$\n", "\n", "$$x = x_k=x_b$$\n", "\n", "$$F_k = kx_k \\rightarrow F_k = kx$$\n", "\n", "$$F_b = b\\dot x_b \\rightarrow F_b = b\\dot x$$\n", "\n", "$$F = kx+b\\dot x$$\n", "\n", "$$\\dot x =   - \\frac{kx}{b} + \\frac{F}{b}$$\n", "\n"]}, {"cell_type": "markdown", "id": "4141afbd-dccb-4b96-924e-c532a19e64aa", "metadata": {}, "source": ["\n", "\n", "$$\\dot x_1 = -\\frac{kx_1}{b} + \\frac{F}{b}$$\n", "\n", "$$x = x_1$$"]}, {"cell_type": "markdown", "id": "e1a317fc-52bd-474f-90da-214ed45e5447", "metadata": {}, "source": ["### Constant force\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "id": "d2996a99-1e3c-401c-b9c6-2050b2f3835e", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dt = 0.01\n", "\n", "k = 0.5\n", "b = 0.1\n", "\n", "t = np.arange(0, 10, dt)\n", "x = np.zeros_like(t)\n", "x1 = 0\n", "\n", "for i in range(len(t)):\n", "    if 0<t[i]<=1:\n", "        F = 0\n", "    elif 1<t[i]<=4:\n", "        F = 1\n", "    else:\n", "        F = 0\n", "    x1dot = -k/b*x1 + F/b\n", "    x1 = x1 + dt*x1dot # Euler method\n", "    x[i] = x1\n", "    \n", "plt.figure()\n", "plt.plot(t, x)\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "99c8fc6a-ac8c-4d04-b160-8ac47f50749c", "metadata": {}, "source": ["### Sinusoidal force"]}, {"cell_type": "code", "execution_count": 59, "id": "cb15c033-4f6c-4230-a89e-8848301a3cad", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["\n", "dt = 0.01\n", "\n", "k = 0.5\n", "b = 0.1\n", "\n", "t = np.arange(0, 10,dt)\n", "F = 1 + 0.2*np.cos(2*np.pi*2*t)\n", "x = np.zeros_like(t)\n", "x1 = 1\n", "\n", "for i in range(len(t)):\n", "    x1dot = -k/b*x1 + F[i]/b\n", "    x1 = x1 + dt*x1dot # Euler method\n", "    x[i] = x1\n", "    \n", "plt.figure()\n", "plt.plot(t, x)\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "c4a25d4c-43da-4419-93d2-34501e51e49b", "metadata": {}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "84085259-0e30-4715-8544-4940d34ca200", "metadata": {}, "source": ["$$F = F_1+F_2$$\n", "\n", "$$F_1 = F_{k_s} = F_b $$\n", "\n", "$$F_2 = F_{k_p}$$\n", "\n", "$$ x = x_s + x_b$$\n", "\n", "$$ \\dot x = \\dot x_s + \\dot x_b$$\n", "\n", "$$ F_{k_s} = k_sx_s \\rightarrow \\dot F_{k_s} = k_s\\dot x_s \\rightarrow \\dot x_s = \\frac{\\dot F_{k_s}}{k_s}$$\n", "\n", "$$ F_{b} = b\\dot x_b \\rightarrow \\dot x_b = \\frac{F_{b}}{b}$$\n", "\n", "$$F_{k_p} = $$\n", "\n", "$$ \\dot x = \\frac{\\dot F_{1}}{k_s} + \\frac{F_{1}}{b}$$\n", "\n", "$$ \\dot F_{1} = - \\frac{k_sF_{1}}{b} + k_s\\dot x$$\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "9ff81eed-9ba0-4708-8ebe-ace72c3c8918", "metadata": {}, "source": ["### Length step\n", "\n", "\n", "\n", "$$\\dot x_1 =  - \\frac{k_sx_{1}}{b} + x$$\n", "\n", "$$F_1 =  - \\frac{k_s^2x_{1}}{b} + k_sx$$\n", "\n", "$$F = F_1+k_px$$"]}, {"cell_type": "code", "execution_count": 11, "id": "33119f5a-f743-48e4-be26-a0a2942a692f", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x = 0.3\n", "dt = 0.01\n", "\n", "ks = 0.5\n", "kp = 0.1\n", "b = 1\n", "\n", "t = np.arange(0, 10, dt)\n", "F = np.zeros_like(t)\n", "x1 = 0\n", "\n", "for i in range(len(t)):\n", "    if 1< t[i]<1.3:\n", "        x = 3.3*(t[i]-1)\n", "    elif t[i] >= 1.3:\n", "        x = 1\n", "    else:\n", "        x = 0\n", "    x1dot = -ks*x1/b + x\n", "    x1 = x1 + dt*x1dot # Euler method\n", "    F[i] = ks*x1dot + kp*x\n", "    \n", "plt.figure()\n", "plt.plot(t, F)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "c71e0df5-87bf-4136-a8ad-4ec815db3b53", "metadata": {}, "source": ["### Force step\n", "\n", "$$\\dot x = \\frac{\\dot F - k_p\\dot x}{k_s} + \\frac{F - k_px}{b} $$\n", "\n", "$$\\dot x\\left(\\frac{k_s+k_p}{k_s} \\right) = \\frac{\\dot F}{k_s} + \\frac{F - k_px}{b} $$\n", "\n", "$$\\dot x = \\frac{\\dot F}{k_s+k_p} + \\frac{k_sF}{b(k_s+k_p)} - \\frac{k_sk_px}{b(k_s+k_p)} $$\n", "\n", "$$ \\dot x_1 = F - \\frac{k_sk_px}{b(k_s+k_p)}$$\n", "\n", "$$x = \\frac{\\dot x_1}{k_s+k_p} + \\frac{k_sx_1}{b(k_s+k_p)} $$"]}, {"cell_type": "code", "execution_count": 44, "id": "86f38ba3-2f22-4f9c-99ed-78d65094d218", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dt = 0.01\n", "\n", "ks = 0.5\n", "kp = 0.1\n", "b = 0.1\n", "\n", "t = np.arange(0, 10, dt)\n", "x = np.zeros_like(t)\n", "x1 = 0\n", "\n", "for i in range(len(t)):\n", "    if 0<t[i]<1:\n", "        F = 0\n", "    elif 1<t[i]<5:\n", "        F = 1\n", "    else:\n", "        F = 0\n", "    x1dot = -ks*kp/(ks+kp)/b*x1 + F\n", "    x1 = x1 + dt*x1dot\n", "    x[i] = ks/(b*(ks+kp))*x1 + x1dot/(ks+kp)\n", "    \n", "plt.figure()\n", "plt.plot(t, x)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "6016f7bd-4bee-4c85-a39d-c14f2cd89389", "metadata": {}, "source": ["## References"]}, {"cell_type": "markdown", "id": "dd8cba74-b01e-4fcd-b3fe-0a3bb73e3664", "metadata": {}, "source": ["- <PERSON><PERSON><PERSON>, GT, <PERSON><PERSON><PERSON><PERSON><PERSON> MODELING OF MUSCULOSKELETAL MOTION A Vectorized Approach\n", "for Biomechanical Analysis in Three Dimensions, 2001"]}, {"cell_type": "code", "execution_count": null, "id": "3c607dee-0f51-4fe0-ab52-73ca0a6c12f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}