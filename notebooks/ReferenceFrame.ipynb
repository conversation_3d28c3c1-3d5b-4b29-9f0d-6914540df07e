{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Frame of reference\n", "\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Frame-of-reference-for-human-motion-analysis\" data-toc-modified-id=\"Frame-of-reference-for-human-motion-analysis-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Frame of reference for human motion analysis</a></span></li><li><span><a href=\"#Cartesian-coordinate-system\" data-toc-modified-id=\"Cartesian-coordinate-system-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Cartesian coordinate system</a></span><ul class=\"toc-item\"><li><span><a href=\"#Standardizations-in-movement-analysis\" data-toc-modified-id=\"Standardizations-in-movement-analysis-2.1\"><span class=\"toc-item-num\">2.1&nbsp;&nbsp;</span>Standardizations in movement analysis</a></span></li></ul></li><li><span><a href=\"#Determination-of-a-coordinate-system\" data-toc-modified-id=\"Determination-of-a-coordinate-system-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Determination of a coordinate system</a></span><ul class=\"toc-item\"><li><span><a href=\"#Definition-of-a-basis\" data-toc-modified-id=\"Definition-of-a-basis-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>Definition of a basis</a></span></li><li><span><a href=\"#Using-the-cross-product\" data-toc-modified-id=\"Using-the-cross-product-3.2\"><span class=\"toc-item-num\">3.2&nbsp;&nbsp;</span>Using the cross product</a></span></li><li><span><a href=\"#Coordinate-system:-origin-and-basis\" data-toc-modified-id=\"Coordinate-system:-origin-and-basis-3.3\"><span class=\"toc-item-num\">3.3&nbsp;&nbsp;</span>Coordinate system: origin and basis</a></span><ul class=\"toc-item\"><li><span><a href=\"#Visualization-of-the-coordinate-system\" data-toc-modified-id=\"Visualization-of-the-coordinate-system-3.3.1\"><span class=\"toc-item-num\">3.3.1&nbsp;&nbsp;</span>Visualization of the coordinate system</a></span></li></ul></li><li><span><a href=\"#Gram–Schmidt-process\" data-toc-modified-id=\"Gram–Schmidt-process-3.4\"><span class=\"toc-item-num\">3.4&nbsp;&nbsp;</span>Gram–Schmidt process</a></span><ul class=\"toc-item\"><li><span><a href=\"#Visualization-of-the-coordinate-system\" data-toc-modified-id=\"Visualization-of-the-coordinate-system-3.4.1\"><span class=\"toc-item-num\">3.4.1&nbsp;&nbsp;</span>Visualization of the coordinate system</a></span></li></ul></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["<a href=\"http://en.wikipedia.org/wiki/Motion_(physics)\">Motion</a> (a change of position in space with respect to time) is not an absolute concept; a reference is needed to describe the motion of the object in relation to this reference. Likewise, the state of such reference cannot be absolute in space and so motion is relative.    \n", "A [frame of reference](http://en.wikipedia.org/wiki/Frame_of_reference) is the place with respect to we choose to describe the motion of an object. In this reference frame, we define a [coordinate system](http://en.wikipedia.org/wiki/Coordinate_system) (a set of axes) within which we measure the motion of an object (but frame of reference and coordinate system are often used interchangeably).  \n", "\n", "Often, the choice of reference frame and coordinate system is made by convenience. However, there is an important distinction between reference frames when we deal with the dynamics of motion, where we are interested to understand the forces related to the motion of the object. In dynamics, we refer to [inertial frame of reference](http://en.wikipedia.org/wiki/Inertial_frame_of_reference) (a.k.a., Galilean reference frame) when the Newton's laws of motion in their simple form are valid in this frame and to non-inertial frame of reference when the Newton's laws in their simple form are not valid (in such reference frame, fictitious accelerations/forces appear). An inertial reference frame is at rest or moves at constant speed (because there is no absolute rest!), whereas a non-inertial reference frame is under acceleration (with respect to an inertial reference frame).\n", "\n", "The concept of frame of reference has changed drastically since <PERSON>, <PERSON>, <PERSON>, and <PERSON>. To read more about that and its philosophical implications, see [Space and Time: Inertial Frames](http://plato.stanford.edu/entries/spacetime-iframes/)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Frame of reference for human motion analysis\n", "\n", "In anatomy, we use a simplified reference frame composed by perpendicular planes to provide a standard reference for qualitatively describing the structures and movements of the human body, as shown in the next figure.\n", "\n", "<div class='center-align'><figure><img src=\"http://upload.wikimedia.org/wikipedia/commons/3/34/BodyPlanes.jpg\" width=\"300\" alt=\"Anatomical body position\"/><figcaption><center><i>Figure. Anatomical body position and body planes (<a href=\"http://en.wikipedia.org/wiki/Human_anatomical_terms\" target=\"_blank\">image from Wikipedia</a>).</i></center></figcaption> </figure></div> "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Cartesian coordinate system\n", "\n", "As we perceive the surrounding space as three-dimensional, a convenient coordinate system is the [Cartesian coordinate system](http://en.wikipedia.org/wiki/Cartesian_coordinate_system) in the [Euclidean space](http://en.wikipedia.org/wiki/Euclidean_space) with three orthogonal axes as shown below. The axes directions are commonly defined by the [right-hand rule](http://en.wikipedia.org/wiki/Right-hand_rule) and attributed the letters X, Y, Z. The orthogonality of the Cartesian coordinate system is convenient for its use in classical mechanics, most of the times the structure of space is assumed having the [Euclidean geometry](http://en.wikipedia.org/wiki/Euclidean_geometry) and as consequence, the motion in different directions are independent of each other.  \n", "\n", "<div class='center-align'><figure><img src=\"https://raw.githubusercontent.com/demotu/BMC/master/images/CCS.png\" width=350/><figcaption><center><i>Figure. A point in three-dimensional Euclidean space described in a Cartesian coordinate system.</i></center></figcaption> </figure></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Standardizations in movement analysis\n", "\n", "The concept of reference frame in Biomechanics and motor control is very important and central to the understanding of human motion. For example, do we see, plan and control the movement of our hand with respect to reference frames within our body or in the environment we move? Or a combination of both?  \n", "The figure below, although derived for a robotic system, illustrates well the concept that we might have to deal with multiple coordinate systems.  \n", "\n", "<div class='center-align'><figure><img src=\"https://raw.githubusercontent.com/demotu/BMC/master/images/coordinatesystems.png\" width=450/><figcaption><center><i>Figure. Multiple coordinate systems for use in robots (figure from <PERSON><PERSON> (2017)).</i></center></figcaption></figure></div>\n", "\n", "For three-dimensional motion analysis in Biomechanics, we may use several different references frames for convenience and refer to them as global, laboratory, local, anatomical, or technical reference frames or coordinate systems (we will study this later).  \n", "There has been proposed different standardizations on how to define frame of references for the main segments and joints of the human body. For instance, the International Society of Biomechanics has a [page listing standardization proposals](https://isbweb.org/activities/standards) by its standardization committee and subcommittees:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T20:48:31.724735Z", "start_time": "2021-11-03T20:48:31.716733Z"}, "slideshow": {"slide_type": "skip"}}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"400\"\n", "            src=\"https://isbweb.org/activities/standards\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x7f6e207ab520>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import IFrame\n", "IFrame('https://isbweb.org/activities/standards', width='100%', height=400)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": [" Another initiative for the standardization of references frames is from the [Virtual Animation of the Kinematics of the Human for Industrial, Educational and Research Purposes (VAKHUM)](https://github.com/BMClab/BMC/blob/master/courses/refs/VAKHUM.pdf) project."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Determination of a coordinate system\n", "\n", "In Biomechanics, we may use different coordinate systems for convenience and refer to them as global, laboratory, local, anatomical, or technical reference frames or coordinate systems. For example, in a standard gait analysis, we define a global or laboratory coordinate system and a different coordinate system for each segment of the body to be able to describe the motion of a segment in relation to anatomical axes of another segment. To define this anatomical coordinate system, we need to place markers on anatomical landmarks on each segment. We also may use other markers (technical markers) on the segment to improve the motion analysis and then we will also have to define a technical coordinate system for each segment.\n", "\n", "As we perceive the surrounding space as three-dimensional, a convenient coordinate system to use is the [Cartesian coordinate system](http://en.wikipedia.org/wiki/Cartesian_coordinate_system) with three orthogonal axes in the [Euclidean space](http://en.wikipedia.org/wiki/Euclidean_space). From [linear algebra](http://en.wikipedia.org/wiki/Linear_algebra), a set of unit linearly independent vectors (orthogonal in the Euclidean space and each with norm (length) equals to one) that can represent any vector via [linear combination](http://en.wikipedia.org/wiki/Linear_combination) is called a <a href=\"http://en.wikipedia.org/wiki/Basis_(linear_algebra)\">basis</a> (or orthonormal basis). The figure below shows a point and its position vector in the Cartesian coordinate system and the corresponding versors (unit vectors) of the basis for this coordinate system. See the notebook [Scalar and vector](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ScalarVector.ipynb) for a description on vectors.  \n", "\n", "<div class='center-align'><figure><img src=\"https://raw.githubusercontent.com/demotu/BMC/master/images/vector3Dijk.png\" width=350/><figcaption><center><i>Figure. Representation of a point <b>P</b> and its position vector $\\overrightarrow{\\mathbf{r}}$ in a Cartesian coordinate system. The versors $\\hat{\\mathbf{i}}, \\hat{\\mathbf{j}}, \\hat{\\mathbf{k}}$ form a basis for this coordinate system and are usually represented in the color sequence RGB (red, green, blue) for easier visualization.</i></center></figcaption></figure></div>\n", "\n", "One can see that the versors of the basis shown in the figure above have the following coordinates in the Cartesian coordinate system:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\hat{\\mathbf{i}} = \\begin{bmatrix}1\\\\0\\\\0 \\end{bmatrix}, \\quad \\hat{\\mathbf{j}} = \\begin{bmatrix}0\\\\1\\\\0 \\end{bmatrix}, \\quad \\hat{\\mathbf{k}} = \\begin{bmatrix} 0 \\\\ 0 \\\\ 1 \\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Using the notation described in the figure above, the position vector $\\overrightarrow{\\mathbf{r}}$ (or the point $\\overrightarrow{\\mathbf{P}}$) can be expressed as:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{r}} = x\\hat{\\mathbf{i}} + y\\hat{\\mathbf{j}} + z\\hat{\\mathbf{k}}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Definition of a basis\n", "\n", "The mathematical problem of determination of a coordinate system is to find a basis and an origin for it (a basis is only a set of vectors, with no origin). There are different methods to calculate a basis given a set of points (coordinates), for example, one  can use the scalar product or the cross product for this problem."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Using the cross product\n", "\n", "Let's now define a basis using a common method in motion analysis (employing the cross product):  \n", "Given the coordinates of three noncollinear points in 3D space (points that do not all lie on the same line), $\\overrightarrow{\\mathbf{m}}_1, \\overrightarrow{\\mathbf{m}}_2, \\overrightarrow{\\mathbf{m}}_3$, which would represent the positions of markers captured from a motion analysis session, a basis can be found following these steps:  \n", "\n", "1. First axis, $\\overrightarrow{\\mathbf{v}}_1$, the vector $\\overrightarrow{\\mathbf{m}}_2-\\overrightarrow{\\mathbf{m}}_1$ (or any other vector difference);  \n", "\n", "2. Second axis, $\\overrightarrow{\\mathbf{v}}_2$, the cross or vector product between the vectors $\\overrightarrow{\\mathbf{v}}_1$ and $\\overrightarrow{\\mathbf{m}}_3-\\overrightarrow{\\mathbf{m}}_1$ (or $\\overrightarrow{\\mathbf{m}}_3-\\overrightarrow{\\mathbf{m}}_2$);  \n", "\n", "3. Third axis, $\\overrightarrow{\\mathbf{v}}_3$, the cross product between the vectors $\\overrightarrow{\\mathbf{v}}_1$ and $\\overrightarrow{\\mathbf{v}}_2$; and  \n", "\n", "4. Make all vectors to have norm 1 dividing each vector by its norm."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The positions of the points used to construct a coordinate system have, by definition, to be specified in relation to an already existing coordinate system. In motion analysis, this coordinate system is the coordinate system from the motion capture system and it is established in the calibration phase. In this phase, the positions of markers placed on an object with perpendicular axes and known distances between the markers are captured and used as the reference (laboratory) coordinate system."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["For example, given the positions $\\overrightarrow{\\mathbf{m}}_1 = [1,2,5], \\overrightarrow{\\mathbf{m}}_2 = [2,3,3], \\overrightarrow{\\mathbf{m}}_3 = [4,0,2]$, a basis can be found with:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:27:55.327026Z", "start_time": "2021-11-03T23:27:55.300280Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors: \n", "e1 = [ 0.40824829  0.40824829 -0.81649658] \n", "e2 = [-0.76834982 -0.32929278 -0.5488213 ] \n", "e3 = [-0.49292179  0.85141036  0.17924429]\n", "\n", "Norm of each versor: \n", "||e1|| = 1.0 \n", "||e2|| = 1.0 \n", "||e3|| = 1.0\n", "\n", "Test of orthogonality (cross product between versors): \n", "e1 x e2: 1.0 \n", "e1 x e3: 1.0000000000000002 \n", "e2 x e3: 0.9999999999999999\n"]}], "source": ["import numpy as np\n", "\n", "m1 = np.array([1, 2, 5])\n", "m2 = np.array([2, 3, 3])\n", "m3 = np.array([4, 0, 2])\n", "\n", "v1 = m2 - m1                # first axis\n", "v2 = np.cross(v1, m3 - m1)  # second axis\n", "v3 = np.cross(v1, v2)       # third axis\n", "\n", "# Vector normalization\n", "e1 = v1/np.linalg.norm(v1)\n", "e2 = v2/np.linalg.norm(v2)\n", "e3 = v3/np.linalg.norm(v3)\n", "\n", "print('Versors:', '\\ne1 =', e1, '\\ne2 =', e2, '\\ne3 =', e3)\n", "\n", "print('\\nNorm of each versor:',\n", "      '\\n||e1|| =', np.linalg.norm(e1),\n", "      '\\n||e2|| =', np.linalg.norm(e2),\n", "      '\\n||e3|| =', np.linalg.norm(e3))\n", "\n", "print('\\nTest of orthogonality (cross product between versors):',\n", "      '\\ne1 x e2:', np.linalg.norm(np.cross(e1, e2)),\n", "      '\\ne1 x e3:', np.linalg.norm(np.cross(e1, e3)),\n", "      '\\ne2 x e3:', np.linalg.norm(np.cross(e2, e3)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Due to rounding errors ([see here](https://en.wikipedia.org/wiki/Round-off_error)) and to the imprecision of the computation with decimal floating-point numbers ([see here](http://docs.python.org/2/tutorial/floatingpoint.html)), the norms are not exactly equal to 1.  \n", "We could round the result if desired:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Test of orthogonality (cross product between versors): \n", "e1 x e2: 1.0 \n", "e1 x e3: 1.0 \n", "e2 x e3: 1.0\n"]}], "source": ["print('\\nTest of orthogonality (cross product between versors):',\n", "      '\\ne1 x e2:', np.linalg.norm(np.cross(e1, e2)).round(8),\n", "      '\\ne1 x e3:', np.linalg.norm(np.cross(e1, e3)).round(8),\n", "      '\\ne2 x e3:', np.linalg.norm(np.cross(e2, e3)).round(8))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or format the text representing the result:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Test of orthogonality (cross product between versors): \n", "e1 x e2: 1 \n", "e1 x e3: 1 \n", "e2 x e3: 1\n"]}], "source": ["print('\\nTest of orthogonality (cross product between versors):',\n", "      f'\\ne1 x e2: {np.linalg.norm(np.cross(e1, e2)):g}',\n", "      f'\\ne1 x e3: {np.linalg.norm(np.cross(e1, e3)):g}',\n", "      f'\\ne2 x e3: {np.linalg.norm(np.cross(e2, e3)):g}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Where we used `f'{:.g}'` to format the number to a string ([see here](https://docs.python.org/dev/library/stdtypes.html#printf-style-string-formatting) and a [Python F-Strings Number Formatting Cheat Sheet](https://cheatography.com/brianallan/cheat-sheets/python-f-strings-number-formatting/)).  \n", "\n", "For a permanent change, we could use the numpy settings for the representation of the decimal point precision with the function [`np.set_printoptions`](https://numpy.org/doc/stable/reference/generated/numpy.set_printoptions.html) but it only works for numpy arrays, not with numbers. Or use the IPython magic command [`%precision`](https://ipython.readthedocs.io/en/stable/interactive/magics.html#magic-precision), but in this case you can not use the function print for showing the result."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Coordinate system: origin and basis\n", "\n", "To define a coordinate system using the calculated basis, we also need to define an origin. In principle, we could use any point as origin, but if the calculated coordinate system should follow anatomical conventions, e.g., the coordinate system origin should be at a joint center, we will have to calculate the basis and origin according to standards used in motion analysis as discussed before.   \n", "\n", "If the coordinate system is a technical basis and not anatomic-based, a common procedure in motion analysis is to define the origin for the coordinate system as the centroid (average) position among the markers at the reference frame. Using the average position across markers potentially reduces the effect of noise (for example, from soft tissue artifact) on the calculation.  \n", "\n", "For the markers in the example above, the origin of the coordinate system will be:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:27:56.641599Z", "start_time": "2021-11-03T23:27:56.631715Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Origin:  [2.33333333 1.66666667 3.33333333]\n"]}], "source": ["origin = np.mean((m1, m2, m3), axis=0)\n", "print('Origin: ', origin)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Let's plot the coordinate system and the basis using the custom Python function `CCS.py`.  \n", "We could copy and paste the content of the  `CCS.py` here (and execute the cell) or we could load the function from a directory in the Google Drive, let's do that. First, create a folder \"functions\" and place the `CCS.py` file in it.  "]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:27:58.011948Z", "start_time": "2021-11-03T23:27:57.815650Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["if 'google.colab' in str(get_ipython()):  # only if you are in Google Colab\n", "    print('Google drive will be mounted.')\n", "    print('Create folder \"functions\" and place functions in it.')\n", "    from google.colab import drive\n", "    drive.mount('/content/drive', force_remount=True)\n", "    path2 = r'/content/drive/MyDrive/functions'\n", "else:\n", "    path2 = r'./../functions'\n", "import sys\n", "sys.path.insert(1, path2)  # add to pythonpath\n", "from CCS import CCS"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:27:58.372074Z", "start_time": "2021-11-03T23:27:58.368385Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["markers = np.vstack((m1, m2, m3))\n", "basis = np.vstack((e1, e2, e3))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["#### Visualization of the coordinate system   \n", "\n", "To have an interactive matplotlib plot we have to install the library `ipympl` and restart the Google Colab environment before using it for the first time."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/bin/bash: line 1: pip: command not found\n"]}], "source": ["# to install ipympl in Google Colab (for interactive matplotlib)\n", "!pip install -q ipympl"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<p style=\"text-align:center; color:red\"><b>RESTART THE GOOGLE COLAB ENVIRONMENT AND RE-EXECUTE ALL CELLS BEFORE THIS POINT!</b></p>"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["if 'google.colab' in str(get_ipython()):\n", "    from google.colab import output\n", "    output.enable_custom_widget_manager()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:28:00.153411Z", "start_time": "2021-11-03T23:28:00.079554Z"}, "slideshow": {"slide_type": "-"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b7c60c7afc354cfab927c75de24c3808", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=500.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib widget  \n", "\n", "CCS(xyz=[], Oijk=origin, ijk=basis, point=markers, vector=True);"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### <PERSON><PERSON><PERSON> process\n", "\n", "Another classical procedure in mathematics, employing the scalar product, is known as the [<PERSON><PERSON> process](http://en.wikipedia.org/wiki/Gram%E2%80%93Schmidt_process). See the notebook [Scalar and Vector](http://nbviewer.jupyter.org/github/bmclab/BMC/blob/master/notebooks/ScalarVector.ipynb) for a demonstration of the <PERSON><PERSON>Schmidt process and how to implement it in Python.\n", "\n", "The [<PERSON><PERSON><PERSON> process](http://en.wikipedia.org/wiki/Gram%E2%80%93Schmidt_process) is a method for orthonormalizing (orthogonal unit versors) a set of vectors using the scalar product. The <PERSON><PERSON><PERSON> process works for any number of vectors.   \n", "\n", "For example, given three vectors, <span class=\"notranslate\">\n", "$\\overrightarrow{\\mathbf{a}}, \\overrightarrow{\\mathbf{b}}, \\overrightarrow{\\mathbf{c}}$</span>, in the 3D space, a basis $\\{\\hat{e}_a, \\hat{e}_b, \\hat{e}_c\\}$ can be found using the <PERSON><PERSON> process by: "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The first versor is in the <span class=\"notranslate\">\n", "$\\overrightarrow{\\mathbf{a}}$</span> direction (or in the direction of any of the other vectors):  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\hat{e}_a = \\frac{\\overrightarrow{\\mathbf{a}}}{||\\overrightarrow{\\mathbf{a}}||}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The second versor, orthogonal to $\\hat{e}_a$, can be found considering we can express vector $\\overrightarrow{\\mathbf{b}}$ in terms of the $\\hat{e}_a$ direction as:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{b}} = \\overrightarrow{\\mathbf{b}}^\\| + \\overrightarrow{\\mathbf{b}}^\\bot\n", "\\end{equation}\n", "</span>\n", "\n", "Then:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{b}}^\\bot = \\overrightarrow{\\mathbf{b}} - \\overrightarrow{\\mathbf{b}}^\\| = \\overrightarrow{\\mathbf{b}} - (\\overrightarrow{\\mathbf{b}} \\cdot \\hat{e}_a ) \\hat{e}_a\n", "\\end{equation}\n", "</span>\n", "\n", "Finally:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\hat{e}_b = \\frac{\\overrightarrow{\\mathbf{b}}^\\bot}{||\\overrightarrow{\\mathbf{b}}^\\bot||}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The third versor, orthogonal to $\\{\\hat{e}_a, \\hat{e}_b\\}$, can be found expressing the vector $\\overrightarrow{\\mathbf{C}}$ in terms of $\\hat{e}_a$ and $\\hat{e}_b$ directions as:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{c}} = \\overrightarrow{\\mathbf{c}}^\\| + \\overrightarrow{\\mathbf{c}}^\\bot\n", "\\end{equation}\n", "</span>\n", "\n", "Then:\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{c}}^\\bot = \\overrightarrow{\\mathbf{c}} - \\overrightarrow{\\mathbf{c}}^\\|\n", "\\end{equation}\n", "</span>\n", "\n", "Where:\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{c}}^\\| = (\\overrightarrow{\\mathbf{c}} \\cdot \\hat{e}_a ) \\hat{e}_a + (\\overrightarrow{\\mathbf{c}} \\cdot \\hat{e}_b ) \\hat{e}_b\n", "\\end{equation}\n", "</span>\n", "\n", "Finally:\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\hat{e}_c = \\frac{\\overrightarrow{\\mathbf{c}}^\\bot}{||\\overrightarrow{\\mathbf{c}}^\\bot||}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And an animation of the Gram-Schmidt process being executed:\n", "\n", "<div class='center-align'><figure><img src=\"https://upload.wikimedia.org/wikipedia/commons/e/ee/Gram<PERSON>Schmidt_orthonormalization_process.gif\" width=450/><figcaption><center><i>Figure. The Gram-Schmidt process being executed on three vectors of a basis for R3 (<a href=\"https://commons.wikimedia.org/w/index.php?curid=24396471\">image from Wikipedia</a>).</i></center></figcaption></figure></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Let's implement the <PERSON><PERSON><PERSON> process in Python.\n", "\n", "For example, given the positions we seen before, $\\overrightarrow{\\mathbf{m}}_1 = [1,2,5], \\overrightarrow{\\mathbf{m}}_2 = [2,3,3], \\overrightarrow{\\mathbf{m}}_3 = [4,0,2]$, a basis can be found with:"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The first versor is:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:37:40.604375Z", "start_time": "2021-11-03T23:37:40.599445Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.18257419 0.36514837 0.91287093]\n"]}], "source": ["ea = m1/np.linalg.norm(m1)\n", "print(ea)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The second versor is:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:37:42.965121Z", "start_time": "2021-11-03T23:37:42.955392Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.59020849  0.70186955 -0.39878952]\n"]}], "source": ["eb = m2 - np.dot(m2, ea)*ea\n", "eb = eb/np.linalg.norm(eb)\n", "print(eb)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["And the third version is:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:37:54.663236Z", "start_time": "2021-11-03T23:37:54.656571Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.78633365 -0.61159284  0.08737041]\n"]}], "source": ["ec = m3 - np.dot(m3, ea)*ea - np.dot(m3, eb)*eb\n", "ec = ec/np.linalg.norm(ec)\n", "print(ec)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Let's check the orthonormality between these versors:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2021-11-03T23:38:07.791011Z", "start_time": "2021-11-03T23:38:07.776900Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Versors: \n", "ea = [0.18257419 0.36514837 0.91287093] \n", "eb = [ 0.59020849  0.70186955 -0.39878952] \n", "ec = [ 0.78633365 -0.61159284  0.08737041]\n", "\n", " Norm of each versor: \n", " ||ea|| = 1.0 \n", " ||eb|| = 1.0 \n", " ||ec|| = 1.0\n", "\n", " Test of orthogonality (scalar product between versors): \n", " ea . eb: -3.3306690738754696e-16 \n", " eb . ec: 5.551115123125783e-17 \n", " ec . ea: 8.326672684688674e-17\n"]}], "source": ["print(\" Versors:\", \"\\nea =\", ea, \"\\neb =\", eb, \"\\nec =\", ec)\n", "\n", "print(\n", "    \"\\n Norm of each versor:\",\n", "    \"\\n ||ea|| =\",\n", "    np.linalg.norm(ea),\n", "    \"\\n ||eb|| =\",\n", "    np.linalg.norm(eb),\n", "    \"\\n ||ec|| =\",\n", "    np.linalg.norm(ec),\n", ")\n", "\n", "print(\n", "    \"\\n Test of orthogonality (scalar product between versors):\",\n", "    \"\\n ea . eb:\",\n", "    np.dot(ea, eb),\n", "    \"\\n eb . ec:\",\n", "    np.dot(eb, ec),\n", "    \"\\n ec . ea:\",\n", "    np.dot(ec, ea),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Visualization of the coordinate system"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# your turn ...\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "- [The right frame of reference makes it simple: An example of introductory mechanics supported by video analysis of motion](https://www.researchgate.net/publication/267761615_The_right_frame_of_reference_makes_it_simple_An_example_of_introductory_mechanics_supported_by_video_analysis_of_motion)  \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the Internet\n", "\n", "- [Introduction to reference - Khan Academy](https://www.khanacademy.org/science/physics/one-dimensional-motion/displacement-velocity-time/v/introduction-to-reference-frames)  \n", "- [Introduction to orthonormal bases - Khan Academy](https://www.khanacademy.org/math/linear-algebra/alternate-bases/orthonormal-basis/v/linear-algebra-introduction-to-orthonormal-bases)  \n", "- [The Gram-Schmidt process - Khan Academy](https://www.khanacademy.org/math/linear-algebra/alternate-bases/orthonormal-basis/v/linear-algebra-the-gram-schmidt-process)  \n", "- [Biomechanics of Movement | Demo: Motion Capture Placement and Reference Frames](https://youtu.be/ctwoH59Obew)  "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Problems\n", "\n", "1. Right now, how fast are you moving? In your answer, consider your motion in relation to Earth and in relation to Sun.\n", "\n", "2. Go to the website [http://www.wisc-online.com/Objects/ViewObject.aspx?ID=AP15305](http://www.wisc-online.com/Objects/ViewObject.aspx?ID=AP15305) and complete the interactive lesson to learn about the anatomical terminology to describe relative position in the human body.\n", "\n", "3. To learn more about Cartesian coordinate systems go to the website [http://www.mathsisfun.com/data/cartesian-coordinates.html](http://www.mathsisfun.com/data/cartesian-coordinates.html), study the material, and answer the 10 questions at the end.\n", "\n", "4. Given the points in the 3D space, m1 = [2, 2, 0], m2 = [0, 1, 1], m3 = [1, 2, 0], find an orthonormal basis.\n", "\n", "5. Determine if the following points form a basis in the 3D space, m1 = [2, 2, 0], m2 = [1, 1, 1], m3 = [1, 1, 0].\n", "\n", "6. Derive expressions for the three axes of the pelvic basis considering the convention of the [Virtual Animation of the Kinematics of the Human for Industrial, Educational and Research Purposes (VAKHUM)](https://github.com/BMClab/BMC/blob/master/courses/refs/VAKHUM.pdf) project (use RASIS, LASIS, RPSIS, and LPSIS as names for the pelvic anatomical landmarks and indicate the expression for each axis).\n", "\n", "7. Determine the basis for the pelvis following the convention of the [Virtual Animation of the Kinematics of the Human for Industrial, Educational and Research Purposes (VAKHUM)](https://github.com/BMClab/BMC/blob/master/courses/refs/VAKHUM.pdf) project for the following anatomical landmark positions (units in meters): RASIS=[0.5, 0.8, 0.4], LASIS=[0.55, 0.78, 0.1], RPSIS=[0.3, 0.85, 0.2], LPSIS=[0.29, 0.78, 0.3]."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## References\n", "\n", "- <PERSON><PERSON> (2017) [Robotics, Vision and Control: Fundamental Algorithms in MATLAB](https://petercorke.com/RVC/). 2nd ed. Springer-Verlag Berlin.  \n", "- [Standards - International Society of Biomechanics](https://isbweb.org/activities/standards).  \n", "- Stanford Encyclopedia of Philosophy. [Space and Time: Inertial Frames](http://plato.stanford.edu/entries/spacetime-iframes/).  \n", "- [Virtual Animation of the Kinematics of the Human for Industrial, Educational and Research Purposes (VAKHUM)](https://github.com/BMClab/BMC/blob/master/courses/refs/VAKHUM.pdf).  "]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "348.906px"}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}