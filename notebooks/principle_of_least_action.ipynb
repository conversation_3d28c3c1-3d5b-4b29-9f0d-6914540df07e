{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Principle of least action\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The [principle of least action](https://en.wikipedia.org/wiki/Principle_of_least_action) applied to the movement of a mechanical system states that \"the average kinetic energy less the average potential energy is as little as possible for the path of an object going from one point to another\" (Prof. <PERSON> in [The <PERSON><PERSON><PERSON> Lectures on Physics](http://www.feynmanlectures.caltech.edu/II_19.html)). \n", "\n", "This principle is so fundamental that it can be used to derive the equations of motion of a system, for example, independent of the Newton's laws of motion. Let's now see the principle of least action in mathematical terms.\n", "\n", "The difference between the kinetic and potential energy in a system is known as the [Lagrange or Lagrangian function](https://en.wikipedia.org/wiki/Lagrangian_mechanics): \n", "\n", "\\begin{equation} \n", "\\mathcal{L} = T - V \n", "\\label{eq_lagrange}\n", "\\end{equation}\n", "\n", "The [principle of least action](https://en.wikipedia.org/wiki/Principle_of_least_action) states that the actual path which a system follows between two points in the time interval $t_1$ and $t_2$ is such that the integral\n", "\n", "\\begin{equation} \n", "\\mathcal{S}\\; =\\; \\int _{t_1}^{t_2} \\mathcal{L} \\; dt\n", "\\label{eq_action}\n", "\\end{equation}\n", "\n", "is stationary, meaning that $\\delta \\mathcal{S}=0$ (i.e., the value of $\\mathcal{S}$ is an extremum), and it can be shown in fact that the value of this integral is a minimum for the actual path of the system. The integral above is known as the action integral and $\\mathcal{S}$ is known as the action.\n", "\n", "A formal demonstration that the the value of this integral above is stationary and that one can derive the <PERSON>uler-<PERSON> equation in the present form we use today was first formulated by the mathematician [<PERSON>](https://en.wikipedia.org/wiki/<PERSON>) in the XIXth century; because that the principle of least action as presented above is also known as [<PERSON>'s principle](https://en.wikipedia.org/wiki/<PERSON>%27s_principle).\n", "\n", "For a simple and didactic example where the integral above is stationary, see [The <PERSON><PERSON><PERSON> Lectures on Physics](http://www.feynmanlectures.caltech.edu/II_19.html).  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> (2013) [The <PERSON><PERSON><PERSON> Lectures on Physics - HTML edition](http://www.feynmanlectures.caltech.edu/).  \n", "- <PERSON> (2005) [Classical Mechanics](https://books.google.com.br/books?id=P1kCtNr-pJsC). University Science Books.  \n"]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}