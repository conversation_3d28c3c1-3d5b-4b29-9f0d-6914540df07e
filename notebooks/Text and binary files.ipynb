{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Text and binary files\n", "\n", "There are two kinds of computer files: text and binary files. Text files are structured as a sequence of lines of electronic text. The most common formats of a text file are ASCII (with 128 ($2^7$)) different characters and UTF-8 (which includes non-English characters). A binary file is a file that is not structured as a text file. Because in fact everything in a computer is stored in binary format (a sequence of zeros and ones), text files are binary files that store text codes.\n", "\n", "To open and read a text file is simple and straightforward. A text file doesn't need additional information to be read, and can be opened by any text-processing software. This is not the case of a binary file, we need to have extra information about how the file is structured to be able to read it. However, binary files can store more information per file size than text files and we can read and write binary files faster than text files. This is one of the reasons why software developers would choose a binary format."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}