{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sympy as sym\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["def rotationGlobalX(alpha):\n", "    return np.array([[1,0,0],[0,np.cos(alpha),-np.sin(alpha)],[0,np.sin(alpha),np.cos(alpha)]])\n", "\n", "def rotationGlobalY(beta):\n", "    return np.array([[np.cos(beta),0,np.sin(beta)], [0,1,0],[-np.sin(beta),0,np.cos(beta)]])\n", "\n", "def rotationGlobalZ(gamma):\n", "    return np.array([[np.cos(gamma),-np.sin(gamma),0],[np.sin(gamma),np.cos(gamma),0],[0,0,1]])\n", "\n", "def rotationLocalX(alpha):\n", "    return np.array([[1,0,0],[0,np.cos(alpha),np.sin(alpha)],[0,-np.sin(alpha),np.cos(alpha)]])\n", "\n", "def rotationLocalY(beta):\n", "    return np.array([[np.cos(beta),0,-np.sin(beta)], [0,1,0],[np.sin(beta),0,np.cos(beta)]])\n", "\n", "def rotationLocalZ(gamma):\n", "    return np.array([[np.cos(gamma),np.sin(gamma),0],[-np.sin(gamma),np.cos(gamma),0],[0,0,1]])"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to  previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overriden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"1000\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(3, 900)\n"]}], "source": ["from mpl_toolkits.mplot3d import Axes3D\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "%matplotlib notebook\n", "plt.rcParams['figure.figsize']=10,10\n", "\n", "\n", "coefs = (1, 3, 15)  # Coefficients in a0/c x**2 + a1/c y**2 + a2/c z**2 = 1 \n", "# Radii corresponding to the coefficients:\n", "rx, ry, rz = 1/np.sqrt(coefs)\n", "\n", "# Set of all spherical angles:\n", "u = np.linspace(0, 2 * np.pi, 30)\n", "v = np.linspace(0, np.pi, 30)\n", "\n", "# Cartesian coordinates that correspond to the spherical angles:\n", "# (this is the equation of an ellipsoid):\n", "x = rx * np.outer(np.cos(u), np.sin(v))\n", "y = ry * np.outer(np.sin(u), np.sin(v))\n", "z = rz * np.outer(np.ones_like(u), np.cos(v))\n", "\n", "\n", "fig = plt.figure(figsize=plt.figaspect(1))  # Square figure\n", "ax = fig.add_subplot(111, projection='3d')\n", "\n", "xr = np.reshape(x, (1,-1))\n", "yr = np.reshape(y, (1,-1))\n", "zr = np.reshape(z, (1,-1))\n", "\n", "RX = rotationGlobalX(np.pi/3)\n", "RY = rotationGlobalY(np.pi/3)\n", "RZ = rotationGlobalZ(np.pi/3)\n", "Rx = rotationLocalX(np.pi/3)\n", "Ry = rotationLocalY(np.pi/3)\n", "Rz = rotationLocalZ(np.pi/3)\n", "\n", "rRotx = RZ@RY@<EMAIL>((xr,yr,zr))\n", "print(np.shape(rRotx))\n", "# Plot:\n", "ax.plot_surface(np.reshape(rRotx[0,:],(30,30)), np.reshape(rRotx[1,:],(30,30)), \n", "                np.reshape(rRotx[2,:],(30,30)), rstride=4, cstride=4, color='b')\n", "\n", "# Adjustment of the axes, so that they all have the same span:\n", "max_radius = max(rx, ry, rz)\n", "for axis in 'xyz':\n", "    getattr(ax, 'set_{}lim'.format(axis))((-max_radius, max_radius))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to  previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overriden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"1000\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(3, 900)\n"]}], "source": ["\n", "\n", "coefs = (1, 3, 15)  # Coefficients in a0/c x**2 + a1/c y**2 + a2/c z**2 = 1 \n", "# Radii corresponding to the coefficients:\n", "rx, ry, rz = 1/np.sqrt(coefs)\n", "\n", "# Set of all spherical angles:\n", "u = np.linspace(0, 2 * np.pi, 30)\n", "v = np.linspace(0, np.pi, 30)\n", "\n", "# Cartesian coordinates that correspond to the spherical angles:\n", "# (this is the equation of an ellipsoid):\n", "x = rx * np.outer(np.cos(u), np.sin(v))\n", "y = ry * np.outer(np.sin(u), np.sin(v))\n", "z = rz * np.outer(np.ones_like(u), np.cos(v))\n", "\n", "\n", "fig = plt.figure(figsize=plt.figaspect(1))  # Square figure\n", "ax = fig.add_subplot(111, projection='3d')\n", "\n", "xr = np.reshape(x, (1,-1))\n", "yr = np.reshape(y, (1,-1))\n", "zr = np.reshape(z, (1,-1))\n", "\n", "RX = rotationGlobalX(np.pi/3)\n", "RY = rotationGlobalY(np.pi/3)\n", "RZ = rotationGlobalZ(np.pi/3)\n", "Rx = rotationLocalX(np.pi/3)\n", "Ry = rotationLocalY(np.pi/3)\n", "Rz = rotationLocalZ(np.pi/3)\n", "\n", "rRotx = RY@<EMAIL>((xr,yr,zr))\n", "print(np.shape(rRotx))\n", "# Plot:\n", "ax.plot_surface(np.reshape(rRotx[0,:],(30,30)), np.reshape(rRotx[1,:],(30,30)), \n", "                np.reshape(rRotx[2,:],(30,30)), rstride=4, cstride=4, color='b')\n", "\n", "# Adjustment of the axes, so that they all have the same span:\n", "max_radius = max(rx, ry, rz)\n", "for axis in 'xyz':\n", "    getattr(ax, 'set_{}lim'.format(axis))((-max_radius, max_radius))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.71414284285428498"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sin(np.arc<PERSON>(0.7))"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0.25       -0.0580127   0.96650635]\n", " [ 0.4330127   0.89951905 -0.0580127 ]\n", " [-0.8660254   0.4330127   0.25      ]]\n"]}], "source": ["print(RZ@RY@RX)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["import sympy as sym\n", "sym.init_printing()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["a,b,g = sym.symbols('alpha, beta, gamma')"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAucAAABMBAMAAAA8bQT/AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA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\n", "text/latex": ["$$\\left ( \\left[\\begin{matrix}1 & 0 & 0\\\\0 & \\cos{\\left (\\alpha \\right )} & - \\sin{\\left (\\alpha \\right )}\\\\0 & \\sin{\\left (\\alpha \\right )} & \\cos{\\left (\\alpha \\right )}\\end{matrix}\\right], \\quad \\left[\\begin{matrix}\\cos{\\left (\\beta \\right )} & 0 & \\sin{\\left (\\beta \\right )}\\\\0 & 1 & 0\\\\- \\sin{\\left (\\beta \\right )} & 0 & \\cos{\\left (\\beta \\right )}\\end{matrix}\\right], \\quad \\left[\\begin{matrix}\\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\gamma \\right )} & 0\\\\\\sin{\\left (\\gamma \\right )} & \\cos{\\left (\\gamma \\right )} & 0\\\\0 & 0 & 1\\end{matrix}\\right]\\right )$$"], "text/plain": ["⎛⎡1    0        0   ⎤  ⎡cos(β)   0  sin(β)⎤  ⎡cos(γ)  -sin(γ)  0⎤⎞\n", "⎜⎢                  ⎥  ⎢                  ⎥  ⎢                  ⎥⎟\n", "⎜⎢0  cos(α)  -sin(α)⎥, ⎢   0     1    0   ⎥, ⎢sin(γ)  cos(γ)   0⎥⎟\n", "⎜⎢                  ⎥  ⎢                  ⎥  ⎢                  ⎥⎟\n", "⎝⎣0  sin(α)  cos(α) ⎦  ⎣-sin(β)  0  cos(β)⎦  ⎣  0        0     1⎦⎠"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["RX = sym.Matrix([[1,0,0],[0,sym.cos(a),-sym.sin(a)],[0,sym.sin(a),sym.cos(a)]])\n", "RY = sym.Matrix([[sym.cos(b),0,sym.sin(b)],[0,1,0],[-sym.sin(b),0,sym.cos(b)]])\n", "RZ = sym.Matrix([[sym.cos(g),-sym.sin(g),0],[sym.sin(g),sym.cos(g),0],[0,0,1]])\n", "RX,RY,RZ"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1oAAABMBAMAAACG69u2AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA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\n", "text/latex": ["$$\\left[\\begin{matrix}\\cos{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} - \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\gamma \\right )} + \\sin{\\left (\\beta \\right )} \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )}\\\\\\sin{\\left (\\gamma \\right )} \\cos{\\left (\\beta \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} + \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )} + \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )}\\\\- \\sin{\\left (\\beta \\right )} & \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )} & \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )}\\end{matrix}\\right]$$"], "text/plain": ["⎡cos(β)⋅cos(γ)  sin(α)⋅sin(β)⋅cos(γ) - sin(γ)⋅cos(α)  sin(α)⋅sin(γ) + sin(β)⋅c\n", "⎢                                                                             \n", "⎢sin(γ)⋅cos(β)  sin(α)⋅sin(β)⋅sin(γ) + cos(α)⋅cos(γ)  -sin(α)⋅cos(γ) + sin(β)⋅\n", "⎢                                                                             \n", "⎣   -sin(β)                sin(α)⋅cos(β)                          cos(α)⋅cos(β\n", "\n", "os(α)⋅cos(γ) ⎤\n", "             ⎥\n", "sin(γ)⋅cos(α)⎥\n", "             ⎥\n", ")            ⎦"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["R = RZ@RY@RX\n", "R"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["mm = np.array([2.71, 10.22, 26.52])\n", "lm = np.array([2.92, 10.10, 18.85])\n", "fh = np.array([5.05, 41.90, 15.41])\n", "mc = np.array([8.29, 41.88, 26.52])\n", "ajc = (mm + lm)/2\n", "kjc = (fh + mc)/2"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [], "source": ["i = np.array([1,0,0])\n", "j = np.array([0,1,0])\n", "k = np.array([0,0,1])\n", "v1 = kjc - ajc\n", "v1 = v1 / np.sqrt(v1[0]**2+v1[1]**2+v1[2]**2)\n", "v2 = (mm-lm) - ((mm-lm)@v1)*v1\n", "v2 = v2/ np.sqrt(v2[0]**2+v2[1]**2+v2[2]**2)\n", "v3 = k - (k@v1)*v1 - (k@v2)*v2\n", "v3 = v3/ np.sqrt(v3[0]**2+v3[1]**2+v3[2]**2)"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.12043275,  0.99126617, -0.05373394])"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["v1"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.12043275, -0.02238689,  0.99246903],\n", "       [ 0.99126617,  0.05682604, -0.11900497],\n", "       [-0.05373394,  0.99813307,  0.02903508]])"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["R = np.array([v1,v2,v3])\n", "RGlobal = R.T\n", "RGlobal"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAIAAAAAOBAMAAAAVqglQAAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAEHaJmUSrVGYizbvd\n7zLJaKAlAAAACXBIWXMAAA7EAAAOxAGVKw4bAAACG0lEQVQoFVWTPYgTQRSAv73Nz242P4taKok/\nCNoYDBxB9MwRRbTQIJaCi1xxWOhWtgna2BwX8FrJnha2sVARLQLCidp4hYgWmkKshDOed/EUWd/M\nLmxui4+dN9+8efN2FnbsP00E4G7t6QSKK81aje9ztVrVaN4ekttbPAO76jOxc9/96kODjB8B7Abt\nTgIrDMNNFoVezjdeUwjDKjxkd1c7fAnXIdvBDjSkgKkxpUGCVBc8zkKGBThGbvoQWO8wA+3w4KoI\nZh9joCEJ0u8pBwlMmR4im87wBNpuRhQcj+xIO5JcnvRf1+xoqJFonW24qGLpCptwxdcJSh7pceR4\napIbv+sx1Mh4uQ30VdDB+CUJKpk7B4aUG6R/RuL8rfMya4bLMWRgH5+dBGZHgtSxpVtrVce1N+hV\nKa5H4iN6Lbh5+V8rgnI5IaEEZRUyfmBLBWvSDF7Qa6gEseMMsDyOrGooFwqrkzisQlNBfAR5v9CN\njxCJqS0KXYpjDZm2uzjjBPBWJchLI6SJbf8anGxJE7ORkwkwN1BFzmvIS2kkCRJgyDooe/AcFt1P\nUoHr9LFG2ikEpEaqAp5piFrok99KQFGOLodvoC7SObmvvFEXKRVoJyXFBaSXsSoaxh+sDu1KArI6\nQVsW5n3jM0tYH+EVe4bakfu8INt/a8rPpLECl049noT9QQrgni9f4ujBIXZzzoWd+67HztL0LP8B\nLdLN6pflbmIAAAAASUVORK5CYII=\n", "text/latex": ["$$88.3337705035$$"], "text/plain": ["88.3337705035"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha = np.arctan2(RGlobal[2,1],RGlobal[2,2])*180/np.pi\n", "alpha"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAIAAAAAPBAMAAADe9tr1AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAIom7VJlmdt1E7xDN\nqzIhoty3AAAACXBIWXMAAA7EAAAOxAGVKw4bAAACSUlEQVQoFaWSz2sTURSFv0zSyY/JJKOgiBtD\npFVcaKRudCEiuBFKByWLqoH5D6xC6bKhulTMRnDXSIuoXTgUSkUoCSiKBGlcWKVSGwoupdqGapvQ\n8c7LIMnaWQzn3XPf9867M7A/ewb/mR84CQu3RwJ14Op5VQ5eofxgA7Rj0jOf/QiB/R1CNsOO3/WG\nlxY25gWljDqnSnDZFScyC4toOxjvuYHxhMNWYHMHok3SRenSy2jViEOoqpTpkigbn9YEsFhowVd4\nTthmELOMaStbNk9D/BmVqgDCdSItLYdRVCpRxNyDCT9BVABPYa0xZckqXUX7GdgHX0uBzhXSdeLN\n+JarOUolW72AJVcA5/zuSo7oXmBnFMCYVHWb+G/WtgeoKAUxOfhfAml54LbXx0okJMGOLMXWLR8Q\nOn7dBxzJkNpE82qBgqlMDyC+abQdZiQ/yW3ZIPYCnSucKPkA2wcMPdotdRTcl2pXgljd8Fyuubzj\nYlMssTMBICGfqRNcrzMxG1yhz+4F5OEP3Cyhj6/LeMWONHxAyCLsA2V0kWbCItVUCsak2JUgKrx7\nAnCkasp0xJ7DB6RbHUA4h96qiLusFBGbfd2AS3LUikoASTlM7KHV1d3PJHLEfsk+vUxfVRJwWink\nJ73VBUjZRK0NmQH6YzaswGZaNjoMjxptuMurRryGPqpUaDJbKHYBDmX7v2BmjBmiNeMtgc2WHP0w\nf1aNdG78G/y4MiJ3E5X0PK9I4cUHh76V9jJLnicj7C805JUvBTZHvWUh/N/zF0rm5PkTQCzHAAAA\nAElFTkSuQmCC\n", "text/latex": ["$$3.08021126924$$"], "text/plain": ["3.08021126924"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["beta = np.arctan2(-RGlobal[2,0],np.sqrt(RGlobal[2,1]**2+RGlobal[2,2]**2))*180/np.pi\n", "beta"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAIAAAAAPBAMAAADe9tr1AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAEHaJmUSrVGYizbvd\n7zLJaKAlAAAACXBIWXMAAA7EAAAOxAGVKw4bAAACdElEQVQoFaWTz2sTQRTHP9tNNrv5ubSexJJq\ni1Avhkak+KNGUitUxCAehQYrpQhqTr1mwYsXMWIPemp6EAVBevAHUpVAoaK9pAcpKsiC4rVE7Q9b\n6/pmNv+BAzPz3vvMfnnz5i109p6Cu/kXqLFrcIj0YjGfZ/rMG0KGOTBEfG/6NEbxhk9IyPeKyTeZ\nBayKU6Dqic1D9tTsIAjWjTInSpphPGCUVBDkiFeMd4RkdylSl/NLEPNw6h0bZBri203MeqQGZcsl\n1dSMRIF54of74RYcQROekpiDjs9gzmE0oh/IKsFEmVjLlKCfamD91YwxEcSSyXOouppEfyufe48h\nuu2antj6Cpky0Q1xzhNviUDIXksgFFiHsYomiaYKkhMBrm8OymosqEC2QPSH7JIdJFsh2/raV8K6\n2ecbv0SgR5PMkwEpvV1TAmYwC86xk2IykyP9U0KecsZymhlbHiskXGfNEbSa0yT7iFSFTpTA1MWd\nkmzH1TJT0AJZMWExZEbgctYV/5UjGWiBRbItIsvklIBd5uCy4JRa2lc4ICaRQpv9gdtK/dyd9hWE\nyJvZazFfCaRqpDecGglVPCliTPYlMelrM96LgHcF6QspYrWiSbKOvdmFElDpTmZaoYC8rd3CkIPE\nCtzXjKsqg0+SgTsvlquJ1ZQMpiYmdi6pDHiZmiOpXtZuIg2WllRVk/ZrxqrUQHpSMpFGGg1JWmrQ\nlFPSSNFZ7B7bo9pjbMFbun1iIuAs5EcammHljBWmsT+SrBhfQsI43XIZtmV+L8qLXhh+psvete+a\nfLwCcfkhGiHj0IiPU7zsYgzs99vEHJaDHA0mZf2/8Q8vudLvoq3yhAAAAABJRU5ErkJggg==\n", "text/latex": ["$$83.0728650264$$"], "text/plain": ["83.0728650264"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["gamma = np.arctan2(RGlobal[1,0],RGlobal[0,0])*180/np.pi\n", "gamma"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.  ,  0.71,  0.7 ],\n", "       [ 0.  ,  0.7 , -0.71],\n", "       [-1.  ,  0.  ,  0.  ]])"]}, "execution_count": 138, "metadata": {}, "output_type": "execute_result"}], "source": ["R2 = np.array([[0, 0.71, 0.7],[0,0.7,-0.71],[-1,0,0]])\n", "R2"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABoAAAAOBAMAAADDIxFwAAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAEJmJZjLNVN0i77ur\nRHZ72Yd1AAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAdUlEQVQIHWNgEDIxZQABCJ3OIDEBxAPTLAsY\nmBOAHAjNfYCB/SOQB6H5DzDwfgfyILS8AgPvPyAPQs83YGD9DORB6PkKUB6YRlUJ1M0ONQVIc29g\nYAHbAKaBtjIlAE2B0u0MUgGM3xgYQDQDg/C7owwMPRAaABkSKGrnHpT1AAAAAElFTkSuQmCC\n", "text/latex": ["$$0.0$$"], "text/plain": ["0.0"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha = np.arctan2(R[2,1],R[2,2])*180/np.pi\n", "alpha"]}, {"cell_type": "code", "execution_count": 127, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAABoAAAAOBAMAAADDIxFwAAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAEJmJZjLNVN0i77ur\nRHZ72Yd1AAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAdUlEQVQIHWNgEDIxZQABCJ3OIDEBxAPTLAsY\nmBOAHAjNfYCB/SOQB6H5DzDwfgfyILS8AgPvPyAPQs83YGD9DORB6PkKUB6YRlUJ1M0ONQVIc29g\nYAHbAKaBtjIlAE2B0u0MUgGM3xgYQDQDg/C7owwMPRAaABkSKGrnHpT1AAAAAElFTkSuQmCC\n", "text/latex": ["$$0.0$$"], "text/plain": ["0.0"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["gamma = np.arctan2(R[1,0],R[0,0])*180/np.pi\n", "gamma"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAACQAAAAOBAMAAAC1GaP7AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAEJmJqyLvZs27djJE\nVN3lLQH2AAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAt0lEQVQYGWNgEDJ2ZWAQMrdmAAEwzZjOEBbA\n0MsgFwASAtMsCQwsCuwJDMwNQBEIzd/AwPyB04GB9yNQCELLT2Dg+s3vwMDzFygEodmAqv7IKzDw\n/AIKQWjmDwwc3+MNGLg/A4WgdBfD5b/xClAhCM2+avNvVI1A5SwfgcbyQo0H0wwcDpwTGNjBjgDT\n7KkM+wOATmRqAKqH0FyNjO0MDG8ZxDYw/oDQDAxGSgcYGIRXeTMwPIHQAAacNpe4udDtAAAAAElF\nTkSuQmCC\n", "text/latex": ["$$90.0$$"], "text/plain": ["90.0"]}, "execution_count": 129, "metadata": {}, "output_type": "execute_result"}], "source": ["beta = np.arctan2(-R[2,0],np.sqrt(R[2,1]**2+R[2,2]**2))*180/np.pi\n", "beta"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA20AAABMBAMAAAAMxEL3AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA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\nYQNFYGJ2dpb+D15B5857Z5jOAAAAAElFTkSuQmCC\n", "text/latex": ["$$\\left[\\begin{matrix}\\cos{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} - \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\beta \\right )} + \\sin{\\left (\\beta \\right )} \\cos{\\left (\\alpha \\right )}\\\\\\sin{\\left (\\gamma \\right )} & \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )}\\\\- \\sin{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} & \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )} + \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )} & - \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} + \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )}\\end{matrix}\\right]$$"], "text/plain": ["⎡cos(β)⋅cos(γ)   sin(α)⋅sin(β) - sin(γ)⋅cos(α)⋅cos(β)  sin(α)⋅sin(γ)⋅cos(β) + \n", "⎢                                                                             \n", "⎢    sin(γ)                 cos(α)⋅cos(γ)                         -sin(α)⋅cos(\n", "⎢                                                                             \n", "⎣-sin(β)⋅cos(γ)  sin(α)⋅cos(β) + sin(β)⋅sin(γ)⋅cos(α)  -sin(α)⋅sin(β)⋅sin(γ) +\n", "\n", "sin(β)⋅cos(α) ⎤\n", "              ⎥\n", "γ)            ⎥\n", "              ⎥\n", " cos(α)⋅cos(β)⎦"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["R = RY@RZ@RX\n", "R"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAIAAAAAPBAMAAADe9tr1AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv3aB7AAAAD3RSTlMAMpndu3bvImbNiRBU\nq0Qb3U6NAAAACXBIWXMAAA7EAAAOxAGVKw4bAAACLUlEQVQoFaWSvWtTURjGf0luktt809HFawZB\nitLBRVG8SHFRNAg6OAUsLq0adBGXZlFx8goKImLTakUKQlAsWJBEd2lEKkUJDf4DDn62iY3POQlc\nd8/w5Dy/9z3Pve/JBRiFu/XRpnZaMs+fzmmzeKbA1cVjhkU8SC3MkSxOFBhAWxmW2QsX+5tqNEvm\nAS8qvOpEg6THSlUs5pN+yC5ek<PERSON>r<PERSON>wl<PERSON>Ujk/C/dWKdgPjlkkFHMbx4z3y44KnfRyPCT7DOSwMReUr\nd6BlDpsl47TIdHO/ZHIXaASQvOnz1TzgLKwXLAxFuPZvgEy+Ra7nlFXRMiOkRnyOGDNbV8AQDoYz\nZbeigLVrO0yHNQ2P3I/87YU98un9khsK2Nh2sqMth+pDaCtWXqKAo8zYujEzNSKbjXskmiQfjam/\nNOKnN6pM63xOd21hKNRMADjjRo2Z8UxAl+iUwJMOKRTQr7NTD4+1TJdgKJnCICBqbs2awQjjuFsi\niSneKIBvcEvHikIWhrKMArKB+YfBGl1iphcLcH8nKzi9dM0E7FNAlbinCQwMhXft9s/VREC0qwBr\nHB+3my3rDfJd9Wba7fXzpY/2Dd7quIWh6BSTRDVdYLbGuGWiQUR3UE74xMxk+t2uOyDiEa9YGIrK\nfDef5/VKemNgOMBSgU8sNd0qKyXBvE+2lp7m8uL8BywMReXH/TUuPRuDg0OzfOq9vp6i5EvxuFjq\nxJ8q87sLzPb7ukwLQ1HDf62/TD/Zp8Zq5ZoAAAAASUVORK5CYII=\n", "text/latex": ["$$45.4063462333$$"], "text/plain": ["45.4063462333"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha = np.arctan2(-R2[1,2],R2[1,1])*180/np.pi\n", "alpha"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [], "source": ["gamma = 0"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [], "source": ["beta = 90"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sympy as sym\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["sym.init_printing()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["a,b,g = sym.symbols('alpha, beta, gamma')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAucAAABMBAMAAAA8bQT/AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA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\n", "text/latex": ["$$\\left ( \\left[\\begin{matrix}1 & 0 & 0\\\\0 & \\cos{\\left (\\alpha \\right )} & - \\sin{\\left (\\alpha \\right )}\\\\0 & \\sin{\\left (\\alpha \\right )} & \\cos{\\left (\\alpha \\right )}\\end{matrix}\\right], \\quad \\left[\\begin{matrix}\\cos{\\left (\\beta \\right )} & 0 & \\sin{\\left (\\beta \\right )}\\\\0 & 1 & 0\\\\- \\sin{\\left (\\beta \\right )} & 0 & \\cos{\\left (\\beta \\right )}\\end{matrix}\\right], \\quad \\left[\\begin{matrix}\\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\gamma \\right )} & 0\\\\\\sin{\\left (\\gamma \\right )} & \\cos{\\left (\\gamma \\right )} & 0\\\\0 & 0 & 1\\end{matrix}\\right]\\right )$$"], "text/plain": ["⎛⎡1    0        0   ⎤  ⎡cos(β)   0  sin(β)⎤  ⎡cos(γ)  -sin(γ)  0⎤⎞\n", "⎜⎢                  ⎥  ⎢                  ⎥  ⎢                  ⎥⎟\n", "⎜⎢0  cos(α)  -sin(α)⎥, ⎢   0     1    0   ⎥, ⎢sin(γ)  cos(γ)   0⎥⎟\n", "⎜⎢                  ⎥  ⎢                  ⎥  ⎢                  ⎥⎟\n", "⎝⎣0  sin(α)  cos(α) ⎦  ⎣-sin(β)  0  cos(β)⎦  ⎣  0        0     1⎦⎠"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["RX = sym.Matrix([[1,0,0],[0,sym.cos(a), -sym.sin(a)],[0,sym.sin(a), sym.cos(a)]])\n", "RY = sym.Matrix([[sym.cos(b),0, sym.sin(b)],[0,1,0],[-sym.sin(b),0, sym.cos(b)]])\n", "RZ = sym.Matrix([[sym.cos(g), -sym.sin(g), 0],[sym.sin(g), sym.cos(g),0],[0,0,1]])\n", "RX,RY,RZ"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAM4AAABLCAMAAAAPvEidAAAAPFBMVEX///8AAAAAAAAAAAAAAAAA\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAo1xBWAAAAE3RSTlMA\nMquZdlQQQOkwRO/NZondIrtsRPdGkQAAAAlwSFlzAAAOxAAADsQBlSsOGwAABMNJREFUaAXtm9GW\nmyAQhlHUbqOglvd/18IACooMIOY0PclFVhFn5gMG4XdDGgGflnz0Z9YUhDSCdvLTfzQNYYqhFQqn\n+WySPXpWBWegnPFH2iTT8gGnH4cdNf1okgN1mJf0G5JrZlp2cYaWtqIEh40qPD4lB5lcMdeyiyOd\ndEU4K0yKfdG9cbJcy1VwBOC8RP25MddyDZxBUNXGL9HFmzr/arblGjiL4CrSRv/JD/r6jmzLdXCg\ndx7BybRcAyd7SFx3x+FKtuUaOEQnbP/YVJBuuQrOuqpWZU9M1JmWq+Dohx197DGabrkKDpnVImd8\nYtGWadnDoe0oppYd8jHhdOBybf4EDcm07OEkBP6PV/ni/Msd9O2db++8rQW+g+1tTV3g6Ns7BY32\ntlsq985yXCGx6mpV1IWHk6nRndt8gfW8V94W8jRjWHmIu/BwMjU6L2w4mc6xD4XbhqutetyFi5Or\n0Z1wGOzsD8U83MqHWqmniAsXJ1ejO4UQlHUHkEhPdQsLEBcuTq5Gd4xomY8lcD69gsVI4dKE9k+Y\nCwcHl03kVMGpkjrVpoqrKJuuZ53Njm4ba/Iy3SD4VowQwGVjcVlV4zbTSBu5M9zGK+bCwUE1umWU\nKM24kAa6fO3IADOZ7ZTVzNLLLFEGWW8BlZdZ3BSa3eIESvEsvZBF2CkGc+HhIBrdpOLq5but2aie\nzQven9i2U/t69Rnh8kpJB68jeourr8a/d4s6k1fIPGEfZ5gLBwcbbOa6Um913FLEncXa2ZYjox5f\nVAtUUn4BLFn9imCY5u0zmVTZLBoc6NpN/cZcKJxfP7/BIaL+bZJ6L3TcYpVJNAktuEsLxteon6V8\n5Ppd0TVOCHOzaHDA2BHn0sWfH9XaumUQ9e/cO3RRATNDB/KUTBqjvHMrir5yBttuMYijB9u1C2ew\nEUz9g9yR2UNGSLKXePWQNlbKMnlqBjq3YyxrKtgtBnEwFy4Opv4to8pILmc2UAgnTvSr1NVkj5mR\nKUxJjArSwGijOoVCI+tctlvUY0V/b4MNc+HhYBqdfHnaSRo5c8oHUCsT6MVZx+ABpAKzo4pzxiQ4\nNcsbm+Tn2AMl1mLTCtEO8N30qxihiXAXHk7AfFZRcAVy8SDPMrxXRlxUxUHWh3tQ5UeIi6o4xGaR\nE27pBsEx4R3GXdTFCeytAu698HJP4i7q4pDozjc38nD9qIvKOOEI3lf6xXlfW+d7+vZOfpu97466\nvROdde5BpZn2cFJlwzJJ7xZO4HETEiQ9nFTZsEzSu4UTVws30y7OXdkQWU9tPgsOUk27OHrDVP5P\ng8HVbh3VMNW0i5MsG5ZJegWdYm+52GScBUkHB1NypKigRcJCSc/GFvpbS5B0cFDZ8KakF8IwZdUE\nSQ8HkQ1vSnoRnGqCpIODD7Z7kt41zlnyKhUkHRz8nwbvSXrXOPUESRcHkQ3JTUnvGufcO6WCpIuD\nyYY3Jb1rHFJNkHRxMNnwpqQXwakmSHo4iGx4U9KL4JBagqSHE3OYcC24Erl4oCeYc6ukmq6Jk7pO\ndONMPE41XRPnSdUwINeFBMmqOIE9ViCMxA7xqyWarorzpGpYsLn2G+QTz3Tv/Fc/gx3UD0i7Tr+/\n/cQ+gZjhZ7BdR/4CrRhMdwDv9NQAAAAASUVORK5CYII=\n", "text/latex": ["$$\\left[\\begin{matrix}1 & 0 & 0\\\\0 & \\cos{\\left (\\alpha \\right )} & - \\sin{\\left (\\alpha \\right )}\\\\0 & \\sin{\\left (\\alpha \\right )} & \\cos{\\left (\\alpha \\right )}\\end{matrix}\\right]$$"], "text/plain": ["⎡1    0        0   ⎤\n", "⎢                  ⎥\n", "⎢0  cos(α)  -sin(α)⎥\n", "⎢                  ⎥\n", "⎣0  sin(α)  cos(α) ⎦"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1oAAABMBAMAAACG69u2AAAAMFBMVEX///8AAAAAAAAAAAAAAAAA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\n", "text/latex": ["$$\\left[\\begin{matrix}\\cos{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} - \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\gamma \\right )} + \\sin{\\left (\\beta \\right )} \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )}\\\\\\sin{\\left (\\gamma \\right )} \\cos{\\left (\\beta \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} + \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )} + \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )}\\\\- \\sin{\\left (\\beta \\right )} & \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )} & \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )}\\end{matrix}\\right]$$"], "text/plain": ["⎡cos(β)⋅cos(γ)  sin(α)⋅sin(β)⋅cos(γ) - sin(γ)⋅cos(α)  sin(α)⋅sin(γ) + sin(β)⋅c\n", "⎢                                                                             \n", "⎢sin(γ)⋅cos(β)  sin(α)⋅sin(β)⋅sin(γ) + cos(α)⋅cos(γ)  -sin(α)⋅cos(γ) + sin(β)⋅\n", "⎢                                                                             \n", "⎣   -sin(β)                sin(α)⋅cos(β)                          cos(α)⋅cos(β\n", "\n", "os(α)⋅cos(γ) ⎤\n", "             ⎥\n", "sin(γ)⋅cos(α)⎥\n", "             ⎥\n", ")            ⎦"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["RXYZ = RZ*RY*RX\n", "RXYZ"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA34AAABMBAMAAADAXXgcAAAAMFBMVEX///8AAAAAAAAAAAAAAAAA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\nCuxZWzsDt8/NfeS1JjLI31UFDs7Ntf4PomkUpqz2rf4AAAAASUVORK5CYII=\n", "text/latex": ["$$\\left[\\begin{matrix}- \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )} + \\cos{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\alpha \\right )} & \\sin{\\left (\\alpha \\right )} \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\beta \\right )} + \\sin{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )}\\\\\\sin{\\left (\\alpha \\right )} \\sin{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} + \\sin{\\left (\\gamma \\right )} \\cos{\\left (\\beta \\right )} & \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\gamma \\right )} & - \\sin{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )} \\cos{\\left (\\gamma \\right )} + \\sin{\\left (\\beta \\right )} \\sin{\\left (\\gamma \\right )}\\\\- \\sin{\\left (\\beta \\right )} \\cos{\\left (\\alpha \\right )} & \\sin{\\left (\\alpha \\right )} & \\cos{\\left (\\alpha \\right )} \\cos{\\left (\\beta \\right )}\\end{matrix}\\right]$$"], "text/plain": ["⎡-sin(α)⋅sin(β)⋅sin(γ) + cos(β)⋅cos(γ)  -sin(γ)⋅cos(α)  sin(α)⋅sin(γ)⋅cos(β) +\n", "⎢                                                                             \n", "⎢sin(α)⋅sin(β)⋅cos(γ) + sin(γ)⋅cos(β)   cos(α)⋅cos(γ)   -sin(α)⋅cos(β)⋅cos(γ) \n", "⎢                                                                             \n", "⎣           -sin(β)⋅cos(α)                  sin(α)                  cos(α)⋅cos\n", "\n", " sin(β)⋅cos(γ) ⎤\n", "               ⎥\n", "+ sin(β)⋅sin(γ)⎥\n", "               ⎥\n", "(β)            ⎦"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["RZXY = RZ*RX*RY\n", "RZXY"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.3"}}, "nbformat": 4, "nbformat_minor": 2}