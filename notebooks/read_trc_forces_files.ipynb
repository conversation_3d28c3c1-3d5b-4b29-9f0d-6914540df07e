{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Read Cortex Motion Analysis Corporation .trc and .forces files (example)\n", "\n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.577078Z", "start_time": "2020-05-15T00:40:49.036264Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "%matplotlib inline\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "sns.set_style('whitegrid')\n", "sns.set_context('notebook', font_scale=1.2, rc={\"lines.linewidth\": 2})\n", "from scipy import signal\n", "import sys, os\n", "import pyversions  # https://pypi.org/project/pyversions/\n", "sys.path.insert(1, r'./../functions')\n", "import io_cortexmac as io  # from https://github.com/BMClab/BMC/tree/master/functions"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.581199Z", "start_time": "2020-05-15T00:40:49.578137Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Linux 5.4.0-29-generic 64-bit #33-Ubuntu SMP Wed Apr 29 14:32:27 UTC 2020\n", "CPython 3.7.6 packaged by conda-forge (default, Mar 23 2020, 23:03:20) [GCC 7.3.0]\n", "May 14 2020, 21:40:49\n", "\n", "Module            Version\n", "io_cortexmac        1.0.3\n", "ipython            7.14.0\n", "jupyterlab          2.1.2\n", "matplotlib          3.2.1\n", "notebook            6.0.3\n", "numpy              1.18.4\n", "pandas              1.0.3\n", "pyversions          0.0.3\n", "scipy               1.4.1\n", "seaborn            0.10.1\n"]}], "source": ["pyversions.versions();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use function `io_cortexmac.py` from BMClab's repo"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.584114Z", "start_time": "2020-05-15T00:40:49.582285Z"}}, "outputs": [], "source": ["path2 = r'/mnt/B/Dropbox/BMClab/stuff/Biomecanica/2020/'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.625554Z", "start_time": "2020-05-15T00:40:49.585260Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"/mnt/B/Dropbox/BMClab/stuff/Biomecanica/2020/walk_diurno.trc\" ...  Units changed from \"mm\" to \"m\"\n", "done.\n"]}], "source": ["fname = os.path.join(path2, 'walk_diurno.trc')\n", "h_trc, trc = io.read_trc(fname, fname2='', units='m', dropna=False, na=0.0, df_multi=False)\n", "trc.set_index('Time', drop=True, inplace=True)\n", "trc.drop('Frame#', axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.637756Z", "start_time": "2020-05-15T00:40:49.626526Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"/mnt/B/Dropbox/BMClab/stuff/Biomecanica/2020/walk_diurno.forces\" ... done.\n"]}], "source": ["fname = os.path.join(path2, 'walk_diurno.forces')\n", "h_grf, grf = io.read_forces(fname, time=True, forcepla=[], mm2m=True, show_msg=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.658868Z", "start_time": "2020-05-15T00:40:49.638710Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>L.ASISx</th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th>L.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>L.PSISx</th>\n", "      <th>...</th>\n", "      <th>R.MT2z</th>\n", "      <th><PERSON><PERSON>.Medialx</th>\n", "      <th><PERSON><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON>.Medialz</th>\n", "      <th><PERSON><PERSON>.Medialx</th>\n", "      <th><PERSON><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON>.Medialz</th>\n", "      <th>L.MT2x</th>\n", "      <th>L.MT2y</th>\n", "      <th>L.MT2z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000</th>\n", "      <td>-0.148018</td>\n", "      <td>1.039826</td>\n", "      <td>-0.479703</td>\n", "      <td>-0.165592</td>\n", "      <td>1.043436</td>\n", "      <td>-0.249120</td>\n", "      <td>-0.342912</td>\n", "      <td>1.070820</td>\n", "      <td>-0.409471</td>\n", "      <td>-0.349497</td>\n", "      <td>...</td>\n", "      <td>-0.412118</td>\n", "      <td>-0.062734</td>\n", "      <td>0.545297</td>\n", "      <td>-0.324152</td>\n", "      <td>-0.236939</td>\n", "      <td>0.148704</td>\n", "      <td>-0.333784</td>\n", "      <td>-0.150985</td>\n", "      <td>0.051092</td>\n", "      <td>-0.261883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.007</th>\n", "      <td>-0.141058</td>\n", "      <td>1.038861</td>\n", "      <td>-0.479913</td>\n", "      <td>-0.158079</td>\n", "      <td>1.042954</td>\n", "      <td>-0.249257</td>\n", "      <td>-0.336116</td>\n", "      <td>1.070181</td>\n", "      <td>-0.409000</td>\n", "      <td>-0.342493</td>\n", "      <td>...</td>\n", "      <td>-0.412210</td>\n", "      <td>-0.053445</td>\n", "      <td>0.546187</td>\n", "      <td>-0.324160</td>\n", "      <td>-0.213441</td>\n", "      <td>0.143997</td>\n", "      <td>-0.333000</td>\n", "      <td>-0.123441</td>\n", "      <td>0.050214</td>\n", "      <td>-0.260259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013</th>\n", "      <td>-0.134332</td>\n", "      <td>1.037895</td>\n", "      <td>-0.479989</td>\n", "      <td>-0.150414</td>\n", "      <td>1.042240</td>\n", "      <td>-0.249171</td>\n", "      <td>-0.329322</td>\n", "      <td>1.069535</td>\n", "      <td>-0.408503</td>\n", "      <td>-0.335404</td>\n", "      <td>...</td>\n", "      <td>-0.412325</td>\n", "      <td>-0.044440</td>\n", "      <td>0.546737</td>\n", "      <td>-0.324199</td>\n", "      <td>-0.189872</td>\n", "      <td>0.139392</td>\n", "      <td>-0.331940</td>\n", "      <td>-0.095634</td>\n", "      <td>0.049809</td>\n", "      <td>-0.259605</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020</th>\n", "      <td>-0.127682</td>\n", "      <td>1.036866</td>\n", "      <td>-0.480055</td>\n", "      <td>-0.142959</td>\n", "      <td>1.041588</td>\n", "      <td>-0.249026</td>\n", "      <td>-0.322433</td>\n", "      <td>1.068873</td>\n", "      <td>-0.407999</td>\n", "      <td>-0.328255</td>\n", "      <td>...</td>\n", "      <td>-0.412357</td>\n", "      <td>-0.035515</td>\n", "      <td>0.546795</td>\n", "      <td>-0.323374</td>\n", "      <td>-0.166322</td>\n", "      <td>0.134914</td>\n", "      <td>-0.330772</td>\n", "      <td>-0.068500</td>\n", "      <td>0.048900</td>\n", "      <td>-0.258710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.027</th>\n", "      <td>-0.120731</td>\n", "      <td>1.035838</td>\n", "      <td>-0.479962</td>\n", "      <td>-0.135651</td>\n", "      <td>1.041068</td>\n", "      <td>-0.248977</td>\n", "      <td>-0.315505</td>\n", "      <td>1.068227</td>\n", "      <td>-0.407452</td>\n", "      <td>-0.321039</td>\n", "      <td>...</td>\n", "      <td>-0.412444</td>\n", "      <td>-0.026123</td>\n", "      <td>0.546789</td>\n", "      <td>-0.323470</td>\n", "      <td>-0.142575</td>\n", "      <td>0.130622</td>\n", "      <td>-0.329480</td>\n", "      <td>-0.041296</td>\n", "      <td>0.048466</td>\n", "      <td>-0.257679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.393</th>\n", "      <td>2.669729</td>\n", "      <td>1.013948</td>\n", "      <td>-0.443166</td>\n", "      <td>2.679452</td>\n", "      <td>1.018445</td>\n", "      <td>-0.210724</td>\n", "      <td>2.486804</td>\n", "      <td>1.050265</td>\n", "      <td>-0.350918</td>\n", "      <td>2.489218</td>\n", "      <td>...</td>\n", "      <td>-0.365184</td>\n", "      <td>2.759086</td>\n", "      <td>0.523217</td>\n", "      <td>-0.321459</td>\n", "      <td>2.908786</td>\n", "      <td>0.116326</td>\n", "      <td>-0.302856</td>\n", "      <td>3.050601</td>\n", "      <td>0.100390</td>\n", "      <td>-0.247426</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.400</th>\n", "      <td>2.678802</td>\n", "      <td>1.013561</td>\n", "      <td>-0.442502</td>\n", "      <td>2.689003</td>\n", "      <td>1.018013</td>\n", "      <td>-0.209994</td>\n", "      <td>2.496053</td>\n", "      <td>1.049709</td>\n", "      <td>-0.349817</td>\n", "      <td>2.498577</td>\n", "      <td>...</td>\n", "      <td>-0.365177</td>\n", "      <td>2.768020</td>\n", "      <td>0.522362</td>\n", "      <td>-0.321177</td>\n", "      <td>2.913835</td>\n", "      <td>0.114138</td>\n", "      <td>-0.303386</td>\n", "      <td>3.055600</td>\n", "      <td>0.097254</td>\n", "      <td>-0.248036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.407</th>\n", "      <td>2.688065</td>\n", "      <td>1.013247</td>\n", "      <td>-0.441769</td>\n", "      <td>2.698695</td>\n", "      <td>1.017800</td>\n", "      <td>-0.209276</td>\n", "      <td>2.505448</td>\n", "      <td>1.049106</td>\n", "      <td>-0.348723</td>\n", "      <td>2.508022</td>\n", "      <td>...</td>\n", "      <td>-0.365195</td>\n", "      <td>2.777018</td>\n", "      <td>0.521624</td>\n", "      <td>-0.320795</td>\n", "      <td>2.917893</td>\n", "      <td>0.111588</td>\n", "      <td>-0.303889</td>\n", "      <td>3.059447</td>\n", "      <td>0.093263</td>\n", "      <td>-0.248779</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.413</th>\n", "      <td>2.697386</td>\n", "      <td>1.012931</td>\n", "      <td>-0.441014</td>\n", "      <td>2.708502</td>\n", "      <td>1.017577</td>\n", "      <td>-0.208479</td>\n", "      <td>2.515101</td>\n", "      <td>1.048503</td>\n", "      <td>-0.347683</td>\n", "      <td>2.517958</td>\n", "      <td>...</td>\n", "      <td>-0.365259</td>\n", "      <td>2.786064</td>\n", "      <td>0.520972</td>\n", "      <td>-0.320384</td>\n", "      <td>2.920929</td>\n", "      <td>0.108704</td>\n", "      <td>-0.304379</td>\n", "      <td>3.062302</td>\n", "      <td>0.088656</td>\n", "      <td>-0.250159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.420</th>\n", "      <td>2.707048</td>\n", "      <td>1.012652</td>\n", "      <td>-0.440176</td>\n", "      <td>2.718473</td>\n", "      <td>1.017529</td>\n", "      <td>-0.207658</td>\n", "      <td>2.524919</td>\n", "      <td>1.047971</td>\n", "      <td>-0.346768</td>\n", "      <td>2.527594</td>\n", "      <td>...</td>\n", "      <td>-0.365344</td>\n", "      <td>2.795329</td>\n", "      <td>0.520493</td>\n", "      <td>-0.319815</td>\n", "      <td>2.923379</td>\n", "      <td>0.105627</td>\n", "      <td>-0.304921</td>\n", "      <td>3.064462</td>\n", "      <td>0.084283</td>\n", "      <td>-0.250614</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>364 rows × 84 columns</p>\n", "</div>"], "text/plain": ["        R.ASISx   R.ASISy   R.ASISz   L.ASISx   L.ASISy   L.ASISz   R.PSISx  \\\n", "Time                                                                          \n", "0.000 -0.148018  1.039826 -0.479703 -0.165592  1.043436 -0.249120 -0.342912   \n", "0.007 -0.141058  1.038861 -0.479913 -0.158079  1.042954 -0.249257 -0.336116   \n", "0.013 -0.134332  1.037895 -0.479989 -0.150414  1.042240 -0.249171 -0.329322   \n", "0.020 -0.127682  1.036866 -0.480055 -0.142959  1.041588 -0.249026 -0.322433   \n", "0.027 -0.120731  1.035838 -0.479962 -0.135651  1.041068 -0.248977 -0.315505   \n", "...         ...       ...       ...       ...       ...       ...       ...   \n", "2.393  2.669729  1.013948 -0.443166  2.679452  1.018445 -0.210724  2.486804   \n", "2.400  2.678802  1.013561 -0.442502  2.689003  1.018013 -0.209994  2.496053   \n", "2.407  2.688065  1.013247 -0.441769  2.698695  1.017800 -0.209276  2.505448   \n", "2.413  2.697386  1.012931 -0.441014  2.708502  1.017577 -0.208479  2.515101   \n", "2.420  2.707048  1.012652 -0.440176  2.718473  1.017529 -0.207658  2.524919   \n", "\n", "        R.PSISy   R.PSISz   L.PSISx  ...    R.MT2z  L.Knee.Medialx  \\\n", "Time                                 ...                             \n", "0.000  1.070820 -0.409471 -0.349497  ... -0.412118       -0.062734   \n", "0.007  1.070181 -0.409000 -0.342493  ... -0.412210       -0.053445   \n", "0.013  1.069535 -0.408503 -0.335404  ... -0.412325       -0.044440   \n", "0.020  1.068873 -0.407999 -0.328255  ... -0.412357       -0.035515   \n", "0.027  1.068227 -0.407452 -0.321039  ... -0.412444       -0.026123   \n", "...         ...       ...       ...  ...       ...             ...   \n", "2.393  1.050265 -0.350918  2.489218  ... -0.365184        2.759086   \n", "2.400  1.049709 -0.349817  2.498577  ... -0.365177        2.768020   \n", "2.407  1.049106 -0.348723  2.508022  ... -0.365195        2.777018   \n", "2.413  1.048503 -0.347683  2.517958  ... -0.365259        2.786064   \n", "2.420  1.047971 -0.346768  2.527594  ... -0.365344        2.795329   \n", "\n", "       L<PERSON>.Medialy  L.Knee.Medialz  L.Ankle.Medialx  L.Ankle.Medialy  \\\n", "Time                                                                      \n", "0.000        0.545297       -0.324152        -0.236939         0.148704   \n", "0.007        0.546187       -0.324160        -0.213441         0.143997   \n", "0.013        0.546737       -0.324199        -0.189872         0.139392   \n", "0.020        0.546795       -0.323374        -0.166322         0.134914   \n", "0.027        0.546789       -0.323470        -0.142575         0.130622   \n", "...               ...             ...              ...              ...   \n", "2.393        0.523217       -0.321459         2.908786         0.116326   \n", "2.400        0.522362       -0.321177         2.913835         0.114138   \n", "2.407        0.521624       -0.320795         2.917893         0.111588   \n", "2.413        0.520972       -0.320384         2.920929         0.108704   \n", "2.420        0.520493       -0.319815         2.923379         0.105627   \n", "\n", "       L.Ankle.Medialz    L.MT2x    L.MT2y    L.MT2z  \n", "Time                                                  \n", "0.000        -0.333784 -0.150985  0.051092 -0.261883  \n", "0.007        -0.333000 -0.123441  0.050214 -0.260259  \n", "0.013        -0.331940 -0.095634  0.049809 -0.259605  \n", "0.020        -0.330772 -0.068500  0.048900 -0.258710  \n", "0.027        -0.329480 -0.041296  0.048466 -0.257679  \n", "...                ...       ...       ...       ...  \n", "2.393        -0.302856  3.050601  0.100390 -0.247426  \n", "2.400        -0.303386  3.055600  0.097254 -0.248036  \n", "2.407        -0.303889  3.059447  0.093263 -0.248779  \n", "2.413        -0.304379  3.062302  0.088656 -0.250159  \n", "2.420        -0.304921  3.064462  0.084283 -0.250614  \n", "\n", "[364 rows x 84 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["trc"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.679690Z", "start_time": "2020-05-15T00:40:49.660102Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FX1</th>\n", "      <th>FY1</th>\n", "      <th>FZ1</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>MZ1</th>\n", "      <th>FX2</th>\n", "      <th>FY2</th>\n", "      <th>FZ2</th>\n", "      <th>...</th>\n", "      <th>Y6</th>\n", "      <th>Z6</th>\n", "      <th>MZ6</th>\n", "      <th>FX7</th>\n", "      <th>FY7</th>\n", "      <th>FZ7</th>\n", "      <th>X7</th>\n", "      <th>Y7</th>\n", "      <th>Z7</th>\n", "      <th>MZ7</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.002222</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004444</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.006667</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.008889</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.415556</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.417778</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.420000</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.422222</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.424444</th>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>2.149</td>\n", "      <td>0.0</td>\n", "      <td>0.9767</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>-0.203</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>-0.0</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "      <td>0.2</td>\n", "      <td>-0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1092 rows × 49 columns</p>\n", "</div>"], "text/plain": ["          FX1  FY1  FZ1     X1   Y1      Z1  MZ1  FX2  FY2  FZ2  ...   Y6  \\\n", "Time                                                             ...        \n", "0.000000  0.0  0.0  0.0  2.149  0.0  0.9767  0.0  0.0  0.0  0.0  ...  0.0   \n", "0.002222 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "0.004444 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "0.006667 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "0.008889 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "...       ...  ...  ...    ...  ...     ...  ...  ...  ...  ...  ...  ...   \n", "2.415556 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "2.417778 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "2.420000 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "2.422222 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "2.424444 -0.0 -0.0 -0.0  2.149  0.0  0.9767 -0.0 -0.0 -0.0 -0.0  ...  0.0   \n", "\n", "             Z6  MZ6  FX7  FY7  FZ7   X7   Y7   Z7  MZ7  \n", "Time                                                     \n", "0.000000 -0.203 -0.0  0.0  0.0  0.0  0.3  0.0  0.2  0.0  \n", "0.002222 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "0.004444 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "0.006667 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "0.008889 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "...         ...  ...  ...  ...  ...  ...  ...  ...  ...  \n", "2.415556 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "2.417778 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "2.420000 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "2.422222 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "2.424444 -0.203 -0.0 -0.0 -0.0 -0.0  0.3  0.0  0.2 -0.0  \n", "\n", "[1092 rows x 49 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["grf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### trc and forces data have different sampling rates"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.683763Z", "start_time": "2020-05-15T00:40:49.681120Z"}}, "outputs": [{"data": {"text/plain": ["150.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["freq_trc = 1/np.mean(np.diff(trc.index))\n", "freq_trc"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.687753Z", "start_time": "2020-05-15T00:40:49.685029Z"}}, "outputs": [{"data": {"text/plain": ["450.0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["freq_grf = 1/np.mean(np.diff(grf.index))\n", "freq_grf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Resample trc to the force sampling rate (150 Hz to 450 Hz)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.724044Z", "start_time": "2020-05-15T00:40:49.688688Z"}}, "outputs": [], "source": ["# allocate variable\n", "nrows = int(trc.shape[0]*np.round(freq_grf/freq_trc))\n", "ncols = trc.shape[1]\n", "data = np.nan*np.zeros((nrows, 1+ncols), dtype='float64')\n", "# time column\n", "data[:, 0] = np.linspace(start=0, stop=nrows/freq_grf, num=nrows, endpoint=False)\n", "# resample data\n", "for i in range(ncols):\n", "     data[:, i+1] = signal.resample_poly(trc.iloc[:, i], np.round(freq_grf),\n", "                                         np.round(freq_trc), window='blackman')\n", "# create datafrane with new data\n", "trc = pd.DataFrame(data=data[:, 1:], index=data[:, 0], columns=trc.columns)\n", "trc.index.name = trc.index.name"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.749265Z", "start_time": "2020-05-15T00:40:49.725603Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>L.ASISx</th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th>L.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>L.PSISx</th>\n", "      <th>...</th>\n", "      <th>R.MT2z</th>\n", "      <th><PERSON><PERSON>.Medialx</th>\n", "      <th><PERSON><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON>.Medialz</th>\n", "      <th><PERSON><PERSON>.Medialx</th>\n", "      <th><PERSON><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON>.Medialz</th>\n", "      <th>L.MT2x</th>\n", "      <th>L.MT2y</th>\n", "      <th>L.MT2z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>-0.148013</td>\n", "      <td>1.039790</td>\n", "      <td>-0.479687</td>\n", "      <td>-0.165587</td>\n", "      <td>1.043400</td>\n", "      <td>-0.249111</td>\n", "      <td>-0.342900</td>\n", "      <td>1.070784</td>\n", "      <td>-0.409457</td>\n", "      <td>-0.349485</td>\n", "      <td>...</td>\n", "      <td>-0.412104</td>\n", "      <td>-0.062732</td>\n", "      <td>0.545279</td>\n", "      <td>-0.324140</td>\n", "      <td>-0.236931</td>\n", "      <td>0.148699</td>\n", "      <td>-0.333772</td>\n", "      <td>-0.150980</td>\n", "      <td>0.051090</td>\n", "      <td>-0.261874</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.002222</th>\n", "      <td>-0.165754</td>\n", "      <td>1.176067</td>\n", "      <td>-0.542727</td>\n", "      <td>-0.185522</td>\n", "      <td>1.180283</td>\n", "      <td>-0.281859</td>\n", "      <td>-0.386272</td>\n", "      <td>1.211210</td>\n", "      <td>-0.463102</td>\n", "      <td>-0.393678</td>\n", "      <td>...</td>\n", "      <td>-0.466229</td>\n", "      <td>-0.068713</td>\n", "      <td>0.617092</td>\n", "      <td>-0.366665</td>\n", "      <td>-0.262359</td>\n", "      <td>0.167078</td>\n", "      <td>-0.377417</td>\n", "      <td>-0.164174</td>\n", "      <td>0.057534</td>\n", "      <td>-0.295793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004444</th>\n", "      <td>-0.158350</td>\n", "      <td>1.141277</td>\n", "      <td>-0.526916</td>\n", "      <td>-0.177347</td>\n", "      <td>1.145552</td>\n", "      <td>-0.273660</td>\n", "      <td>-0.372476</td>\n", "      <td>1.175510</td>\n", "      <td>-0.449371</td>\n", "      <td>-0.379591</td>\n", "      <td>...</td>\n", "      <td>-0.452610</td>\n", "      <td>-0.063299</td>\n", "      <td>0.599354</td>\n", "      <td>-0.355922</td>\n", "      <td>-0.246086</td>\n", "      <td>0.160462</td>\n", "      <td>-0.366086</td>\n", "      <td>-0.149304</td>\n", "      <td>0.055510</td>\n", "      <td>-0.286510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.006667</th>\n", "      <td>-0.141053</td>\n", "      <td>1.038825</td>\n", "      <td>-0.479896</td>\n", "      <td>-0.158074</td>\n", "      <td>1.042918</td>\n", "      <td>-0.249249</td>\n", "      <td>-0.336105</td>\n", "      <td>1.070144</td>\n", "      <td>-0.408986</td>\n", "      <td>-0.342482</td>\n", "      <td>...</td>\n", "      <td>-0.412196</td>\n", "      <td>-0.053444</td>\n", "      <td>0.546169</td>\n", "      <td>-0.324149</td>\n", "      <td>-0.213434</td>\n", "      <td>0.143992</td>\n", "      <td>-0.332988</td>\n", "      <td>-0.123436</td>\n", "      <td>0.050212</td>\n", "      <td>-0.260250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.008889</th>\n", "      <td>-0.129460</td>\n", "      <td>0.974991</td>\n", "      <td>-0.450652</td>\n", "      <td>-0.145114</td>\n", "      <td>0.978988</td>\n", "      <td>-0.234049</td>\n", "      <td>-0.312638</td>\n", "      <td>1.004536</td>\n", "      <td>-0.383805</td>\n", "      <td>-0.318513</td>\n", "      <td>...</td>\n", "      <td>-0.387078</td>\n", "      <td>-0.046193</td>\n", "      <td>0.513157</td>\n", "      <td>-0.304436</td>\n", "      <td>-0.190172</td>\n", "      <td>0.133177</td>\n", "      <td>-0.312260</td>\n", "      <td>-0.103831</td>\n", "      <td>0.046922</td>\n", "      <td>-0.243902</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.415556</th>\n", "      <td>2.966845</td>\n", "      <td>1.112183</td>\n", "      <td>-0.483880</td>\n", "      <td>2.979224</td>\n", "      <td>1.117381</td>\n", "      <td>-0.228523</td>\n", "      <td>2.766774</td>\n", "      <td>1.151119</td>\n", "      <td>-0.381335</td>\n", "      <td>2.769873</td>\n", "      <td>...</td>\n", "      <td>-0.401145</td>\n", "      <td>3.064043</td>\n", "      <td>0.571839</td>\n", "      <td>-0.351553</td>\n", "      <td>3.208781</td>\n", "      <td>0.117855</td>\n", "      <td>-0.334532</td>\n", "      <td>3.363905</td>\n", "      <td>0.095137</td>\n", "      <td>-0.275038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.417778</th>\n", "      <td>3.059988</td>\n", "      <td>1.145627</td>\n", "      <td>-0.498158</td>\n", "      <td>3.072851</td>\n", "      <td>1.151074</td>\n", "      <td>-0.235108</td>\n", "      <td>2.853934</td>\n", "      <td>1.185643</td>\n", "      <td>-0.392496</td>\n", "      <td>2.857043</td>\n", "      <td>...</td>\n", "      <td>-0.413277</td>\n", "      <td>3.159964</td>\n", "      <td>0.588913</td>\n", "      <td>-0.361939</td>\n", "      <td>3.306487</td>\n", "      <td>0.120246</td>\n", "      <td>-0.344823</td>\n", "      <td>3.466175</td>\n", "      <td>0.096371</td>\n", "      <td>-0.283475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.420000</th>\n", "      <td>2.706955</td>\n", "      <td>1.012618</td>\n", "      <td>-0.440161</td>\n", "      <td>2.718380</td>\n", "      <td>1.017494</td>\n", "      <td>-0.207651</td>\n", "      <td>2.524833</td>\n", "      <td>1.047936</td>\n", "      <td>-0.346756</td>\n", "      <td>2.527507</td>\n", "      <td>...</td>\n", "      <td>-0.365331</td>\n", "      <td>2.795234</td>\n", "      <td>0.520475</td>\n", "      <td>-0.319804</td>\n", "      <td>2.923279</td>\n", "      <td>0.105623</td>\n", "      <td>-0.304910</td>\n", "      <td>3.064357</td>\n", "      <td>0.084280</td>\n", "      <td>-0.250606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.422222</th>\n", "      <td>1.874230</td>\n", "      <td>0.700753</td>\n", "      <td>-0.304532</td>\n", "      <td>1.882157</td>\n", "      <td>0.704158</td>\n", "      <td>-0.143631</td>\n", "      <td>1.748197</td>\n", "      <td>0.725172</td>\n", "      <td>-0.239891</td>\n", "      <td>1.750007</td>\n", "      <td>...</td>\n", "      <td>-0.252832</td>\n", "      <td>1.935280</td>\n", "      <td>0.360155</td>\n", "      <td>-0.221260</td>\n", "      <td>2.023259</td>\n", "      <td>0.072808</td>\n", "      <td>-0.211053</td>\n", "      <td>2.120854</td>\n", "      <td>0.057945</td>\n", "      <td>-0.173424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.424444</th>\n", "      <td>0.834408</td>\n", "      <td>0.311876</td>\n", "      <td>-0.135515</td>\n", "      <td>0.837941</td>\n", "      <td>0.313400</td>\n", "      <td>-0.063906</td>\n", "      <td>0.778315</td>\n", "      <td>0.322738</td>\n", "      <td>-0.106746</td>\n", "      <td>0.779107</td>\n", "      <td>...</td>\n", "      <td>-0.112529</td>\n", "      <td>0.861567</td>\n", "      <td>0.160283</td>\n", "      <td>-0.098459</td>\n", "      <td>0.900547</td>\n", "      <td>0.032324</td>\n", "      <td>-0.093944</td>\n", "      <td>0.943972</td>\n", "      <td>0.025685</td>\n", "      <td>-0.077179</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1092 rows × 84 columns</p>\n", "</div>"], "text/plain": ["           R.ASISx   R.ASISy   R.ASISz   L.ASISx   L.ASISy   L.ASISz  \\\n", "0.000000 -0.148013  1.039790 -0.479687 -0.165587  1.043400 -0.249111   \n", "0.002222 -0.165754  1.176067 -0.542727 -0.185522  1.180283 -0.281859   \n", "0.004444 -0.158350  1.141277 -0.526916 -0.177347  1.145552 -0.273660   \n", "0.006667 -0.141053  1.038825 -0.479896 -0.158074  1.042918 -0.249249   \n", "0.008889 -0.129460  0.974991 -0.450652 -0.145114  0.978988 -0.234049   \n", "...            ...       ...       ...       ...       ...       ...   \n", "2.415556  2.966845  1.112183 -0.483880  2.979224  1.117381 -0.228523   \n", "2.417778  3.059988  1.145627 -0.498158  3.072851  1.151074 -0.235108   \n", "2.420000  2.706955  1.012618 -0.440161  2.718380  1.017494 -0.207651   \n", "2.422222  1.874230  0.700753 -0.304532  1.882157  0.704158 -0.143631   \n", "2.424444  0.834408  0.311876 -0.135515  0.837941  0.313400 -0.063906   \n", "\n", "           R.PSISx   R.PSISy   R.PSISz   L.PSISx  ...    R.MT2z  \\\n", "0.000000 -0.342900  1.070784 -0.409457 -0.349485  ... -0.412104   \n", "0.002222 -0.386272  1.211210 -0.463102 -0.393678  ... -0.466229   \n", "0.004444 -0.372476  1.175510 -0.449371 -0.379591  ... -0.452610   \n", "0.006667 -0.336105  1.070144 -0.408986 -0.342482  ... -0.412196   \n", "0.008889 -0.312638  1.004536 -0.383805 -0.318513  ... -0.387078   \n", "...            ...       ...       ...       ...  ...       ...   \n", "2.415556  2.766774  1.151119 -0.381335  2.769873  ... -0.401145   \n", "2.417778  2.853934  1.185643 -0.392496  2.857043  ... -0.413277   \n", "2.420000  2.524833  1.047936 -0.346756  2.527507  ... -0.365331   \n", "2.422222  1.748197  0.725172 -0.239891  1.750007  ... -0.252832   \n", "2.424444  0.778315  0.322738 -0.106746  0.779107  ... -0.112529   \n", "\n", "          L.Knee.Medialx  L.Knee.Medialy  L.Knee.Medialz  L.Ankle.Medialx  \\\n", "0.000000       -0.062732        0.545279       -0.324140        -0.236931   \n", "0.002222       -0.068713        0.617092       -0.366665        -0.262359   \n", "0.004444       -0.063299        0.599354       -0.355922        -0.246086   \n", "0.006667       -0.053444        0.546169       -0.324149        -0.213434   \n", "0.008889       -0.046193        0.513157       -0.304436        -0.190172   \n", "...                  ...             ...             ...              ...   \n", "2.415556        3.064043        0.571839       -0.351553         3.208781   \n", "2.417778        3.159964        0.588913       -0.361939         3.306487   \n", "2.420000        2.795234        0.520475       -0.319804         2.923279   \n", "2.422222        1.935280        0.360155       -0.221260         2.023259   \n", "2.424444        0.861567        0.160283       -0.098459         0.900547   \n", "\n", "          L.Ankle.Medialy  L.Ankle.Medialz    L.MT2x    L.MT2y    L.MT2z  \n", "0.000000         0.148699        -0.333772 -0.150980  0.051090 -0.261874  \n", "0.002222         0.167078        -0.377417 -0.164174  0.057534 -0.295793  \n", "0.004444         0.160462        -0.366086 -0.149304  0.055510 -0.286510  \n", "0.006667         0.143992        -0.332988 -0.123436  0.050212 -0.260250  \n", "0.008889         0.133177        -0.312260 -0.103831  0.046922 -0.243902  \n", "...                   ...              ...       ...       ...       ...  \n", "2.415556         0.117855        -0.334532  3.363905  0.095137 -0.275038  \n", "2.417778         0.120246        -0.344823  3.466175  0.096371 -0.283475  \n", "2.420000         0.105623        -0.304910  3.064357  0.084280 -0.250606  \n", "2.422222         0.072808        -0.211053  2.120854  0.057945 -0.173424  \n", "2.424444         0.032324        -0.093944  0.943972  0.025685 -0.077179  \n", "\n", "[1092 rows x 84 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["trc"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot of some data"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.753109Z", "start_time": "2020-05-15T00:40:49.750303Z"}}, "outputs": [{"data": {"text/plain": ["Index(['R.ASISx', 'R.ASISy', 'R.ASISz', 'L.ASISx', 'L.ASISy', 'L.ASISz',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'L.<PERSON>', 'L.<PERSON>ISy', 'L.<PERSON>IS<PERSON>',\n", "       'L.Iliac.Crestx', 'L.Iliac.Cresty', 'L.Iliac.Crestz', 'R.Iliac.Crestx',\n", "       'R.Iliac.Cresty', 'R.<PERSON>iac.Crestz', 'R.GTRx', 'R.GTRy', 'R.GTRz',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'R.TTx',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON>', 'R.MT1x', 'R.MT1y', 'R.MT1z', 'R.MT5x', 'R.MT5y',\n", "       'R.MT5z', 'L.GTRx', 'L.GTRy', 'L.GTRz', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',\n", "       '<PERSON><PERSON>HFx', '<PERSON><PERSON>', '<PERSON><PERSON>HFz', 'L.TTx', '<PERSON><PERSON>TTy', 'L.TTz', '<PERSON><PERSON>',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'L.MT1x',\n", "       'L.MT1y', 'L.MT1z', 'L.MT5x', 'L.MT5y', 'L.MT5z', 'R.<PERSON>nee.Medialx',\n", "       '<PERSON><PERSON>.<PERSON>ly', '<PERSON><PERSON>.Medialz', '<PERSON><PERSON>.Medialx',\n", "       '<PERSON><PERSON><PERSON><PERSON>.Medialy', '<PERSON><PERSON>.Medialz', 'R.MT2x', 'R.MT2y', 'R.MT2z',\n", "       'L.<PERSON>.Medialx', '<PERSON><PERSON>Medialy', 'L.<PERSON>.Medialz', 'L.<PERSON>kle.Medialx',\n", "       '<PERSON>.<PERSON>kle.Medialy', 'L.Ankle.Medialz', 'L.MT2x', 'L.MT2y', 'L.MT2z'],\n", "      dtype='object')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["trc.columns"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:49.757043Z", "start_time": "2020-05-15T00:40:49.754111Z"}}, "outputs": [{"data": {"text/plain": ["Index(['FX1', 'FY1', 'FZ1', 'X1', 'Y1', 'Z1', 'MZ1', 'FX2', 'FY2', 'FZ2', 'X2',\n", "       'Y2', 'Z2', 'MZ2', 'FX3', 'FY3', 'FZ3', 'X3', 'Y3', 'Z3', 'MZ3', 'FX4',\n", "       'FY4', 'FZ4', 'X4', 'Y4', 'Z4', 'MZ4', 'FX5', 'FY5', 'FZ5', 'X5', 'Y5',\n", "       'Z5', 'MZ5', 'FX6', 'FY6', 'FZ6', 'X6', 'Y6', 'Z6', 'MZ6', 'FX7', 'FY7',\n", "       'FZ7', 'X7', 'Y7', 'Z7', 'MZ7'],\n", "      dtype='object')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["grf.columns"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:50.150815Z", "start_time": "2020-05-15T00:40:49.758004Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(2, 1, sharex = True, squeeze=True, figsize=(10, 5))\n", "trc.plot(y=['<PERSON><PERSON>', '<PERSON><PERSON>'], ax=axs[0], title='Data diurno')\n", "grf.plot(y=['FY5', 'FY6'], ax=axs[1], colormap='viridis')\n", "axs[0].set_ylabel('Position [m]')\n", "axs[1].set_ylabel('Force [N]')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This means that the subject stepped on force plate **#6** with her/his **left** foot and then stepped on force plate **#5** with her/his **right** foot."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## For data noturno"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T00:40:50.670146Z", "start_time": "2020-05-15T00:40:50.151798Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"/mnt/B/Dropbox/BMClab/stuff/Biomecanica/2020/walk_noturno.trc\" ...  Number of markers changed from 28 to 55.\n", " Units changed from \"mm\" to \"m\"\n", "done.\n", "Opening file \"/mnt/B/Dropbox/BMClab/stuff/Biomecanica/2020/walk_noturno.forces\" ... done.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fname = os.path.join(path2, 'walk_noturno.trc')\n", "h_trc, trc = io.read_trc(fname, fname2='', units='m', dropna=False, na=0.0, df_multi=False)\n", "trc.set_index('Time', drop=True, inplace=True)\n", "trc.drop('Frame#', axis=1, inplace=True)\n", "fname = os.path.join(path2, 'walk_noturno.forces')\n", "h_grf, grf = io.read_forces(fname, time=True, forcepla=[], mm2m=True, show_msg=True)\n", "\n", "freq_trc = 1/np.mean(np.diff(trc.index))\n", "freq_grf = 1/np.mean(np.diff(grf.index))\n", "\n", "# allocate variable\n", "nrows = int(trc.shape[0]*np.round(freq_grf/freq_trc))\n", "ncols = trc.shape[1]\n", "data = np.nan*np.zeros((nrows, 1+ncols), dtype='float64')\n", "# time column\n", "data[:, 0] = np.linspace(start=0, stop=nrows/freq_grf, num=nrows, endpoint=False)\n", "# resample data\n", "for i in range(ncols):\n", "     data[:, i+1] = signal.resample_poly(trc.iloc[:, i], np.round(freq_grf),\n", "                                         np.round(freq_trc), window='blackman')\n", "# create datafrane with new data\n", "trc = pd.DataFrame(data=data[:, 1:], index=data[:, 0], columns=trc.columns)\n", "trc.index.name = trc.index.name\n", "\n", "fig, axs = plt.subplots(2, 1, sharex = True, squeeze=True, figsize=(10, 5))\n", "trc.plot(y=['<PERSON><PERSON>', '<PERSON><PERSON>'], ax=axs[0], title='Data noturno')\n", "grf.plot(y=['FY5', 'FY6'], ax=axs[1], colormap='viridis')\n", "axs[0].set_ylabel('Position [m]')\n", "axs[1].set_ylabel('Force [N]')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This means that the subject stepped on force plate **#6** with her/his **right** foot and then stepped on force plate **#5** with her/his **left** foot."]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}