{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/BMClab/BMC/blob/master/notebooks/KinematicsAngular2D.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "txzjCkPddNYW"}, "source": ["# Angular kinematics in a plane (2D)\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)    \n", "> Federal University of ABC, Brazil\n"]}, {"cell_type": "markdown", "metadata": {"id": "gORkllQRhF6w"}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Angles-in-a-plane\" data-toc-modified-id=\"Angles-in-a-plane-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Angles in a plane</a></span></li><li><span><a href=\"#Angle-between-two-3D-vectors\" data-toc-modified-id=\"Angle-between-two-3D-vectors-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Angle between two 3D vectors</a></span></li><li><span><a href=\"#Angular-position-velocity-and-acceleration\" data-toc-modified-id=\"Angular-position-velocity-and-acceleration-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Angular position, velocity, and acceleration</a></span><ul class=\"toc-item\"><li><span><a href=\"#The-antiderivative\" data-toc-modified-id=\"The-antiderivative-4.1\"><span class=\"toc-item-num\">4.1&nbsp;&nbsp;</span>The antiderivative</a></span></li></ul></li><li><span><a href=\"#Relationship-between-linear-and-angular-kinematics\" data-toc-modified-id=\"Relationship-between-linear-and-angular-kinematics-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Relationship between linear and angular kinematics</a></span></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "-j8tXrk1dSIc"}, "source": ["Human motion is a combination of linear and angular movement and occurs in the three-dimensional (3D) space. For certain movements and depending on the desired or needed degree of detail for the motion analysis, it's possible to perform a two-dimensional (2D, planar) analysis at the main plane of movement. Such simplification is appreciated because the instrumentation and analysis are much more complicated in order to measure 3D motion than for the 2D case."]}, {"cell_type": "markdown", "metadata": {"id": "-Wb-NcroiILC"}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "o2Ger3R_iXqq"}, "outputs": [], "source": ["# Import the necessary libraries\n", "import numpy as np\n", "from IPython.display import display, Latex\n", "import pandas as pd\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "sns.set_context(\"notebook\", font_scale=1.2, rc={\"lines.linewidth\": 2, \"lines.markersize\": 8})"]}, {"cell_type": "markdown", "metadata": {"id": "jiqckVRriY7j"}, "source": ["## <PERSON>les in a plane"]}, {"cell_type": "markdown", "metadata": {"id": "9tzd9_9Xincq"}, "source": ["For the planar case, the calculation of angles is reduced to the application of trigonometry to the kinematic data. For instance, given the coordinates in a plane of markers on a segment as shown in the figure below, the angle of the segment can be calculated using the inverse function of <span class=\"notranslate\"> $\\sin,\\cos$ </span>, or <span class=\"notranslate\"> $\\tan$ </span>.\n", "\n", "<div class='center-align'><figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/segment.png?raw=1\" width=250/><figcaption><figcaption><center><i>Figure. A segment in a plane and its coordinates.</i></center></figcaption> </figure></div>"]}, {"cell_type": "markdown", "metadata": {"id": "CeGV6Zp5jrKl"}, "source": ["For better numerical accuracy (and also to distinguish values in the whole quadrant), the inverse function of <span class=\"notranslate\">$\\tan$</span> is preferred.   \n", "For the data shown in the previous figure:   \n", "\n", "<span class=\"notranslate\">\n", "$$ \\theta = arctan\\left(\\frac{y_2-y_1}{x_2-x_1}\\right) $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "rdDtjZZzjsTa"}, "source": ["In computer programming (here, Python/Numpy) this is calculated using: `numpy.arctan((y2-y1)/(x2-x1))`. However, for the previous figure the function `arctan` can not distinguish if the segment is at 45o or at 225o, `arctan` will return the same value. Because this, the function `numpy.arctan2(y, x)` is used, but be aware that `arctan2` will return angles between <span class=\"notranslate\"> $[-\\pi,\\pi]$: </span>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 102}, "id": "1Z-GtDVmjxBk", "outputId": "673c1132-b22c-438e-b8d3-99d7e1b7c169"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "Segment\\;at\\;45^o:"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "Using\\;arctan: 45.0\\;Using\\;arctan2: 45.0"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "Segment\\;at\\;225^o:"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "Using\\;arctan: 45.0\\;Using\\;arctan2: -135.0"}, "metadata": {}}], "source": ["x1, y1 = 0, 0\n", "x2, y2 = 1, 1\n", "\n", "display(Latex(r'Segment\\;at\\;45^o:'))\n", "angs = [ np.arctan((1-0)/(1-0))*180/np.pi, np.arctan2(1-0, 1-0)*180/np.pi ]\n", "display(Latex(r'Using\\;arctan: '+str(angs[0])+r'\\;Using\\;arctan2: '+str(angs[1])))\n", "display(Latex(r'Segment\\;at\\;225^o:'))\n", "angs = [ np.arctan((-1-0)/(-1-0))*180/np.pi, np.arctan2(-1-0, -1-0)*180/np.pi ]\n", "display(Latex(r'Using\\;arctan: '+str(angs[0])+r'\\;Using\\;arctan2: '+str(angs[1])))"]}, {"cell_type": "markdown", "metadata": {"id": "NykbWZxWj2Yz"}, "source": ["And <PERSON><PERSON><PERSON> has a function to convert an angle in rad to degrees and the other way around:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IdUVzjKGj3OJ", "outputId": "5a09ce2e-9419-4b83-b008-c329f8961bc9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["np.rad2deg(np.pi/2) = 180.0\n", "np.deg2rad(180) = 3.141592653589793\n"]}], "source": ["print('np.rad2deg(np.pi/2) =', np.rad2deg(np.pi))\n", "print('np.deg2rad(180) =', np.deg2rad(180))"]}, {"cell_type": "markdown", "metadata": {"id": "7kc6Bi3Dj6eL"}, "source": ["Let's simulate a 2D motion of the arm performing two complete turns around the shoulder to exemplify the use of `arctan2`:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 394}, "id": "GbTRsYVoj8GL", "outputId": "7e50c4f9-a836-4b45-ab11-f2663fecc8e1"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x400 with 3 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["t = np.arange(0, 2, 0.01)\n", "x = np.cos(2*np.pi*t)\n", "y = np.sin(2*np.pi*t)\n", "ang = np.arctan2(y, x)*180/np.pi\n", "\n", "plt.figure(figsize=(12, 4))\n", "hax1 = plt.subplot2grid((2, 3), (0, 0), rowspan=2)\n", "hax1.plot(x, y, 'go')\n", "hax1.plot(0, 0, 'go')\n", "hax1.set_xlabel('x [m]')\n", "hax1.set_ylabel('y [m]')\n", "hax1.set_xlim([-1.1, 1.1])\n", "hax1.set_ylim([-1.1, 1.1])\n", "hax2 = plt.subplot2grid((2, 3), (0, 1), colspan=2)\n", "hax2.plot(t, x, 'bo', label='x')\n", "hax2.plot(t, y, 'ro', label='y')\n", "hax2.legend(numpoints=1, frameon=True, framealpha=.8)\n", "hax2.set_ylabel('Position [m]')\n", "hax2.set_ylim([-1.1, 1.1])\n", "hax3 = plt.subplot2grid((2, 3), (1, 1), colspan=2)\n", "hax3.plot(t, ang, 'go')\n", "hax3.set_yticks(np.arange(-180, 181, 90))\n", "hax3.set_xlabel('Time [s]')\n", "hax3.set_ylabel('Angle [ o]')\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {"id": "LAJn4ll4j-lz"}, "source": ["Because the output of the `arctan2` is bounded to $[-\\pi,\\pi]$, the angle measured appears chopped in the figure. This problem can be solved using the function `numpy.unwrap`, which detects sudden jumps in the angle and corrects that:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 293}, "id": "E3V-Ynh3kAS3", "outputId": "5ce4d4f7-2b56-4ba4-c599-1f5045be7ca5"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x300 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["ang = np.unwrap(np.arctan2(y, x))*180/np.pi\n", "\n", "hfig, hax = plt.subplots(1,1, figsize=(8,3))\n", "hax.plot(t, ang, 'go')\n", "hax.set_yticks(np.arange(start=0, stop=721, step=90))\n", "hax.set_xlabel('Time [s]')\n", "hax.set_ylabel('<PERSON>le [ o]')\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {"id": "p0oq7S6TkB2_"}, "source": ["If now we want to measure the angle of a joint (i.e., the angle of a segment in relation to other segment) we just have to subtract the two segment angles (but this is correct only if the angles are at the same plane):"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 293}, "id": "DZnVaSq1kD8g", "outputId": "a81a5e16-e190-4915-8bf0-2c04823594fe"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x300 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["x1, y1 = 0.0, 0.0\n", "x2, y2 = 1.0, 1.0\n", "x3, y3 = 1.1, 1.0\n", "x4, y4 = 2.1, 0.0\n", "\n", "hfig, hax = plt.subplots(1,1, figsize=(8,3))\n", "hax.plot((x1,x2), (y1,y2), 'b-', (x1,x2), (y1,y2), 'ro', linewidth=3, markersize=12)\n", "hax.add_patch(matplotlib.patches.FancyArrowPatch(posA=(x1+np.sqrt(2)/3, y1),\n", "                                                 posB=(x2/3, y2/3),\\\n", "      arrowstyle='->,head_length=10,head_width=5', connectionstyle='arc3,rad=0.3'))\n", "plt.text(1/2, 1/5, '$\\\\theta_1$', fontsize=24)\n", "hax.plot((x3,x4), (y3,y4), 'b-', (x3,x4), (y3,y4), 'ro', linewidth=3, markersize=12)\n", "hax.add_patch(matplotlib.patches.FancyArrowPatch(posA=(x4+np.sqrt(2)/3, y4),\n", "                                                 posB=(x4-1/3, y4+1/3),\\\n", "      arrowstyle='->,head_length=10,head_width=5', connectionstyle='arc3,rad=0.3'))\n", "hax.xaxis.set_ticks((x1,x2,x3,x4))\n", "hax.yaxis.set_ticks((y1,y2,y3,y4))\n", "hax.xaxis.set_ticklabels(('x1','x2','x3','x4'), fontsize=20)\n", "#hax.yaxis.set_ticklabels(('$y_1,\\,y_4′,′y_2,\\,y_3$'), fontsize=20)\n", "plt.text(x4+.2,y4+.3,'$\\\\theta_2$', fontsize=24)\n", "hax.add_patch(matplotlib.patches.FancyArrowPatch(posA=(x2-1/3, y2-1/3),\n", "                                                 posB=(x3+1/3, y3-1/3),\\\n", "      arrowstyle='->,head_length=10,head_width=5', connectionstyle='arc3,rad=0.3'))\n", "plt.text(x1+.8,y1+.35,'$\\\\theta_J=\\\\theta_2-\\\\theta_1$', fontsize=24)\n", "hax.set_xlim(min([x1,x2,x3,x4])-0.1, max([x1,x2,x3,x4])+0.5)\n", "hax.set_ylim(min([y1,y2,y3,y4])-0.1, max([y1,y2,y3,y4])+0.1)\n", "hax.grid(xdata=(0,1), ydata=(0,1))\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {"id": "DPEfJdRMkGzq"}, "source": ["The joint angle shown above is simply the difference between the adjacent segment angles:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 81}, "id": "0zcls8TbkHY4", "outputId": "385b95ab-710f-4490-dbda-0a8eabd11017"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\theta_1=\\;45.0^o"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\theta_2=\\;135.0^o"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\theta_J=\\;90.0^o"}, "metadata": {}}], "source": ["x1, y1, x2, y2 = 0, 0, 1, 1\n", "x3, y3, x4, y4 = 1.1, 1, 2.1, 0\n", "ang1 = np.arctan2(y2-y1, x2-x1)*180/np.pi\n", "ang2 = np.arctan2(y3-y4, x3-x4)*180/np.pi\n", "\n", "#print('Angle 1:', ang1, '\\nAngle 2:', ang2, '\\nJoint angle:', ang2-ang1)\n", "display(Latex(r'\\theta_1=\\;' + str(ang1) + '^o'))\n", "display(Latex(r'\\theta_2=\\;' + str(ang2) + '^o'))\n", "display(Latex(r'\\theta_J=\\;' + str(ang2-ang1)+'^o'))"]}, {"cell_type": "markdown", "metadata": {"id": "ET0AYfIzkMX_"}, "source": ["The following convention is commonly used to describe the knee and ankle joint angles at the sagittal plane (figure from Winter 2005):\n", "\n", "<div class='center-align'><figure><img src='https://github.com/BMClab/BMC/blob/master/images/jointangles.png?raw=1' width=350 alt='Joint angle convention'/> <figcaption><center><i>Figure. Convention for the sagital joint angles of the lower limb (from Winter, 2009).</i></center></figcaption></figure></div>"]}, {"cell_type": "markdown", "metadata": {"id": "vZOIrIH6kO58"}, "source": ["## Angle between two 3D vectors\n", "\n", "In certain cases, we have access to the 3D coordinates of markers but we just care for the angle between segments in the plane defined by these segments (but if there is considerable movement in different planes, this simple 2D angle might give unexpected results).   \n", "Consider that `p1` and `p2` are the 3D coordinates of markers placed on segment 1 and `p2` and `p3` are the 3D coordinates of the markers on segment 2.    \n", "\n", "To determine the 2D angle between the segments, one can use the definition of the dot product:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{a} \\cdot \\mathbf{b} = ||\\mathbf{a}||\\:||\\mathbf{b}||\\:cos(\\theta)\\;\\;\\; \\Rightarrow \\;\\;\\; angle = arccos\\left(\\frac{dot(p2-p1,\\;p3-p2)}{norm(p2-p1)*norm(p3-p2)\\;\\;\\;\\;\\;} \\right) $$\n", "</span>\n", "\n", "Or using the definition of the cross product:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{a} \\times \\mathbf{b} = ||\\mathbf{a}||\\:||\\mathbf{b}||\\:sin(\\theta) \\;\\; \\Rightarrow \\;\\; angle = arcsin\\left(\\frac{cross(p2-p1,\\;p3-p2)}{norm(p2-p1)*norm(p3-p2)\\;\\;\\;\\;\\;} \\right) $$\n", "</span>\n", "\n", "But because `arctan2` has a better numerical accuracy, combine the dot and cross products, and in Python notation:\n", "```python\n", "angle = np.arctan2(np.linalg.norm(np.cross(p1-p2, p4-p3)), np.dot(p1-p2, p4-p3))\n", "```\n", "See [this notebook](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ScalarVector.ipynb) for a review on the mathematical functions cross product and scalar product."]}, {"cell_type": "markdown", "metadata": {"id": "4Ic0Cl3rkjpH"}, "source": ["We can use the formula above for the angle between two 3D vectors to calculate the joint angle even with the 2D vectors we calculated before:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ISMWh6i0klHq", "outputId": "c3bb0844-3a58-43ae-fbff-e7697dd0afe2"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Joint angle: 90.0\n"]}], "source": ["p1, p2 = np.array([0, 0]),   np.array([1, 1])    # segment 1\n", "p3, p4 = np.array([1.1, 1]), np.array([2.1, 0])  # segment 2\n", "\n", "angle = np.arctan2(np.linalg.norm(np.cross(p1-p2, p4-p3)), np.dot(p1-p2, p4-p3))*180/np.pi\n", "\n", "print('Joint angle:', '{0:.1f}'.format(angle))"]}, {"cell_type": "markdown", "metadata": {"id": "l5dkxdTbkma_"}, "source": ["As expected, the same result.  \n", "\n", "In <PERSON><PERSON><PERSON>, if the third components of vectors are zero, we don't even need to type them; <PERSON><PERSON><PERSON> takes care of adding zero as the third component for the cross product."]}, {"cell_type": "markdown", "metadata": {"id": "5FGBwWBvkp7z"}, "source": ["## Angular position, velocity, and acceleration\n", "\n", "The angular position is a vector, its direction is given by the perpendicular axis to the plane where the angular position is described, and the motion if it occurs it's said to occur around this axis."]}, {"cell_type": "markdown", "metadata": {"id": "3rcNt5Ymkuz3"}, "source": ["Angular velocity is the rate (with respect to time) of change of the angular position:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{\\omega}(t) = \\frac{\\mathbf{\\theta}(t_2)-\\mathbf{\\theta}(t_1)}{t_2-t_1} = \\frac{\\Delta \\mathbf{\\theta}}{\\Delta t}$$\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{\\omega}(t) = \\frac{d\\mathbf{\\theta}(t)}{dt} $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "uqJN5TWNk0xZ"}, "source": ["Angular acceleration is the rate (with respect to time) of change of the angular velocity, which can also be given by the second-order rate of change of the angular position:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{\\alpha}(t) = \\frac{\\mathbf{\\omega}(t_2)-\\mathbf{\\omega}(t_1)}{t_2-t_1} = \\frac{\\Delta \\mathbf{\\omega}}{\\Delta t}$$\n", "</span>\n", "\n", "Likewise, angular acceleration is the first-order derivative of the angular velocity or the second-order derivative of the angular position vector:   \n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{\\alpha}(t) = \\frac{d\\mathbf{\\omega}(t)}{dt} = \\frac{d^2\\mathbf{\\theta}(t)}{dt^2} $$\n", "</span>\n", "\n", "The direction of the angular velocity and acceleration vectors is the same as the angular position (perpendicular to the plane of rotation) and the sense is given by the right-hand rule."]}, {"cell_type": "markdown", "metadata": {"id": "ol0tvt7Yk31i"}, "source": ["### The antiderivative\n", "\n", "As the angular acceleration is the derivative of the angular velocity which is the derivative of angular position, the inverse mathematical operation is the [antiderivative](http://en.wikipedia.org/wiki/Antiderivative) (or integral):\n", "\n", "<span class=\"notranslate\">\n", "$$ \\mathbf{\\theta}(t) = \\mathbf{\\theta}_0 + \\int \\mathbf{\\omega}(t) dt $$\n", "\n", "$$ \\mathbf{\\omega}(t) = \\mathbf{\\omega}_0 + \\int \\mathbf{\\alpha}(t) dt $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "BZys4xBhk8-Z"}, "source": ["## Relationship between linear and angular kinematics\n", "\n", "Consider a particle rotating around a point at a fixed distance `r` (circular motion), as the particle moves along the circle, it travels an arc of length `s`.   \n", "The angular position of the particle is:\n", "\n", "<span class=\"notranslate\">\n", "$$ \\theta = \\frac{s}{r} $$\n", "</span>\n", "\n", "Which is in fact similar to the definition of the angular measure radian:\n", "\n", "<div class='center-align'><figure><img src='http://upload.wikimedia.org/wikipedia/commons/thumb/3/3d/Radian_cropped_color.svg/220px-Radian_cropped_color.svg.png' width=200/><figcaption><center><i>Figure. An arc of a circle with the same length as the radius of that circle corresponds to an angle of 1 radian (<a href=\"https://en.wikipedia.org/wiki/Radian\">image from Wikipedia</a>).</i></center></figcaption></figure></div>"]}, {"cell_type": "markdown", "metadata": {"id": "l9BWl2kLlGkd"}, "source": ["Then, the distance travelled by the particle is the arc length:\n", "\n", "<span class=\"notranslate\">\n", "$$ s = r\\theta $$\n", "</span>\n", "\n", "As the radius is constant, the relation between linear and angular velocity and acceleration is straightfoward:\n", "\n", "<span class=\"notranslate\">\n", "$$ v = \\frac{ds}{dt} = r\\frac{d\\theta}{dt} = r\\omega $$\n", "\n", "$$ a = \\frac{dv}{dt} = r\\frac{d\\omega}{dt} = r\\alpha $$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "T9Q-JU9blLJS"}, "source": ["## Further reading\n", "\n", "- Read pages 718-742 of the 15th chapter of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book] (http://ruina.tam.cornell.edu/Book/index.html) about circular motion of particle."]}, {"cell_type": "markdown", "metadata": {"id": "VQOn2E5FlaPo"}, "source": ["## Video lectures on the Internet\n", "\n", "- Khan Academy: [Uniform Circular Motion Introduction](https://www.khanacademy.org/science/ap-physics-1/ap-centripetal-force-and-gravitation)\n", "- [Angular Motion and Torque](https://www.youtube.com/watch?v=jNc2SflUl9U)\n", "- [Rotational Motion Physics, Basic Introduction, Angular Velocity & Tangential Acceleration](https://www.youtube.com/watch?v=WQ9AH2S8B6Y)  "]}, {"cell_type": "markdown", "metadata": {"id": "qn_mn7izlmHv"}, "source": ["## Problems\n", "\n", "1. A gymnast performs giant circles around the horizontal bar (with the official dimensions for  Artistic Gymnastics) at a constant rate of one circle every 2 s and consider that his center of mass is 1 m distant from the bar. At the lowest point (exactly beneath the bigh bar), the gymnast releases the bar, moves forward, and lands standing on the ground.   \n", " a) Calculate the angular and lineat velocity of the gymnast's center of mass at the point of release.  \n", " b) Calculate the horizontal distance travelled by the gymnast's center of mass.  \n", "\n", "2. With the data from Table A1 of Winter (2009) and the convention for the sagital joint angles of the lower limb:   \n", " a. Calculate and plot the angles of the foot, leg, and thigh segments.   \n", " b. Calculate and plot the angles of the ankle, knee, and hip joint.   \n", " c. Calculate and plot the velocities and accelerations for the angles calculated in B.\n", " <PERSON><PERSON> <PERSON>mpare the ankle angle using the two different conventions described by <PERSON> (2009), that is, defining the foot segment with the MT5 or the TOE marker.  \n", " e. Knowing that a stride period corresponds to the data between frames 1 and 70 (two subsequents toe-off by the right foot), can you suggest a possible candidate for automatic determination of a stride? Hint: look at the vertical displacement and acceleration of the heel marker.  \n", "\n", " [Clik here for the data from Table A.1 (Winter, 2009)](./../data/WinterTableA1.txt) from [Winter's book student site](http://bcs.wiley.com/he-bcs/Books?action=index&bcsId=5453&itemId=0470398183).  \n", "\n", " Example: load data and plot the markers' positions:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 642}, "id": "BBFKdpVllr_q", "outputId": "5374bb96-f4e3-43c1-a179-f0fc852a1e13"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 3 Axes>"], "image/png": "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********************************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\n"}, "metadata": {}}], "source": ["# load data file\n", "# use Pandas just to read data from internet\n", "data = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data/WinterTableA1.txt',\n", "                   sep=' ', header=None, skiprows=2).to_numpy()\n", "markers = ['RIB CAGE', 'HIP', 'KNEE', 'FIBULA', 'ANKLE', 'HEEL', 'MT5', 'TOE']\n", "\n", "fig = plt.figure(figsize=(10, 6))\n", "\n", "ax = plt.subplot2grid((2,2),(0, 0))\n", "ax.plot(data[: ,1], data[:, 2::2])\n", "ax.set_xlabel('Time [s]', fontsize=14)\n", "ax.set_ylabel('Horizontal [cm]', fontsize=14)\n", "\n", "ax = plt.subplot2grid((2, 2),(0, 1))\n", "ax.plot(data[: ,1], data[:, 3::2])\n", "ax.set_xlabel('Time [s]', fontsize=14)\n", "ax.set_ylabel('Vertical [cm]', fontsize=14)\n", "\n", "ax = plt.subplot2grid((2, 2), (1, 0), colspan=2)\n", "ax.plot(data[:, 2::2], data[:, 3::2])\n", "ax.set_xlabel('Horizontal [cm]', fontsize=14)\n", "ax.set_ylabel('Vertical [cm]', fontsize=14)\n", "plt.suptitle('Table A.1 (Winter, 2009): female, 22 yrs, 55.7 kg, 156 cm, ' \\\n", "             'fast cadence (115 steps/min)', y=1.02, fontsize=14)\n", "ax.legend(markers, loc=\"upper right\", title='Markers')\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {"id": "zKR_iv2vlvCS"}, "source": ["## References\n", "\n", "- Winter DA (2009) [Biomechanics and motor control of human movement](http://books.google.com.br/books?id=_bFHL08IWfwC). 4th edition. Hoboken, EUA: Wiley."]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 0}