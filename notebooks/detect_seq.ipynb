{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Detect indices of sequential data identical to a value\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://demotu.org/](http://demotu.org/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The function `detect_seq.py` from Python module `detecta` detects initial and final indices of sequential data identical to parameter `value` in `x`.  \n", "Use parameter `min_seq` to set the minimum number of sequential values to detect.\n", "\n", "The signature of `detect_seq.py` is:\n", "\n", "```python\n", "idx = detect_seq(x, value=np.nan, index=False, min_seq=1, max_alert=0, show=False, ax=None)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installation\n", "\n", "```bash\n", "pip install detecta\n", "```\n", "\n", "Or\n", "\n", "```bash\n", "conda install -c duartexyz detecta\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "%matplotlib inline\n", "\n", "from detecta import detect_seq"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function detect_seq in module detecta.detect_seq:\n", "\n", "detect_seq(x, value=nan, index=False, min_seq=1, max_alert=0, show=False, ax=None)\n", "    Detect indices in x of sequential data identical to value.\n", "    \n", "    Parameters\n", "    ----------\n", "    x : 1D numpy array_like\n", "        data\n", "    value : number, optional. Default = np.nan\n", "        Value to be found in data\n", "    index : bool, optional. Default = False\n", "        Set to True to return a 2D array of initial and final indices where\n", "        data is equal to value or set to False to return an 1D array of Boolean\n", "        values with same length as x with True where x is equal to value and \n", "        False where x is not equal to value.\n", "    min_seq : integer, optional. Default = 1\n", "        Minimum number of sequential values to detect        \n", "    max_alert : number, optional. Default = 0\n", "        Minimal number of sequential data for a message to be printed with\n", "        information about these indices. Set to 0 to not print any message.\n", "    show : bool, optional. Default = False\n", "        Show plot (True) of not (False).\n", "    ax : matplotlib object, optional. Default = None\n", "        Mat<PERSON>lot<PERSON>b axis object where to plot.\n", "        \n", "    Returns\n", "    -------\n", "    idx : 1D or 2D numpy array_like\n", "        2D numpy array [indi, indf] of initial and final indices (if index is\n", "        equal to True) or 1D array of Boolean values with same length as x (if\n", "        index is equal to False).\n", "            \n", "    References\n", "    ----------\n", "    .. [1] https://github.com/demotu/detecta/blob/master/docs/detect_seq.ipynb\n", "    \n", "    Examples\n", "    --------\n", "    >>> x = [1, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0]\n", "    >>> detect_seq(x, 0)\n", "    \n", "    >>> detect_seq(x, 0, index=True)\n", "    \n", "    >>> detect_seq(x, 0, index=True, min_seq=2)  \n", "    \n", "    >>> detect_seq(x, 10)\n", "    \n", "    >>> detect_seq(x, 10, index=True)\n", "    \n", "    >>> detect_seq(x, 0, index=True, min_seq=2, show=True)\n", "    \n", "    >>> detect_seq(x, 0, index=True, max_alert=2)\n", "    \n", "    Version history\n", "    ---------------\n", "    '1.0.1':\n", "        Part of the detecta module - https://pypi.org/project/detecta/\n", "\n"]}], "source": ["help(detect_seq)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["x = [1, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([False,  True,  True,  True, False, False, False,  True, False,\n", "        True,  True,  True])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 7,  7],\n", "       [ 9, 11]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 9, 11]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True, min_seq=2)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([<PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse,\n", "       False, False, False])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 10)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([], shape=(0, 2), dtype=int64)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 10, index=True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[4, 6]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 1, index=True, min_seq=2, show=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([ True, False, False, False,  True,  True,  True, False,  True,\n", "       False, False, False])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 1, index=False, min_seq=1, show=True)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 9, 11]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True, min_seq=2, show=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential data equal or longer than 2: ([2], [1 3])\n", "Sequential data equal or longer than 2: ([2], [ 9 11])\n"]}, {"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 7,  7],\n", "       [ 9, 11]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True, max_alert=2)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[2, 3],\n", "       [7, 7]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["x = [1, 2, np.nan, np.nan, 5, 4, 5, np.nan, 2, 1, 2]\n", "detect_seq(x, np.nan, index=True, max_alert=2, show=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}