{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Resample data\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://demotu.org/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "%matplotlib notebook\n", "# tk qt notebook inline ipympl\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "import sys, os\n", "sys.path.insert(1, r'./../functions')\n", "\n", "from resample import resample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.00</th>\n", "      <td>1.203531</td>\n", "      <td>-1.026002</td>\n", "      <td>0.345930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.01</th>\n", "      <td>0.745963</td>\n", "      <td>0.944473</td>\n", "      <td>-0.054512</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.02</th>\n", "      <td>0.789788</td>\n", "      <td>-0.049776</td>\n", "      <td>-1.072692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.03</th>\n", "      <td>2.005024</td>\n", "      <td>-1.013743</td>\n", "      <td>-0.354158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.04</th>\n", "      <td>-1.185777</td>\n", "      <td>-0.818023</td>\n", "      <td>-1.550145</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             0         1         2\n", "0.00  1.203531 -1.026002  0.345930\n", "0.01  0.745963  0.944473 -0.054512\n", "0.02  0.789788 -0.049776 -1.072692\n", "0.03  2.005024 -1.013743 -0.354158\n", "0.04 -1.185777 -0.818023 -1.550145"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["y = np.random.randn(100, 3)\n", "freq_new = 100\n", "y = pd.DataFrame(data=y, columns=None)\n", "y.index = y.index/freq_new\n", "y.index.name = y.index.name\n", "y.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000</th>\n", "      <td>1.204336</td>\n", "      <td>-1.026688</td>\n", "      <td>0.346161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.001</th>\n", "      <td>1.298574</td>\n", "      <td>-0.925828</td>\n", "      <td>0.396714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.002</th>\n", "      <td>1.364617</td>\n", "      <td>-0.776893</td>\n", "      <td>0.436292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003</th>\n", "      <td>1.398068</td>\n", "      <td>-0.586546</td>\n", "      <td>0.459980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004</th>\n", "      <td>1.396121</td>\n", "      <td>-0.364184</td>\n", "      <td>0.463271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.995</th>\n", "      <td>-0.672521</td>\n", "      <td>0.643513</td>\n", "      <td>-0.066679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.996</th>\n", "      <td>-0.541424</td>\n", "      <td>0.524998</td>\n", "      <td>-0.065225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.997</th>\n", "      <td>-0.399792</td>\n", "      <td>0.391877</td>\n", "      <td>-0.055730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.998</th>\n", "      <td>-0.256741</td>\n", "      <td>0.253914</td>\n", "      <td>-0.040121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.999</th>\n", "      <td>-0.120937</td>\n", "      <td>0.120501</td>\n", "      <td>-0.020723</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1000 rows × 3 columns</p>\n", "</div>"], "text/plain": ["              0         1         2\n", "0.000  1.204336 -1.026688  0.346161\n", "0.001  1.298574 -0.925828  0.396714\n", "0.002  1.364617 -0.776893  0.436292\n", "0.003  1.398068 -0.586546  0.459980\n", "0.004  1.396121 -0.364184  0.463271\n", "...         ...       ...       ...\n", "0.995 -0.672521  0.643513 -0.066679\n", "0.996 -0.541424  0.524998 -0.065225\n", "0.997 -0.399792  0.391877 -0.055730\n", "0.998 -0.256741  0.253914 -0.040121\n", "0.999 -0.120937  0.120501 -0.020723\n", "\n", "[1000 rows x 3 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["y2 = resample(y, freq_old=100, freq_new=1000, limit=1000, method='resample_poly')\n", "y2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}