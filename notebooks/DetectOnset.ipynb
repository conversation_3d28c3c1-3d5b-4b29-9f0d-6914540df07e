{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Detection of onset in data\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://demotu.org/](http://demotu.org/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["One of the simplest methods to automatically detect or identify the change or occurrence of a particular event in the data, for example, its beginning and ending, or simply the data onset, is based on amplitude threshold, where the signal is considered to be 'on' when it is above a certain threshold. This threshold can be proportional to the amplitude of the baseline (the part of the data that we know there is no real signal, only noise).  \n", "\n", "For instance, a threshold equals to two or three times the standard deviation of the baseline is a common procedure employed in the analysis of electromyographic data. Other way to set the threshold would be as a percentage value of the maximum or peak of the data. For instance, in movement analysis it's common to define the onset period as the signal above 5% of the peak velocity of the investigated movement. \n", "\n", "The function `detect_onset.py` from Python module `detecta` implements such onset detection based on the amplitude-threshold method with a parameter to specify a minimum number of samples above threshold to detect as onset, other parameter to specify the minimum number of samples (continuous or not) below threshold that will be ignored in the detection of data greater or equal to threshold (to avoid the detection of spikes or transients in the data), and a second threshold parameter to specify the minimum amplitude that a minimum number of samples should have (to avoid the detection of baseline fluctuations that are above the first threshold but are not actual signals).  \n", "\n", "`detect_onset.py` signature is:\n", "```python\n", "inds = detect_onset(x, threshold=0, n_above=1, n_below=0, threshold2=None, n_above2=1, show=False, ax=None)\n", "```\n", "Let's see how `detect_onset.py` works; first let's import the necessary Python libraries and configure the environment:  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installation\n", "\n", "```bash\n", "pip install detecta\n", "```\n", "\n", "Or\n", "\n", "```bash\n", "conda install -c duartexyz detecta\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "from detecta import detect_onset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's run the function examples:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 61, 143]])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> detect_onset(x, np.std(x[:50]), n_above=10, n_below=0, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add some noise:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 57,  79],\n", "       [ 81,  99],\n", "       [101, 119],\n", "       [121, 142]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, np.std(x[:50]), n_above=10, n_below=0, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the `n_below` parameter to not detect the noise from former example as onsets:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 52, 145]])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, np.std(x[:50]), n_above=10, n_below=2, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`detect_onset` works with missing values (NaNs):"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 2,  2],\n", "       [ 6,  8],\n", "       [10, 11]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = [0, 0, 2, 0, np.nan, 0, 2, 3, 3, 0, 1, 1, 0]\n", "    >>> detect_onset(x, threshold=1, n_above=1, n_below=0, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Consider the case where there is a false onset because some data in the baseline are greater than the `threshold`:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[  8,  42],\n", "       [ 59, 140]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[11:41] = np.ones(30)*.3\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, .1, n_above=10, n_below=1, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can't increase the value of the `threshold` because this would delay the detection of the actual onset:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgEAAAEaCAYAAACIH4SkAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAgAElEQVR4nOydeXwV1fn/308CBMhCQnbCviirBYnYFrdqVXCjVduqVavWrbWtttrt237r0tr2a7ev9euG1bZaFFtriyK4/FTcF8Ai+xKQQAJZyAokhCzn98eZuZm735vcNfe8X6+8cmfmzMy5c+7M+czzPOc5opTCYDAYDAZD6pEW7woYDAaDwWCID0YEGAwGg8GQohgRYDAYDAZDimJEgMFgMBgMKYoRAQaDwWAwpChGBBgMBoPBkKIYEWAwGAwGQ4piRAAgIuNFRInIoBica5WIXNvHfXeLyOf9bDtNRKr6V7vkQkSuEpG3412PZEFE/iIiv4j1vomEudcNYJ4dTlJSBAS6wVINERkpIv8SkcMiUikil4WwzxAR2WoeRIERkZ+LyAYR6RKRO3xsv8y65odF5N8iMjIO1Ux4RGSxiGwTkR4RuSrMfQf0vS4iGSLyqPU7Oigi/xGRhX04zmmWOLrfY/3b4V5zg29EpEhEnhKRfSLSIiLviMiJfThOqYg8Zx1Hicj4/tQrJUVAf4nFW0QMuR84ChQDXwUeFJEZQfb5PlAX7YoNACqAHwAveG6wrvHDwBXoa98GPBDT2iUPHwPfBD6K9YmT4F4fBOwFTgVGAP8N/L2PHcNh4Mr+dioGv2QBq4G5wEjgr8ALIpIV5nF6gBeBiyJRqZQTASLyBDAWeF5EDonIDxybvyoie0TkgIj8xLHPHSLyjIj8TURagatEJE1EfiQiO0WkQUT+br/JichQq2yDiDSLyGoRKXacZ5ylAg+KyMsiUuA41wUissnab5WITPPzPYZZJtomEdkMnNCHa5GJ/iH9t1LqkFLqbeA5dMfkb58JwOXAr8I81x3WNXrc+t6bRKQ8hP3sa3xQRDaLyBe9i8h9lrLeKiJnODaMshRzo4hUiMh1jvXtzjdvEZljtftga/kaEdliXd+XRGRcON8XQCn1V6XUSuCgj81fBZ5XSr2plDqEfnhfKCLZIVyT00SkSkRuFZE6EdkvIleHWK0CEXnFup5vOL+XiEy1tjVab95fDlCH66xr2mhd41HW+jtF5D7r82DRVo57rOVhInJERPJCrCsASqn7lVKvAkfC2S8V7nWl1GGl1B1Kqd1KqR6l1HLgE3RHEy7NwF+A2/3UI01Efira6lBn3csjrG22m+Vrfq6r32sYjIHy7FBK7VJK/V4ptV8p1a2UWgwMAY4N5TrYKKVqlVIPoAVF/1FKpdwfsBv4vGN5PKCAR4BhwKeADmCatf0OoBP4Alo4DQNuAd4HRgMZ6Le6p6zyNwDPA8OBdPQNmWNtWwXsBI6xjrMK+LW17Ri0Gj8TGIx+i6wAhnjWG/g18BZaUY4BNgJVju+0HH1T+/pbbpWZA7R7XJvb0J2Tv2u3HPgicJrzfCFc8zvQD/FzrGvyK+D9EPb7EjDKuu5fsa5PqbXtKqAL+K51vb4CtAAjre1voN+uhwKzgXrgDGvba8B1jvP8BnjI+vwF67pPQ79p/RR411F2fYBr+4CP7/A34A6PdcuAH3qsOwTMDeGanGZ977us730O2pKQF2S/v6AFySno3+y9wNvWtkz0G+XV1nc+HjgAzHDs+wvr8+nWtuOt49wHvOnYtsH6/Fn0b/0Dx7aPHfXxdw2bgR/5qP/bwFXmXne/1z2+bzH6PptqLY8Ncp0vc/ymqoASoBU41vOaA9dYdZyIfqt9FngixOvq9xqm0rPDo36zre81wlo+KUhbneSx/yDrmo8P557wqkd/dk7WP/w/GEY71n0IXOL4Eb7pcYwt9o/CWi5FPzwGWTfLu8BxPs69CvipY/mbwIvW5/8G/u7YlgZUA6d51hvYBSxwlL2eMDpla5+TgRqPddcBq/yU/6KjrqeFcz7rGv4/x/J0PARIiMdZByyyPl8F7APEo92uQD8su4Fsx7ZfAX+xPl8LvGZ9FnQHeIq1vBL4ukc7tAHj+vh78yUCXgVu9FjnausgxzsNaAcGOdbVAZ8Ost9fgKWO5SzrGo1BPwTf8ij/MHC7Y19bBDwK3ONxnE70fTQM/WDLB34E/Be6c8kC7gT+2JdraJ0nkiJgwN3r6M7s/wEP9+HanmYfE7gHeNrzmlu/2W869jnWcR2CXVe/1zCEut3BAHt2ADnABuDH/bgfIiICUs4dEIQax+c29IPLZq9H2XHAvyxTXjP6R96NVuJPAC8BS0UHb9xjm4qCnGcUUGlvUEr1WOct81HXUR51qvRRJhiH0D9GJzn4MF+Ldh3cA3y7D+ex8fzeQyWIz1VErhSRdY7rPBMocBSpVtYdYVGJvjajgEal1EGPbfa1fAb4jGXGPgV9M71lbRsH3Os4ZyP6ZvfVDn0l5GvvhwalVJdj2fP36g/Xb0ZpN0Qj+lqNA060v7P1vb+KfjP0xPN3eghoAMqUUu3AGrSP+hT0G9W7wHxr3Rshfr9oM6DudRFJs+pyFPiWj2OEw/8AZ4vIp3zUw3nuSnRH5HR/+Pu+ga5hKAyYZ4eIDENbj95XSoXlVo0GqSoCVPAiQffZCyxUSuU6/oYqpaqVUp1KqTuVUtPRJtHzgCtDOMc+9I8I0A4rtCqt9lF2v7XNZqxzo4istPygvv5WWsW2A4NEZIpj108Bm3ycbwpa7b8lIjVoU2CpiNRIlAKJLF/aI+iHWr5SKhdtChVHsTLrOtmMRV/HfcBIcfexj8W6lkqpZuBl4MvAZWjTpN3Ge4EbPNp2mFLqXatemwJc24dC/Hqb0Nfa/q4T0WbS7SHu31dcvxnRAUkj0ddqL/CGx3fOUkp9w8cxPH+nmeg3f/t3+gba9D8H7bd8AzgbmAe86djP3zU8JCL/FaHvmwr3uqCtM8XARUqpTse2sUGu81e9vrxSDcD/Aj8PVGerHl1AbfCv6/8ahrBv2CTwsyMD+Ld1rBs86nxykLY6OQKXxotUFQG1aL9Wf3gIuNsO+hCRQhFZZH3+nIjMEpF0tH+tE616g/F34FwROcN6m7gV7Vd710/ZH4tInoiMxuMNXSm10HqI+/pbaJU5jO7M7xKRTBGZDyxCv1F4shH9IJpt/V2Lvo6zsd5SRA/HuiqE7xkqmegHcr11/KvRat5JEfAd0UFoX0L74lYopfair9uvRAdvHQd8HVji2PdJ9AP7IuuzzUPoazvDOu8I69gAKKVmBLi2N9rlrDoNRd9ng6x6pFublwDnWzd+Jtq//6z99iE6EOwvfb1wAThHRE4SkSHoh/wH1rVaDhwjIldY9R4sIieI72C1J4GrRWS29VD7pXWc3db2N9DXdbNS6ijaLH4t8IlSqt4+SIBrmKWU+qVdTvSQ1KHoB/hg6zqmWdtOE5FAHf2Av9eBB9G/+/MtS4wLpdSeINd5Cb75PVrUONv/KeC7IjLBEpC/RLsNunwdwAO/19BaHvDPDqudn0G78q60rD8ulFJvBWkr29qAdT9kWIsZ1nKfSFUR8Cvgp6JNNrf18Rj3oiPpXxaRg+igF3vMZwm6sVvRZq830H7hgCiltqEj7+9DB16dj76xj/oofifaRPUJWpX66rhD4ZtoP24d+ib/hlJqE/QqU6tuXUqpGvsPbebqsZa7rU4lH30dIoJSajPwO+A99MN8FvCOR7EP0FaKA8DdwMXWmwzApWjrxT7gX2j/9iuOfZ+z9q1VSn3sOO+/0CbRpaIjxDcCYY+9Rr+JtFv1+In1+QrrHJuAG9EPljogG90WNmN8fNdI8CQ6+rsRHcT2Vas+B4GzgEvQ16sGfQ0yPA+gdKT+fwP/RL+lTrL2s3kX/Zuy3/o3o+ME3qRvvIy+dp8FFlufT7G2jUH/PvwxoO91q1O9AS3GawK94YeDUqoV7f5zRvA/Zp37TasuRwjdPej3GqbQs8O2FJ0FNPfzDb8d7VIE2Got9wlRqi/WMoPBHRE5CbhJKXVpvOuS7FgPxY/RwWadwcqnMiLyJ+AfSqmX4l0XQ98wz474YkSAwWAwGAwpSqq6AwwJQpDApbHBj2BwIv6DFvtlHjYYEg3z7IgMCWUJEJHH0D6TOqWUZxAH1oPsh9biIbT/+mPPcgaDwWAwGIKTaCLgFHTn/rgfEfBZYItSqkn0JBl3KKUCTsBQUFCgxo8fH5X6GgyRYN8+GDUq3rUwxBLT5oZos3bt2gNKqcJg5RJqcgyl1JsSYMy5PdbSwk5BGZDx48ezZs2a/lfOYIgSa9fC3L5kejckLabNDdFGREJKIJfMMQFfR6do9EJErheRNSKypr6+3lcRg8FgMBhSnqQUASLyObQI+KGv7UqpxUqpcqVUeWFhUGuIwRBXyoPOh2YYaJg2NyQKCeUOCAUre9Of0CkoG4KVNxgMBoPB4JuksgRYwz6eBa5QSkU7x7rBYDAYDAOahLIEiMhT6CktC0SkCp3edDCAUuoh4Gfo9JIPiJ73oUspZQxrhqTm9tvjXQNDrDFtbkgUEmqIYDQoLy9XZnSAwWAwGFIJEVkbyktyUrkDDIaBiBkvnnqYNjckCkYEGAxxZv/+eNfAEGtMmxsSBSMCDAaDwWBIUYwIMBjizPHHx7sGhlhj2tyQKBgRYDDEmbVr410DQ6wxbW5IFIwIMBjizPXXx7sGhlhj2tyQKBgRYDDEmUceiXcNDLHGtLkhUTAiwGAwGAyGFMWIAIPBYDAYUhQjAgyGOFNdHe8aGGKNaXNDomBEgMEQZ1I6UnzDBpg3L961iDkp3eaGhMKIAIMhzlxwQbxrEEcef1z3iEePxrsmMSWl29yQUBgRYDAY4kN3Nzz5JAwfDrt2xbs2BkNKYkSAwWCID2+8AUVFcMopsH17vGtjMKQkg+JdAYMh1Xn44XjXIE4sWQKXXw5VVSknAlK2zQ0Jh7EEGAxxJiWzxx05Av/6F1xyCRxzDOzYEe8axZSUbHNDQmJEgMEQZ0TiXYM48PrrMGsWlJXBlCkpZwlIyTY3JCRGBBgMhtizaxdMn64/H3NMyokAgyFRSCgRICKPiUidiGz0s11E5I8iUiEi60XETMhpMCQjVVUwerT+PHo0NDXBoUPxrZPBkIIklAgA/gIsCLB9ITDF+rseeDAGdTIYosp558W7BnHAKQLS0mDy5JSKC0jJNjckJAklApRSbwKNAYosAh5XmveBXBEpjU3tDIbo8Pzz8a5BHNi7F8aM6V1OMZdASra5ISFJKBEQAmXAXsdylbXODRG5XkTWiMia+vr6mFXOYOgL558f7xrEgJ07tcnfxmkJgJQTASnR5oakINlEgK+YWuW1QqnFSqlypVR5YWFhDKplMPSd5cvjXYMYcPfd8Oc/689K6Rl0UlgEpESbG5KCZBMBVYDDhshoYF+c6mIwGEKlsxO2btWfDxzQqYKHD+/dPmVKSsUEGAyJQrKJgOeAK61RAp8GWpRS++NdKYPBEISurl4R4OkKAG0J2LZNWwkMBkPMSKi0wSLyFHAaUCAiVcDtwGAApdRDwArgHKACaAOujk9NDYbIkRL9XleX7uTBtwgoKID2dv3ntBAMUFKizQ1JQUKJAKXUpUG2K+CmGFXHYIgJixenQBrZri6oq4PGRu+RAaBT6OXlQXNzSoiAlGhzQ1KQbO4Ag2HAccMN8a5BDOjq0v+3bfNtCQDIzdUiIAVIiTY3JAVGBBgMhujT2alN/lu3akuALxFgWwIMBkPMMCLAYDBEn64uPWHQ1q3aEuDpDgBtCXDmEjAYDFHHiACDIc4891y8axAhDh6Ea66BPXu8t3V1wcyZxh1gMWDa3JD0GBFgMMSZuXPjXYMIsXIlvPIKzJsHL73kvq2rC2bMgC1bjAhgALW5IelJqNEBBkMqUlaWQEPGlIKGBu2/D5fnn4ef/ERPEbxwIdTUQHa23tbVBVOn6imEs7MhM9N7/7y8lHEHJFSbG1IaYwkwGAy9rF8Pp5/uvu6SSyDYHBxdXbBihZ4e75RTdCff1ua+PTMTxo/3bQWAlLIEGAyJghEBBoOhl+pq2OeRiXv58t5EP/545x33Dn7QoN5hgaA/DxqkrQFGBBgMCYNxBxgMcea66+JdAwf19dod0NkJgwfD4cP6b+/ewPs9/7z71HiDB/sXAa2tvo+RQkMEE6rNDSmNEQEGQ5xZvDjeNXBQV6f/HzgApaW9y04R8N57cPQonHqqXlYKli2Dp5/uLTNokBYSNrYIuPZanRrYFyk0RDCh2tyQ0hh3gMEQZyIWKX70qP8ONlTsTt/zv1ME3H8/fP7z8NvfQksLPPCAPu+cOb1l/FkCjj0WZs/2fe4UcgeY0QGGRMGIAIMhznz0UYQOtHgx/Pd/9+8YdqdfW9v7Pz3dXQTs3Al//Ss89ZS2Frzyiv4s0lvGnyUgEKGKAKVgv8fkoR0dgfdxTmWcAESszQ2GfmJEgMEwUKir0/78/h4jN9ddDEyf7p4AaOdO+Nzn4P33dRDhv/8NJ5/sfhx/gYGB8DdE8PHH4V//6l1+9VW44AL3MjNnBo5beP11mDZN5zBYtixwPQyGFMKIAIMhzpSWRuhALS1w6FD/jlFXpztU2xJQVwfl5b0dbGurDhQsKdEm/9xc38fxdAd0dgYXASNG6O/Q09O7rqsL/uu/tBCweecd9yGLSun6BXrTb2qCCy+EW2+FG28MXI8YELE2Nxj6iREBBkOc8RyR12daWnTq3v5QX69FgNMSMGuW7vzb27UVYOJEd9O/L3y5AwYPDr7P8OHuQmbZMsjK0h2/nV3nvffcLQbt7dodsGOH/2MfPKgtDeee2/9rFAEi1uYGQz8xIsBgiDN33BGhAzU3988SoJTu/GfMcA8MLCnRY/urqrQImDw5+LH8BQYGw3OY4P/9H9x5Jwwdqjv5nh744APdkXd36zK2IAgkAlpbISdHi4z2dndrQxyIWJsbDP3EiACDIc7ceWeEDtRfd8DBg7rzHj/ePTCwqEjP+rd3rxYBkyYFP1ZfAgPBfZjgpk06SdEXvwgnnaStAVu3wsiR2nVgi4VwREBaGgwb5p7NMA5ErM0Nhn5iRIDBMFDorwioq9MdflGRuyWguLhXBFRUhC4C+mIJcI4QePhhnVVnyBCYPx/eflu7Aj7zGfcgwqYmvRxMBNjzGGRl9T92wmAYICSUCBCRBSKyTUQqRORHPraPFZHXReQ/IrJeRM6JRz0NhoQkUiKguNg9JqAvloC+ugOcIuD99+Hss/Vn2xLgTwTMnQuVle7ndGJbAsCIAIPBQcKIABFJB+4HFgLTgUtFZLpHsZ8Cf1dKzQEuAR6IbS0NhsizZk2EDtTfwEBbBBQW6s6/s1MfMz/fXQSEEhPgdAf09Oi/tBAeN3bnrpR2BRx7rF4/c6bODbBypW8RUFqqxYtzKKOTBBMBEWtzg6GfJIwIAOYBFUqpXUqpo8BSYJFHGQVYdzIjABNjazCA7jSbm3XQmx0wFy62CBg+XJvgd+7UAiA9XYuAigo9PfDYscGP5bQEdHdrURBsRAH0WgJqa/Ux8vP1+vR03fk3N8Nxx/l2B0yZ4t8l4BQB2dlxFwEGQ6KQSCKgDHBm+6iy1jm5A7hcRKqAFcC3Y1M1gyF6lJdH4CBtbbrjHj6870Fv9fXaCgD6rXrDBi0KQIuA99/X/0Mx6zstAaG6AqBXBDitADbz58MJJ+hj9UcEJIAlICJtbjBEgEQSAb5eE5TH8qXAX5RSo4FzgCdExOs7iMj1IrJGRNbUB5sH3WAYCLS06Ij5/rzl2pYA0P83bNBiAHTn39YWWjwAuFsCwhEB9hBBXyLg2mvhnnvcy0FSigCDIVFIJBFQBYxxLI/G29z/deDvAEqp94ChQIHngZRSi5VS5Uqp8kL7zcZgGMjYIiArq+9xAYFEQF6etjKEEg8A/bMENDX5FgGlpTrtr10fT0vA5Mn+RcDBg+4iIAESBhkMiUAiiYDVwBQRmSAiQ9CBf895lNkDnAEgItPQIsC86huSmttvj8BBnCIgEpYAT3eAiLYGhGoJcA4RDCVlsE0gd4CTJHcHRKTNDYYIEOKdGX2UUl0i8i3gJSAdeEwptUlE7gLWKKWeA24FHhGR76JdBVcppTxdBgZDUhGR7HHNzVoEtLdHzh2wa1evJQDgmGN0NsFQ6K87YN++8EXAxIl6BENnp3uKYqX0m38C5QkwGQMNiULCiAAApdQKdMCfc93PHJ83A/NjXS+DIZqMGhWBXPItLfotOj09ciJAKXcR8M9/ht6Ze7oDgs0bYGPPYLh3b2Crgy8RkJGhXQaVle5uCzto0q57AoiAiLS5wRABEskdYDCkJPv3R+AgzsDAvvi7e3qgsREKrBAbu/O3RQHojjyUYX522b5YAnJztStg7FjdcQcq5ykCQE8XvHGje1mnKwASQgREpM0NhghgRIDBMBAINSbg8OHe2ficNDbqjtLurJ2xAX2hP4GBSgV2BYC3JcCe0vjkk2HVKveyCSgCDIZEwYgAgyHOHH98BA4Sqgg48US4/HLvXAJOVwBERgT0xRKQna0zC4YqAtrbtWgYNkyv/9znkkIERKTNDYYIYESAwRBn1q6NwEHswMBAHVxLC+zerU36n/0sVFf3bvMUAXbn39chtn11B6Sl6e8RTATk5urOvaFBCwLbTTF3rg5obGjoLespAhIgY2BE2txgiABGBBgMceb66yNwEDswMJAI+M9/4FOfgieegDPPhO99r3fbxx9DmSNB58iRulxGRt/q01d3AOjvEUwEpKfr71pZ2RsPAFp8zJ8Pb7zRuy4BLQERaXODIQIYEWAwxJlHHonAQUIJDFyzRr8pi+gxau+8Ax98oPP03303/PCHvWVFtNugr/TVEgDwwAPabRGMvDz91u8UAaBdAq+/3rvsTBQECZEsKCJtbjBEgIQaImgwGCyU6p14JxRCiQlYuxYWLtSfMzPhrrvgtttg9Gi4+mptJYgU/bEELFgQWrlAIuCaa3qXE9ASYDAkCsYSYDAkIkuXwk03hV4+FBGwZo37zDVf+5re74MPIp/Crq+BgeHgTwTMmaPzDNjzhrS29iYKAiMCDAYHRgQYDHHGGZ/nYs8e3ZGFSrDAwOZmPQ2w09eeng5PPQXLlul5ASJJf9wBoeJPBAwaBCed1DtKIAEtAT7b3GCIA0YEGAxxxmekeH09HDgQ+kHswEB/MQEffQSzZ+uO38mMGTBrVlj1DQmnOyCcuQPCIS8PPvnEWwSAHoO3aZP+7CkCMjP950uIEWZ0gCFRMCLAYIgzF1zgY+WBA6GLADs3fk6O/7dcT1dAtImVJaC62rcImDhRWwnAWwSkp+tRD+3tka9TiPhsc4MhDhgRYDAkIuFYAg4dgqFDdUfrTwSsXatHBsSKvs4dEA525+9LBEya5F8EQEK4BAyGRMCIAIMhETlwQL/dd3QEL2sHBULqWQKc/50EsgSAEQEGg4URAQZDnHn4YR8r7ch2Z+Y7f9hBgeC7czt4UAcFTpnSr3qGRaxGBzj/Oykt1WmF29p8iwBn1sD334ejRyNfvwD4bHODIQ6EJQJEpFxEviIimdZypoiYXAMGQz/wmT3uwAEYMyY0l4AdFAg6yv/IEZ1jwGbLFpg61TsoMJr0J09AqAQSAWlpMH68Dhz0TBYE7gmDrrwS1q2LfP0CYDIGGhKFkESAiBSLyAfAh8CTgD2ryO+B30WpbgZDSuA1O29Hh+7IJ04MXQTYloC0NC0EDh/u3b55M0yfHrH6hkS83QHQ6xII5A7o6dGph2NsCQh1RmaDIdqEagn4A1AD5APO6cf+AZwV6UoZDClNfT0UFOjJe8IVAeDtEti0KfYiIBaWANv64U8E2MGBnsmCoPca1dZqARBjEWAwJAqhioAzgJ8opZo81u8Exka2SgZDinPggBYBBQWREQGbN+t8ALEkFpaAkSNhyBD/iY4mToStW7UYsacatrGvUWWlXg4lANNgGICEKgKGAb6kciFwJHLVMRhSj/PO81hRX6+tAAUF4QcGgvcEOQPVElBQAO++69+2PnGi9vXn5HiXsUXA7t16OZAloKdHi4kI4tXmBkOcCFUEvAlc5VhWIpIO/BB4NVKVEZEFIrJNRCpE5Ed+ynxZRDaLyCYReTJS5zYY4sXzz3usOHCgVwSEGxgI7pHvhw5BXR1MmBCx+oZELEYHQODcBxMnwvr13vEA4G0JcIqANWu0uLB5/fWIZ/fxanODIU6EKgJ+AFwnIq8AGehgwM3AfODHkaiIJSruBxYC04FLRWS6R5kp1vnmK6VmALdE4twGQzw5/3yPFXZMQKgioKnJXQQ43QFbtuj5AmI5MgBi4w4IxsSJeohguCLg+efhnnt6l1et0rEDEcSrzQ2GOBGSCFBKbQZmAe8CLwND0UGBc5RSOyNUl3lAhVJql1LqKLAUWORR5jrgfjs2QSlVF6FzGwxxY/lyjxXhWgJ274Zx43qXnSIgHiMDIDZzBwRj+HAoKQksAnbvhvx8dxFw9Ci89lpv/Vet0sGFEYwb8GpzgyFOhHxnKqVqgAjPN+pGGeCcNq0KONGjzDEAIvIOkA7coZR60fNAInI9cD3A2LEmbtGQZNTXw8yZoYuAnTt1JLyNMyYgXiIgESwBoK0BwSwBU6a4i4CODn39PvxQT0v8n/9oS8uBA1BWFru6GwwxwO+dKSKnhHoQpdSbEaiLr+gez2m+BgFTgNOA0cBbIjJTKdXsUZ/FwGKA8vLy+E0VZjD0BWdgYDAR0NWlpx12+vydMQGbNsG110avrv6IxdwBoTBxYm89nNizLVZWwoUXur/ld3Ro4fDyy/rzccfp61lfb0SAYcARSJ6vQnfCdudsd6aey6DfyvtLFTDGsTwa2OejzPtKqU7gExHZhhYFqyNwfoMhLnjNaGu7A/Lzg4uAPXu0yTsjo3ddorgDnJaAoUNjXwfQFpJ9no8R9DXavVuLk8JCb3fAggXwyit6+bTTtFXATuUcAeI4i7HB4EagmIBCoMj6fx6wDbgSmGz9XQlsBSIVNrsamCIiE0RkCHAJ8JxHmX8DnwMQkQK0e2BXhM5vMF8RAIYAACAASURBVMSFxYs9VtiBgZmZOv1vW5vP/QBvVwD0ioDDh2H/fv02HGsSxR1wySVw1VXe67OytEAaN07nGvB0B5x+OmzYAMuWwamnaqFQF7kQJK82NxjihF8RoJRqsP+AnwM3K6WWWIF7u5RSS9DR+b+IREWUUl3At4CXgC3A35VSm0TkLhGxhcZLQIOIbAZeB75v1c9gSFpuuMFjhW0JEAmeK6Ciwr8IuPdeOOus+HTAscgTEApTp8JnP+u93o6bGDdOW1E8LQEjRsBnPgMbN8L8+bo9ImgJ8GpzgyFOhHpnTkeb4j2pBqZGqjJKqRXACo91P3N8VsD3rD+DYeDR0wONjdoVAL1xASNHwve/Dw884F7enyXg449h6VI95j0eJIolwB9ZWfr/+PHaEuDMsNjRoYXBWWfpUQFZWREXAQZDohBqnoBNwO0i4sq9aX3+mbXNYDBEguZm3enYgXS2CPh//w/+/Gfv8jt3wuTJ7uuys+Htt+FHP9KdXDxIFEuAP2wR4M8dMGSIDqh85BG9rqiobyKgowN+ERFjqcEQFUIVAd9A++KrRWSViKxCWwZOt7YZDIY+8pwz8sUeGWBji4AXXtAdimdEmS9LwNix2gR+881Rq3NQksUSYIsA5+iAo0e1JSA3F2bN0uv6GhOwaxf8/Ode7facZ7STwRAnQrozlVKrRWQCcDna/C/AEuBJpdThgDsbDIaAuGW+tScPsiko0MJgxQrdkTiH2ynlWwTMm6ctAfGcrzZWaYP7Smam/j9+vL6+viwBTvrqDtizRx/78OFe4UHgbMcGQywJJ1lQG9bYe4PBEDnKyhwvir4sAa+9pofYDR+uOyhbBNTU6HW+kuHEe8L6RHcHDB6s3Sbjx+v4Cc/AQOeQS+i7O2DPHv2/ocFNBLi1ucEQR0K6M0XkwkDblVLPRqY6BkOKYw8PtCko0K6Ab3wDliyBI0d6OxNf8QCJwqBBenijbb1INBEAel6F/Hzv0QF2YKATT0tAe7v39MS+sEVAY6N7ameDIUEI9c58xs96W8vGeHYSg2GAYg8PtCko0J3ouefCM8+4+659DQ9MFET0pEVdXfGbOyAYdvY/f4GBTnJz9QiCo0f1tpkz4cUXdcph0DM5DhrU62awcVoCDIYEJNQJhNKcf8AQdF7/t4CQ0wsbDAZvrrvOseDLEjB8uE5Yk5HhLgJ8xQMkEnZwYKJaAmw8RYAvd0BaWm8GxwMHdMDfli2923/6Ux0A6MmePXp4p4cIcGtzgyGOhDo6wA2lVJdSajXwX8ADwcobDAb/uGWPq6uD4uLe5TlzdNKfoUP135EjvdsSXQTYcQHxnDsgFDxHB/iyBEBvXMD69Xp5x47ebZs36+BNT/bsgdmzvURAsIyBPT09IVbeYOgffRIBDpqBBH4KGQyJj1ukeF2d7mxs8vN7JwDytARUV8MY53QbCYY9QmAgWAKgNy7g4491PEBFRe+2bdtg+3aocuRU6+nRy7Nn65gAB4FGB1RVVfGpT32qj1/GYAiPkESAiBzv8TdXRM4DHgb+E90qGgwDm48+cix4jg5w4ikC2tu1qyBRSRZ3gK/AQF+WADtXwMcfwznn9FoCDh3SnfwXvqDjBGxqa3UsQVmZmyXgoyVL+OijZvxRU1PDtm3bjDXAEBNCtQSsQU/ws8bx+Tl0QKDxbhkMkcLTEuDEUwQcORK/2flCwekOSGQR4CswMJgl4KKLei0B27frURrnnOPuEtizRydu8ogJuPXGG4G1fqvT0tJCZ2cn9SZNsSEGhCoCJgATrf8TgHHAcKXUZ5VSW6NVOYMhFSgttT4oFdgS4BkTcORIaMPU4kWyWAKcIkCp3hEAnhQV6VkZt23TozVqanQbbNsGxx4LZ5+tczrYx7JFQH6+mzugsKuL3BG1fqvT2toKaLeAwRBtQhUB44BqpVSl9bdXKXVERAaJiBkdYEhpVD+zvrimu29u1uZ9X2+hYCwB0cIpArq69EiAdB+jngsL4a23dMeek6P/f/KJtgQcc4wO6Jw8Gd59V5d3igDbEtDZSeHRo9x1p/+3/JaWFgD27t0byW9pMPgkVBHwOjDSx/oR1jaDISWpqKhgwoQJ/Xpru+MO60MgVwD4jglIdBGQLJYA+7r6CwoELQI++ACOO04vT56s4wJsSwDAwoU6uRP4dgfs309hZibPPOs/b4AtAowlwBALQhUBQm9iICf5gJk7wJCSHDlyhC9/+csopVi6dGmfj3PnndaHQK4A0J2TpzsgkUVAMroD/AUFgm6b7m6wI/enTNFxAU4RcMklOrNjZ6dvS0BVFYUjR/Lmm4EtAYMHDzYiwBATAooAEXlORJ5DC4C/2cvW3wvAK8C7saiowZBofP/732fChAk89thjPPnkk37Ltba2st4eWx6IYJaAoUONOyAaOEcH+AsKhN62cYqAHTu0O8AWATNmwMSJ2hpgi4C8PJ1RsKcHqqspKi4G/IuA1tZWjj32WCMCDDEh2J1p26wEaALaHduOAm8Dj0ShXgZDQlNTU8Pf/vY3PvnkE7Kzs6mpqWHLli1MmzbNq+zTTz/Nk08+yeuvB/GcheMO6OrSnUoiJ+FJRktAMHcA9IqAyZPhwQd1HEdubm+5G27Q2YBsETBokJ6sqLlZWwJGj4YPA1sCZsyYYUSAISYEvDOVUlcDiMhu4Ldm2mCDQVNdXc2ECRPItR7+l1xyCU899RR33XWXV9l169bxySef+D3WmjXWh3BEQEeHtgLEe7bAQNiWgESdO8AmVHdAXp5ODzx6tF6eMkVnCjzpJPdyF18M3/2uzh9gCwc7LqC6msJx45gwYQv+aGlpYdasWaxx/TAMhugR6twBd8ZCAIjIAhHZJiIVIvKjAOUuFhElIuXRrpPB4Iu6ujqKHB32ZZddxlNPPeVzpMC6devYu3cvnfbUug7uXbSIhk922AcN7g6wYwISPSgQBp4lIC1Nzw9gC69x4/T3sl0BNsOGwVe/qrM5plmPWDsuoKqKwilTaGys81sd2xJQXV3d75EnBkMw/IoAEVkvInnW5w3Wss+/SFRERNKB+4GFwHTgUhGZ7qNcNvAd4INInNdg6Au1tbUUO3L8z507l/T0dJYsWeJWrqenh/Xr15OXl8cee0Y5B7958UXO/pLV6dTXh24JSPR4AHAfHZDIbgu7nj09gS0BvvabMMFbBAB85ztw1VW9y3augOpq8o89lpaWZrq7u30etrW1lZKSEoYPH06DmX3QEGUCyfN/AnYUkr+phCPJPKBCKbULQESWAouAzR7lfg7cA9wWgzoZUpQrr7ySm266iRNPPNHndk8RICL84x//4KyzzgLg8ssvB2Dnzp0UFBQwYcIEdu3axSTHhD89PT3UdXUB1nHq6oKPDrCGjyWNCEiGwECRXmtAoMBAX8yb53sigEmT4Cc/6V12WALSx40DcmloaKCoqIiuri56enoYYomPlpYWcnJyGD16NFVVVRQ4Z5U0GCKM3ztTKXWnr89RpAxwZseoQk9X7EJE5gBjlFLLRcSvCBCR64HrAcaOHRuFqhoGOh9++CHnn3++XxFQV1fHqFGj3NbNmjWLV199lTPPPJPi4mLOPPNM1q1bx+zZs8nPz2fXrl1u5ZuamsjMyKC5PcM+aHBLgO0OSPRsgZA87gDoHSEQyB3gi7/9LbRyI0fqKYj37YNRo4BC6uvrKSoq4t5776Wuro7/+Z//AbQIGDFiBKNHj2bv3r3Mnj07/O9jMIRIf2cRjCS+IpxcDjERSQP+ANwa7EBKqcVKqXKlVHlhoDcrg8EHSin27NnjSt/qi9raWreYAJvp06dz991383//938A/Oc//2H27NlMnDjRKziwtraW4oICbh/zmF4RzhBBYwmILE5LQKjugHDIz9f5BLKzYdgwxo4tdM0NsHHjRrfsgE4RYEYIGKJNoJiAgHEAkY4JQL/5O+dFHQ3scyxnAzOBVdZohU8Dz5ngQEOkqa+vp729nYMHD/ot4+kOcHLRRRfxxhtvUF9f77IE2O4Ar2OMHs0drd/THWVzs+4s/OGMCTCBgZHFFgHhWgJCJT8f1q/XMwoC5eW9ImDHjh3U1uq5BLq7u2lvbycrKyskEfD2229z++23R76+hpQh0J0ZizgAJ6uBKSIyAagGLgEuszcqpVoAl3NMRFYBtymlzDgaQ0SprKwECGoJ8CcCsrOzOe+881i6dKlLBNTU1PgVAaM+eJZ969frIWi+ctbbJGNgoLEEaPLzYcMGOEVPtfLii4WccUavCLCtSq2trWRlZZGWlsbo0aNZtWpVwMOuX7+e3/72t9x6663k5OREvt6GAU9IMQGxQCnVJSLfAl5CT1H8mFJqk4jcBaxRSj0Xy/oYUhc7ij+QCPAcIujJlVdeyTe/+U2OHDnC2LFjGT58uG8RUFzM/p4SeO1vgYMCwX2IYLKIgGSyBHR0hB8YGCojR+q8AZYloK2tiPr6elpbW6mvr3cNBbRdAUBIloCmpiba2tpYunQp119/feTrbRjwhBUTICKTROQ8629S8D3CQym1Qil1jFJqklLqbmvdz3wJAKXUacYKYIgGlZWVDBs2zK87oLu7m4aGBgLFm5xxxhm0tbUxe/ZsRISCggI6Oztpbm52lXGzJrz2WuB4APC2BJjAwMgRC3cA9CYasgIDKyoqmD59Ok1NTXR3d9Pa2hpQBNxyyy28+uqrruXGxkY+97nP8eijj0a+zoaUICQRICL5IvJvYAfwb+tvu4gsE5EATkyDIfmorKxk+vTpfi0BDQ0N5ObmMihAx5aens7VV1/NvHnzAD2EcMKECW7BgbYIOH7sAXjzzfBFQDJYAuz6piVSDLIP7NEB0XQHgMsSMH68FgE7duxg6tSp5ObmcuDAAZ+WAGfCoC1btrBjxw7XclNTE5dccgnV1dVs2LAh8vU2DHhCvTP/BEwGTgaGWn+nABMwcwcYBhiVlZXMnDnTryUgUDyAk7vuuou7777btTxx4kQ3l4B9nLV/3wmHD4cmApItY2B7e+JbASD6loCR1kzsliXgT38qpK6ujh07djB58mSKi4upq6tz5QgAHVuilOLw4d5kra2trW4JhJqamigsLOTqq6821gBDnwhVBJwNXKeUekcp1WX9vQPcYG0zGBKe7u5uNmzYwGOPPeZmUvWksrKSWbNm+bUEBIsHsElPTyfdEejnOUzQFgHXPzRHrwh2zGQcIphsIiBaloCcHH0dLEvAAw/0WgKmTJlCUVERtbW1bpYAvVuO2+/QUwQ0NjaSl5fHokWLgk9QZTD4IFQRUA/4mjugjd6ZBg2GhOY3v/kNCxYs4Nlnn+WWW27xWy5SlgBPPIcJ2sd55C9DdB76YIGByegOOHIk+URANCwBInD55TrNMPDss70xAVOmTHFZApwxAQAjRowIKAKamprIy8sjLy+PQ4cORb7ehgFPqCLgLuB/RaTMXmF9/p21zWBIePbt28cPfvADli1bRk1Njc9c/gcPHqSjo4OJEyf6tQT0VQQ43QFKKerq6nqPM2+eq4PwSzIGBh45ktjzBtjYowOi5Q4A+POfITPTWiigsbGR7du3e1kCnEP9cnJyaLFTRePbEjBy5Eiys7MD5rUwGPwRqkS/BRgP7BaRamtdGXAEKBKR79gFlVLHRbSGBkOEaGlpITc3l/T0dBYsWMCKFSu48cYb3crs2bOHsWPHkpOT4/ehGqo7wJNJkyaxfft2V12GDBnCMLsjf/rp4NMCew4RzMoKuw4xZdAgaG1NDkuAMzBw+PAYnHAwWVlZtLe3U1JS4rIEAH4tAT09PRw8eNCnJSAtLc2IAEOfCPXujHXiIIMh4jQ3N5ObmwvAueeey5IlS7xEQGVlpUsEBLIETJ48OezzT5kyhfr6ehobG6mvr3dZAaqrCS4AwNsSkOgTy9iWgGQQAdEODPSguhpOO62Q4cOHIyIUFRVRUVHB0KFDKStzGVzdLAGHDx9GKeUSAR0dHXR2dpJpWRc6Ozvp6uoKOGrFYPAkpF9LrBMHDSTuuusuli9fHu9qxIU5c+bw8MMPx7saLpqbm11vWWeffTbXX3897e3tvW/jaBEwbtw4hg4dSldXF0ePHmXIkCEcOXKEjo4ORowY0Wd3QHp6Oscffzxr164lIyPDdYy1a605ZYKRbGmDTWCgX9auhcLCQtckVLYlIDc3168loLW1lSFDhrhEgG0FEEtAZmVlcejQIZfQNRhCIey7U0SG4hFLoJRqi1iNBhjLli3j5ptvZurUqfGuSkxpb2/nvPPO46GHHnI9pOKN7Q4AyMvLY/bs2axatYqFCxe6ytgiQERcLoH8/Hwee+wxli9fzooVK/osAgBOOOEEVq9e7QoGA7jgAnAMBfdPMs4imGyWgGgFBnpwwQXwhS8UMWXKFABXTID9u7NxWgIOHjzI2LFj+eSTT+ju7naJABsjAgx9IaS7U0TGAX8EPgdk+igSIOF5arNnzx7OPvvsPncayczQoUPdg9/ijNMdANol8MILL3iJgHPPPRfAFWyVn5/P/v37efHFF9m9e3efYwIAysvLefrpp8nJyaGkpCS8nZNxiGCyiYAYuQMA5s+fz4wZM4BeS8CwYcP8DhFsbW0lLy+PhoYGmpqavESACQ409IVQ786/oRMEfRuoxTHFr8E/bW1tHDp0KGB62YHMpEmTqKioSFgRcMYZZ3DNNde4lbFjAsD9AWybav/0pz/1S9iccMIJ3HbbbcycOTP8Y9idaVdX8oiAZHIH2HMHxMAdAHDbbbe5PtuWAF/ugH379GSqra2t5OTkkJ+fT0NDg2tkgE1WVpYRAYawCfXunAOcoJTaEs3KDDT27t3LmDFjSEv0lKlRYvLkyVRUVDB//vx4V8UVWZ2dne1aN2bMGPbv3+9Wbu/evS4R4Hyzqq+v55ZbbuHee+8lIyODoX3sgCdOnEh7ezvr1q3jvPPOAyCssAk7LiAZREAyuQPs0QExsgR4tvnw4cMZPHgwVVVVXpaArVu3At4iwJclwOQKMIRLqL3Tx0Bqvs72A+dbZSpii4BE4ODBg2RlZbll8CsoKKC5uZmuri5AC4Xa2lpKS0sBd0tAfX09p556KtOnT++zKwD0HALl5eW8+uqrLktAWJO/2XEBJjAwssQ4MNBXmxcVFdHY2OgWEzBixAhXTEAoIsBYAgzhEqoIuB64XUQWWTMJjnX+RbOCyYw95jxVmTx5Mjt37ox3NQBvVwDoaP38/Hzq6/W87o2NjWRlZZFhvQl6WgIKCwu58cYbGe2aCa5vnHDCCbS3t7tEQFhxk3ZcQLIEBiajCIiBJcBXm9u/h0AxAdnZ2QHdAcYSYAiXUO/ONKAI+Bfu8QBiLZvAQB+kugiwYwISAefwQCfFxcXU1NRQWlrK/v37XVYA8LYEFBYWctlll7kFEvaFE044wXXusEkmd4BtCXB0VAnLkCFw6FBMAwM9KSoqYtiwYQx2ZFj0ZQkYNGiQyxIwwZFl0lgCDH0hVEvAX9HzB5wPnAjMs/5OsP4bfJDqIiDe7oAtW7bQ09MDuA8PdFJcXExtbS0ANTU1bhH79kO1s7OTlpYWRo4ciYi4vX31hX6LgCNHkkcEJEtMQIzdAb4oKipycwWAtyXAMzDQc4igEQGGcAlVBEwFblRKvaCUWqOUWuv8i2YFk5lUFwEFBQV0d3fT2NgYl/NfeOGFrF69GvDtDgAoKSmhpqYGwK8loKGhgZEjR7rFE/SHUaNGsXLlSlemNys+MDSc7oBEFwHJOHdAjNwBvtq8uLjYy1rlTBZ08OBBr5gApyA1gYGGvhCqCPgQCDK7icGTVBcBIhJXa0Btba0r+j+QO8BpCXCKANsSYLsCIsmCBQtcn59/PowdbXeACQyMLM7RATGwBPhq86KiIq/fqDNZkAkMNESDUEXAg+hZBK8VkRNF5HjnX6QqIyILRGSbiFSIyI98bP+eiGwWkfUi8qqVxCgh6enpoaqqijFjxsS7KnElXiKgs7OTpqYmlwjw5w7wtAQ43QG2JSAaIsDJ+eeHUdgZE5AMgYFHjyaHCIhxYKCvNvdlCcjOzqatrY3u7m6XCCgoKPDrDjCWAEO4hHp3PmX9X+xjW0QCA0UkHbgfOBOoAlaLyHNKqc2OYv8BypVSbSLyDeAe4Cv9PXc0qKurY8SIEW556VORSZMmxWWEgJ1f3e7g/bkDiouLWbt2raus7a8HXGmD+5MhMBTCmloi2WICnP8TGWfGwBhYAny1+YIFC5g0aZLburS0NDIzMzl48KBLBOTm5vp1BxhLgCFcQrUETPDzNwm4IUJ1mQdUKKV2KaWOAkuBRc4CSqnXHfMUvA/0b6xWFEl1V4CNL0vAa6+95po2NVrYw/7CcQd4WgKys7NjYgkIi6FDtYm9szNuAWwhk4wiIEaWAF/k5OQwd+5cr/V2XEAwd4AJDDT0hZBEgFKq0vkHdAFXAK8CkZomrgzY61iustb54+vASl8bROR6EVkjImvsziDWpHqiIBtfIuAXv/gFr7/+elTPa7d7MEuA0x3gGRMQK3dAWGRkQGur/p8gEzP5xQ4INCKgX9hxAU4RUFNTQ1pamlvmShMYaOgLIeezFZF0EfmiiLwA7Aa+CDwEhD+xup9T+Fjnc44CEbkcKAd+42u7UmqxUqpcKVUer4e3sQRofImA+vp6mpqaonreuro6xo0bFzQmIJglIFqBgU5CmkHQJiMDmpsT3xUAyWcJ6OiImTsgnDZ3WgKys7MZNmwYGRkZXkNVjTvA0BeCigAROVZEfgPsA36H9ssDXKGUukcp9UmE6lIFOKPoRlvn9KzP54GfABcopToidO6IY0SAprS0lObmZtrb213r6urqaG5ujup56+vrmTVrlpslwJc7ID8/n5aWFlpaWujo6HATCk5LQDRjAhb7irTxx9ChWgQkQ6xJMlkC7IDLGImAcNrc0xIA+nfrdAWACQxMZjo7O/nVr34Vl3MHFAEi8hba954LfFkpNVEp9dMo1WU1MEVEJojIEOAS4DmP+sxBux8uUEpF16ncT4wI0IiI29t2d3c3Bw4ciLolwBYBtbW19PT0+HUHpKenU1BQwPr16ykpKUEcJnb7zaquri6qloAbwomqyciAlhZjCYg0Q4bA4cO6rjGY8CucNh8xYgT19fUopVwprX2JAE9LwMqVK+nu7o5IfQ3RZfv27dx5551xOXewX/tngMeBe5VSb0SzIkqpLuBbwEvAFuDvSqlNInKXiFxgFfsNkAX8Q0TWichzfg4Xd4wI6MUpAhobG+np6YmJCCgrKyM7O5uGhga/7gC7fuvWrXOLB4AEjglIFhGQTJaAIUPg4MGEiwcA/TusqqoiJyfHJVLz8/ODugMuvfRStm/fHtO6GvrG1q1b6ejocE1mFkuC3Z3lwHXAWyKyGy0Ingq4Rz9QSq0AVnis+5nj8+ejde5IY08jbHAPvrNHBcTCHVBUVERpaSk1NTV+3QF2/T7++GO3eACAjIwMenp6qK6ujqo7ICwyMqC6OjlEQLJZAhJUBIwYMcIlAmzy8/MZPny4W7nMzEza2tro6emhvb2dlpYWamtrmTZtWqyrbAiTLVu2AHD48GG/z6loEdASoJRap5S6CSgFfo8esrfX2u9cEckLtH+qcuTIEVpbWxOn44gzThFQX1+PiMQkMLCwsJCSkhL2798fUAT4swSICDk5ORw6dKjf8wUE4rlw7FlDhyaPJSDZRMChQzEbdhlOm+fk5LB3714vEeDpDkhLS2PYsGG0tbWxb58Op7ItcIbEZuvWrYAWAbEm1CGCR5RSTyilTgOmoc3y3wVqRMTnML1Uprq6mlGjRpEWA99iMuBpCRg7dmxM3AGFhYWUlpaya9cuBg8e7PKn+qrfxo0bvSwBgGvq1kjNG+ALH0PD/WOPDkimwMBkmTvg0KGYWQLCaXOnO8Dm2GOPZfJk74FZdnCgEQHJRcKLACdKqQql1I/QkfxfBo5GvFZJzt69e/s95/xAwlMEHHPMMTETASUlJWzZsiWgia24uJiOjg4vSwDoB3C04wHKAmXD8MQMEYwOducfI0tAOG0+YsQIL0vALbfcwk033eRV1o4LqK6uBowIiAYdHR2u2UkjQU9PD1u3bmXcuHHJIQJslFLdSqllSqlFwUunFlVVVUYEOCgpKXE9jGwREM2YgO7ubpqbm8nPz6e0tJStW7f6DQqE3il9fYmA7OzsxHLrJJM7INkCAyEhYwJycnJobGz0mmbYF7YI2LdvH9nZ2VEVAUePpub734033sj9998fseNVV1eTnZ3NqFGjkksEGPxjggLdKS4u9rIEtLS0RFRNO2loaCA3N5f09HRKSkqCigDbDeDLHRALS0BYJNPogGSyBCSwCLCtWKGIAKc7YPbs2VETARUVFXz605+OyrETnU2bNvHUU5GLj9+6dSvTpk0jMzPTiIBkYf369a5JZ3xhLAHueAYGjho1iuHDh7uGM7W1tRHJ9M7O5D6lpaXs2bMnqDvALutJLETAddeFUdhOamNEQGSxRUCM3AHhtLnd+WdnZwct63QHzJkzx3XfRZp9+/axd+/e4AUHIBUVFWzYsCFi33/Lli1MnTrViIBk4qmnnuI3v/GZsRgwlgBPbBGglHLNyJeXl+dyCTzxxBPcECR7ysGDB1Eh5lp1Jvex3+5DsQT4MvtnZ2dHXQSElTHQflNNpsDAZBIBMbIEhNPm4VoCbHfA8ccfHzVLQFNTkyvnRygMlKRFDQ0N9PT0cPHFF/PPf/4zIsfcunUrU6dOJSsry4iAZKGxsZG33nrLb6dkLAHuZGVlkZaW5jYtb25uris4cO/evbz33ns+r+eyZcs466yzyM3NZeXK0AaiOJP72G/3gURAfn4+K1euZLCPKPZTTz2V+fPnh3TevhLW6ADbAmAsAZElLU3XM0aWgHBHBzj/B8KeRKi6uprjjz+eurq6kMVzONgCoLW1NWjZO+64g29/2Mlu6QAAIABJREFU+9sRr0M8qKioYMqUKXzpS1/imWeeicgxbRGQmZkZl7TPRgT0gaamJvbt28euXbt8bq+qqjKWAA/s4ED7LT0vL88lAqqrq6mpqfFpXrvyyiu54ooruPPOO1m2bFlI53KKgBEjRjB06NCA7gARYcGCBT63XXHFFZx11lkhnbevfPRRGIXtN1UjAiLPkCExswSE0+bhWAKcgYETJ05kyJAhtLS09LWafrHv3cbGxoDldu7cya9//WtXMpxkZ8eOHUyePJnPf/7zbN682TUKI1S2bNni5aIxMQFJSGNjI4WFhbz11lte2+xEQQkVTJYAlJSUsGfPHg4dOkReXp6bCNi3bx9ZWVl8+OGHbvt0dnbS1tbG5Zdfzpe+9CVeeOGFkN5qnCJARCgpKQloCUgqkkkEiEB6uhEB/WTYsGGkp6eH7A7Ys2cPQ4cOJTMz0y1ldySxO/9gIuDWW2/l0ksvHTDxA7YlYMiQIZx//vk8++yzIe1XXV3NVVddxcyZM7nvvvtc6+2JocrKyowIiBcffPABL7zwQlj7NDY2csEFF/Dmm296bTOJgnxTXFzMxo0bKSgoIC0tjdzcXFdMQHV1Neeccw4ffPCB2z6NjY3k5eUhIhxzzDEMHTqUjz/+OOi5PHP9l5aWJrQI8BGP6J9kcgeAFgDJJAJi5A4Ip81FhBEjRoRsCdi2bRtlViKCaIkAW8A3NDT4LfPKK6+wceNGfv/731NVVRW10UCxxLYEAMyePduvNdiTb33rW2RkZHDvvfdSWVnpWr9t2zaOOeYY0tLSjAiIF3V1dfzud78La5/GxkYWLVrk0xJgEgX5pqSkhPXr17uC7zwtARdeeKGXCGhoaCA/Px/QD8LzzjsvJMFmxx3YjBs3LrHG+nuwz2vC7AAkU2Ag6ODAZBIBMbIEhNXmaFdAqJaArVu3MmrUKCB6IqCxsZG0tLSAloBly5Zx0003kZeXR1ZWVkRHAPni6aef5umnn47qOWxLAIQ3dXN1dTVXX301xx13HLt373at37lzp0tUGBEQJ04//XRWr14dUoCLTWNjIyeddBKNjY2u9Jw2JijQN/5EQHt7O21tbZx99tl89NFHbrNoNTQ0uOXsP/fcc1m+fDmgXQUdHR0+z+VpCfjTn/7EF77whWh8rYhwxx1hFE4mdwAYS4AfwmpzdGyMrzTBnmRnZ7N79243EdCXYYLB3tobGxsZP358QEtATU2NyyIxZsyYqLsEXnvtNdfzIVo4LQHhiAD7mTRu3DgvETBx4kTAiIC4kZmZyfz583nllVdCKm/7qUeMGMFJJ53kZQ0wwwN9U1JSwqZNm1wiwHYH7Nu3z2WuHzNmDBs3bnTt47QEAJxyyils3ryZpUuXMmPGDG677Taf5/IUAZmZmVHN/d9fwppGPNlEwODByTF3AOhrGyNLQLhTx995551u94I/srOzUUr1yx2glKK8vJxf//rXfss0NTUxefLkgJaA2tpa1/DbWIiAvXv3+gxAbGhoYNKkSf0eJWGPiCgoKAD6JgLKysqor693ZVvctWsXkyZNAowIiCvnnHMOK1asCF4QPQVubm4uaWlpnHzyyV5xAcYS4JuSkhLa29tdnbNtCdi3b5/rgXXiiSe6uQQaGxvdHnwZGRmcddZZ/PjHP+ayyy7jvffe83kuTxEwoDAxAdEjQQMDwyErKwvAZQlwpuwOldWrV9PU1MRjjz3mNx9KY2MjkydPDmgJqK2tdSXiioUIqKqqYuvWrV6d/csvv8yuXbv6nKp88eLFtLe3u6wAIgKELgLa2tro6uoiOzubQYMGUVpa6roWu3btMpaARMAWAaEErjQ2NrpM1KeddhqrVq1y224sAb7xTMhjiwA7kBK8RYCnJQDg0UcfZcuWLXz/+99n8+bNXvnLV69eTXp6ukutDzhMTED0iKE7IFrYWQX7ExPwxBNPcM011/D666/z4IMP8tJLL3mVCcUSUFNT4xIBY8eOjYklALQYcGLnF9m/f3/Yx2xra+OGG27gnnvuoaKiws0lYydmCob9UmKLh/Hjx7uCA3fu3OmyBIRjWYgkRgQAkydPJicnh3Xr1gUt6xQBc+bMYf/+/W4/LmMJ8I39MHC6AzwtAccffzzr16937eNLBOTk5LiGP02YMMHNfdDT08N3vvMdfvnLXzIoWToeYM2aMAonmzvAWAJ8Elabh0EoIqClpcUrlsnm6NGjLF26lMsvv5yysjLOP/98Nm/e7Famp6eHlpYWJk6c6FcEtLW1cfToUVeOg2hbAg4dOkRHRwfz5s1zcwn09PTw4osvMnHixD6JgIqKCkpKSvjjH//Iyy+/7AoKhNA7bU/LpB0XcOTIEerq6lz9hbEExJlQXQL2sDWA9PR0Tj31VF577TXXdpMoyDeeIsBOG+y0BIwePdrt4eRLBDiZO3cuHzmyrjzxxBP09PTwta99LRpfITEwIiB6DABLgO0OCBQTcMUVV/CDH/zAtdzd3c0zzzxDR0cHK1euZNq0aUyYMAGAwsJCr6j+1tZWsrKyKCws9OsOsF0B9ttvX0VAU1MTb7zxRtBy1dXVjB49mmnTprmJgLVr11JQUMCJJ57YJxGwY8cO5s2bx/e+9z0ef/xxL0tAKCLAc7SSbQnYvXs3Y8aMcb2wGBEAiMgCEdkmIhUi8iMf2zNE5Glr+wciMj5S5z7zzDN5/fXXg5ZzWgIAzjjjDJcIqKqqoqOjY+D6o/tBRkYGeXl5Xu4ApyWguLiYAwcOuEYIeI4O8GTu3LmuiZwOHTrEj3/8Y+67776ky9FQXh5G4cGDdRKeZBEByeYOiJElIKw2DwPbEmC732wRYPvJly9fzhtvvOH2dr9hwwYuvfRSZs2axd13382VV17p2lZYWEhdXZ3bOexnYH5+vl9LgDMeALQI2LNnT9jfZ/ny5fzsZz9zW7dixQovv789NHvq1KluImDlypUsXLiQkpKSPouAKVOmcNtttzFnzhzmzJnj2tZXS8D48ePZvXu3W1AghC4C3nvvPb7zne+E+U38kzBPSxFJB+4HFgLTgUtFZLpHsa8DTUqpycAfgP+J1PknTJgQklJtamryEgGvvvoqSin++Mc/cs011yRdJxQrLr30Upc5zXYHOC0BgwYNIj8/3/XQ8QwM9MQpApYsWcKnP/1p5s2bF+VvEWdEdEeVLCIgmSwBMRwdEC1yc3P55z//6ZoHwx4Z09raSnt7OzfffDOPPfYY27Ztc8VAbdiwgYsvvpg//OEP5OXlcfHFF7uOV1RU5GUJsK2hI0eODGgJcE7NXVZWRm1trdsQYIDLL788YEe6Y8cOt3N0dXVx7rnnerkobDespyVgxYoVLFy4kNLS0j4NlbRFQEZGBmvXruW4445zbbNz/QcbdeDLHVBZWekWFGgfLxQRsHXr1j4HOfoikXqreUCFUmqXUuoosBRY5FFmEfBX6/MzwBli25v6SVlZGdXV1UEb1NMSMHXqVI4ePcq6det49NFHufnmmyNRnQHJ/fff7+rUhw0bhoiwc+dOlyUAtC/TdgkEcwfMnj2bTZs20dnZyYMPPsg3vvGN6H6BRCEjwwQGRoMB4A4QES688EK3dcXFxaxfv55rr72WOXPmcNFFFzFy5EhXcNrGjRuZOXMm5557Li+99JJbdk1f7gD7RSgvL4+WlhafAdXOoECAwYMHU1BQ4PY23tHRwZIlSwK+fO3YsYMDBw64lm1B8OKLL7qVs92wThFQX1/Pli1bOPnkkyktLe2XJQDAs6sZMmQI6enpfvOV2PhyB+zevdstRwD4FwHr1693C3asrKxk/PjxYX8XfySSCCgDnL+GKmudzzJKqS6gBQg+eDYEcnJySE9PDzrZhqcIEBFOP/10rrrqKs4888yINs5AJy8vj3379rksARCeCMjKymLcuHE8+uijHD58mDPOOCPqdY4Gt98e5g5lZRBgQqSEYuRIsGJoEp6CAv0XA8Ju835QUlLC2WefTWFhIY888giAW2e5ceNGZs2a5XNfXyLAtgQMGjSIrKwsn2+lnpYA8I4LsN0DgTIJ2pYA++XMFgSeIsB2B4waNYqOjg4aGhq47777uOiii8jIyIiICPCFPWsjwOHDh3nooYe8ynhaAkaPHs3+/fvZvn27mztg6NChdHZ2ek27fPfdd/P444+7lisrKxk3blzY38UfiSQCfL3Re76Wh1IGEbleRNaIyJpwUlWWlZV5DS/xxBkYaHPGGWewfv16v8lrDL7Jzc1lxIgRZGZmutaNGjXKZZEJFhMA2iXw4x//mBtuuCFp3TDhZo9j0ybduSYDL70EfjqYhOPBB2GRp/ExOoTd5v3g3nvvZcuWLfzv//6v69k1ffp0l0l9w4YNzJw50+e+RUVFXjEBTpeov7gAz5gA8BYBduY8f89opRQVFRUopVwZXevr65kzZw7vv/++21uz7Q4QEaZOncpbb73F/fff74on6IsIOHjwIC0tLW6WSk+ccQHbtm3jdh/qzlMEDBkyhKKiIt555x03S4CIMHz4cC9rwM6dO6moqHAt7969e8CKgCrAGVY/GvAcx+IqIyKDgBGA1y9QKbVYKVWulCoPJ0jPdgkEwtMSAHDeeedx++23Ux6taJ8BSl5enpsVAHotAYcPHyY9PZ1hQczec+fOpb29nauvvjqaVY0qHpfAkALEss3Ly8u9Og3bEtDS0uJKAeyL3Nxc2tra3EzezhehkSNH+hQBnu4A8M4VYIsAT5Fhc+DAAdLS0hgzZozLAnDgwAEmTJhAeXm5W44WZ36WadOmcfPNN/OVr3zF9b1KSkrCjgmoqKhg0qRJAV8unCKgubmZuro6r9wlnu4A0C4Be5ilE0+XgC2EnCJgIFsCVgNTRGSCiAwBLgGe8yjzHGCP/7oYeE31Nxekg1BEgGdgIGiT2R2xlPYDhLy8PC+VbYuAYEGBNosWLeL3v/99SGUTlT5YKQ1JTrzbfNq0aWzevJmNGzcyY8YMvx2diFBQUODml/e0BPgKDgzFHVBZWcngwYP9WgLsDH0FBQWucxw4cICCggIWLFjw/9u78+Aqy3uB499fTggBjrJkgSQsYZElZAgNgVIw4LSWq9UKylj2SovFS9sZGefOvdh6vd5tpmi9da62t2LLSKXeQrULCr2WOgxQFa5R2XJZsiE7QfYlhO25f7zvezx7cpKT856T8/vMZHLynjdvnifPWX7n92wBXQL+67OMGjWKxsZGnnrqKd/9vXv35urVqzQ1NUX9v/g7cOAAw4cPj3qOfxDgbIgWHGyEW8F00KBB5ObmhmwKFTzj4PTp01y8eNEXBNy8eZOjR4/GdRp60gQBdh//94F3gL3AWmNMtYj8i4g8YJ/2SyBHRGqBJ4CQaYTt0b9//zZlAlTb9OrVK2ImoKXxAI7Bgwfz3e9+t6OKqFSnVFJSwt69e6N2BTiCxwW0JhPQ2u6AMWPGRMwEOP3xOTk5viDEeUP1DwIuX75MU1OT7/VixowZ/OxnPwt4bRGRmKcJtjQeAEIzAUDIe0i4IKC4uDgkCwChmYC6ujrKyso4e/Ysly9f5vjx4+Tk5JAdx9lBSTVs1xizAdgQdOxpv9tXgYc76u8XFRUF7Fe/detWJk+eHBAlaxAQP7179w6JhGMNAjqD8nK3S6ASze02z83NpUuXLmzcuJE777wz6rnB0wT9XwMjTRMM1x0wePDgkL7tCRMmRM0E3HHHHYhISHfAmDFjuHz5Mnv37iUjI8M3HgBgxIgRjBgxIuR6zriAIUOGsGnTJsrKyqK+ltfU1DBlypSI90P4IMB/wTP/fQP8DR8+POz4s+AgoLa2luHDh9Pc3ExdXR0XLlyIa1cAJFEmIBn4dwdcvHiRKVOmBOxPfevWLc6ePRsyMFC1zbx585g1a1bAsXQMAuylDlQaSYY2LykpYcOGDRFnBjiCFwzyfw0MNzDQeeMLDvBLS0upra31peQPHjzI+PHjWwwCwnUHiAiLFy/mueeea/VS7f6ZgMcee4y1a9dGPT/WTIDTHeCfCQjeN8Axb968sDMJwmUChg4dyrBhw6itrY37eADQICCAfxCwa9cu+vTpw5NPPsnVq1cBKzDo1q2bbyEO1T5f+tKXKCsrCziWl5fH+fPnOX78eNpkXBYvdrsEKtGSoc1HjRrF1atX29QdEC0TELxksKNr166MHDmSnTt3cu3aNU6dOsXYsWPb1B0A8Pjjj7Nu3Tq2bNnSqj5yZ8GgY8eOUVNTE7INfKS/H01wJqCgoCAgExBpR1OPxxM2pR8uEzBs2DANAhLFf0zAzp07mTlzJmVlZbz00kuAdgUkQkZGBn379qW6ujptMgH21G2VRpKhzUtKSsjJyQlJ2wcLDgJamiIYrivAUVFRQVVVFYcPH6awsJCCgoKwmQBnVHykTABY3YlLlizh2WefbVUmwOkO2Lx5M6WlpWzZsiXi4nDnzp2jqakpZHBjMP+dBM+ePcvo0aMDMgHhZgZEEy0TUFdXF/eFgkCDgAD5+fmcO3eO5uZmduzYwdixY1m+fDnLly/nzJkzYWcGqPgrLCxk9+7daRMEKOWGCRMmMHXq1JBP7MHCjQmINjAw3MwAhxMEHDx4kOLiYt/sguBVBxsbG8nKyqJ3794BsxP8gwCApUuXkpmZGXMQsGjRIm7cuOFbNTFYXV0dw4YNa/F/E5wJKCkpaVUmIBLNBLgsIyODfv36cezYMXbs2EFZWRkjR45k2rRpvP7665oJSJDCwkL27NmjQYBSHWjixIm8+eabLZ7nnwlobm7m+vXrvgW+wk0RDDczwBEcBHTp0oWePXuGBBL+qXinO8AYE/KmmpOTw6pVq7jvvvtarIczJmDz5s1MnTqVyspKtmzZEvbc4HX9IwkOAoIzAe0JAi5evMilS5coKCjwBQHxXigINAgIUVRUxKFDh6iurvZtFrFgwQJWr16tQUCCFBYWcvny5bQJAlqYlao6oVRqc/+Bgc6gQOcTcrhMQLTugNLSUurr66murvaltcMtTeysEQD4ugOuXLniW1XP30MPPdSqN8aCggJ27drFiRMnGDNmDJWVlRHHBbQlCHC6A/wzAW3pDnCu5+wtICIMGDCAkydPahCQCP3792fTpk0UFBT4pnXcfffdHDx4kG3btunMgARw5vemS8CVDCPFVWKlUpv7v0kHfxCKlAmI1B2QlZVFaWkpf/jDHwKCgODBgQ0NDb43YScTENwVECtn0F5lZSUej4cpU6ZEzAQ4ffEtCc4EDBo0iJs3b/rGCbQnE+B0BYC1w+qgQYPwer14vd5WX681NAgIUlRUxPr16xk7dqzvWGZmJrNnz2blypVp88bkJicISJdMwAMPtHyO6lxSqc39xwQET5Hu2bMnt27dCvj0e+jQoagD6saNG0dDQ4MvCAi3XfHhw4cZOHAg8HmgEesbarh6iAhTp04FrKxEY2MjJ0+eDDm3rd0BzlLozv8j1jJ7vV5fEBAciAwbNizuWQDQICBEUVERVVVVAUEAWF0C58+f1yAgAdItCFAqmfXq1YtLly5x/fp1Tp06FRAEeDweHn74YV577TXASn+/9957fPnLX454PWePFecNLVx3wKFDh3xBQHZ2Nl27dqW+vr5dmYDMzEz69evHXXfd5Sv75MmT2bx5c8i59fX1rcoEOLsINjc3c+PGDbp37x4w1bw9swP8MwGgQUDCOGvZB89fLy8vZ+TIkdodkACFhYWIiP6vlUoCGRkZvpT8hg0bqKysDLh/0aJFrFy5EmMMK1eu5MEHH4z63K2oqMDj8fhG9IfrDvAPAsD6QLBv3752BQEA77//PuV+yzXOnj2b559/PmB2wvXr1zl69GjA34/EyQScO3eOXr16ISK+TEBzczM1NTUMHjy41eXzDwIOHDgQEATceeedIf/7eEiqZYOTgfPADM4EiAirVq1q1QNDtc/AgQMZN24cHo/H7aIkxMsvu10ClWip1uZ5eXl8+umnvPHGG+zevTvgvokTJ5KRkcHWrVt5+eWXW1yJr7S0lNWrV5OZab395Ofns3//ft/9t27dCtgVEKzBgfv27YvpU3U4wXPs586dywsvvMCaNWuYM2cOYAUgBQUFZGVltXi94CAAPt8OfePGjZSWlra4DoM/Jwi4efMmn3zySUDA8vDDHbNivmYCghQXF5Ofnx923umECRNaXDxCtV/Pnj358MMP3S5GwiTD6nEqsVKtzfPz83nllVcYP358yM6fIsKiRYt49NFHyc3NZfz48VGv5fF4mD17tu/n4EzAqVOnuO222wJmAcQrExAsIyODn/zkJyxbtsy3nHFrBwXC50GA/1iJoqIijh07xhtvvBHzG7cTBFRXV1NYWJiQ7mcNAoIMHDiQffv2tbhIhFLxog+19JNqbZ6Xl8fq1atZuHBh2PsXLFhAQ0MDS5YsifnawQMDg7MAYGUC9u/fH/cgAKCyspLx48fz4osvAq0fFAiRMwENDQ2sW7eOmTNnxlQWJwjYvn07X/ziF2OrSBtpEBCG9kUrpdTn8vLyyM7OZsaMGWHv79u3L+vXr2fu3LlturZ/EBA8HgCsIODKlSvtmh0QzbJly1ixYgXGmDYHAf6ZgHfeeYdRo0aFZE1a4gQB27ZtY+LEiTHXoy00CFBKKRXVgAEDmDNnDt26dYt4zrRp09q0z31wd0C4IMCZKdQRmQCwpi1mZmayffv2mLoDsrKyuHXrFo2NjQGZgGvXrrWpD99ZLCiRQYAODFTKZfff73YJVKKlWps/8cQTIev7x0tubi5nz57l5s2beDyeiJkA/+/xJiLMnz+f1atXx5QJEBG8Xi9HjhwJCAK6dOkSc1cAWEHAZ599xpkzZ1rc4jleNBOglMveesvtEqhES7U2z8zMbNVo+bZe+/bbb/ctPxwtCOio7gCwZgqsWbPGt1xva3m9Xg4fPuzrDsjOzqa+vr5V2xuHu1ZTUxPl5eW+2RMdTYMApVz29a+7XQKVaNrmgfzHBfivFuhwugM6crT8kCFDGDFiBB6PJ6a/4wQBTiYAaNWuhuFkZ2cjIgkbFAgaBCjlurffdrsEKtG0zQP179+f2tpawMoEhJsd0Lt37w7/dDx//vyYsgBASHdAe4gIPXr0SNh4AEiSIEBE+ojIRhGpsb+HDM8XkbEi8oGIVIvILhGZ5UZZlVJKxdf06dNZu3Ytzc3NnDlzJmQ9lqFDh7J06dIOL8fChQt59dVXY/odr9fL0aNH4zarrLS0lEmTJsXlWq2RFEEAsAx41xhzB/Cu/XOwK8A3jTGjgXuAF0Sk/aGXUkopV82aNYu3336b/fv3U1hYGLJaqNfr5emnn+7wcmRnZ8c8IM/r9XLjxo24ZAIAPvjgAwoKCuJyrdZIliBgOrDKvr0KCJmMaow5YIypsW8fAxqBjhslolSCGON2CVSiaZsHys/PZ9KkSbz44osptzS7s+V8vIKAREuWIKCvMeY4gP096gLRIjIByALqIty/WESqRKQqeHcqpZLNihVul0AlmrZ5qHnz5qXk/ixerxdI3UXmEhYEiMhfRGRPmK/pMV6nAHgN+JYxJuzEVWPMCmNMhTGmoiOnlCgVD4895nYJVKJpm4eaMWMGWVlZbZpa5yYnCOjZs6fLJWmbhC0WZIy5O9J9InJSRAqMMcftN/nGCOfdDqwHnjLGbOugoiqllEqwHj16MHfuXEaPHu12UWLi9Xrp3r17h62j0NGSZcXAdcAjwI/s738MPkFEsoDfA78yxvw2scVTSinV0VakYD+J1+tN2a4ASJ4xAT8CvioiNcBX7Z8RkQoR+YV9zjeAKcBCEdlhf411p7hKxc+6dW6XQCWatnnn4fV6U3ZQICRJJsAYcxr4SpjjVcCj9u3VwOoEF02pDjdunNslUImmbd55aCZAKdUuMe42qjoBbfPOo0+fPuTnR53QltSSIhOglFJKpaJ7772XyspKt4vRZpoJUEoppdrI4/Gk7PRA0CBAKdd95ztul0Almra5ShYaBCjlshScFaXaSdtcJQsNApRymY4UTz/a5ipZaBCglMs+/tjtEqhE0zZXyUKDAKWUUipNaRCglMsSuHW4ShLa5ipZaBCglMuOHXO7BCrRtM1VstAgQCmXPfOM2yVQiaZtrpKFGGPcLkOHqqioMFVVVW4XQ6mIRKCTPw1VEG1z1dFE5CNjTEVL52kmQCmllEpTGgQopZRSaUqDAKVcpr1V6UfbXCULDQKUUkqpNKVBgFIuq2hx6I7qbLTNVbLQIEAppZRKUxoEKKWUUmmq068TICKngE9bOC0X+CwBxUk0rVdq0XqlFq1X6uiMdYLo9RpkjMlr6QKdPghoDRGpas2iCqlG65VatF6pReuVOjpjnSA+9dLuAKWUUipNaRCglFJKpSkNAiwr3C5AB9F6pRatV2rReqWOzlgniEO9dEyAUkoplaY0E6CUUkqlKQ0ClFJKqTSV9kGAiNwjIvtFpFZElrldnrYQkQEisklE9opItYg8bh9/RkSOisgO++trbpc1ViJyUER22+Wvso/1EZGNIlJjf+/tdjljISIj/Npkh4hcEJGlqdheIrJSRBpFZI/fsbDtI5b/tJ9ru0Sk3L2SRxehXs+JyD677L8XkV728WIRafJrt5+7V/LoItQr4uNORJ6022u/iPyNO6VuWYR6rfGr00ER2WEfT6X2ivTaHr/nmDEmbb8AD1AHDAGygJ1AidvlakM9CoBy+/ZtwAGgBHgG+Du3y9fOuh0EcoOOPQsss28vA5a7Xc521M8DnAAGpWJ7AVOAcmBPS+0DfA34EyDARGC72+WPsV7TgEz79nK/ehX7n5fMXxHqFfZxZ7+G7AS6AoPt10qP23Vobb2C7n8eeDoF2yvSa3vcnmPpngmYANQaY+qNMdeA3wDTXS5TzIwxx40xH9u3LwJ7gSJ3S9WhpgOr7NurgBkulqW9vgLUGWNaWtUyKRljtgBngg5Hap/pwK+MZRtrx0JgAAAGNklEQVTQS0QKElPS2ISrlzHmz8aYG/aP24D+CS9YO0Vor0imA78xxjQbYxqAWqzXzKQTrV4iIsA3gP9OaKHiIMpre9yeY+keBBQBh/1+PkKKv3mKSDHwBWC7fej7dlpoZaqlzW0G+LOIfCQii+1jfY0xx8F6kgD5rpWu/WYT+OKU6u0FkdunMz3fvo31icsxWEQ+EZHNIlLpVqHaIdzjrrO0VyVw0hhT43cs5dor6LU9bs+xdA8CJMyxlJ0zKSJe4E1gqTHmAvBfwFBgLHAcKyWWaiYbY8qBe4HvicgUtwsULyKSBTwA/NY+1BnaK5pO8XwTkR8CN4Bf24eOAwONMV8AngBeF5Hb3SpfG0R63HWK9gLmEBhop1x7hXltj3hqmGNR2yzdg4AjwAC/n/sDx1wqS7uISBesB8mvjTG/AzDGnDTG3DTG3AJeIUlTedEYY47Z3xuB32PV4aST4rK/N7pXwna5F/jYGHMSOkd72SK1T8o/30TkEeB+YJ6xO2HtdPlp+/ZHWH3nw90rZWyiPO46Q3tlAg8Ba5xjqdZe4V7bieNzLN2DgA+BO0RksP2pbDawzuUyxczu8/olsNcY8x9+x/37gh4E9gT/bjITkR4icptzG2tg1h6sNnrEPu0R4I/ulLDdAj6hpHp7+YnUPuuAb9ojmCcC552UZioQkXuAfwAeMMZc8TueJyIe+/YQ4A6g3p1Sxi7K424dMFtEuorIYKx6/W+iy9dOdwP7jDFHnAOp1F6RXtuJ53PM7dGPbn9hjaY8gBUN/tDt8rSxDndipXx2ATvsr68BrwG77ePrgAK3yxpjvYZgjU7eCVQ77QPkAO8CNfb3Pm6XtQ116w6cBnr6HUu59sIKYo4D17E+hSyK1D5Yqcqf2s+13UCF2+WPsV61WP2tznPs5/a5M+3H507gY+Drbpc/xnpFfNwBP7Tbaz9wr9vlj6Ve9vFXgb8NOjeV2ivSa3vcnmO6bLBSSimVptK9O0AppZRKWxoEKKWUUmlKgwCllFIqTWkQoJRSSqUpDQKUUkqpNKVBgFJKKZWmNAhQKo2JyKsiYuyv6/Z2rJtE5Hv2SmWxXOsu+zq5cS5jhljbLQ+3f67pTMtHK+UmDQKUUn/B2rK0GGtVxreAfwa22is1uq0UaDbGHBCRfGAg1mqfSql20iBAKdVsjDlhjDlqjNlhrOVJ78Lan/3vnZNEZL6IfCgiF+2MwW9FpMi+rxjYZJ96ys4IvGrfd4+IbBWRsyJyRkTeEZFRMZRvEvCefbsS+MQY09SO+iqlbLpioFJpzH6jzjXG3B/mvnXAEGNMqf3zt4ETwD4gF1gOeIwxU+y12KdjbXQyGmtv9yZjzHkRmWlfchfQDXgKK8AoMcZci1K2c/bNbKzlUJuAroAHuAL8NVy5lVKtl+l2AZRSSev/sDZgAcAYs9LvvnoRWQLsFZH+xpgjInLGvq/RGPOZ3++96X9REfkWcAFrt7q/Rvn7Y7He/D8C5mIFH38GngHeB662sV5KKZt2ByilIhH89iIXkXIR+aOIfCoiF4Eq+66BUS8iMlREXheROhG5AJzEeu2J+nvGmINAHnDFGPM/WJvDFAJvGmMOGmNOtLViSimLZgKUUpGUYG+xag8QfAdrEOECrP3Lc4GtQFYL13kLOAo8Zn+/gZVliPh7IvInrP7/TCBTRC5hdQN0BU6LCMYYb5trppQCNAhQSoUhIqXAPcC/2YdGYr3p/8AY02Cf81DQrzn9+x6/6+QAo4DvGWM22cfKafm151Gs8QOrgN9h7Zf+Y6wugV+0rVZKqWAaBCiluopIP6wUfR7wFeAHWH3xP7bPOQQ0A98XkZ9ivbH/a9B1PsXqPrhPRN7CGsh3FvgM+I6IHAaKgOewsgERGWOOikgmMAaYb4xpEJExwHJjTG17K6yUsuiYAKXU3cBxrDf6d4EHsNYJmGKMuQxgjDkFPALMwErl/xPwhP9FjDFH7eP/jtXv/5Ix5hYwC+vNfA/wU+AfsQKKllQA5+wAoD/Ql8/HISil4kCnCCqllFJpSjMBSimlVJrSIEAppZRKUxoEKKWUUmlKgwCllFIqTWkQoJRSSqUpDQKUUkqpNKVBgFJKKZWmNAhQSiml0tT/A8eOdU9uMrFyAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 74, 132]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[11:41] = np.ones(30)*.3\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, .4, n_above=10, n_below=1, show=True)  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["For this situation we can use the second threshold parameter, `threshold2` with corresponding `n_above2` parameter:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 56, 142]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[11:41] = np.ones(30)*.3\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, .1, n_above=10, n_below=1,\n", "                     threshold2=.4, n_above2=5, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Performance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The performance of the `detect_onset` function varies with the data and parameters.  \n", "Here is a simple test of the `detect_onset.py` performance:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Detection of onset (data size= 10000):\n", "472 µs ± 2.99 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["x = np.random.randn(10000)\n", "print('Detection of onset (data size= %d):' %x.size)\n", "%timeit detect_onset(x, threshold=0, n_above=10, n_below=1, threshold2=.5, n_above2=5, show=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"state": {}, "version": "1.1.2"}}, "nbformat": 4, "nbformat_minor": 4}