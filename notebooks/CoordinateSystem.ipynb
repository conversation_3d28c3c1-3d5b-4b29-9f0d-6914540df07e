{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Determination of a coordinate system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In mechanics, a [frame of reference](http://en.wikipedia.org/wiki/Frame_of_reference) is the place with respect to we choose to describe the motion of a body. In this reference frame, we define a [coordinate system](http://en.wikipedia.org/wiki/Coordinate_system) (a set of axes) within which we measure the motion of a body (but frame of reference and coordinate system are often used interchangeably).  \n", "\n", "In biomechanics, we may use different coordinate systems for convenience and refer to them as global, laboratory, local, anatomical, or technical reference frames or coordinate systems. For example, in a standard gait analysis, we define a global or laboratory coordinate system and a different coordinate system for each segment of the body to be able to describe the motion of a segment in relation to anatomical axes of another segment. To define this anatomical coordinate system, we need to place markers on anatomical landmarks on each segment. We also may use other markers (technical markers) on the segment to improove the motion analysis and then we will also have to define a technical coordinate system for each segment.\n", "\n", "As we perceive the surrounding space as three-dimensional, a convenient coordinate system to use is the [Cartesian coordinate system](http://en.wikipedia.org/wiki/Cartesian_coordinate_system) with three ortogonal axes in the [Euclidean space](http://en.wikipedia.org/wiki/Euclidean_space). From [linear algebra](http://en.wikipedia.org/wiki/Linear_algebra), a set of unit linearly independent vectors (orthogonal in the Euclidean space and each with norm (length) equals to one) that can represent any vector via [linear combination](http://en.wikipedia.org/wiki/Linear_combination) is called a <a href=\"http://en.wikipedia.org/wiki/Basis_(linear_algebra)\">basis</a> (or orthonormal basis). The figure below shows a point and its position vector in the Cartesian coordinate system and the corresponding versors (unit vectors) of the basis for this coordinate system. See the notebook [Scalar and vector](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ScalarVector.ipynb) for a description on vectors.\n", "\n", "<div class='center-align'><figure><img src=\"./../images/vector3Dijk.png\" width=300/><figcaption><i>Representation of a point **P** and its position vector **a** in a Cartesian coordinate system. The versors <b>i, j, k</b> form a basis for this coordinate system and are usually represented in the color sequence RGB (red, green, blue) for easier visualization.</i></figcaption></figure></div> \n", "\n", "One can see that the versors of the basis shown in the figure above have the following coordinates in the Cartesian coordinate system:\n", "\n", "$$ \\mathbf{i}= \\begin{bmatrix}1\\\\0\\\\0 \\end{bmatrix}, \\quad \\mathbf{j}=\\begin{bmatrix}0\\\\1\\\\0 \\end{bmatrix}, \\quad \\mathbf{k}=\\begin{bmatrix} 0 \\\\ 0 \\\\ 1 \\end{bmatrix}$$\n", "\n", "Using the notation described in the figure above the position vector $\\mathbf{a}$ can be expressed as:\n", "\n", "$$ \\mathbf{a} = a_x \\mathbf{i} + a_y \\mathbf{j} + a_z \\mathbf{k} $$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Definition of a basis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The mathematical problem of determination of a coordinate system is to find a basis and an origin for it (a bais is only a set of vectors, with no origin). In the three-dimensional space, given the coordinates of three noncollinear points (points that do not all lie on the same line), **m1**, **m2**, **m3**, which would represent the positions of markers captured from a motion analysis session, a basis can be found following these steps:\n", "\n", "1. First axis, **v1**, the vector **m2-m1** (or any other vector difference);   \n", "2. Second axis, **v2**, the cross or vector product between the vectors **v1** and **m3-m1** (or **m3-m2**);   \n", "3. Third axis, **v3**, the cross product between the vectors **v1** and **v2**.  \n", "4. Make all vectors to have norm 1 dividing each vector by its norm.\n", "\n", "The positions of the points used to construct a coordinate system have, by definition, to be specified in relation to an already existing coordinate system. In motion analysis, this coordinate system is the coordinate system from the motion capture system and it is established in the calibration phase. In this phase, the positions of markers placed on an object with perpendicular axes and known distances between the markers are captured and used as the reference (laboratory) coordinate system.\n", "\n", "For example, given the positions m1 = [1,2,0], m2 = [0,1,3], m3 = [1,0,1], a basis can be found:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"code_folding": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors: \n", "e1 = [-0.30151134 -0.30151134  0.90453403] \n", "e2 = [ 0.91287093  0.18257419  0.36514837] \n", "e3 = [-0.27524094  0.9358192   0.22019275]\n", "\n", "Test of orthogonality (cross product between versors): \n", "e1 x e2: 1.0 \n", "e1 x e3: 1.0 \n", "e2 x e3: 1.0\n", "\n", "Norm of each versor: \n", "||e1|| = 1.0 \n", "||e2|| = 1.0 \n", "||e3|| = 1.0\n"]}], "source": ["# Import the necessary libraries\n", "from __future__ import division, print_function  # version compatibility\n", "import numpy as np\n", "\n", "m1 = np.array([1, 2, 0])\n", "m2 = np.array([0, 1, 3])\n", "m3 = np.array([1, 0, 1])\n", "\n", "v1 = m2 - m1                # first axis\n", "v2 = np.cross(v1, m3 - m1)  # second axis\n", "v3 = np.cross(v1, v2)       # third axis\n", "# Vector normalization\n", "e1 = v1/np.linalg.norm(v1)\n", "e2 = v2/np.linalg.norm(v2)\n", "e3 = v3/np.linalg.norm(v3)\n", "\n", "print('Versors:', '\\ne1 =', e1, '\\ne2 =', e2, '\\ne3 =', e3)\n", "print('\\nTest of orthogonality (cross product between versors):',\n", "      '\\ne1 x e2:', np.linalg.norm(np.cross(e1, e2)),\n", "      '\\ne1 x e3:', np.linalg.norm(np.cross(e1, e3)),\n", "      '\\ne2 x e3:', np.linalg.norm(np.cross(e2, e3)))\n", "print('\\nNorm of each versor:',\n", "      '\\n||e1|| =', np.linalg.norm(e1),\n", "      '\\n||e2|| =', np.linalg.norm(e2),\n", "      '\\n||e3|| =', np.linalg.norm(e3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To define a coordinate system using the calculated base, we also need to define an origin. In principle, we could use any point as origin, but if the calculated coordinate system should follow anatomical conventions, e.g., the coordinate system origin should be at a joint center, we will have to calculate the basis and origin according to [standards used in motion analysis](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/Frame%20of%20reference.ipynb).   \n", "\n", "If the coordinate system is a technical basis and not anatomic-based, a common procedure in motion analysis is to define the origin for the coordinate system as the centroid (average) position among the markers at the reference frame. Using the average position across markers pottentially reduces the effect of noise (for example, from soft tissue artifact) on the calculation.  \n", "\n", "For the markers in the example above, the origin of the coordinate system will be:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Origin:  [ 0.66666667  1.          1.33333333]\n"]}], "source": ["origin = np.mean((m1, m2, m3), axis=0)\n", "print('Origin: ', origin)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's plot the coordinate system and the basis using the custom Python function `CCS.py`:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath\n", "from CCS import CCS"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["markers = np.vstack((m1, m2, m3))\n", "basis = np.vstack((e1, e2, e3))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "#%matplotlib qt"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXYAAAEuCAYAAACagzn/AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\niMX8H79ojNzYwM+FAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7209da0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["markers = np.vstack((m1, m2, m3))\n", "basis = np.vstack((e1, e2, e3))\n", "CCS(xyz='auto', Oijk=origin, ijk=basis, point=markers, vector=False);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> process"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another equivalent form to calculate a basis based on vectors is knwon as the [<PERSON><PERSON> process](http://en.wikipedia.org/wiki/Gram%E2%80%93Schmidt_process). This algorithm is not difficult to implement but we will not see it here because for an anatomical-based coordinate system we will have to explicitly handle the vectors in order to obtain the basis in the desired directions. In addition, for a technical basis it's typical to use more than three markers on the segment to pottentially reduce the effect of the soft tissue artifact and in this case it's used yet another procedure to calculate a basis: the [singular value decomposition (SVD) algorithm](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/SVDalgorithm.ipynb)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Problems"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. Given the points in the 3D space, m1 = [2,2,0], m2 = [0,1,1], m3 = [1,2,0], find an orthonormal basis.\n", "\n", "2. Determine if the following points forma a bais in the 3d space, m1 = [2,2,0], m2 = [1,1,1], m3 = [1,1,0]."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- [Standards - International Society of Biomechanics](http://isbweb.org/information-services/standards)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["> This page was written in the [IPython Notebook](http://ipython.org/notebook.html).  \n", "To download the notebook click on this option at the top menu or get it from the [github repo](https://github.com/demotu/BMC)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 1}