{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multibody dynamics of one and two-link systems\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The human body is composed of multiple interconnected segments (which can be modeled as rigid or flexible) and each segment may have translational and rotational movement. The part of mechanics for the study of movement and forces of interconnected bodies is called [multibody system](http://en.wikipedia.org/wiki/Multibody_system) or multibody dynamics. \n", "\n", "There are different approaches to deduce the kinematics and dynamics of such bodies, the most common are the [Newton-Euler](http://en.wikipedia.org/wiki/Newton%E2%80%93Euler_equations) and the [Langrangian](http://en.wikipedia.org/wiki/Lagrangian_mechanics) formalisms. The Newton-Euler formalism is based on the well known Newton-Euler equations. The Langrangian formalism uses the [principle of least action](http://en.wikipedia.org/wiki/Principle_of_least_action) and describes the movement based on [generalized coordinates](http://en.wikipedia.org/wiki/Generalized_coordinates), a set of parameters (typically, a convenient minimal set) to describe the configuration of the system taking into account its constraints. For a system with multiple bodies and several constraints, e.g., the human body, it is easier to describe the dynamics of such system using the Langrangian formalism. \n", " \n", "<PERSON><PERSON><PERSON> and <PERSON> (1989) and <PERSON><PERSON><PERSON> (1993) offer excellent discussions about applying multibody system concepts to understanding human body movement.\n", "\n", "Next, we will study two simple problems of multibody systems in the context of biomechanics which we can handle well using the Newton-Euler approach.  \n", "First a planar one-link system (which is not a multibody), which can represent the movement of one limb of the body or the entire body as a single inverted pendulum.  \n", "Second, a planar two-link system, which can represent the movement of two segments of the body, e.g., upper arm and forearm. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Newton-Euler equations\n", "\n", "For a two-dimensional movement in the $XY$ plane, the Newton-Euler equations are:  \n", "\n", "\\begin{align}\n", "\\left\\{ \\begin{array}{l l}\n", "\\sum F_X &=& m \\ddot{x}_{cm} \\\\\n", "\\\\\n", "\\sum F_Y &=& m \\ddot{y}_{cm} \\\\\n", "\\\\\n", "\\sum M_Z &=& I_{cm} \\ddot{\\alpha}_Z\n", "\\end{array} \\right.\n", "\\label{}\n", "\\end{align}\n", "\n", "Where the movement is described around the body center of mass ($cm$).  \n", "$(F_X,\\,F_Y)$ and $M_Z$ are, respectively, the forces and moment of forces (torques) acting on the body.  \n", "$(\\ddot{x}_{cm},\\,\\ddot{y}_{cm})$ and $\\ddot{\\alpha}_Z$ are, respectively, the linear and angular accelerations.  \n", "$I_{cm}$ is the body moment of inertia around the body center of mass at the $Z$ axis.  \n", "\n", "Let's use Sympy to derive some of the characteristics of the systems."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:31:37.361418Z", "start_time": "2018-08-14T05:31:36.599829Z"}}, "outputs": [], "source": ["import sympy as sym\n", "from sympy import Symbol, symbols, cos, sin, Matrix, simplify\n", "from sympy.vector import CoordSys3D\n", "from sympy.physics.mechanics import dynamicsymbols, mlatex, init_vprinting\n", "init_vprinting()\n", "from IPython.display import display, Math\n", "\n", "eq = lambda lhs, rhs: display(Math(lhs + '=' + mlatex(rhs)))\n", "eq = lambda lhs, rhs: display(Math(r'\\begin{array}{l l}' + lhs +\n", "                                   '&=&' + mlatex(rhs) + r'\\end{array}'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## One-link system\n", "\n", "Let's study the dynamics of a planar inverted pendulum as a model for the movement of a human body segment with an external force acting on the segment (see Figure 1). \n", "\n", "<figure><img src=\"./../images/invpend1.png\" alt=\"Inverted pendulum\"/><figcaption><i><center>Figure 1. Planar inverted pendulum (one link attached to a fixed body by a hinge joint in a plane) with a joint actuators and corresponding free body diagram as a model of a human body segment. See text for notation convention.</center></i></figcaption> \n", "\n", "The following notation convention will be used for this problem:  \n", "\n", " - $L$ is the length of the segment.  \n", " - $d$ is the distance from the joint of the segment to its center of mass position.  \n", " - $m$ is the mass of the segment. \n", " - $g$ is the gravitational acceleration (+).   \n", " - $\\alpha$ is the angular position of the joint w.r.t. horizontal, $\\ddot{\\alpha_i}$ is the corresponding angular acceleration.  \n", " - $I$ is the moment of inertia of the segment around its center of mass position.  \n", " - $F_{r}$ is the joint reaction force.  \n", " - $F_{e}$ is the external force acting on the segment.\n", " - $T$ is the joint moment of force (torque). \n", " \n", "In the case of a human body segment, muscles responsible for the movement of the segment are represented as a single pair of antagonistic joint actuators (e.g., flexors and extensors). We will consider that all joint torques are generated only by these muscles (we will disregard the torques generated by ligaments and other tissues) and the total or net joint torque will be the sum of the torques generated by the two muscles:\n", "\n", "\\begin{equation}\n", "T \\quad=\\quad T_{net} \\quad=\\quad T_{extension} - T_{flexion}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where we considered the extensor torque as positive (counter-clockwise). In what follows, we will determine only the net torque and we will be unable to decompose the net torque in its components."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Kinetics\n", " \n", "From the free body diagram, the Newton-<PERSON><PERSON><PERSON> equations for the planar inverted pendulum are (writing the equation for the torques around the center of mass):\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "F_{r,x} + F_{e,x} & = & m\\ddot{x} \\\\\n", "\\\\\n", "F_{r,y} - mg + F_{e,y} & = & m\\ddot{y} \\\\\n", "\\\\\n", "T + dF_{r,x}\\sin\\alpha - dF_{r,y}\\cos\\alpha - (L-d)F_{e,x}\\sin\\alpha + (L-d)F_{e,y}\\cos\\alpha & = & I\\ddot{\\alpha}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "However, manually placing the terms in the Newton-<PERSON><PERSON> equations as we did above where we calculated the signs of the cross products is error prone. We can avoid this manual placing by treating the quantities as vectors and express them in matricial form:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\vec{\\mathbf{F}}_r + \\vec{\\mathbf{F}}_g + \\vec{\\mathbf{F}}_e &=& m\\ddot{\\vec{\\mathbf{r}}} \\\\\n", "\\\\\n", "\\vec{\\mathbf{T}} + \\vec{\\mathbf{r}}_{cm,j} \\times \\vec{\\mathbf{F}}_r + \\vec{\\mathbf{r}}_{cm,e} \\times \\vec{\\mathbf{F}}_e &=& I\\ddot{\\vec{\\mathbf{\\alpha}}}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where:\n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\begin{bmatrix} F_{rx} \\\\ F_{ry} \\\\ 0 \\end{bmatrix}  + \\begin{bmatrix} 0 \\\\ -g \\\\ 0 \\end{bmatrix}  + \\begin{bmatrix} F_{ex} \\\\ F_{ey} \\\\ 0 \\end{bmatrix} &=& m\\begin{bmatrix} \\ddot{x} \\\\ \\ddot{y} \\\\ 0 \\end{bmatrix} , \\quad \\begin{bmatrix} \\hat{i} \\\\ \\hat{j} \\\\ \\hat{k} \\end{bmatrix}\n", "\\\\\n", "\\begin{bmatrix} 0 \\\\ 0 \\\\ T_z \\end{bmatrix}  + \\begin{bmatrix} -d\\cos\\alpha \\\\ -d\\sin\\alpha \\\\ 0 \\end{bmatrix}  \\times \\begin{bmatrix} F_{rx} \\\\ F_{ry} \\\\ 0 \\end{bmatrix} + \\begin{bmatrix} (L-d)\\cos\\alpha \\\\ (L-d)\\sin\\alpha \\\\ 0 \\end{bmatrix}  \\times \\begin{bmatrix} F_{ex} \\\\ F_{ey} \\\\ 0 \\end{bmatrix} &=& I_z\\begin{bmatrix} 0 \\\\ 0 \\\\ \\ddot{\\alpha} \\end{bmatrix} , \\quad \\begin{bmatrix} \\hat{i} \\\\ \\hat{j} \\\\ \\hat{k} \\end{bmatrix}\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Note that $\\times$ represents the cross product, not matrix multiplication. Then, both in symbolic or numeric manipulation we would use the cross product function to perform part of the calculations.  \n", "There are different computational tools that can be used for the formulation of the equations of motion. For instance, Sympy has a module, [Classical Mechanics](http://docs.sympy.org/dev/modules/physics/mechanics/), and see [this list](http://real.uwaterloo.ca/~mbody/#Software) for other software.  \n", "Let's continue with the explicit manual formulation of the equations for now.  \n", "\n", "We can rewrite the equation for the moments of force in a form that doesn't explicitly involve the joint reaction force expressing the moments of force around the joint center:\n", "\n", "\\begin{equation}\n", "T - mgd\\cos\\alpha - LF_{e,x}\\sin\\alpha + LF_{e,y}\\cos\\alpha \\quad=\\quad I_o\\ddot{\\alpha}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $I_o$ is the moment of inertia around the joint, $I_o=I_{cm}+md^2$, using the parallel axis theorem.  \n", "\n", "The torque due to the joint reaction force does not appear on this equation; this torque is null because by the definition the reaction force acts on the joint. If we want to determine the joint torque and we know the kinematics, we perform inverse dynamics:\n", "\n", "\\begin{equation}\n", "T \\quad=\\quad I_o\\ddot{\\alpha} + mgd \\cos \\alpha + LF_{e,x}\\sin\\alpha - LF_{e,y}\\cos\\alpha\n", "\\label{}\n", "\\end{equation}\n", "\n", "If we want to determine the kinematics and we know the joint torque, we perform direct dynamics:\n", "\n", "\\begin{equation}\n", "\\ddot{\\alpha} \\quad=\\quad I_o^{-1}[T - mgd \\cos \\alpha - LF_{e,x}\\sin\\alpha + LF_{e,y}\\cos\\alpha ]\n", "\\label{}\n", "\\end{equation}\n", "\n", "The expression above is a second-order differential equation which typically is solved numerically. So, unless we are explicitly interested in estimating the joint reaction forces, we don't need to use them for calculating the joint torque or simulate movement. Anyway, let's look at the kinematics of this problem to introduce some important concepts which will be needed later."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Kinematics\n", "\n", "A single planar inverted pendulum has one degree of freedom, the rotation movement of the segment around the pin joint. In this case, if the angular position $\\alpha(t)$ is known, the coordinates $x(t)$ and $y(t)$ of the center of mass and their derivatives can be readily determined (a process referred as [forward kinematics)](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/KinematicChain.ipynb#Forward-and-inverse-kinematics):"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:35:47.186478Z", "start_time": "2018-08-14T05:35:47.179988Z"}}, "outputs": [], "source": ["t = Symbol('t')\n", "d, L = symbols('d L', positive=True)\n", "a = dynamicsymbols('alpha')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:35:48.170520Z", "start_time": "2018-08-14T05:35:48.140322Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}x&=&d \\operatorname{cos}\\left(\\alpha\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{x}&=&- d \\operatorname{sin}\\left(\\alpha\\right) \\dot{\\alpha}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{x}&=&- d \\operatorname{sin}\\left(\\alpha\\right) \\ddot{\\alpha} - d \\operatorname{cos}\\left(\\alpha\\right) \\dot{\\alpha}^{2}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}y&=&d \\operatorname{sin}\\left(\\alpha\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{y}&=&d \\operatorname{cos}\\left(\\alpha\\right) \\dot{\\alpha}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{y}&=&- d \\operatorname{sin}\\left(\\alpha\\right) \\dot{\\alpha}^{2} + d \\operatorname{cos}\\left(\\alpha\\right) \\ddot{\\alpha}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x, y = d*cos(a), d*sin(a)\n", "xd, yd = x.diff(t), y.diff(t)\n", "xdd, ydd = xd.diff(t), yd.diff(t)\n", "\n", "eq(r'x', x)\n", "eq(r'\\dot{x}', xd)\n", "eq(r'\\ddot{x}', xdd)\n", "eq(r'y', y)\n", "eq(r'\\dot{y}', yd)\n", "eq(r'\\ddot{y}', ydd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The terms in $\\ddot{x}$ and $\\ddot{y}$ proportional to $\\dot{\\alpha}^2$ are components of the centripetal acceleration on the body. As the name suggests, the [centripetal](http://en.wikipedia.org/wiki/Centripetal_force) acceleration is always directed to the center (towards the joint) when the segment is rotating. See the notebook [Kinematic chain](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/KinematicChain.ipynb) for more on that."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We could also use the methods of the Sympy physics/mechanics module and explicitly create a coordinate system in 3-D space, which will employ the versors $\\hat{\\mathbf{i}}, \\hat{\\mathbf{j}}, \\hat{\\mathbf{k}}$ for representing the vector components:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:23.857663Z", "start_time": "2018-08-14T05:36:23.832166Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}\\vec{\\mathbf{r}}&=&(d \\cos{\\left (\\alpha{\\left (t \\right )} \\right )})\\mathbf{\\hat{i}_{}} + (d \\sin{\\left (\\alpha{\\left (t \\right )} \\right )})\\mathbf{\\hat{j}_{}}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{\\vec{\\mathbf{r}}}&=&(- d \\sin{\\left (\\alpha{\\left (t \\right )} \\right )} \\frac{d}{d t} \\alpha{\\left (t \\right )})\\mathbf{\\hat{i}_{}} + (d \\cos{\\left (\\alpha{\\left (t \\right )} \\right )} \\frac{d}{d t} \\alpha{\\left (t \\right )})\\mathbf{\\hat{j}_{}}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{\\vec{\\mathbf{r}}}&=&(- d \\left(\\sin{\\left (\\alpha{\\left (t \\right )} \\right )} \\frac{d^{2}}{d t^{2}} \\alpha{\\left (t \\right )} + \\cos{\\left (\\alpha{\\left (t \\right )} \\right )} \\left(\\frac{d}{d t} \\alpha{\\left (t \\right )}\\right)^{2}\\right))\\mathbf{\\hat{i}_{}} + (- d \\left(\\sin{\\left (\\alpha{\\left (t \\right )} \\right )} \\left(\\frac{d}{d t} \\alpha{\\left (t \\right )}\\right)^{2} - \\cos{\\left (\\alpha{\\left (t \\right )} \\right )} \\frac{d^{2}}{d t^{2}} \\alpha{\\left (t \\right )}\\right))\\mathbf{\\hat{j}_{}}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["G = CoordSys3D('')\n", "r = d*cos(a)*G.i + d*sin(a)*G.j + 0*G.k\n", "rd = r.diff(t)\n", "rdd = r.diff(t, 2)\n", "\n", "eq(r'\\vec{\\mathbf{r}}', r)\n", "eq(r'\\dot{\\vec{\\mathbf{r}}}', rd)\n", "eq(r'\\ddot{\\vec{\\mathbf{r}}}', rdd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But for now, let's continue writing the components ourselves."]}, {"cell_type": "markdown", "metadata": {}, "source": ["As an exercise, let's go back to the Newton-<PERSON><PERSON> equation for the sum of torques around the center of mass where the torques due to the joint reaction forces are explicit.  \n", "From the equation for the the sum of forces, hence we have expressions for the linear accelerations, we can isolate the reaction forces and substitute them on the equation for the torques. With a little help from Sympy:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:34.811901Z", "start_time": "2018-08-14T05:36:34.804474Z"}}, "outputs": [], "source": ["m, I, g = symbols('m I g', positive=True)\n", "Fex, Fey = symbols('F_ex F_ey')\n", "add = a.diff(t, 2)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:35.647801Z", "start_time": "2018-08-14T05:36:35.629885Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}F_{rx}&=&- F_{ex} + m \\left(- d \\operatorname{sin}\\left(\\alpha\\right) \\ddot{\\alpha} - d \\operatorname{cos}\\left(\\alpha\\right) \\dot{\\alpha}^{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}F_{ry}&=&- F_{ey} + g m + m \\left(- d \\operatorname{sin}\\left(\\alpha\\right) \\dot{\\alpha}^{2} + d \\operatorname{cos}\\left(\\alpha\\right) \\ddot{\\alpha}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Frx = m*xdd - Fex\n", "Fry = m*ydd + m*g - <PERSON>y\n", "eq(r'F_{rx}', Frx)\n", "eq(r'F_{ry}', <PERSON>)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:36.939864Z", "start_time": "2018-08-14T05:36:36.921805Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}T&=&F_{ex} \\left(L - d\\right) \\operatorname{sin}\\left(\\alpha\\right) - F_{ey} \\left(L - d\\right) \\operatorname{cos}\\left(\\alpha\\right) + I \\ddot{\\alpha} - d \\left(- F_{ex} + m \\left(- d \\operatorname{sin}\\left(\\alpha\\right) \\ddot{\\alpha} - d \\operatorname{cos}\\left(\\alpha\\right) \\dot{\\alpha}^{2}\\right)\\right) \\operatorname{sin}\\left(\\alpha\\right) + d \\left(- F_{ey} + g m + m \\left(- d \\operatorname{sin}\\left(\\alpha\\right) \\dot{\\alpha}^{2} + d \\operatorname{cos}\\left(\\alpha\\right) \\ddot{\\alpha}\\right)\\right) \\operatorname{cos}\\left(\\alpha\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["T = I*add - d*sin(a)*Frx + d*cos(a)*<PERSON> + (L-d)*sin(a)*Fex - (L-d)*cos(a)*<PERSON><PERSON>\n", "eq(r'T', T)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This equation for the torques around the center of mass of only one rotating segment seems too complicated. The equation we derived before for the torques around the joint was much simpler. However, if we look at the terms on this last equation, we can simplify most of them. Let's use Sympy to simplify this equation:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:40.534400Z", "start_time": "2018-08-14T05:36:40.272557Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}T&=&F_{ex} L \\operatorname{sin}\\left(\\alpha\\right) - F_{ey} L \\operatorname{cos}\\left(\\alpha\\right) + I \\ddot{\\alpha} + d^{2} m \\ddot{\\alpha} + d g m \\operatorname{cos}\\left(\\alpha\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["T = simplify(T)\n", "eq(r'T', T)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we are back to the more simple equation we've seen before. The first two terms on the right side are the torque due to the external force, the third and fourth are the moment of inertia around the joint (use the theorem of parallel axis) times the acceleration, and the last term is the gravitational torque.  \n", "\n", "But what happened with all the other terms in the equation?  \n", "\n", "First, the terms proportional to the angular acceleration were just components from each direction of the 'inertial' torque that when summed resulted in $md^2\\ddot{\\alpha}$. \n", "Second, the terms proportional to $\\dot{\\alpha}^2$ are components of the torque due to the centripetal force (acceleration). But the centripetal force passes through the joint as well as through the center of mass, i.e., it has zero lever arm and this torque should be zero. Indeed, when summed these terms are canceled out.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The Jacobian matrix\n", "\n", "Another way to deduce the velocity and acceleration of a point at the rotating link is to use the [Jacobian matrix](http://en.wikipedia.org/wiki/Jacobian_matrix_and_determinant) (see [Kinematic chain](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/KinematicChain.ipynb)).  \n", "Remember that in the context of kinematic chains, the Jacobian relates changes in the joint space to changes in the Cartesian space. The Jacobian is a matrix of all first-order partial derivatives of the linear position vector of the endpoint with respect to the angular position vector.  \n", "For the center of mass of the planar one-link system, this means that the Jacobian matrix is:\n", "\n", "\\begin{equation}\n", "\\mathbf{J} \\quad=\\quad\n", "\\begin{bmatrix}\n", "\\dfrac{\\partial x}{\\partial \\alpha} \\\\\n", "\\dfrac{\\partial y}{\\partial \\alpha} \\\\\n", "\\end{bmatrix}\n", "\\label{}\n", "\\end{equation}"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:48.832998Z", "start_time": "2018-08-14T05:36:48.825640Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}\\mathbf{J}&=&\\left[\\begin{matrix}- d \\operatorname{sin}\\left(\\alpha\\right)\\\\d \\operatorname{cos}\\left(\\alpha\\right)\\end{matrix}\\right]\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r = Matrix([x, y])\n", "J = r.diff(a)\n", "eq(r'\\mathbf{J}', J)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And <PERSON><PERSON><PERSON> has a function to calculate the Jacobian:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:36:54.333076Z", "start_time": "2018-08-14T05:36:54.324740Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}\\mathbf{J}&=&\\left[\\begin{matrix}- d \\operatorname{sin}\\left(\\alpha\\right)\\\\d \\operatorname{cos}\\left(\\alpha\\right)\\end{matrix}\\right]\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["J = r.jaco<PERSON>([a])\n", "eq(r'\\mathbf{J}', J)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The linear velocity of a point in the link will be given by the product between the Jacobian of the kinematic link and its angular velocity:\n", "\n", "\\begin{equation}\n", "\\vec{\\mathbf{v}} \\quad=\\quad \\mathbf{J} \\dot{\\vec{\\alpha}}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Using Sympy, the linear velocity of the center of mass is:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:32:30.553096Z", "start_time": "2018-08-14T05:32:30.538736Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}\\begin{bmatrix} \\dot{x} \\\\ \\dot{y} \\end{bmatrix}&=&\\left[\\begin{matrix}- d \\operatorname{sin}\\left(\\alpha\\right) \\dot{\\alpha}\\\\d \\operatorname{cos}\\left(\\alpha\\right) \\dot{\\alpha}\\end{matrix}\\right]\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vel = J*a.diff(t)\n", "eq(r'\\begin{bmatrix} \\dot{x} \\\\ \\dot{y} \\end{bmatrix}', vel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And the linear acceleration of a point in the link will be given by the derivative of this last expression:\n", " \n", "\\begin{equation}\n", "\\vec{\\mathbf{a}} \\quad=\\quad \\dot{\\mathbf{J}} \\dot{\\vec{\\alpha}} + \\mathbf{J} \\ddot{\\vec{\\alpha}}\n", "\\label{}\n", "\\end{equation}\n", "\n", "And using Sympy again, the linear acceleration of the center of mass is:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:37:37.820063Z", "start_time": "2018-08-14T05:37:37.802918Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}\\begin{bmatrix} \\ddot{x} \\\\ \\ddot{y} \\end{bmatrix}&=&\\left[\\begin{matrix}- d \\operatorname{sin}\\left(\\alpha\\right) \\ddot{\\alpha} - d \\operatorname{cos}\\left(\\alpha\\right) \\dot{\\alpha}^{2}\\\\- d \\operatorname{sin}\\left(\\alpha\\right) \\dot{\\alpha}^{2} + d \\operatorname{cos}\\left(\\alpha\\right) \\ddot{\\alpha}\\end{matrix}\\right]\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["acc = (J*a.diff(t)).diff(t)\n", "eq(r'\\begin{bmatrix} \\ddot{x} \\\\ \\ddot{y} \\end{bmatrix}', acc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Same expressions as before.\n", "\n", "We can also use the Jacobian matrix to calculate the torque due to a force on the link:\n", "\n", "\\begin{equation}\n", "T \\quad=\\quad \\mathbf{J}^T \\begin{bmatrix} F_{ex} \\\\ F_{ey} \\end{bmatrix}\n", "\\label{}\n", "\\end{equation}"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:37:53.505213Z", "start_time": "2018-08-14T05:37:53.493946Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}T_e&=&- F_{ex} d \\operatorname{sin}\\left(\\alpha\\right) + F_{ey} d \\operatorname{cos}\\left(\\alpha\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Te = J.T*Matrix((<PERSON><PERSON>, <PERSON>))\n", "eq(r'T_e', Te[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Where in this case we considered that the force was applied to the center of mass, just because we already had the Jacobian calculated at that position.\n", "\n", "We could simulate the movement of this one-link system for a typical human movement to understand the magnitude of these physical quantities.  \n", "The reader is invited to that now. We will perform this simulation for a two-link system next"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Two-link system\n", "\n", "Let's study the dynamics of a planar double inverted pendulum (see Figure 2) as a model of two interconnected segments in the human body with an external force acting on the distal segment. Once again, we will consider that there are muscles around each joint and they generate torques.\n", "\n", "<figure><img src=\"./../images/invpend2.png\" alt=\"Double inverted pendulum\"/><figcaption><i><center>Figure 2. Planar double inverted pendulum connected by hinge joints with joint actuators and corresponding free body diagrams. See text for notation convention.</center></i></figcaption> \n", "\n", "The following notation convention will be used for this problem:  \n", " - Subscript $i$ runs 1 or 2 meaning first (most proximal) or second joint when referring to angles, joint moments, or joint reaction forces, or meaning first or second segment when referring to everything else.  \n", " - $L_i$ is the length of segment $i$.  \n", " - $d_i$ is the distance from the proximal joint of segment $i$ to its center of mass position.  \n", " - $m_i$ is the mass of segment $i$. \n", " - $g$ is the gravitational acceleration (+).   \n", " - $\\alpha_i$ is the angular position of joint $i$ in the joint space, $\\ddot{\\alpha_i}$ is the corresponding angular acceleration.\n", " - $\\theta_i$ is the angular position of joint $i$ in the segmental space w.r.t. horizontal, $\\theta_1=\\alpha_1$ and $\\theta_2=\\alpha_1+\\alpha_2$.  \n", " - $I_i$ is the moment of inertia of segment $i$ around its center of mass position.  \n", " - $F_{ri}$ is the reaction force at joint $i$.  \n", " - $F_{e}$ is the external force acting on the distal segment.\n", " - $T_i$ is the moment of force (torque) at joint $i$.  \n", "\n", "Hence we know we will need the linear accelerations for solving the Newton-Euler equations, let's deduce them first."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:37:57.727866Z", "start_time": "2018-08-14T05:37:57.714478Z"}}, "outputs": [], "source": ["t = Symbol('t')\n", "d1, d2, L1, L2 = symbols('d1, d2, L_1 L_2', positive=True)\n", "a1, a2 = dynamicsymbols('alpha1 alpha2')\n", "a1d, a2d = a1.diff(t), a2.diff(t)\n", "a1dd, a2dd = a1.diff(t, 2), a2.diff(t, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Kinematics\n", "\n", "Once again, if the angular positions $\\alpha_1(t)$ and $\\alpha_2(t)$ are known, the coordinates $(x_1(t), y_1(t))$ and $(x_2(t), y_2(t))$ and their derivatives can be readily determined (by forward kinematics):\n", "\n", "#### Link 1"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:00.172316Z", "start_time": "2018-08-14T05:38:00.134899Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}x_1&=&d_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{x_1}&=&- d_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{x_1}&=&- d_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) \\ddot{\\alpha}_{1} - d_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1}^{2}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}y_1&=&d_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{y_1}&=&d_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{y_1}&=&- d_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1}^{2} + d_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) \\ddot{\\alpha}_{1}\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x1, y1 = d1*cos(a1), d1*sin(a1)\n", "x1d, y1d = x1.diff(t), y1.diff(t)\n", "x1dd, y1dd = x1d.diff(t), y1d.diff(t)\n", "\n", "eq(r'x_1', x1)\n", "eq(r'\\dot{x_1}', x1d)\n", "eq(r'\\ddot{x_1}', x1dd)\n", "eq(r'y_1', y1)\n", "eq(r'\\dot{y_1}', y1d)\n", "eq(r'\\ddot{y_1}', y1dd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Link 2"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:02.874967Z", "start_time": "2018-08-14T05:38:02.812444Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}x_2&=&L_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) + d_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{x_2}&=&- L_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1} - d_{2} \\left(\\dot{\\alpha}_{1} + \\dot{\\alpha}_{2}\\right) \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{x_2}&=&- L_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) \\ddot{\\alpha}_{1} - L_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1}^{2} - d_{2} \\left(\\dot{\\alpha}_{1} + \\dot{\\alpha}_{2}\\right)^{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right) - d_{2} \\left(\\ddot{\\alpha}_{1} + \\ddot{\\alpha}_{2}\\right) \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}y_2&=&L_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) + d_{2} \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\dot{y_2}&=&L_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1} + d_{2} \\left(\\dot{\\alpha}_{1} + \\dot{\\alpha}_{2}\\right) \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}\\ddot{y_2}&=&- L_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) \\dot{\\alpha}_{1}^{2} + L_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) \\ddot{\\alpha}_{1} - d_{2} \\left(\\dot{\\alpha}_{1} + \\dot{\\alpha}_{2}\\right)^{2} \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right) + d_{2} \\left(\\ddot{\\alpha}_{1} + \\ddot{\\alpha}_{2}\\right) \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x2, y2 = L1*cos(a1) + d2*cos(a1+a2), L1*sin(a1) + d2*sin(a1+a2)\n", "x2d, y2d = x2.diff(t), y2.diff(t)\n", "x2dd, y2dd = x2d.diff(t), y2d.diff(t)\n", "\n", "eq(r'x_2', x2)\n", "eq(r'\\dot{x_2}', x2d)\n", "eq(r'\\ddot{x_2}', x2dd)\n", "eq(r'y_2', y2)\n", "eq(r'\\dot{y_2}', y2d)\n", "eq(r'\\ddot{y_2}', y2dd)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inspecting the equations above, we see a new kind of acceleration, proportional to $\\dot{\\alpha_1}\\dot{\\alpha_2}$. This acceleration is due to the [Corio<PERSON> effect](http://en.wikipedia.org/wiki/Coriolis_effect) and is  present only when there are movement in the two joints."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Jacobian matrix for the two-link system\n", "\n", "The Jacobian matrix for the two-link system w.r.t. the center of mass of the second link is:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:06.520339Z", "start_time": "2018-08-14T05:38:06.486050Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}\\mathbf{J}&=&\\left[\\begin{matrix}- L_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) - d_{2} \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right) & - d_{2} \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\\\L_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) + d_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right) & d_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{matrix}\\right]\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r2 = Matrix([[x2, y2]])\n", "J2 = r2.j<PERSON><PERSON>([a1, a2])\n", "eq(r'\\mathbf{J}', J2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Kinetics\n", "\n", "From the free body diagrams, the <PERSON><PERSON><PERSON><PERSON><PERSON> equations for links 1 and 2 of the planar double inverted pendulum are:\n", " \n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "F_{r2x} + F_{e,x} &=& m_2\\ddot{x}_{2} \\\\\n", "\\\\\n", "F_{r2y} - m_2g + F_{e,y} &=& m_2\\ddot{y}_{2} \\\\\n", "\\\\\n", "T_2 + d_2F_{r2x}\\sin(\\alpha_1+\\alpha_2) - d_2F_{r2y}\\cos(\\alpha_1+\\alpha_2) - (L_2-d_2)F_{e,x}\\sin(\\alpha_1+\\alpha_2) - (L_2-d_2)F_{e,y}\\cos(\\alpha_1+\\alpha_2) &=& I_{2}(\\ddot{\\alpha}_1+\\ddot{\\alpha}_2) \\\\\n", "\\\\\n", "F_{r1x} - F_{r2x} &=& m_1\\ddot{x}_{1} \\\\\n", "\\\\\n", "F_{r1y} - F_{r2y} - m_1g &=& m_1\\ddot{y}_{1} \\\\\n", "\\\\\n", "T_1 - T_2 + d_1F_{r1x}\\sin\\alpha_1 - d_1F_{r1y}\\cos\\alpha_1 + (L_1-d_1)F_{r2x}\\sin\\alpha_1 - (L_1-d_1)F_{r2y}\\cos\\alpha_1 &=& I_{1}\\ddot{\\alpha}_1\n", "\\end{array}\n", "\\label{}\n", "\\end{equation}\n", "\n", "If we want to determine the joint torques and we know the kinematics of the links, the inverse dynamics approach, we isolate the joint torques in the equations above, start solving for link 2 and then link 1. To determine the kinematics knowing the joint torques, the direct dynamics approach, we isolate the joint angular accelerations in the equations above and solve the ordinary differential equations.\n", "\n", "Let's express the equations for the torques substituting the terms we know:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:13.546111Z", "start_time": "2018-08-14T05:38:13.540964Z"}}, "outputs": [], "source": ["m1, m2, I1, I2, g = symbols('m_1, m_2, I_1 I_2 g', positive=True)\n", "Fex, Fey = symbols('F_ex F_ey')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:24.567473Z", "start_time": "2018-08-14T05:38:15.397976Z"}}, "outputs": [], "source": ["# link 2\n", "Fr2x = m2*x2dd - Fex\n", "Fr2y = m2*y2dd + m2*g - Fey\n", "T2 = I2*(a1dd+a2dd) - d2*Fr2x*sin(a1+a2) + d2*Fr2y*cos(a1+a2) + (L2-d2)*Fex*sin(a1+a2) - (L2-d2)*Fey*cos(a1+a2)\n", "T2 = simplify(T2)\n", "# link 1\n", "Fr1x = m1*x1dd + Fr2x\n", "Fr1y = m1*y1dd + Fr2y + m1*g\n", "T1 = I1*a1dd + T2 - d1*Fr1x*sin(a1) + d1*Fr1y*cos(a1) - (L1-d1)*Fr2x*sin(a1) + (L1-d1)*Fr2y*cos(a1)\n", "T1 = simplify(T1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The expressions for the joint moments of force (torques) are:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:24.704022Z", "start_time": "2018-08-14T05:38:24.666400Z"}, "scrolled": false}, "outputs": [{"data": {"text/latex": ["$$\\begin{array}{l l}T_1&=&F_{ex} L_{1} \\operatorname{sin}\\left(\\alpha_{1}\\right) + F_{ex} L_{2} \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right) - F_{ey} L_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) - F_{ey} L_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right) + I_{1} \\ddot{\\alpha}_{1} + I_{2} \\ddot{\\alpha}_{1} + I_{2} \\ddot{\\alpha}_{2} + L_{1}^{2} m_{2} \\ddot{\\alpha}_{1} - 2 L_{1} d_{2} m_{2} \\operatorname{sin}\\left(\\alpha_{2}\\right) \\dot{\\alpha}_{1} \\dot{\\alpha}_{2} - L_{1} d_{2} m_{2} \\operatorname{sin}\\left(\\alpha_{2}\\right) \\dot{\\alpha}_{2}^{2} + 2 L_{1} d_{2} m_{2} \\operatorname{cos}\\left(\\alpha_{2}\\right) \\ddot{\\alpha}_{1} + L_{1} d_{2} m_{2} \\operatorname{cos}\\left(\\alpha_{2}\\right) \\ddot{\\alpha}_{2} + L_{1} g m_{2} \\operatorname{cos}\\left(\\alpha_{1}\\right) + d_{1}^{2} m_{1} \\ddot{\\alpha}_{1} + d_{1} g m_{1} \\operatorname{cos}\\left(\\alpha_{1}\\right) + d_{2}^{2} m_{2} \\ddot{\\alpha}_{1} + d_{2}^{2} m_{2} \\ddot{\\alpha}_{2} + d_{2} g m_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$$\\begin{array}{l l}T_2&=&F_{ex} L_{2} \\operatorname{sin}\\left(\\alpha_{1} + \\alpha_{2}\\right) - F_{ey} L_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right) + I_{2} \\ddot{\\alpha}_{1} + I_{2} \\ddot{\\alpha}_{2} + L_{1} d_{2} m_{2} \\operatorname{sin}\\left(\\alpha_{2}\\right) \\dot{\\alpha}_{1}^{2} + L_{1} d_{2} m_{2} \\operatorname{cos}\\left(\\alpha_{2}\\right) \\ddot{\\alpha}_{1} + d_{2}^{2} m_{2} \\ddot{\\alpha}_{1} + d_{2}^{2} m_{2} \\ddot{\\alpha}_{2} + d_{2} g m_{2} \\operatorname{cos}\\left(\\alpha_{1} + \\alpha_{2}\\right)\\end{array}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eq(r'T_1', T1)\n", "eq(r'T_2', T2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is an elegant form to display the equations for the torques using generalized coordinates, $q=[\\alpha_1, \\alpha_2]^T$ and grouping the terms proportional to common quantities in matrices, see for example, <PERSON> (2005, page 180), <PERSON><PERSON> (2001), and <PERSON><PERSON><PERSON><PERSON> (2002, page 383):\n", "\n", "\\begin{equation}\n", "\\tau \\quad=\\quad M(q)\\ddot{q} + C(q,\\dot{q}) + G(q) + E(q,\\dot{q})\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where, for this two-link system:  \n", "- $\\tau$ is a matrix (2x1) of joint torques;  \n", "- $M$ is the mass or inertia matrix (2x2);  \n", "- $\\ddot{q}$ is a matrix (2x1) of angular accelerations;  \n", "- $C$ is a matrix (2x1) of [centipetal](http://en.wikipedia.org/wiki/Centripetal_force) and [Coriolis](http://en.wikipedia.org/wiki/Coriolis_effect) torques;  \n", "- $G$ is a matrix (2x1) of  gravitational torques;  \n", "- $E$ is a matrix (2x1) of external torques.   \n", "\n", "Let's use Sympy to display the equations for the torques in this new form:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:49.910436Z", "start_time": "2018-08-14T05:38:49.837555Z"}}, "outputs": [], "source": ["T1, T2 = T1.expand(), T2.expand()\n", "q1, q2 = dynamicsymbols('q_1 q_2')\n", "q1d, q2d = q1.diff(t), q2.diff(t)\n", "q1dd, q2dd = q1.diff(t, 2), q2.diff(t, 2)\n", "T1 = T1.subs({a1:q1, a2:q2, a1d:q1d, a2d:q2d, a1dd:q1dd, a2dd:q2dd})\n", "T2 = T2.subs({a1:q1, a2:q2, a1d:q1d, a2d:q2d, a1dd:q1dd, a2dd:q2dd})"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T05:38:52.777506Z", "start_time": "2018-08-14T05:38:51.780860Z"}}, "outputs": [{"data": {"text/latex": ["$$\\begin{eqnarray}\\tau&\\quad=\\quad&\\begin{bmatrix}\\tau_1\\\\ \\tau_2\\\\ \\end{bmatrix} \\\\M(q)&\\quad=\\quad&\\left[\\begin{matrix}I_{1} + I_{2} + L_{1}^{2} m_{2} + 2 L_{1} d_{2} m_{2} \\operatorname{cos}\\left(q_{2}\\right) + d_{1}^{2} m_{1} + d_{2}^{2} m_{2} & I_{2} + L_{1} d_{2} m_{2} \\operatorname{cos}\\left(q_{2}\\right) + d_{2}^{2} m_{2}\\\\I_{2} + L_{1} d_{2} m_{2} \\operatorname{cos}\\left(q_{2}\\right) + d_{2}^{2} m_{2} & I_{2} + d_{2}^{2} m_{2}\\end{matrix}\\right]\\\\\\ddot{q}&\\quad=\\quad&\\left[\\begin{matrix}\\ddot{q}_{1}\\\\\\ddot{q}_{2}\\end{matrix}\\right]\\\\C(q,\\dot{q})&\\quad=\\quad&\\left[\\begin{matrix}- L_{1} d_{2} m_{2} \\left(2 \\dot{q}_{1} + \\dot{q}_{2}\\right) \\operatorname{sin}\\left(q_{2}\\right) \\dot{q}_{2}\\\\L_{1} d_{2} m_{2} \\operatorname{sin}\\left(q_{2}\\right) \\dot{q}_{1}^{2}\\end{matrix}\\right]\\\\G(q)&\\quad=\\quad&\\left[\\begin{matrix}g \\left(L_{1} m_{2} \\operatorname{cos}\\left(q_{1}\\right) + d_{1} m_{1} \\operatorname{cos}\\left(q_{1}\\right) + d_{2} m_{2} \\operatorname{cos}\\left(q_{1} + q_{2}\\right)\\right)\\\\d_{2} g m_{2} \\operatorname{cos}\\left(q_{1} + q_{2}\\right)\\end{matrix}\\right]\\\\E(q,\\dot{q})&\\quad=\\quad&\\left[\\begin{matrix}F_{ex} \\left(L_{1} \\operatorname{sin}\\left(q_{1}\\right) + L_{2} \\operatorname{sin}\\left(q_{1} + q_{2}\\right)\\right) - F_{ey} \\left(L_{1} \\operatorname{cos}\\left(q_{1}\\right) + L_{2} \\operatorname{cos}\\left(q_{1} + q_{2}\\right)\\right)\\\\L_{2} \\left(F_{ex} \\operatorname{sin}\\left(q_{1} + q_{2}\\right) - F_{ey} \\operatorname{cos}\\left(q_{1} + q_{2}\\right)\\right)\\end{matrix}\\right]\\end{eqnarray}$$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["M = Matrix(((simplify(T1.coeff(q1dd)), simplify(T1.coeff(q2dd))),\n", "            (simplify(T2.coeff(q1dd)), simplify(T2.coeff(q2dd)))))\n", "C = Matrix((simplify(T1.coeff(q1d**2)*q1d**2 + T1.coeff(q2d**2)*q2d**2 + T1.coeff(q1d*q2d)*q1d*q2d),\n", "            simplify(T2.coeff(q1d**2)*q1d**2 + T2.coeff(q2d**2)*q2d**2 + T2.coeff(q1d*q2d)*q1d*q2d)))\n", "G = Matrix((simplify(T1.coeff(g)*g),\n", "            simplify(T2.coeff(g)*g)))\n", "E = Matrix((simplify(T1.coeff(Fex)*Fex + T1.coeff(<PERSON>y)*Fey),\n", "            simplify(T2.coeff(Fex)*Fex + T2.coeff(<PERSON>y)*<PERSON>y)))\n", "\n", "display(Math(r'\\begin{eqnarray}\\tau&\\quad=\\quad&\\begin{bmatrix}\\tau_1\\\\ \\tau_2\\\\ \\end{bmatrix} \\\\' +\n", "             r'M(q)&\\quad=\\quad&' + mlatex(M) + r'\\\\' +\n", "             r'\\ddot{q}&\\quad=\\quad&' + mlatex(Matrix((q1dd, q2dd))) + r'\\\\' +\n", "             r'C(q,\\dot{q})&\\quad=\\quad&' + mlatex(C) + r'\\\\' +\n", "             r'G(q)&\\quad=\\quad&' + mlatex(G) + r'\\\\' +\n", "             r'E(q,\\dot{q})&\\quad=\\quad&' + mlatex(E) + r'\\end{eqnarray}'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With this convention, to perform inverse dynamics we would calculate:\n", "\n", "\\begin{equation}\n", "\\tau \\quad=\\quad M(q)\\ddot{q} + C(q,\\dot{q}) + G(q) + E(q,\\dot{q})\n", "\\label{}\n", "\\end{equation}\n", "\n", "And for direct dynamics we would solve the differential equation:\n", "\n", "\\begin{equation}\n", "\\ddot{q} \\quad=\\quad M(q)^{-1} \\left[\\tau - C(q,\\dot{q}) - G(q) - E(q,\\dot{q}) \\right]\n", "\\label{}\n", "\\end{equation}\n", "\n", "The advantage of calculating analytically the derivatives of the position vector as function of the joint angles and using the notation above is that each term that contributes to each joint torque or acceleration can be easily identified. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Coupling (or interaction) effects\n", "\n", "The two terms off the main diagonal in the inertia matrix (which are the same) and the centripetal and Coriolis terms represent the effects of the movement (nonzero velocity) of one joint over the other. These torques are referred as coupling or interaction effects (see for example <PERSON><PERSON><PERSON> and <PERSON> (1982) for an application of this concept in the study of the motor control of the upper limb movement)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Planar double pendulum\n", "\n", "Using the same equations above, one can represent a planar double pendulum (hanging from the top, not inverted) considering the angles $\\alpha_1$ and $\\alpha_2$ negative, e.g., at $\\alpha_1=-90^o$ and $\\alpha_2=0$ the pendulum is hanging vertical.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### WARNING: $F_r$ is not the actual joint reaction force!\n", "\n", "For these two examples, in the Newton-<PERSON><PERSON><PERSON> equations based on the free body diagrams we represented the consequences of all possible muscle forces on a joint as a net muscle torque and all forces acting on a joint as a resultant joint reaction force. That is, all forces between segments were represented as a resultant force that doesn't generate torque and a force couple (or free moment) that only generates torque. This is an important principle in mechanics of rigid bodies, see for example [this text](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/FreeBodyDiagram.ipynb). However, this principle creates the unrealistic notion that the sum of forces is applied directly on the joint (which has no further implication for a rigid body), but it is inaccurate for the understanding of the local effects on the joint. So, if we are trying to understand the stress on the joint or mechanisms of joint injury, the forces acting on the joint and on the rest of the segment must be considered individually."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Determination of muscle force\n", "\n", "The torque $T$ exerted by a muscle is given by the product between the muscle-tendon moment arm $r$ and its force $F$. For the human body, there is more than one muscle crossing a joint and several joints. In such case, the torques due to the muscles are expressed in the following matrix form considering $n$ joints and $m$ muscles:\n", "\n", "\\begin{eqnarray}\n", "\\begin{bmatrix} T_1 \\\\ \\vdots \\\\ T_n \\end{bmatrix} &\\quad=\\quad& \\begin{bmatrix} r_{11} & \\cdots & r_{1m} \\\\ \\vdots & \\ddots & \\vdots \\\\ r_{n1} & \\cdots & r_{nm} \\end{bmatrix} \\begin{bmatrix} F_1 \\\\ \\vdots \\\\ F_m \\end{bmatrix}\n", "\\label{}\n", "\\end{eqnarray}\n", "\n", "Where $r_{nm}$ is the moment arm about joint $n$ of the muscle $m$.  \n", "In the example of the two-link system, we sketched two uniarticular muscles for each of the two joints, consequently:  \n", "\n", "\\begin{eqnarray}\n", "\\begin{bmatrix} T_1 \\\\ T_2 \\end{bmatrix} &\\quad=\\quad& \\begin{bmatrix} r_{1,ext} & -r_{1,flex} & 0 & 0 \\\\ 0 & 0 & r_{1,ext} & -r_{1,flex} \\end{bmatrix} \\begin{bmatrix} F_{1,ext} \\\\ F_{1,flex} \\\\ F_{2,ext} \\\\ F_{2,flex} \\end{bmatrix}\n", "\\label{}\n", "\\end{eqnarray} \n", "\n", "Note the opposite signs for the moment arms of the extension and flexion muscles hence they generate opposite torques. We could have represented the opposite signs in the muscle forces instead of in the moment arms.\n", "\n", "The moment arm of a muscle varies with the motion of the joints it crosses. In this case, using the [virtual work principle](http://en.wikipedia.org/wiki/Virtual_work) the moment arm can be given by (<PERSON> et al., 2013; <PERSON><PERSON> and <PERSON>, 2006, page 634):\n", "\n", "\\begin{equation}\n", "r(q) \\quad=\\quad \\dfrac{\\partial L_{MT}(q)}{\\partial q}\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $L_{MT}(q)$ is the length of the muscle-tendon unit expressed as a function of angle $q$.\n", "\n", "For the simulation of human movement, muscles can be modeled as [Hill-type muscles](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/MuscleSimulation.ipynb), the torques they generate are given by the matrix above, and this matrix is entered in the ODE for a multibody system dynamics we deduced before:\n", "\n", "\\begin{equation}\n", "\\ddot{q} \\quad=\\quad M(q)^{-1} \\left[R_{MT}(q)F_{MT}(a,L_{MT},\\dot{L}_{MT}) - C(q,\\dot{q}) - G(q) - E(q,\\dot{q}) \\right]\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $R_{MT}$ and $F_{MT}$ are matrices for the moment arms and muscle-tendon forces, respectively.\n", "This ODE is then solved numerically given initial values; but this problem is far from trivial for a simulation with several segments and muscles."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Numerical simulation of inverse dynamics\n", "\n", "Let's simulate a voluntary movement of the upper limb using the planar two-link system as a model in order to visualize the contribution of each torque term.  \n", "\n", "We will ignore the muscle dynamics and we will calculate the joint torques necessary to move the upper limb from one point to another under the assumption that the movement is performed with the smoothest trajectory possible. I.e., the movement is performed with a [minimum-jerk trajectory](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/MinimumJerkHypothesis.ipynb), a hypothesis about control of voluntary movements proposed by <PERSON> and Hogan (1985).\n", "\n", "Once we determine the desired trajectory, we can calculate the velocity and acceleration of the segments and combine with anthropometric measures to calculate the joint torques necessary to move the segments. This means we will perform inverse dynamics. \n", "\n", "Let's simulate a slow (4 s) and a fast (0.5 s) movement of the upper limb starting at the anatomical neutral position (upper limb at the side of the trunk) and ending with the upper arm forward at horizontal and elbow flexed at 90 degrees.\n", "\n", "First, let's import the necessary Python libraries and customize the environment:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:47.030051Z", "start_time": "2018-08-14T06:06:47.018196Z"}}, "outputs": [], "source": ["import numpy as np\n", "%matplotlib inline\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "matplotlib.rcParams['lines.linewidth'] = 3\n", "matplotlib.rcParams['font.size'] = 13\n", "matplotlib.rcParams['lines.markersize'] = 5\n", "matplotlib.rc('axes', grid=True, labelsize=14, titlesize=16, ymargin=0.05)\n", "matplotlib.rc('legend', numpoints=1, fontsize=11)\n", "import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's take the anthropometric data from <PERSON><PERSON><PERSON>'s model (see [Body segment parameters](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/BodySegmentParameters.ipynb)):"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:48.811024Z", "start_time": "2018-08-14T06:06:48.803653Z"}}, "outputs": [], "source": ["height, mass = 1.70,               70  # m, kg\n", "L1n, L2n     = 0.188*height,       0.253*height\n", "d1n, d2n     = 0.436*L1n,          0.682*L2n\n", "m1n, m2n     = 0.0280*mass,        0.0220*mass\n", "rg1n, rg2n   = 0.322,              0.468\n", "I1n, I2n     = m1n*(rg1n*L1n)**2,  m2n*(rg2n*L2n)**2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Considering these lengths, the initial and final positions of the endpoint (finger tip) for the simulated movement will be:"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:50.416919Z", "start_time": "2018-08-14T06:06:50.411279Z"}}, "outputs": [], "source": ["xi, yi = 0, -L1n-L2n\n", "xf, yf = L1n, L2n\n", "gn = 9.81  # gravity acceleration m/s2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Slow movement"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:52.079312Z", "start_time": "2018-08-14T06:06:52.074752Z"}}, "outputs": [], "source": ["duration = 4  # seconds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The endpoint minimum jerk trajectory will be (see [Kinematic chain in a plane (2D)](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/KinematicChain.ipynb)):"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:53.757630Z", "start_time": "2018-08-14T06:06:53.753232Z"}}, "outputs": [], "source": ["from minjerk import minjerk"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:54.902438Z", "start_time": "2018-08-14T06:06:54.527863Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["time, rlin, vlin, alin, jlin = minjerk([xi, yi], [xf, yf], duration=duration)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's find the joint angles to produce this minimum-jerk trajectory (inverse kinematics):"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:56.518495Z", "start_time": "2018-08-14T06:06:56.513339Z"}}, "outputs": [], "source": ["from invkin2_2d import invkin"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:06:57.949087Z", "start_time": "2018-08-14T06:06:57.469640Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Endpoint value outside working area. Value will be coerced.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x360 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rang = invkin(time, rlin, L1=L1n, L2=L2n)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the joint torques, we need to calculate the angular velocity and acceleration. Let's do that using numerical differentiation:"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:07:00.307395Z", "start_time": "2018-08-14T06:06:59.994940Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def diff_c(ang, duration):\n", "    \"\"\"Numerical differentiations using the central difference for the angular data.\n", "    \"\"\"\n", "    # central difference (f(x+h)-f(x-h))/(2*h)\n", "    dt = duration/(ang.shape[0]-1)\n", "    vang = np.empty_like(rang)\n", "    aang = np.empty_like(rang)\n", "    vang[:, 0] = np.gradient(rang[:, 0], dt)\n", "    vang[:, 1] = np.gradient(rang[:, 1], dt)\n", "    aang[:, 0] = np.gradient(vang[:, 0], dt)\n", "    aang[:, 1] = np.gradient(vang[:, 1], dt)\n", "    \n", "    _, ax = plt.subplots(1, 3, sharex=True, figsize=(10, 3))\n", "    ax[0].plot(time, rang*180/np.pi)\n", "    ax[0].legend(['<PERSON> 1', '<PERSON> 2'], framealpha=.5, loc='best')\n", "    ax[1].plot(time, vang*180/np.pi)\n", "    ax[2].plot(time, aang*180/np.pi)\n", "    ylabel = [r'Displacement [$\\mathrm{^o}$]', r'Velocity [$\\mathrm{^o/s}$]',\n", "              r'Acceleration [$\\mathrm{^o/s^2}$]']\n", "    for i, axi in enumerate(ax):\n", "        axi.set_xlabel('Time [$s$]')\n", "        axi.set_ylabel(ylabel[i])\n", "        axi.xaxis.set_major_locator(plt.MaxNLocator(4))\n", "        axi.yaxis.set_major_locator(plt.MaxNLocator(4))\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    return vang, aang\n", "\n", "vang, aang = diff_c(rang, duration)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:07:12.246714Z", "start_time": "2018-08-14T06:07:12.227941Z"}}, "outputs": [], "source": ["def dyna(time, L1n, L2n, d1n, d2n, m1n, m2n, gn, I1n, I2n, q1, q2, rang, vang, aang, <PERSON>xn, <PERSON><PERSON>, <PERSON>, C, G, E):\n", "    \"\"\"Numerical calculation and plot for the torques of a planar two-link system.\n", "    \"\"\"\n", "    from sympy import lambdify, symbols\n", "    \n", "    Mfun  = lambdify((I1, I2, L1, L2, d1, d2, m1, m2, q1, q2), M, 'numpy')\n", "    Mn    = Mfun(I1n, I2n, L1n, L2n, d1n, d2n, m1n, m2n, rang[:, 0], rang[:, 1])\n", "    M00   = Mn[0, 0]*aang[:, 0]\n", "    M01   = Mn[0, 1]*aang[:, 1]\n", "    M10   = Mn[1, 0]*aang[:, 0]\n", "    M11   = Mn[1, 1]*aang[:, 1]\n", "    Q1d, Q2d = symbols('Q1d Q2d')\n", "    dicti = {q1.diff(t, 1):Q1d, q2.diff(t, 1):Q2d}\n", "    C0fun = lambdify((L1, d2, m2, q2, Q1d, Q2d), C[0].subs(dicti), 'numpy')\n", "    C0    = C0fun(L1n, d2n, m2n, rang[:, 1], vang[:, 0], vang[:, 1])\n", "    C1fun = lambdify((L1, d2, m2, q2, Q1d, Q2d), C[1].subs(dicti), 'numpy')\n", "    C1    = C1fun(L1n, d2n, m2n, rang[:, 1], vang[:, 0], vang[:, 1])\n", "    G0fun = lambdify((L1, d1, d2, m1, m2, g, q1, q2), G[0], 'numpy')\n", "    G0    = G0fun(L1n, d1n, d2n, m1n, m2n, gn, rang[:, 0], rang[:, 1])\n", "    G1fun = lambdify((L1, d1, d2, m1, m2, g, q1, q2), G[1], 'numpy')\n", "    G1    = G1fun(L1n, d1n, d2n, m1n, m2n, gn, rang[:, 0], rang[:, 1])\n", "    E0fun = lambdify((L1, L2, q1, q2, <PERSON>x, <PERSON><PERSON>), E[0], 'numpy')\n", "    E0    = E0fun(L1n, L2n, rang[:, 0], rang[:, 1], 0, 0)\n", "    E1fun = lambdify((L1, L2, q1, q2, <PERSON><PERSON>, <PERSON><PERSON>), E[1], 'numpy')\n", "    E1    = E1fun(L1n, L2n, rang[:, 0], rang[:, 1], <PERSON>xn, <PERSON><PERSON>)\n", "    \n", "    fig, ax = plt.subplots(1, 2, sharex=True, squeeze=True, figsize=(10, 4))\n", "    ax[0].plot(time, M00+M01)\n", "    ax[0].plot(time, C0)\n", "    ax[0].plot(time, G0)\n", "    ax[0].plot(time, E0)\n", "    #ax[0].plot(time, M00+M01+C0+G0, 'k:', linewidth=4)\n", "    ax[0].set_ylabel(r'Torque [Nm]')\n", "    ax[0].set_title('Joint 1')\n", "    ax[1].plot(time, M10+M11, label='Mass/Inertia')\n", "    ax[1].plot(time, C1, label='Centripetal/Coriolis       ')\n", "    ax[1].plot(time, G1, label='Gravitational')\n", "    ax[1].plot(time, E1, label='External')\n", "    #ax[1].plot(time, M10+M11+C1+G1, 'k:', linewidth=4, label='Muscular (sum)')\n", "    ax[1].set_title('Joint 2')\n", "    fig.legend(framealpha=.5, bbox_to_anchor=(1.15, 0.95), fontsize=12)    \n", "    for i, axi in enumerate(ax):\n", "        axi.set_xlabel('Time [$s$]')\n", "        axi.xaxis.set_major_locator(plt.MaxNLocator(4))\n", "        axi.yaxis.set_major_locator(plt.MaxNLocator(4))\n", "    plt.show()\n", "    \n", "    return M00, M01, M10, M11, C0, C1, G0, G1, E0, E1"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:07:13.778100Z", "start_time": "2018-08-14T06:07:13.492972Z"}}, "outputs": [{"data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 720x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON> = 0, 0\n", "M00, M01, M10, M11, C0, C1, G0, G1, E0, E1 = dyna(time, L1n, L2n, d1n, d2n, m1n, m2n, gn, I1n, I2n,\n", "                                                  q1, q2, rang, vang, aang, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>)\n", "T1a = M00+M01+C0+G0+E0\n", "T2a = M10+M11+C1+G1+E1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The joint torques essentially compensate the gravitational torque."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fast movement \n", "\n", "Let's see what is changed for a fast movement:"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:07:19.425499Z", "start_time": "2018-08-14T06:07:18.044949Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Endpoint value outside working area. Value will be coerced.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x360 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x216 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["duration = 0.5  # seconds\n", "time, rlin, vlin, alin, jlin = minjerk([xi, yi], [xf, yf], duration=duration)\n", "rang = invkin(time, rlin, L1=L1n, L2=L2n)\n", "vang, aang = diff_c(rang, duration)\n", "M00, M01, M10, M11, C0, C1, G0, G1, E0, E1 = dyna(time, L1n, L2n, d1n, d2n, m1n, m2n, gn, I1n, I2n,\n", "                                                  q1, q2, rang, vang, aang, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>)\n", "T1b = M00+M01+C0+G0+E0\n", "T2b = M10+M11+C1+G1+E1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The interaction torques are larger than the gravitational torques for most part of the movement."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fast movement in the horizontal plane\n", "\n", "Let's simulate a fast movement in the horizontal plane:"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"ExecuteTime": {"end_time": "2018-08-14T06:07:33.010988Z", "start_time": "2018-08-14T06:07:32.725482Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["gn = 0  # gravity acceleration m/s2\n", "M00, M01, M10, M11, C0, C1, G0, G1, E0, E1 = dyna(time, L1n, L2n, d1n, d2n, m1n, m2n, gn, I1n, I2n,\n", "                                                  q1, q2, rang, vang, aang, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>)\n", "T1b = M00+M01+C0+G0+E0\n", "T2b = M10+M11+C1+G1+E1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Numerical simulation of direct dynamics\n", "\n", "Remember that for direct dynamics we want to solve the following differential equation:\n", "\n", "\\begin{equation}\n", "\\ddot{q} \\quad=\\quad M(q)^{-1} \\left[\\tau - C(q,\\dot{q}) - G(q) - E(q,\\dot{q}) \\right]\n", "\\label{}\n", "\\end{equation}\n", "\n", "Let's use the <PERSON><PERSON><PERSON> method for solving this equation numerically.  \n", "\n", "First, transforming the equation above into a system of two first-order ODE:\n", "\n", "\\begin{equation}\n", "\\left\\{\n", "\\begin{array}{l l}\n", "\\dfrac{\\mathrm{d} q}{\\mathrm{d}t} &=& \\dot{q}, \\quad &q(t_0) = q_0\n", "\\\\\n", "\\dfrac{\\mathrm{d} \\dot{q}}{\\mathrm{d}t} &=& M(q)^{-1} \\left[\\tau - C(q,\\dot{q}) - G(q) - E(q,\\dot{q}) \\right], \\quad &\\dot{q}(t_0) = \\dot{q}_0\n", "\\end{array}\n", "\\right.\n", "\\label{}\n", "\\end{equation}\n", "\n", "Second, we would write a function for the calculation of the system states and another function for the <PERSON><PERSON><PERSON> method. \n", "\n", "Third, now joint torques are the input to the system; just to cast out nines, let's choose as input torques the output torques of the inverse dynamics solution we calculated before.  \n", "\n", "Fourth, plot everything. \n", "\n", "Easy peasy."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exercises\n", "\n", "1. Derive the equations of motion for a single pendulum (not inverted).  \n", "2. Derive the equations of motion for a double pendulum (not inverted).  \n", "3. For the one-link system, simulate a typical trajectory to calculate the joint torque (i.e., perform inverse dynamics).\n", "4. For the one-link system, simulate a typical joint torque to calculate the trajectory (i.e., perform direct dynamics).\n", "5. Consider the double pendulum moving in the horizontal plane and with no external force. Find out the type of movement and which torque terms are changed when:   \n", "  a) $\\dot{\\alpha}_1=0^o$  \n", "  b) $\\alpha_2=0^o$  \n", "  c) $\\dot{\\alpha}_2=0^o$  \n", "  d) $2\\alpha_1+\\alpha_2=180^o$ (hint: a two-link system with this configuration is called polar manipulator)\n", "6. Derive the equations of motion and the torque terms using angles in the segmental space $(\\theta_1,\\,\\theta_2)$.  \n", "7. Run the numerical simulations for the torques with different parameters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON> (2005) [Introduction to Robotics: Mechanics and Control](http://books.google.com.br/books?id=MqMeAQAAIAAJ). 3rd Edition. Prentice Hall.  \n", "- <PERSON>, <PERSON> (1985) [The coordination of arm movements: an experimentally confirmed mathematical model](http://www.jneurosci.org/cgi/reprint/5/7/1688.pdf). Journal of Neuroscience, 5, 1688-1703.   \n", "- <PERSON><PERSON><PERSON>, <PERSON> (1982) [Dynamic interactions between limb segments during planar arm movement](http://link.springer.com/article/10.1007%2FBF00353957). Biological Cybernetics, 44, 67-77.  \n", "- <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2006) [Biomechanics of the Musculo-skeletal System](https://books.google.com.br/books?id=hOIeAQAAIAAJ&dq=editions:ISBN0470017678). 3rd Edition. Wiley.  \n", "- <PERSON><PERSON> (2001) [Computer modeling and simulation](https://drive.google.com/open?id=0BxbW72zV7WmUbXZBR2VRMnF5UTA&authuser=0). Annu. Rev. Biomed. Eng., 3, 245–73.  \n", "- <PERSON>, <PERSON>, <PERSON><PERSON> (2013) [What is a moment arm? Calculating muscle effectiveness in biomechanical models using generalized coordinates](http://simtk-confluence.stanford.edu:8080/download/attachments/3376330/ShermanSethDelp-2013-WhatIsMuscleMomentArm-Final2-DETC2013-13633.pdf?version=1&modificationDate=1369103515834) in Proc. ASME Int. Design Engineering Technical Conferences (IDETC), Portland, OR, USA.  \n", "- <PERSON><PERSON><PERSON> (1993) [Muscle coordination of movement: a perspective](http://e.guigon.free.fr/rsc/article/Zajac93.pdf). J Biomech., 26, Suppl 1:109-24.  \n", "- <PERSON><PERSON><PERSON>, <PERSON> (1989) [Determining muscle's force and action in multi-articular movement](https://drive.google.com/open?id=0BxbW72zV7WmUcC1zSGpEOUxhWXM&authuser=0). Exercise and Sport Sciences Reviews, 17, 187-230.  \n", "- <PERSON><PERSON><PERSON><PERSON> (2002) [Kinetics of human motion](http://books.google.com.br/books?id=wp3zt7oF8a0C). Human Kinetics."]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}