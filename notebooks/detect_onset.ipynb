{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# detect_onset.py\n", "\n", "A function from [detecta](https://pypi.org/project/detecta/) - Python module to detect events in data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["One of the simplest methods to automatically detect or identify the change or occurrence of a particular event in the data, for example, its beginning and ending, or simply the data onset, is based on amplitude threshold, where the signal is considered to be 'on' when it is above a certain threshold. This threshold can be proportional to the amplitude of the baseline (the part of the data that we know there is no real signal, only noise).  \n", "\n", "For instance, a threshold equals to two or three times the standard deviation of the baseline is a common procedure employed in the analysis of electromyographic data. Other way to set the threshold would be as a percentage value of the maximum or peak of the data. For instance, in movement analysis it's common to define the onset period as the signal above 5% of the peak velocity of the investigated movement. \n", "\n", "The function `detect_onset.py` (code at the end) implements such onset detection based on the amplitude-threshold method with a parameter to specify a minimum number of samples above threshold to detect as onset, other parameter to specify the minimum number of samples (continuous or not) below threshold that will be ignored in the detection of data greater or equal to threshold (to avoid the detection of spikes or transients in the data), and a second threshold parameter to specify the minimum amplitude that a minimum number of samples should have (to avoid the detection of baseline fluctuations that are above the first threshold but are not actual signals).  \n", "\n", "`detect_onset.py` signature is:\n", "```python\n", "inds = detect_onset(x, threshold=0, n_above=1, n_below=0,\n", "                    threshold2=None, n_above2=1, show=False, ax=None)\n", "```\n", "Let's see how `detect_onset.py` works; first let's import the necessary Python libraries and configure the environment:  "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from detecta import detect_onset"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's run the function examples:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 60, 138]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> detect_onset(x, np.std(x[:50]), n_above=10, n_below=0, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add some noise:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgIAAAEaCAYAAABjKD+nAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAgAElEQVR4nOydeZgcVdX/PyezJiH7ZJ1skIQsQHaQTRJE9gQQFEVlUYGgvCri8kN2UNEXUUFeZIsIIsjighC2ICZE9iSELRtZCCQzyWRmMpPZMjPpyf39caumq6qru6tnuqerM/fzPP10V9WtqlO3a/nWOefeK0opDAaDwWAwdE96ZNsAg8FgMBgM2cMIAYPBYDAYujFGCBgMBoPB0I0xQsBgMBgMhm6MEQIGg8FgMHRjjBAwGAwGg6EbY4SAwWAwGAzdmG4hBERkrIgoEcnvgn0tFZGLO7juFhH5fJxlc0VkW+es6z505X++PyAiF4nIq129btiwzpnxXbCfB0Xk5x1cN+49xpz3nac73mv3SyGQ6IHa3RCRgSLyTxFpFJFPROSrCcoeLyJLRGS3iGzxWT7WWt4kIuucdSwi94hIg+PTIiL1GTqsnEdEzhWR1626XOqzfLqIrLSWrxSR6VkwM/Skcn77rNth0Z4riMhtIrJBROqta/aCDmzDFhfPeub/RURuTJux3RgRKRKRP1rncL2IrBKRUzuwnUIR+Zv1DFQiMjfIevulEOgs+5mavgtoBYYCXwPuFpFD4pRtBB4Afhxn+V+BVcAg4BrgbyIyGEApdZlS6gD7Y5V9Mn2Hsd+xC7gd+JV3gYgUAv8C/gIMAB4C/mXNN7hJ5fxOKzlyn2gE5gP9gAuBO0Tk6A5u60gROSZtlhmc5ANbgTno/+o64AkRGduBbb0KfB3YEXgNpdR+9QEeBvYBe4AG4CfAWEChL4RPgSrgGsc6NwJ/Q99464CL0SLpKmATUA08AQy0yhdbZauBWmA5MNRathT4GfAaUA8sBkoc+zoDWG2ttxSY7Fi2Bfi89bsn8CBQA6xBP5y3pVgXvdE3yYM99fOrJOt9HtjimXcw0AL0ccz7L3BZnP3WA3MC2jkX2Ab8ENgJbAe+EWC909HCpA59Ed3oWGb/55cC5dY2f+hYXoR+EJdbn9uBImvZWmCeo2y+dc7MtKaPBF63/sP3gLmdOF8vBpZ65p0ElAHimPcpcErAbSrgMmCDdf7c5dxWnHUuss7ZO4HdwDrgBMfyfsAfrXosA34O5DnWfdVR9mj0NbHb+j7amn888IGj3L+Btx3TrwJnZfr8tsr9AmgDmtH3if9LVneOOvodWsj93Jr/TeucqQFeBMZY88Uqu9Oqi/eBQ61lD1rbfhZ9rbwFjEtWh9aypcDF1u884Dbr/NwMXG4dQ36c434ax3UQsJ7HWtv8f8ASx/y/4L7mLgE2WnXzNDAi6DkZrw4D2HaRdd7cZq37MXBqgPW+Ye2v3qq3BY5lc9H3o6utet0CfM1zLfwZqAQ+Aa5FPy+K0PeEQx1lB6OfRUOs6XnAu1a514GpCWx8Hzgnlf/Ks/42At6bOrSDsH9wPFA9J/L96AfsNPRDbbK1/EZgL3CW9Yf2BK4A3gRGWn/wvcBfrfILgGeAXtaFOAvoay1bihYPB1vbWYp1Y7LmNQInAgVokbIRKPTajX5T/C8wEBgFfIhDCACLrJPJ77PIKjMD2OOpmx8BzySpPz8h8AVgrWfe/wF3+qx/gXVxJXz4eC68CHCzVS+nAU3AgADrHWb9Z1OBCqwHieM//yv6gXEY+sK16/dm6/8dgr5YXwd+Zi27HnjEsZ/TgXXW71K0ADzN2u+J1vRga/kfEvwv7/scg58Q+AHwvGfeIgLewK3jXgT0B0Zbx51QRKBvqBFr3wXAl9EPIVv8PoW+BnpbdfY21s0ThxCwztca4Hy0gDrPmh6EFtB7gBJr2Q60COuDvlb2AIMyfX47yi7FeqAGqTtHHX3Xsr8n+p6xEZhszbsWeN0qfzKw0tqWWGWGW8seRD8wj7DWewR4LFkdeu1GP1zXoe8RA4ElxBEClr3bnedCgjquBa7yXEsHoEWgfQ21CwHgc1hiGX2/vBNYFrBe49ZhgP/wIvS9+xL0vfjb6HMqmfA9HRhn/S9z0PcbW+jPtf7n31rHMgd9355oLf8z2mPXx6qbj4BvWcseAH7h2M/lwAvW75loUfgZy9YL0ff8Ih/7hqJF6iRrenSS/+qrPtswQgB/ITDSMe9t4CvW7xudJ601by3uN6Lh1gmXj1avvmoOfZFe65j+juNEuA54wrGsB/rCmuu1G/0gdV6wl5K6R+CzwA7PvEvwPHh81vMTAucDb3rm/QJ40Gf9l3G8KQSwcy76IZDvmLcTODLF470d+J3nP5/kWH4r8Efr9ybgNMeyk+1jBsaj3xR6WdOPANdbv/8f8LBnvy8CF3bwXPUTAtdhPRQc8x4JWqfWcR/rmH4C66aeYJ2L8NxArWvkfPRNqQXo6Vh2HtbbIW4hcD6Ot3xr3hvARdbv/wJno70qiy3bTkF7C2KEUibOb0fZpfgLAd+6s47zU0/557EeAtZ0D/RDZQz64fiRdaw9POs9CCx0TJ9GVGwmq8N2u4H/4PDKob1J8YTAQ8ALBBTojvXG2ttE38/etOY7hcAfgVsd6xyAvl+ODVCvceswgG0XARsd072sfQ1L8RifAr5v/Z6LFgK9PfZeh36AtwBTHMsW2Occ+t652bHsNeAC6/fdWC8bjuXr8XhO0UL838C9qRyDzzEFFgLdLUfAGTNpQp+sNls9ZccA/xSRWhGpRQuDNvRN8WH0zf8xESkXkVtFpCDAfkagXUkAKKX2Wfst9bF1hMemT3zKJKMB6OuZ1xf9kMvItkRkFFpB/znF7VcrpSKOae//E4OIfMZKXqwUkd3ot6MSTzFvHY6wfrv+C+cypdRG9P89X0R6ocM5j1rlxgBfss8L69w4Fi0U00U6/rdE53o8ypR1B7Gw62QM+ua03XHM96I9A1689Wpvxz7HX0HfaI+zfi9Fny9zrOlUSOf57STV+8QdjnrZhX7LLFVK/QftNbsLqBCR+0TEaW+g+4SFsw6dBLpPiMivgUOBcz3/carcDwwVkfk+djjvbQ1oT5nT5njHG7cOA9rUvl2lVJP1M9m941QReVNEdln7PA33vaNGKdXomLavhRKgkNh7h23rf4Ce1r1pDDAd+KfjOH/ouXeMInpPQkR6oJ8vrcD/JD/09LC/CoGOnOjedbaiY039HZ9ipVSZUmqvUuompdQUdCxvHtodnoxy9MkAgIgI+kQo8ym73VpmM9q5UESe92TpOz/PW8U+AvJFZIJj1WnoHIVUWQ0cJCJ9kmzrArRbb3MH9pEqj6JjkaOUUv2Ae9A3ECfeOiy3frv+C88y0CGF84AzgTWWOAB9XjzsOS96K6V+Bb6tJ5yfoPW+GphqnR82U+nY/5YKpZ592nWyFf0WVOI45r5KKb+kPG+92tuxz3GvEHgFHyHQRed3uu4TCzznQ0+l1OsASqnfK6VmAYegQ4PxEnGdJKtDJwnvEwAichNwKnCSUqrOsyxeHTeIyNXebSml9gI3ofOgnOeK997WGx0O8rPZS8I6TDciUgT8HZ1XMFQp1R94DvfxDLCOwca+FqrQng7vvaMM2l/unkDfO76KDmPZwnQrOmzgPM5eSqm/WnYJ2rMyFJ0bsNdh8+gk/9XXOlMn+6sQqAAO6uQ27gF+Yak6RGSwiJxp/T5eRA4TkTx0otpetLcgGU8Ap4vICZYH4YfoG6zfCf8E8FMRGSAiI9FxyXaUUqcqR5a+53OqVaYR+Adws4j0tjJ+z0QrzhhEpIeIFKPf/kREiu1MdaXUR+gklxus+V9AP5z+7tnMBWi3p3fbD4pIzPxO0gfYpZRqFpEj0Beel+tEpJeVSf4N4HFr/l+Ba63/tQSdF/AXx3qPod2s3ybqDcAqM19EThaRPKsu5lr/EcrTesLzaX9w2uui3a09rO3YXqWl6PPpe6KbFdlvBv+x1r1IfJp3poEh1j4LRORL6Jjtc0qp7Wg3/m9EpK91nowTkTk+23gOOFhEvioi+SLyZWAKOj4M+lyfiI6Nv62UWo2+qX4GWGZvJB3nt0SbvY2Nc7zpuk/81Dq/EJF+Vt0hIodbb4YF6BhzM8HuE8nq0MkT6P9spIgMQCc4tyMiP0VfFycqpaq9Kyeo4wOUUrfEse9hdOz8FMe8R4FviG72WgTcAryllNoS4Hjj1qE1vVTS20yxEG1/JRAR3UzvJJ9yN4lujvdZ9Mvek0qpNnSd/0JE+ljPhytx3zseRefYfA33veN+4DLrnBDrnD1doi9Xd6OvuflKqT1OQ5RSnyb5rx6xy1r3jGL7WK17i/cFycX+KgR+ib7J14rIjzq4jTvQb5uLRbeHfxN9swIYhm5lUId2Ib+C+0TwRSm1Ht2s4060spyP/tNbfYrfhHY5fYy+Cfs+vAPwHXSS0E70w+/b1s0XEfmsiDQ4yh6HjtU/h1a5e6x923wFmI1OXPoV8EWlVKW9UESOQidX+jUbHIWOl6WT76AfAvXoB/kTPmVeQScivQzcppSyj+fnwAp0Zu4HwDvWPACsh98baI/P4475W9EPm6vRN5Kt6Le8VK+l89H1ezc61r0HfaPAOh/OQouqWnROylmO8yQTdQk6c30C+tz8Bfr/tR8eF6BvoGvQ///f8AmHWOXnoUVuNTohdp5Sqspa3oiu69WO43kD+EQptbMDNsc9v9H19Anx30rvAL4oIjUi8vsO7Bul1D+B/0WHCevQSb12++++6P+0xrKjGv0WmmybCevQw/3oMOV76Hr9h2f5LehreUOiN/1UsB6GN6CTE+*********************************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**********************************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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 61,  79],\n", "       [ 81,  99],\n", "       [101, 119],\n", "       [121, 143]])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, np.std(x[:50]), n_above=10, n_below=0, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the `n_below` parameter to not detect the noise from former example as onsets:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 57, 147]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, np.std(x[:50]), n_above=10, n_below=2, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`detect_onset` works with missing values (NaNs):"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 2,  2],\n", "       [ 6,  8],\n", "       [10, 11]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = [0, 0, 2, 0, np.nan, 0, 2, 3, 3, 0, 1, 1, 0]\n", "    >>> detect_onset(x, threshold=1, n_above=1, n_below=0, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Consider the case where there is a false onset because some data in the baseline are greater than the `threshold`:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 10,  40],\n", "       [ 60, 149]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[11:41] = np.ones(30)*.3\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, .1, n_above=10, n_below=1, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can't increase the value of the `threshold` because this would delay the detection of the actual onset:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 69, 125]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[11:41] = np.ones(30)*.3\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, .4, n_above=10, n_below=1, show=True)  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["For this situation we can use the second threshold parameter, `threshold2` with corresponding `n_above2` parameter:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 60, 141]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["    >>> x = np.random.randn(200)/10\n", "    >>> x[11:41] = np.ones(30)*.3\n", "    >>> x[51:151] += np.hstack((np.linspace(0,1,50), np.linspace(1,0,50)))\n", "    >>> x[80:140:20] = 0\n", "    >>> detect_onset(x, .1, n_above=10, n_below=1,\n", "                     threshold2=.4, n_above2=5, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Performance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The performance of the `detect_onset` function varies with the data and parameters.  \n", "Here is a simple test of the `detect_onset.py` performance:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Detection of onset (data size= 10000):\n", "485 µs ± 8.47 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["x = np.random.randn(10000)\n", "print('Detection of onset (data size= %d):' %x.size)\n", "%timeit detect_onset(x, threshold=0, n_above=10, n_below=1, threshold2=.5, n_above2=5, show=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"state": {}, "version": "1.1.2"}}, "nbformat": 4, "nbformat_minor": 4}