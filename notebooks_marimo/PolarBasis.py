import marimo

__generated_with = "0.13.15"
app = marimo.App()


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        <a href="https://colab.research.google.com/github/BMClab/BMC/blob/master/notebooks/PolarBasis.ipynb" target="_parent"><img src="https://colab.research.google.com/assets/colab-badge.svg" alt="Open In Colab"/></a>
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        # Polar and Cylindrical Frame of Reference

        > <PERSON><PERSON>, <PERSON>  
        > [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  
        > Federal University of ABC, Brazil
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        <h1>Contents<span class="tocSkip"></span></h1>
        <br>
        <div class="toc"><ul class="toc-item"><li><span><a href="#Python-setup" data-toc-modified-id="Python-setup-1"><span class="toc-item-num">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href="#Time-derivati-of-the-versors-${\bf\hat{e_R}}$-and-${\bf\hat{e_\theta}}$" data-toc-modified-id="Time-derivati-of-the-versors-${\bf\hat{e_R}}$-and-${\bf\hat{e_\theta}}$-2"><span class="toc-item-num">2&nbsp;&nbsp;</span>Time-derivative of the versors ${\bf\hat{e_R}}$ and ${\bf\hat{e_\theta}}$</a></span></li><li><span><a href="#Angular-position-velocity-and-acceleration" data-toc-modified-id="Angular-position-velocity-and-acceleration-3"><span class="toc-item-num">3&nbsp;&nbsp;</span>Angular position, velocity and acceleration</a></span><ul class="toc-item"><li><span><a href="#Position" data-toc-modified-id="Position-3.1"><span class="toc-item-num">3.1&nbsp;&nbsp;</span>Position</a></span></li><li><span><a href="#Velocity" data-toc-modified-id="Velocity-3.2"><span class="toc-item-num">3.2&nbsp;&nbsp;</span>Velocity</a></span></li><li><span><a href="#Acceleration" data-toc-modified-id="Acceleration-3.3"><span class="toc-item-num">3.3&nbsp;&nbsp;</span>Acceleration</a></span></li></ul></li><li><span><a href="#Important-to-note" data-toc-modified-id="Important-to-note-4"><span class="toc-item-num">4&nbsp;&nbsp;</span>Important to note</a></span></li><li><span><a href="#Example" data-toc-modified-id="Example-5"><span class="toc-item-num">5&nbsp;&nbsp;</span>Example</a></span><ul class="toc-item"><li><span><a href="#Solving-numerically" data-toc-modified-id="Solving-numerically-5.1"><span class="toc-item-num">5.1&nbsp;&nbsp;</span>Solving numerically</a></span></li><li><span><a href="#Solved-symbolically" data-toc-modified-id="Solved-symbolically-5.2"><span class="toc-item-num">5.2&nbsp;&nbsp;</span>Solved symbolically</a></span></li></ul></li><li><span><a href="#Further-reading" data-toc-modified-id="Further-reading-6"><span class="toc-item-num">6&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href="#Video-lectures-on-the-Internet" data-toc-modified-id="Video-lectures-on-the-Internet-7"><span class="toc-item-num">7&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href="#Problems" data-toc-modified-id="Problems-8"><span class="toc-item-num">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href="#References" data-toc-modified-id="References-9"><span class="toc-item-num">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Python setup
        """
    )
    return


@app.cell
def _():
    import numpy as np
    import sympy as sym
    from sympy.plotting import plot_parametric,plot3d_parametric_line
    from sympy.vector import CoordSys3D
    import matplotlib.pyplot as plt
    # from matplotlib import rc
    # rc('text', usetex=True)
    sym.init_printing()
    return np, plot_parametric, plt, sym


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        Consider that we have the position vector <span class="notranslate">$\bf\vec{r}$</span> of a particle, moving in a circular path indicated in the figure below by a dashed line. This vector <span class="notranslate">$\bf\vec{r(t)}$</span> is described in a fixed reference frame as:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        {\bf\vec{r}}(t) = x(t){\bf\hat{i}} + y(t){\bf\hat{j}} + z(t){\bf\hat{k}}
        \label{eq_1}
        \end{equation}
        </span>

        <figure><img src="https://github.com/BMClab/BMC/blob/master/images/polarCoord.png?raw=1" width=500/><figcaption><center><i>Figure. Position vector of a moving particle moving in a circular path.</i></center></figcaption></figure>  
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        Naturally, we could describe all the kinematic variables in the fixed reference frame. But in circular motions, is convenient to define a basis with a vector in the direction of the position vector <span class="notranslate"> $\bf\vec{r}$</span>. So, the vector <span class="notranslate"> $\bf\hat{e_R}$</span> is defined as:

        <span class="notranslate">
        \begin{equation}
        {\bf\hat{e_R}} =  \frac{\bf\vec{r}}{\Vert{\bf\vec{r} }\Vert}
        \label{eq_2}
        \end{equation}
        </span>

        The second vector of the basis can be obtained by the cross multiplication between <span class="notranslate">$\bf\hat{k}$</span> and <span class="notranslate">$\bf\hat{e_R}$</span>:
        <br><br>

        <span class="notranslate">
        \begin{equation}
        {\bf\hat{e_\theta}} = {\bf\hat{k}} \times {\bf\hat{e_R}}
        \label{eq_3}
        \end{equation}
        </span>


        The third vector of the basis is the conventional ${\bf\hat{k}}$ vector.

        <figure><img src="https://github.com/BMClab/BMC/blob/master/images/polarCoorderetheta.png?raw=1" width=500/><figcaption><center><i>Figure. Position vector of a particle moving in a circular path and a time-varying basis></figure>  
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        This basis can be used also for non-circular movements. For a 3D movement, the versor <span class="notranslate">${\bf\hat{e_R}}$</span> is obtained by removing the projection of the vector <span class="notranslate">${\bf\vec{r}}$</span> onto the versor <span class="notranslate">${\bf\hat{k}}$</span>:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        {\bf\hat{e_R}} =  \frac{\bf\vec{r} - ({\bf\vec{r}.{\bf\hat{k}}){\bf\hat{k}}}}{\Vert\bf\vec{r} - ({\bf\vec{r}.{\bf\hat{k}}){\bf\hat{k}}\Vert}}
        \label{eq_4}
        \end{equation}
        </span>

        <figure><img src="https://github.com/BMClab/BMC/blob/master/images/polarCilindrical.png?raw=1" width=500/><figcaption><center><i>Figure. Position vector of a moving particle and a time-varying basis></figure>  

        Note that if the movement is on a plane, the expression above is equal to <span class="notranslate">${\bf\hat{e_R}} =  \frac{\bf\vec{r}}{\Vert{\bf\vec{r} }\Vert}$</span> since the projection of <span class="notranslate">$\bf\vec{r}$</span> on the <span class="notranslate">$\bf\hat{k}$</span> versor is zero.
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Time-derivative of the versors ${\bf\hat{e_R}}$ and ${\bf\hat{e_\theta}}$

        To obtain the expressions of the velocity and acceleration vectors, it is necessary to obtain the expressions of the time-derivative of the vectors <span class="notranslate">${\bf\hat{e_R}}$</span> and <span class="notranslate">${\bf\hat{e_\theta}}$</span>.

        This can be done by noting that:
        <br>
        <br>
        <span class="notranslate">
        \begin{align}
        {\bf\hat{e_R}} &= \cos(\theta){\bf\hat{i}} + \sin(\theta){\bf\hat{j}}\\
        {\bf\hat{e_\theta}} &= -\sin(\theta){\bf\hat{i}} + \cos(\theta){\bf\hat{j}}
        \label{eq_5}
        \end{align}
        </span>

        Deriving <span class="notranslate">${\bf\hat{e_R}}$</span> we obtain:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        \frac{d{\bf\hat{e_R}}}{dt} = -\sin(\theta)\dot\theta{\bf\hat{i}} + \cos(\theta)\dot\theta{\bf\hat{j}} = \dot{\theta}{\bf\hat{e_\theta}}
        \label{eq_6}
        \end{equation}
        </span>

        Similarly, we obtain the time-derivative of <span class="notranslate">${\bf\hat{e_\theta}}$</span>:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
            \frac{d{\bf\hat{e_\theta}}}{dt} = -\cos(\theta)\dot\theta{\bf\hat{i}} - \sin(\theta)\dot\theta{\bf\hat{j}} = -\dot{\theta}{\bf\hat{e_R}}
        \label{eq_7}
        \end{equation}
        </span>

        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Position, velocity and acceleration
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ### Position

        The position vector <span class="notranslate">$\bf\vec{r}$</span>, from the definition of <span class="notranslate">$\bf\hat{e_R}$</span>, is:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        {\bf\vec{r}} = R{\bf\hat{e_R}} + z{\bf\hat{k}}
        \label{eq_8}
        \end{equation}
        </span>

        where <span class="notranslate">$R = \Vert\bf\vec{r} - ({\bf\vec{r}.{\bf\hat{k}}){\bf\hat{k}}\Vert}$</span>.
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ### Velocity

        The velocity vector <span class="notranslate">$\bf\vec{v}$</span> is obtained by deriving the vector <span class="notranslate">$\bf\vec{r}$</span>:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        {\bf\vec{v}} = \frac{d(R{\bf\hat{e_R}})}{dt} + \dot{z}{\bf\hat{k}} = \dot{R}{\bf\hat{e_R}}+R\frac{d\bf\hat{e_R}}{dt}=\dot{R}{\bf\hat{e_R}}+R\dot{\theta}{\bf\hat{e_\theta}}+ \dot{z}{\bf\hat{k}}
        \label{eq_9}
        \end{equation}
        </span>
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ### Acceleration

        The acceleration vector <span class="notranslate">$\bf\vec{a}$</span> is obtained by deriving the velocity vector:
        <br>
        <br>
        <span class="notranslate">
        \begin{align}
        {\bf\vec{a}} =& \frac{d(\dot{R}{\bf\hat{e_R}}+R\dot{\theta}{\bf\hat{e_\theta}}+\dot{z}{\bf\hat{k}})}{dt}=\\\nonumber
            =&\ddot{R}{\bf\hat{e_R}}+\dot{R}\frac{d\bf\hat{e_R}}{dt} + \dot{R}\dot{\theta}{\bf\hat{e_\theta}} + R\ddot{\theta}{\bf\hat{e_\theta}} + R\dot{\theta}\frac{d{\bf\hat{e_\theta}}}{dt} + \ddot{z}{\bf\hat{k}}=\\\nonumber
            =&\ddot{R}{\bf\hat{e_R}}+\dot{R}\dot{\theta}{\bf\hat{e_\theta}} + \dot{R}\dot{\theta}{\bf\hat{e_\theta}} + R\ddot{\theta}{\bf\hat{e_\theta}} - R\dot{\theta}^2{\bf\hat{e_R}}+ \ddot{z}{\bf\hat{k}} =\\
            =&\ddot{R}{\bf\hat{e_R}}+2\dot{R}\dot{\theta}{\bf\hat{e_\theta}}+ R\ddot{\theta}{\bf\hat{e_\theta}} - {R}\dot{\theta}^2{\bf\hat{e_R}}+ \ddot{z}{\bf\hat{k}} =\\\nonumber
            =&(\ddot{R}-R\dot{\theta}^2){\bf\hat{e_R}}+(2\dot{R}\dot{\theta} + R\ddot{\theta}){\bf\hat{e_\theta}}+ \ddot{z}{\bf\hat{k}}\nonumber
        \label{eq_10}
        \end{align}
        </span>

        + The term <span class="notranslate">$\ddot{R}$</span> is  an acceleration in the radial direction.

        + The term <span class="notranslate">$R\ddot{\theta}$</span> is an angular acceleration.

        + The term <span class="notranslate">$\ddot{z}$</span> is an acceleration in the $\bf\hat{k}$ direction.

        + The term <span class="notranslate">$-R\dot{\theta}^2$</span> is the well known centripetal acceleration.

        + The term <span class="notranslate">$2\dot{R}\dot{\theta}$</span> is known as Coriolis acceleration. This term may be difficult to understand intuitively. It appears when there is displacement in the radial and angular directions at the same time.
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Important to note

        The reader must bear in mind that the use of a different basis to represent the position, velocity or acceleration vectors is only a different representation of the same vector. For example, for the acceleration vector:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        {\bf\vec{a}} = \ddot{x}{\bf\hat{i}}+ \ddot{y}{\bf\hat{j}} + \ddot{z}{\bf\hat{k}}=(\ddot{R}-R\dot{\theta}^2){\bf\hat{e_R}}+(2\dot{R}\dot{\theta} + R\ddot{\theta}){\bf\hat{e_\theta}} + \ddot{z}{\bf\hat{k}}=\dot{\Vert\bf\vec{v}\Vert}{\bf\hat{e}_t}+\frac{{\Vert\bf\vec{v}\Vert}^2}{\rho}{\bf\hat{e}_n}
        \label{eq_11}
        \end{equation}
        </span>

        In which the last equality is the acceleration vector represented in the path-coordinate of the particle (see http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Time-varying%20frames.ipynb).
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Example

        Consider a particle following the spiral path described below:
        <br>
        <br>
        <span class="notranslate">
        \begin{equation}
        {\bf\vec{r}}(t) = \left(2\sqrt{t}\cos(t)\right){\bf\hat{i}}+ \left(2\sqrt{t}\sin(t)\right){\bf\hat{j}}
        \label{eq_12}
        \end{equation}
        </span>
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ### Solving numerically
        """
    )
    return


@app.cell
def _(np):
    t = np.linspace(0.01,10,30).reshape(-1,1) #create a time vector and reshapes it to a column vector
    R = 2*np.sqrt(t)
    theta = t
    rx = R*np.cos(theta)
    ry = R*np.sin(theta)
    r = np.hstack((rx, ry)) # creates the position vector by stacking rx and ry horizontally
    return R, r, t, theta


@app.cell
def _(np, r):
    e_r = r/np.linalg.norm(r, axis=1, keepdims=True) # defines e_r vector
    e_theta = np.cross([0,0,1],e_r)[:,0:-1] # defines e_theta vector
    return e_r, e_theta


@app.cell
def _(R, e_r, e_theta, np, t, theta):
    dt = t[1,0] #defines delta_t
    Rdot = np.gradient(R, dt, axis=0) #find the R derivative
    thetaDot = np.gradient(theta, dt, axis=0) #find the angle derivative
    v = Rdot*e_r +R*thetaDot*e_theta # find the linear velocity.
    return Rdot, dt, thetaDot, v


@app.cell
def _(Rdot, dt, np, thetaDot):
    Rddot = np.gradient(Rdot, dt, axis=0)
    thetaddot = np.gradient(thetaDot, dt, axis=0)
    return Rddot, thetaddot


@app.cell
def _(R, Rddot, Rdot, e_r, e_theta, thetaDot, thetaddot):
    a = ((Rddot - R*thetaDot**2)*e_r
         + (2*Rdot*thetaDot + R*thetaddot)*e_theta)
    return (a,)


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        The versors <span class="notranslate">$\bf\hat{e_R}$, $\bf\hat{e_\theta}$</span> are plotted below at some points of the path.
        """
    )
    return


@app.cell
def _(e_r, e_theta, np, plt, r, t):
    from matplotlib.patches import FancyArrowPatch
    plt.rcParams['figure.figsize'] = (8, 8)
    _fig = plt.figure()
    _ax = _fig.add_axes([0, 0, 1, 1])
    plt.plot(r[:, 0], r[:, 1], '.')
    for _i in np.arange(len(t) - 2):
        _vec1 = FancyArrowPatch(r[_i, :], r[_i, :] + e_r[_i, :], mutation_scale=30, color='r', label='e_r')
        _vec2 = FancyArrowPatch(r[_i, :], r[_i, :] + e_theta[_i, :], mutation_scale=30, color='g', label='e_theta')
        _ax.add_artist(_vec1)
        _ax.add_artist(_vec2)
    plt.xlim((-10, 10))
    plt.ylim((-10, 10))
    plt.grid()
    plt.legend([_vec1, _vec2], ['$\\vec{e_r}$', '$\\vec{e_{\\theta}}$'])
    plt.show()
    return (FancyArrowPatch,)


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        The velocity and acceleleration of the particle are plotted below at some points of the path.
        """
    )
    return


@app.cell
def _(FancyArrowPatch, a, np, plt, r, t, v):
    plt.rcParams['figure.figsize'] = (8, 8)
    _fig = plt.figure()
    plt.plot(r[:, 0], r[:, 1], '.')
    _ax = _fig.add_axes([0, 0, 1, 1])
    for _i in np.arange(len(t) - 2):
        _vec1 = FancyArrowPatch(r[_i, :], r[_i, :] + v[_i, :], mutation_scale=10, color='r')
        _vec2 = FancyArrowPatch(r[_i, :], r[_i, :] + a[_i, :], mutation_scale=10, color='g')
        _ax.add_artist(_vec1)
        _ax.add_artist(_vec2)
    plt.xlim((-10, 10))
    plt.ylim((-10, 10))
    plt.grid()
    plt.legend([_vec1, _vec2], ['$\\vec{v}$', '$\\vec{a}$'])
    plt.show()
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ### Solved symbolically (extra reading)
        """
    )
    return


@app.cell
def _(sym):
    O = sym.vector.CoordSys3D(' ')
    t_1 = sym.symbols('t')
    return O, t_1


@app.cell
def _(O, sym, t_1):
    r_1 = 2 * sym.sqrt(t_1) * sym.cos(t_1) * O.i + 2 * sym.sqrt(t_1) * sym.sin(t_1) * O.j
    r_1
    return (r_1,)


@app.cell
def _(O, plot_parametric, r_1, t_1):
    plot_parametric(r_1.dot(O.i), r_1.dot(O.j), (t_1, 0, 10))
    return


@app.cell
def _(O, r_1, sym):
    e_r_1 = sym.simplify(r_1 / sym.sqrt(r_1.dot(O.i) ** 2 + r_1.dot(O.j) ** 2 + r_1.dot(O.k) ** 2))
    e_r_1
    return (e_r_1,)


@app.cell
def _(O, e_r_1):
    e_theta_1 = O.k.cross(e_r_1)
    e_theta_1
    return (e_theta_1,)


@app.cell
def _(FancyArrowPatch, O, e_r_1, e_theta_1, np, plt, r_1, t_1):
    plt.rcParams['figure.figsize'] = (8, 8)
    _fig = plt.figure()
    _ax = _fig.add_axes([0, 0, 1, 1])
    _ax.axis('on')
    _time = np.linspace(0, 10, 30)
    for _instant in _time:
        _vt = FancyArrowPatch([float(r_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant))], [float(r_1.dot(O.i).subs(t_1, _instant)) + float(e_r_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant)) + float(e_r_1.dot(O.j).subs(t_1, _instant))], mutation_scale=20, arrowstyle='->', color='r', label='${{e_r}}$')
        _vn = FancyArrowPatch([float(r_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant))], [float(r_1.dot(O.i).subs(t_1, _instant)) + float(e_theta_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant)) + float(e_theta_1.dot(O.j).subs(t_1, _instant))], mutation_scale=20, arrowstyle='->', color='g', label='${{e_{theta}}}$')
        _ax.add_artist(_vn)
        _ax.add_artist(_vt)
    plt.xlim((-10, 10))
    plt.ylim((-10, 10))
    plt.legend(handles=[_vt, _vn], fontsize=20)
    plt.grid()
    plt.show()
    return


@app.cell
def _(sym, t_1):
    R_1 = 2 * sym.sqrt(t_1)
    return (R_1,)


@app.cell
def _(R_1, sym, t_1):
    Rdot_1 = sym.diff(R_1, t_1)
    Rddot_1 = sym.diff(Rdot_1, t_1)
    theta_1 = t_1
    thetadot = sym.diff(theta_1, t_1)
    thetaddot_1 = sym.diff(thetadot, t_1)
    thetaddot_1
    return Rddot_1, Rdot_1, thetaddot_1, thetadot


@app.cell
def _(R_1, Rdot_1, e_r_1, e_theta_1, thetadot):
    v_1 = Rdot_1 * e_r_1 + R_1 * thetadot * e_theta_1
    return (v_1,)


@app.cell
def _(v_1):
    v_1
    return


@app.cell
def _(R_1, Rddot_1, Rdot_1, e_r_1, e_theta_1, thetaddot_1, thetadot):
    a_1 = (Rddot_1 - R_1 * thetadot ** 2) * e_r_1 + (2 * Rdot_1 * thetadot + R_1 * thetaddot_1) * e_theta_1
    aCor = 2 * Rdot_1 * thetadot * e_theta_1
    aCor
    return aCor, a_1


@app.cell
def _(a_1):
    a_1
    return


@app.cell
def _(FancyArrowPatch, O, aCor, a_1, np, plt, r_1, t_1, v_1):
    plt.rcParams['figure.figsize'] = (8, 8)
    _fig = plt.figure()
    _ax = _fig.add_axes([0, 0, 1, 1])
    _ax.axis('on')
    _time = np.linspace(0.1, 10, 30)
    for _instant in _time:
        _vt = FancyArrowPatch([float(r_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant))], [float(r_1.dot(O.i).subs(t_1, _instant)) + float(v_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant)) + float(v_1.dot(O.j).subs(t_1, _instant))], mutation_scale=20, arrowstyle='->', color='r', label='${{v}}$')
        _vn = FancyArrowPatch([float(r_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant))], [float(r_1.dot(O.i).subs(t_1, _instant)) + float(a_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant)) + float(a_1.dot(O.j).subs(t_1, _instant))], mutation_scale=20, arrowstyle='->', color='g', label='${{a}}$')
        vc = FancyArrowPatch([float(r_1.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant))], [float(r_1.dot(O.i).subs(t_1, _instant)) + float(aCor.dot(O.i).subs(t_1, _instant)), float(r_1.dot(O.j).subs(t_1, _instant)) + float(aCor.dot(O.j).subs(t_1, _instant))], mutation_scale=20, arrowstyle='->', color='b', label='${{a_{Cor}}}$')
        _ax.add_artist(_vn)
        _ax.add_artist(_vt)
        _ax.add_artist(vc)
    plt.xlim((-10, 10))
    plt.ylim((-10, 10))
    plt.legend(handles=[_vt, _vn, vc], fontsize=20)
    plt.grid()
    plt.show()
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Further reading

        - Read pages 916-931 of the 18th chapter of the [Ruina and Rudra's book] (http://ruina.tam.cornell.edu/Book/index.html) about polar coordinates.
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Video lectures on the Internet

        - Khan Academy:
            - [Polar coordinates 1](https://www.youtube.com/watch?v=B5dOy4m6I1E)
            - [Polar coordinates 2](https://www.youtube.com/watch?v=1pQXJuWbz9w)
            - [Polar coordinates 3](https://www.youtube.com/watch?v=roSG9V3zApM)  
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Problems

        1. Find the polar basis (using the computer) for a projectile motion of a particle following the parametric equation below:

        <span class="notranslate">
        \begin{equation}
        \vec{r}(t) = (10t-51){\bf{\hat{i}}} + \left(-\frac{9,81}{2}t^2+50t+100\right){\bf{\hat{j}}}
        \label{eq_13}
        \end{equation}
        </span>

        2. Problems from 15.1.1 to 15.1.14 from Ruina and Rudra's book,
        3. Problems from 18.1.1 to 18.1.8 and 18.1.10 from Ruina and Rudra's book.
        4. Solve the problems 1.19 and 1.20 from Rade's book.
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ## Reference

        - Ruina A, Rudra P (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""


        ---


        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        ![Screenshot from 2023-10-25 22-09-10.png](data:image/png;base64,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)
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        *Antes de tudo, os dois itens expressam a posição da partícula em termos de coordenadas radial e ângulo, então precisamos utilizar coordenadas polares e um referencial baseado neste sistema de coordenadas como descrito na aula [Polar and Cylindrical Frame of Reference](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/PolarBasis.ipynb).*  

        No livro do Ruina, quando tem um asterisco no problema, significa que uma solução (sem dedução) é apresentada no final do livro (neste caso, p. 1203).  
        Neste caso tem apenas a solução para o plot 2:

         a) $\quad \vec{v} = \dfrac{\dot{\theta}}{b}\hat{e}_r+\dfrac{\theta\dot{\theta}}{b}\hat{e}_t $

         b) $\quad x=r\cos(br) \quad$ e $\quad y=r\sin(br) \quad$ ou $\quad x=\dfrac{\theta}{b}\cos(\theta) \quad$ e $\quad y=\dfrac{\theta}{b}\sin(\theta) $
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        Dedução para plot 2, que por curiosidade é conhecida como [espiral de Arquimedes](https://en.wikipedia.org/wiki/Archimedean_spiral):

        Velocidade da partícula em termos de $b, \theta, \dot{\theta}$:

        Do enunciado sabe-se que $\theta=br$, que é uma expressão para a magnitude das grandezas, e pede-se a velocidade, que é uma grandeza vetorial.  
        Então não podemos simplesmente calcular a derivada da expressão para $r$, temos que expressar a velocidade num referencial, que neste caso o mais simples é um referencial polar.

        Do notebook [Polar and Cylindrical Frame of Reference](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/PolarBasis.ipynb), sabe-se que a expressão para velocidade em um referencial polar é:  

        $$\vec{v}=\dot{r}\hat{e}_r+r\dot{\theta}{\hat{e}_\theta}$$

        Como $r=\frac{\theta}{b}$, é necessário calcular a derivada de $r$ e colocá-la na expressão acima (no notebook, a base tangencial é referida pelo subscrito '$\theta$' e o livro do Ruina usa '$t$', mas é a mesma coisa:

        $\dot{r}=\frac{\dot{\theta}}{b}$, então:

        $\vec{v}=\dfrac{\dot{\theta}}{b}\hat{e}_r+\dfrac{\theta\dot{\theta}}{b}\hat{e}_t$

        *A maior dificuldade desta parte é lembrar a expressão para a velocidade em termos do referencial polar ou cilíndrico.*  

        Expressões para as coordenadas $x$ e $y$:

        No enunciado do problema é mostrado um sistema de coordenadas cartesiano, $x$ na horizontal e $y$ na vertical.  
        Do enunciado sabe-se que $\theta=br$, então por simples trigonometria:  

        $x = r\cos(br) \quad$ e $\quad y = r\sin(br) \quad$ ou  

        $x=\dfrac{\theta}{b}\cos(\theta) \quad$ e $\quad y=\dfrac{\theta}{b}\sin(\theta)$
        """
    )
    return


@app.cell(hide_code=True)
def _(mo):
    mo.md(
        r"""
        Dedução para plot 1, que por curiosidade é conhecida como [espiral de Lituus](https://en.wikipedia.org/wiki/Lituus_(mathematics)):

        Agora $\quad r=\dfrac{b}{\sqrt{\theta}}$

        Repetindo-se os passos da dedução para o plot 2, chega-se na solução:

         a) $\quad \vec{v} = -\dfrac{b\dot{\theta}}{2\theta^{3/2}}\hat{e}_r+\dfrac{b\dot{\theta}}{\sqrt{\theta}}\hat{e}_t $

         b) $\quad x=r\cos(\dfrac{b^2}{r^2}) \quad$ e $\quad y=r\sin(\dfrac{b^2}{r^2}) \quad$ ou $\quad x=\dfrac{b}{\sqrt{\theta}}\cos(\theta) \quad$ e $\quad y=\dfrac{b}{\sqrt{\theta}}\sin(\theta) $

        *Talvez agora a maior dificuldade é lembrar de como se calcula a derivada temporal de $r=\dfrac{b}{\sqrt{\theta}}$. Lembre que quem depende do tempo é $\theta$, então é necessário usar a regra da cadeia (como foi feito para o plot 2).*

        """
    )
    return


@app.cell
def _():
    import marimo as mo
    return (mo,)


if __name__ == "__main__":
    app.run()
