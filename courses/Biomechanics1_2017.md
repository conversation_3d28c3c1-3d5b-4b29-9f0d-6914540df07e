# Biomechanics I (v. 2017)  


### Instructors  
- <PERSON>
- <PERSON><PERSON>


## Lecture Schedule

### Lecture 1   
- [Introduction](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/Notebooks/Biomechanics.ipynb)

### Lecture 2
- [Python for scientific Computing](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/Notebooks/PythonForScientificComputing.ipynb)
-[Python Tutorial](https://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/PythonTutorial.ipynb)


### Lecture 3
- [Particle kinematics](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/KinematicsParticle.ipynb)
- [Reference frames](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/ReferenceFrame.ipynb)
- [Scalar and vectors](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/ScalarVector.ipynb)

### Lecture 4

- [Time-varying frames](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/Notebooks/Time-varying%20frames.ipynb)
- [Projectile motion](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/ProjectileMotion.ipynb)
 
### Lecture 5 
- [Polar coordinates](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/PolarCoordinates.ipynb)
- [Angular kinematics](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KinematicsAngular2D.ipynb)

### Lecture 6
- [Kinematic chain](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KinematicChain.ipynb)

### Lecture 7
- [Kinematics of rigid body](https://github.com/BMClab/bmc/blob/master/notebooks/KinematicsOfRigidBody.ipynb)

### Lecture 8
- [Transformation 2D](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Transformation2D.ipynb)

### Lecture 9
- [Transformation 3D](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Transformation3D.ipynb)
