# Biomechanics II (v. 2018)   

### Instructors   
- <PERSON>  
- <PERSON><PERSON>  

## Lecture Schedule  

### Lecture 1    
- [Introduction](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Biomechanics.ipynb)  
- [Python Tutorial](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/PythonForScientificComputing.ipynb)  

### Lecture 2  
- [Fundamental Concepts (Newton Laws)](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/KineticsFundamentalConcepts.ipynb)  
- [Newton Law for particles](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/newtonLawForParticles.ipynb)  

### Lecture 3  
- [Vertical jump](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/VerticalJump.ipynb)  

   **Technical report** 1.Mechanical modeling of the problem. 2. Graphs from experimental data (ground reaction force) and acceleration, velocity, vertical displacement and mechanical power of the subjects gravity center. 3. Jump phases identification. 4. Estimate of the jump height using the time of flight and linear momentum variation.

### Lecture 4   
- [Free body diagram to particles](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/FBDParticles.ipynb)  

### Lecture 5  
- Problems  11.1.16, 11.1.19, 11.1.24, 11.1.29, 11.1.30, 11.1.31(a,b,d) 11.1.32 from [[1]](http://ruina.tam.cornell.edu/Book/).

### Lecture 6  
- Problems 3.3.9, 3.3.20, 10.1.6, 12.1.6(a,b,c,d,f), 12.1.7, 12.1.10 (a,b) from [[1]](http://ruina.tam.cornell.edu/Book/).

### Lecture 7  
  Exam (answers [here](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/ProvaIBiomecanicaII2018.ipynb), in Portuguese)

### Lecture 8  
- [Newton-Euler equations for rigid bodies](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/newton_euler_equations.ipynb)

### Lecture 9  
- [Center of mass and moment of inertia](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/CenterOfMassAndMomentOfInertia.ipynb)  

### Lecture 10  
- [Body segment parameters](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/BodySegmentParameters.ipynb)   

### Lecture 11  
- [Free body diagram for rigid bodies](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/FreeBodyDiagramForRigidBodies.ipynb)  

### Lecture 12
- Gait analysis
 **Technical report** 1. Mechanical modeling of the problem 2. Compute joint angles of the ankle and  knee at the three planes. 3. Compute the joint forces and torques at the knee and ankle at the three planes. 4 Plot these curves and show the code used to perform the computing.

### Lecture 13
- Problems  3.2.5, 3.2.7, 11.2.1, 11.2..2, 11.2.9, 11.2.11, 11.2.21 , 15.3.11, 15.3.15, 16.2.9, 18.3.26, 18.3.29 from [[1]  Ruina, A. and Pratap R., Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/) 
- Problem 2 from [Newton-Euler equations for rigid bodies](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/newton_euler_equations.ipynb).
- Problems 1 to 5 from [Body segment parameters](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/BodySegmentParameters.ipynb).
- Problems 6, 7 and 8 from [http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/FreeBodyDiagram.ipynb](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/FreeBodyDiagram.ipynb).


### Lecture 14
- [Rigid body dynamics: 2D gait analysis](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/GaitAnalysis2D.ipynb)  

### Lecture 15
- [3D Kinetics](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/Tridimensional%20rigid%20body%20Kinetics.ipynb)

### Lecture 16
- [Lagrangian mechanics](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/lagrangian_mechanics.ipynb)

### Lecture 17
- [Lagrangian mechanics](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/lagrangian_mechanics.ipynb)

### Lecture 18
- [Matrix formalism for multibody dynamics](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/MatrixFormalism.ipynb)

### Lecture 19 

- Problems from the [Lagrangian mechanics](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/lagrangian_mechanics.ipynb) and [Matrix formalism for multibody dynamics](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/MatrixFormalism.ipynb) notebooks.


## References   
- [[1]  Ruina, A. and Pratap R., Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/)  
