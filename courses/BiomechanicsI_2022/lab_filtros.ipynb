{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Uso de filtros\n", "\n", "<PERSON><PERSON>"], "metadata": {"id": "oU73wk0XV2YI"}}, {"cell_type": "code", "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C5LFPUSb0SAQ", "outputId": "e8a14e5e-21c5-4127-ddd8-f7102e97c541"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Nv7_9I0LOrYE"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd"]}, {"cell_type": "markdown", "source": ["## Abertura do arquivo"], "metadata": {"id": "xDWAsnEfV9xw"}}, {"cell_type": "code", "source": ["dado = pd.read_csv('/content/drive/MyDrive/massa_A.txt', \n", "            sep='\\t', \n", "            skiprows=1)\n", "dado"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "B2UgSz59Pmgg", "outputId": "fb9375d7-647c-427e-bf03-9971e931041b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["         t             x      y\n", "0    0.000  9.480000e-01 -1.042\n", "1    0.046  9.470000e-01 -0.999\n", "2    0.055  9.200000e-01 -0.920\n", "3    0.063  9.250000e-01 -0.882\n", "4    0.071  8.950000e-01 -0.795\n", "..     ...           ...    ...\n", "121  1.046  2.449000e-02 -1.156\n", "122  1.055  1.361000e-02 -1.189\n", "123  1.063  1.088000e-02 -1.233\n", "124  1.071  1.110000e-16 -1.273\n", "125  1.080 -5.442000e-03 -1.320\n", "\n", "[126 rows x 3 columns]"], "text/html": ["\n", "  <div id=\"df-8324eeda-4785-44ef-84a3-7839fef139d3\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>t</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.000</td>\n", "      <td>9.480000e-01</td>\n", "      <td>-1.042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.046</td>\n", "      <td>9.470000e-01</td>\n", "      <td>-0.999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.055</td>\n", "      <td>9.200000e-01</td>\n", "      <td>-0.920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.063</td>\n", "      <td>9.250000e-01</td>\n", "      <td>-0.882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.071</td>\n", "      <td>8.950000e-01</td>\n", "      <td>-0.795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>1.046</td>\n", "      <td>2.449000e-02</td>\n", "      <td>-1.156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td>1.055</td>\n", "      <td>1.361000e-02</td>\n", "      <td>-1.189</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>1.063</td>\n", "      <td>1.088000e-02</td>\n", "      <td>-1.233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>1.071</td>\n", "      <td>1.110000e-16</td>\n", "      <td>-1.273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>1.080</td>\n", "      <td>-5.442000e-03</td>\n", "      <td>-1.320</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>126 rows × 3 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-8324eeda-4785-44ef-84a3-7839fef139d3')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-8324eeda-4785-44ef-84a3-7839fef139d3 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-8324eeda-4785-44ef-84a3-7839fef139d3');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 48}]}, {"cell_type": "code", "source": ["plt.plot(dado['x'], dado['y'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "5U2RxWubQdaH", "outputId": "8731e7fd-4abd-42bd-cde1-45d5a300772e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b654c50>]"]}, "metadata": {}, "execution_count": 49}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON>do de amostragem"], "metadata": {"id": "is01RPeOWBRg"}}, {"cell_type": "code", "source": ["dt = dado['t'].at[2] - dado['t'].at[1]\n", "dt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7vVX_tvyVfoB", "outputId": "d900677e-99ee-4935-8612-602d8e7bf0fe"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.009000000000000001"]}, "metadata": {}, "execution_count": 50}]}, {"cell_type": "markdown", "source": ["## Computação da derivada numérica utilizando for"], "metadata": {"id": "wvTZlUbAWIgH"}}, {"cell_type": "code", "source": ["dxdt_1 = np.zeros(len(dado))\n", "for i in range(len(dado)-1):\n", "  dxdt_1[i] = (dado['x'].at[i+1] \n", "               - dado['x'].at[i])/dt\n", "plt.plot(dado['t'], dxdt_1)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "-cbcCg3vX9z8", "outputId": "50770b33-2b84-4c6d-dd60-4381884f6217"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b642e10>]"]}, "metadata": {}, "execution_count": 51}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## Computação da derivada numérica utilizando `np.diff`"], "metadata": {"id": "DfULipeVWOU5"}}, {"cell_type": "code", "source": ["dxdt_2 = np.diff(dado['x'])/dt\n", "plt.plot(dado['t'].values[0:-1],dxdt_2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "YUhc0IMwYXLC", "outputId": "03d48ce6-3893-47be-b85d-ddadf192b4cd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b5ba6d0>]"]}, "metadata": {}, "execution_count": 52}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## Computação da derivada numérica com o método das diferenças centrais (`np.gradient`)"], "metadata": {"id": "pj8My3S_WUp4"}}, {"cell_type": "code", "source": ["dxdt_3 = np.gradient(dado['x'], dt)\n", "plt.plot(dado['t'], dxdt_3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "1HIF90nLZe_L", "outputId": "d44e43dd-3054-4c2d-9fb6-43d835d7c9cd"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b530550>]"]}, "metadata": {}, "execution_count": 53}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## Cálculo das velocidades e acelerações"], "metadata": {"id": "Z1iVI569Wgqw"}}, {"cell_type": "code", "source": ["dxdt = np.gradient(dado['x'], dt)\n", "dydt = np.gradient(dado['y'], dt)\n", "d2xdt2 = np.gradient(dxdt, dt)\n", "d2ydt2 = np.gradient(dydt, dt)\n", "\n", "plt.plot(dado['t'], d2ydt2)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "Zte1oB8natKV", "outputId": "c8d1b90c-2501-44bf-e897-5c93c2a3e572"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b4a93d0>]"]}, "metadata": {}, "execution_count": 54}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "code", "source": ["d2ydt2_cut = d2ydt2[10:]\n", "t_cut = dado['t'].values[10:]\n", "plt.plot(t_cut, d2ydt2_cut)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "Fw8R4nURbifQ", "outputId": "28295f1a-55d5-44c3-c3e7-618fe2ecc393"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b4197d0>]"]}, "metadata": {}, "execution_count": 55}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## Média dos valores da aceleração na direção vertical para estimar a aceleração da gravidade"], "metadata": {"id": "IHEwXVFXWpZw"}}, {"cell_type": "code", "source": ["np.mean(d2ydt2_cut)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kmlB3DhScL04", "outputId": "7e2d94da-29d4-4e09-9d9b-d9cda77ec885"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["-8.461047254150726"]}, "metadata": {}, "execution_count": 56}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON>, com for"], "metadata": {"id": "8LFt3yiL2rge"}}, {"cell_type": "code", "source": ["x_mm = np.zeros(len(dado))\n", "x_mm[0:3] = dado['x'].values[0:3]\n", "\n", "for i in range(3, len(dado)):\n", "  x_mm[i] = (dado['x'].at[i] +\n", "             dado['x'].at[i-1] +\n", "             dado['x'].at[i-2] +\n", "             dado['x'].at[i-3])/4\n", "\n", "plt.plot(dado['t'], dado['x'])            \n", "plt.plot(dado['t'], x_mm)\n", "# plt.xlim(0.2, 0.4)\n", "# plt.ylim(0.6, 0.8)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "uksGJJOl2qSy", "outputId": "b09c75cf-6b06-4433-b438-28373b386105"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7fac4b3947d0>]"]}, "metadata": {}, "execution_count": 57}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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*****************************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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON>, usando a função lfilter"], "metadata": {"id": "00fGAG686j08"}}, {"cell_type": "code", "source": ["from scipy.signal import lfilter, filtfilt, butter\n", "b = [0.25, 0.25, 0.25, 0.25]\n", "a = [1, 0, 0, 0]\n", "x_mm_2 = lfilter(b, a, dado['x'])                               \n", "plt.plot(dado['t'], dado['x'])            \n", "plt.plot(dado['t'], x_mm, color='red')\n", "plt.plot(dado['t'], x_mm_2, color='magenta')\n", "plt.xlim(0.2, 0.4)\n", "plt.ylim(0.6, 0.8)"], "metadata": {"id": "45reC2JwcXmJ", "colab": {"base_uri": "https://localhost:8080/", "height": 287}, "outputId": "4e4fb260-c192-4974-bf90-2e7eee236c47"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(0.6, 0.8)"]}, "metadata": {}, "execution_count": 67}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON>, usando a função filtfilt"], "metadata": {"id": "a295y0av6ohe"}}, {"cell_type": "code", "source": ["x_mm_3 = filtfilt(b, a, dado['x'])                               \n", "plt.plot(dado['t'], dado['x'])            \n", "plt.plot(dado['t'], x_mm, color='red')\n", "plt.plot(dado['t'], x_mm_2, color='magenta')\n", "plt.plot(dado['t'], x_mm_3, color='green')\n", "plt.xlim(0.2, 0.4)\n", "plt.ylim(0.6, 0.8)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 287}, "id": "0e0JZwwC6Q6P", "outputId": "93d1481a-a526-46ab-bd75-224d031c4dda"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(0.6, 0.8)"]}, "metadata": {}, "execution_count": 60}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "code", "source": ["y_mm_2 = lfilter(b, a, dado['y'])                               \n", "y_mm_3 = filtfilt(b, a, dado['y'])                               \n", "plt.plot(dado['t'], dado['y'])            \n", "\n", "plt.plot(dado['t'], y_mm_2, color='magenta')\n", "plt.plot(dado['t'], y_mm_3, color='green')\n", "plt.xlim(0.2, 0.8)\n", "plt.ylim(-0.4, 0.3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 287}, "id": "X1cXp2BcAw-3", "outputId": "55c7bdc6-956e-44b4-9249-0ead54e579c9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(-0.4, 0.3)"]}, "metadata": {}, "execution_count": 65}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON>"], "metadata": {"id": "WzbkIONm6t-H"}}, {"cell_type": "code", "source": ["fc = 10\n", "fs = 1/dt\n", "fn = fs/2\n", "b, a = butter(N=3, Wn=fc/fn)\n", "y_mm_4 = filtfilt(b, a, dado['y']) \n", "plt.plot(dado['t'], dado['y']) \n", "plt.plot(dado['t'], y_mm_2, color='magenta')\n", "plt.plot(dado['t'], y_mm_3, color='green')\n", "plt.plot(dado['t'], y_mm_4, color='red')\n", "plt.xlim(0, 0.8)\n", "plt.ylim(0.2, 0.3)                              "], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 287}, "id": "UDq7V4xYCH2-", "outputId": "54ab3207-7c45-4b23-c4df-b44c4cc25dab"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(0.2, 0.3)"]}, "metadata": {}, "execution_count": 75}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "code", "source": ["fc = 5\n", "b, a = butter(N=1, Wn=fc/fn)\n", "x_filtrado = filtfilt(b, a, dado['x']) \n", "y_filtrado = filtfilt(b, a, dado['y']) \n", "\n", "dxdt_filtrado = np.gradient(x_filtrado, dt)\n", "dydt_filtrado = np.gradient(y_filtrado, dt)\n", "d2xdt2_filtrado = np.gradient(dxdt_filtrado, dt)\n", "d2ydt2_filtrado = np.gradient(dydt_filtrado, dt)\n", "\n", "plt.plot(dado['t'], d2ydt2)\n", "plt.plot(dado['t'], d2ydt2_filtrado)\n", "print(d2ydt2_filtrado.mean())\n", "print(d2ydt2.mean())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 302}, "id": "8A1Mtl3_F816", "outputId": "fb07d8ac-dfec-40ee-cb88-08dcba9a083d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["-8.206551710522366\n", "-8.107975700568316\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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************************************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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON><PERSON><PERSON> da frequência ótima utilizando o método dos resíduos"], "metadata": {"id": "oTSamHwe6ys3"}}, {"cell_type": "code", "source": ["!pip install optcutfreq\n", "from optcutfreq import optcutfreq\n", "fc_opt = optcutfreq(dado['y'], freq=fs)\n", "fc_opt"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1LFRk4XiH74m", "outputId": "811aa907-5ac7-4330-fef4-ada078f2527e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Requirement already satisfied: optcutfreq in /usr/local/lib/python3.7/dist-packages (0.0.8)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["4.945917329281164"]}, "metadata": {}, "execution_count": 84}]}, {"cell_type": "code", "source": [], "metadata": {"id": "QBhVsOAqIiug"}, "execution_count": null, "outputs": []}]}