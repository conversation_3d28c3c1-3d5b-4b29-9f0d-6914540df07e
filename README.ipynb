{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BMC - Notes on Scientific Computing for Biomechanics and Motor Control\n", "\n", "<PERSON> and <PERSON><PERSON>\n", "\n", "This repository is a collection of lecture notes and code on scientific computing and data analysis for Biomechanics and Motor Control.  \n", "These notes (notebooks) are written using [Jupyter Notebook](http://jupyter.org/), part of the [Python ecosystem for scientific computing]( http://scipy.org/).\n", "\n", "## Introduction\n", "\n", "* [Biomechanics](./notebooks/Biomechanics.ipynb)  \n", "* [The Biomechanics and Motor Control Laboratory @ UFABC](./notebooks/BMClab.ipynb)  \n", "\n", "## Scientific programming\n", "\n", "* [Python for scientific computing](./notebooks/PythonForScientificComputing.ipynb)  \n", "* [Python tutorial](./notebooks/PythonTutorial.ipynb)\n", "* [Version control with Git and GitHub](./notebooks/VersionControlGitGitHub.ipynb)  \n", "* [Code structure for data analysis](./notebooks/CodeStructure.ipynb)  \n", "\n", "## Numerical data analysis\n", "\n", "* [Scalar and vector](./notebooks/ScalarVector.ipynb)  \n", "* [Basic trigonometry](./notebooks/TrigonometryBasics.ipynb)  \n", "* [Matrix](./notebooks/Matrix.ipynb)  \n", "* [Descriptive statistics](./notebooks/Statistics-Descriptive.ipynb)  \n", "* [Bayesian Statistics](./notebooks/statistics_bayesian.ipynb)  \n", "* [Confidence and prediction intervals](./notebooks/ConfidencePredictionIntervals.ipynb)  \n", "  * [Prediction ellipse and prediction ellipsoid](./notebooks/PredictionEllipseEllipsoid.ipynb)  \n", "* [Curve fitting](./notebooks/CurveFitting.ipynb)  \n", "  * [Polynomial fitting](./notebooks/PolynomialFitting.ipynb)  \n", "* [Propagation of uncertainty](./notebooks/PropagationUncertainty.ipynb)  \n", "* Frequency analysis  \n", "  * [Basic properties of signals](./notebooks/SignalBasicProperties.ipynb)  \n", "  * [Fourier series](./notebooks/FourierSeries.ipynb)\n", "  * [Fourier transform](./notebooks/FourierTransform.ipynb)\n", "* [Data filtering in signal processing](./notebooks/DataFiltering.ipynb)  \n", "  * [Residual analysis for the optimal cutoff frequency](./notebooks/ResidualAnalysis.ipynb)  \n", "* [Ordinary Differential Equation](./notebooks/OrdinaryDifferentialEquation.ipynb)  \n", "* [Optimization](./notebooks/Optimization.ipynb)  \n", "* Change detection  \n", "  * [Detection of peaks](./notebooks/DetectPeaks.ipynb)  \n", "  * [Detection of onset](./notebooks/DetectOnset.ipynb)  \n", "  * [Detection of changes using the Cumulative Sum (CUSUM)](./notebooks/DetectCUSUM.ipynb)  \n", "  * [Detection of sequential data](./notebooks/detect_seq.ipynb)  \n", "* [Time normalization of data](./notebooks/TimeNormalization.ipynb)  \n", "* [Ensemble average](./notebooks/EnsembleAverage.ipynb)  \n", "* [Select data vectors by similarity using a metric score](./notebooks/Similarity.ipynb)  \n", "* [Open files in C3D format](./notebooks/OpenC3Dfile.ipynb)  \n", "\n", "## Mechanics\n", "\n", "### Kinematics\n", "\n", "* [Frame of reference](./notebooks/ReferenceFrame.ipynb)  \n", "* [Time-varying frame of reference](./notebooks/PathFrame.ipynb)\n", "* [Polar and cylindrical frame of reference](./notebooks/PolarBasis.ipynb)\n", "* [Kinematics of particle](./notebooks/KinematicsParticle.ipynb)  \n", "  * [Projectile motion](./notebooks/ProjectileMotion.ipynb)  \n", "  * [Spatial and temporal characteristics](./notebooks/SpatialTemporalCharacteristcs.ipynb)  \n", "  * [Minimum jerk hypothesis](./notebooks/MinimumJerkHypothesis.ipynb)  \n", "* [Kinematics of Rigid Body](./notebooks/KinematicsOfRigidBody.ipynb)  \n", "  * [Angular kinematics (2D)](./notebooks/KinematicsAngular2D.ipynb)  \n", "  * [Kinematic chain](./notebooks/KinematicChain.ipynb)  \n", "  * [Rigid-body transformations (2D)](./notebooks/Transformation2D.ipynb)  \n", "  * [Rigid-body transformations (3D)](./notebooks/Transformation3D.ipynb)  \n", "  * [Determining rigid body transformation using the SVD algorithm](./notebooks/SVDalgorithm.ipynb)  \n", "\n", "### Kinetics\n", "\n", "* [Fundamental concepts](./notebooks/KineticsFundamentalConcepts.ipynb)  \n", "* [Center of Mass and Moment of Inertia](./notebooks/CenterOfMassAndMomentOfInertia.ipynb)  \n", "* [Newton Laws for particles](./notebooks/newtonLawForParticles.ipynb)\n", "* [Newton-Euler Laws](./notebooks/newton_euler_equations.ipynb)\n", "* [Free body diagram](./notebooks/FreeBodyDiagram.ipynb)\n", "  * [Free body diagram for particles](./notebooks/FBDParticles.ipynb)\n", "  * [Free body diagram for rigid bodies](./notebooks/FreeBodyDiagramForRigidBodies.ipynb)\n", "* [3D Rigid body kinetics](./notebooks/Kinetics3dRigidBody.ipynb)\n", "* [Matrix formalism of the Newton-Euler equations](./notebooks/MatrixFormalism.ipynb)  \n", "* [Lagrangian Mechanics](./notebooks/lagrangian_mechanics.ipynb)  \n", "\n", "## Modeling and simulation of human movement\n", "\n", "* [Body segment parameters](./notebooks/BodySegmentParameters.ipynb)\n", "* [Muscle modeling](./notebooks/MuscleModeling.ipynb)  \n", "* [Muscle simulation](./notebooks/MuscleSimulation.ipynb)  \n", "* [Musculoskeletal modeling and simulation](./notebooks/MusculoskeletaModelingSimulation.ipynb)  \n", "* [Multibody dynamics of simple biomechanical models](./notebooks/MultibodyDynamics.ipynb)  \n", "\n", "## Biomechanical tasks Analysis\n", "\n", "* [The inverted pendulum model of the human standing posture](./notebooks/IP_Model.ipynb)\n", "* [Measurements in stabilography](./notebooks/Stabilography.ipynb)  \n", "* [Rambling and Trembling decomposition of the COP](./notebooks/IEP.ipynb)  \n", "* [Biomechanical analysis of vertical jumps](./notebooks/VerticalJump.ipynb)  \n", "* [Gait analysis (2D)](./notebooks/GaitAnalysis2D.ipynb)  \n", "* Force plates  \n", "  * [Kistler force plate calculation](./notebooks/KistlerForcePlateCalculation.ipynb)  \n", "  * [Zebris pressure platform](./notebooks/ReadZebrisPressurePlatformASCIIfiles.ipynb)  \n", "\n", "## Electromyography\n", "\n", "* [Introduction to data analysis in electromyography](./notebooks/Electromyography.ipynb)  \n", "\n", "## How to cite this work\n", "\n", "Here is a suggestion to cite this GitHub repository:\n", "\n", "> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (2018) Notes on Scientific Computing for Biomechanics and Motor Control. GitHub repository, <https://github.com/BMClab/BMC>.\n", "\n", "And a possible BibTeX entry:\n", "\n", "```tex\n", "@misc{Duarte2018,  \n", "    author = {<PERSON><PERSON>, M. and <PERSON>, R.N.},\n", "    title = {Notes on Scientific Computing for Biomechanics and Motor Control},  \n", "    year = {2018},  \n", "    publisher = {GitHub},  \n", "    journal = {GitHub repository},  \n", "    howpublished = {\\url{https://github.com/BMClab/BMC}}  \n", "}\n", "```\n", "\n", "## License\n", "\n", "The non-software content of this project is licensed under a [Creative Commons Attribution 4.0 International License](http://creativecommons.org/licenses/by/4.0/), and the software code is licensed under the [MIT license](https://opensource.org/licenses/mit-license.php).\n"]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}