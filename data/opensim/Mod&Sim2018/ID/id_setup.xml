<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="30000">
	<InverseDynamicsTool name="subject_scale_run">
		<!--Directory used for writing results.-->
		<results_directory>H:\OpenSim\Mod&amp;Sim2018\ID\results</results_directory>
		<!--Directory for input files-->
		<input_directory />
		<!--Name of the .osim file used to construct a model.-->
		<model_file>Unassigned</model_file>
		<!--Time range over which the inverse dynamics problem is solved.-->
		<time_range> 0 0.72</time_range>
		<!--List of forces by individual or grouping name (e.g. All, actuators, muscles, ...) to be excluded when computing model dynamics.-->
		<forces_to_exclude> Muscles</forces_to_exclude>
		<!--XML file (.xml) containing the external loads applied to the model as a set of ExternalForce(s).-->
		<external_loads_file>Unassigned</external_loads_file>
		<!--The name of the file containing coordinate data. Can be a motion (.mot) or a states (.sto) file.-->
		<coordinates_file>../IK/results/ik_output_run.mot</coordinates_file>
		<!--Low-pass cut-off frequency for filtering the coordinates_file data (currently does not apply to states_file or speeds_file). A negative value results in no filtering. The default value is -1.0, so no filtering.-->
		<lowpass_cutoff_frequency_for_coordinates>6</lowpass_cutoff_frequency_for_coordinates>
		<!--Name of the storage file (.sto) to which the generalized forces are written.-->
		<output_gen_force_file>inverse_dynamics.sto</output_gen_force_file>
		<!--List of joints (keyword All, for all joints) to report body forces acting at the joint frame expressed in ground.-->
		<joints_to_report_body_forces />
		<!--Name of the storage file (.sto) to which the body forces at specified joints are written.-->
		<output_body_forces_file>body_forces_at_joints.sto</output_body_forces_file>
	</InverseDynamicsTool>
</OpenSimDocument>
