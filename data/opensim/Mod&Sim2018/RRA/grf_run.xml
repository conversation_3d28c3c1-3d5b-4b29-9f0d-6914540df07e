<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="30000">
	<ExternalLoads name="Ex2">
		<defaults>
			<ControlLinear name="default">
				<is_model_control>true</is_model_control>
				<extrapolate>true</extrapolate>
				<default_min>-1</default_min>
				<default_max>1</default_max>
				<filter_on>false</filter_on>
				<use_steps>false</use_steps>
				<x_nodes />
				<min_nodes />
				<max_nodes />
				<kp>100</kp>
				<kv>20</kv>
			</ControlLinear>
			<CoordinateActuator name="default">
				<!--Flag indicating whether the force is disabled or not. Disabled means that the force is not active in subsequent dynamics realizations.-->
				<isDisabled>false</isDisabled>
				<!--Minimum allowed value for control signal. Used primarily when solving for control values.-->
				<min_control>-Inf</min_control>
				<!--Maximum allowed value for control signal. Used primarily when solving for control values.-->
				<max_control>Inf</max_control>
				<!--Name of the generalized coordinate to which the actuator applies.-->
				<coordinate>Unassigned</coordinate>
				<!--The maximum generalized force produced by this actuator.-->
				<optimal_force>300</optimal_force>
			</CoordinateActuator>
			<PointActuator name="default">
				<!--Flag indicating whether the force is disabled or not. Disabled means that the force is not active in subsequent dynamics realizations.-->
				<isDisabled>false</isDisabled>
				<!--Minimum allowed value for control signal. Used primarily when solving for control values.-->
				<min_control>-Inf</min_control>
				<!--Maximum allowed value for control signal. Used primarily when solving for control values.-->
				<max_control>Inf</max_control>
				<!--Name of Body to which this actuator is applied.-->
				<body></body>
				<!--Location of application point; in body frame unless point_is_global=true-->
				<point>0 0 0</point>
				<!--Interpret point in Ground frame if true; otherwise, body frame.-->
				<point_is_global>false</point_is_global>
				<!--Force application direction; in body frame unless force_is_global=true.-->
				<direction>-1 -0 -0</direction>
				<!--Interpret direction in Ground frame if true; otherwise, body frame.-->
				<force_is_global>true</force_is_global>
				<!--The maximum force produced by this actuator when fully activated.-->
				<optimal_force>1000</optimal_force>
			</PointActuator>
			<TorqueActuator name="default">
				<!--Flag indicating whether the force is disabled or not. Disabled means that the force is not active in subsequent dynamics realizations.-->
				<isDisabled>false</isDisabled>
				<!--Minimum allowed value for control signal. Used primarily when solving for control values.-->
				<min_control>-Inf</min_control>
				<!--Maximum allowed value for control signal. Used primarily when solving for control values.-->
				<max_control>Inf</max_control>
				<!--Name of Body to which the torque actuator is applied.-->
				<bodyA>Unassigned</bodyA>
				<!--Name of Body to which the equal and opposite torque is applied.-->
				<bodyB>Unassigned</bodyB>
				<!--Interpret axis in Ground frame if true; otherwise, body A's frame.-->
				<torque_is_global>true</torque_is_global>
				<!--Fixed direction about which torque is applied, in Ground or body A frame depending on 'torque_is_global' property.-->
				<axis>-1 -0 -0</axis>
				<!--The maximum torque produced by this actuator when fully activated.-->
				<optimal_force>1</optimal_force>
			</TorqueActuator>
			<Thelen2003Muscle name="default">
				<!--Flag indicating whether the force is disabled or not. Disabled means that the force is not active in subsequent dynamics realizations.-->
				<isDisabled>false</isDisabled>
				<!--Minimum allowed value for control signal. Used primarily when solving for control values.-->
				<min_control>0.01</min_control>
				<!--Maximum allowed value for control signal. Used primarily when solving for control values.-->
				<max_control>1</max_control>
				<!--The set of points defining the path of the muscle.-->
				<GeometryPath>
					<!--The set of points defining the path-->
					<PathPointSet>
						<objects />
						<groups />
					</PathPointSet>
					<!--The wrap objecs that are associated with this path-->
					<PathWrapSet>
						<objects />
						<groups />
					</PathWrapSet>
					<!--Used to display the path in the 3D window-->
					<VisibleObject name="display">
						<!--Set of geometry files and associated attributes, allow .vtp, .stl, .obj-->
						<GeometrySet>
							<objects />
							<groups />
						</GeometrySet>
						<!--Three scale factors for display purposes: scaleX scaleY scaleZ-->
						<scale_factors> 1 1 1</scale_factors>
						<!--transform relative to owner specified as 3 rotations (rad) followed by 3 translations rX rY rZ tx ty tz-->
						<transform> 0 0 0 0 0 0</transform>
						<!--Whether to show a coordinate frame-->
						<show_axes>false</show_axes>
						<!--Display Pref. 0:Hide 1:Wire 3:Flat 4:Shaded Can be overriden for individual geometries-->
						<display_preference>4</display_preference>
					</VisibleObject>
				</GeometryPath>
				<!--The maximum force this actuator can produce.-->
				<optimal_force>1</optimal_force>
				<!--Maximum isometric force that the fibers can generate-->
				<max_isometric_force>0</max_isometric_force>
				<!--Optimal length of the muscle fibers-->
				<optimal_fiber_length>0</optimal_fiber_length>
				<!--Resting length of the tendon-->
				<tendon_slack_length>0</tendon_slack_length>
				<!--Angle between tendon and fibers at optimal fiber length expressed in radians-->
				<pennation_angle_at_optimal>0</pennation_angle_at_optimal>
				<!--Maximum contraction velocity of the fibers, in optimal fiberlengths/second-->
				<max_contraction_velocity>10</max_contraction_velocity>
				<!--time constant for ramping up muscle activation-->
				<activation_time_constant>0.01</activation_time_constant>
				<!--time constant for ramping down of muscle activation-->
				<deactivation_time_constant>0.04</deactivation_time_constant>
				<!--tendon strain at maximum isometric muscle force-->
				<FmaxTendonStrain>0.033</FmaxTendonStrain>
				<!--passive muscle strain at maximum isometric muscle force-->
				<FmaxMuscleStrain>0.6</FmaxMuscleStrain>
				<!--shape factor for Gaussian active muscle force-length relationship-->
				<KshapeActive>0.5</KshapeActive>
				<!--exponential shape factor for passive force-length relationship-->
				<KshapePassive>4</KshapePassive>
				<!--force-velocity shape factor-->
				<Af>0.3</Af>
				<!--maximum normalized lengthening force-->
				<Flen>1.8</Flen>
			</Thelen2003Muscle>
			<CMC_Joint name="default">
				<!--Flag (true or false) indicating whether or not a task is enabled.-->
				<on>false</on>
				<!--Weight with which a task is tracked relative to other tasks. To track a task more tightly, make the weight larger.-->
				<weight> 1 1 1</weight>
				<!--Name of body frame with respect to which a tracking objective is specified. The special name 'center_of_mass' refers to the system center of mass. This property is not used for tracking joint angles.-->
				<wrt_body>-1</wrt_body>
				<!--Name of body frame in which the tracking objectives are expressed.  This property is not used for tracking joint angles.-->
				<express_body>-1</express_body>
				<!--Array of 3 flags (each true or false) specifying whether a component of a task is active.  For example, tracking the trajectory of a point in space could have three components (x,y,z).  This allows each of those to be made active (true) or inactive (false).  A task for tracking a joint coordinate only has one component.-->
				<active>false false false </active>
				<!--Position error feedback gain (stiffness). To achieve critical damping of errors, choose kv = 2*sqrt(kp).-->
				<kp> 1 1 1</kp>
				<!--Velocity error feedback gain (damping). To achieve critical damping of errors, choose kv = 2*sqrt(kp).-->
				<kv> 1 1 1</kv>
				<!--Feedforward acceleration gain.  This is normally set to 1.0, so no gain.-->
				<ka> 1 1 1</ka>
				<!--Direction vector[3] for component 0 of a task. Joint tasks do not use this propery.-->
				<r0> 0 0 0</r0>
				<!--Direction vector[3] for component 1 of a task. Joint tasks do not use this property.-->
				<r1> 0 0 0</r1>
				<!--Direction vector[3] for component 2 of a task. Joint tasks do not use this property.-->
				<r2> 0 0 0</r2>
				<!--Name of the coordinate to be tracked.-->
				<coordinate />
				<!--Error limit on the tracking accuracy for this coordinate. If the tracking errors approach this limit, the weighting for this coordinate is increased. -->
				<limit>0</limit>
			</CMC_Joint>
		</defaults>
		<objects>
			<ExternalForce name="RightFoot_ExternalForce">
				<!--Flag indicating whether the force is disabled or not. Disabled means that the force is not active in subsequent dynamics realizations.-->
				<isDisabled>false</isDisabled>
				<!--Name of the body the force is applied to.-->
				<applied_to_body>calcn_r</applied_to_body>
				<!--Name of the body the force is expressed in (default is ground).-->
				<force_expressed_in_body>ground</force_expressed_in_body>
				<!--Name of the body the point is expressed in (default is ground).-->
				<point_expressed_in_body>ground</point_expressed_in_body>
				<!--Identifier (string) to locate the force to be applied in the data source.-->
				<force_identifier>R_ground_force_v</force_identifier>
				<!--Identifier (string) to locate the point to be applied in the data source.-->
				<point_identifier>R_ground_force_p</point_identifier>
				<!--Identifier (string) to locate the torque to be applied in the data source.-->
				<torque_identifier>R_ground_torque_</torque_identifier>
				<!--Name of the data source (Storage) that will supply the force data.-->
				<data_source_name>Unassigned</data_source_name>
			</ExternalForce>
			<ExternalForce name="LeftFoot_ExternalForce">
				<!--Flag indicating whether the force is disabled or not. Disabled means that the force is not active in subsequent dynamics realizations.-->
				<isDisabled>false</isDisabled>
				<!--Name of the body the force is applied to.-->
				<applied_to_body>calcn_l</applied_to_body>
				<!--Name of the body the force is expressed in (default is ground).-->
				<force_expressed_in_body>ground</force_expressed_in_body>
				<!--Name of the body the point is expressed in (default is ground).-->
				<point_expressed_in_body>ground</point_expressed_in_body>
				<!--Identifier (string) to locate the force to be applied in the data source.-->
				<force_identifier>L_ground_force_v</force_identifier>
				<!--Identifier (string) to locate the point to be applied in the data source.-->
				<point_identifier>L_ground_force_p</point_identifier>
				<!--Identifier (string) to locate the torque to be applied in the data source.-->
				<torque_identifier>L_ground_torque_</torque_identifier>
				<!--Name of the data source (Storage) that will supply the force data.-->
				<data_source_name>Unassigned</data_source_name>
			</ExternalForce>
		</objects>
		<groups />
		<!--Storage file (.sto) containing (3) components of force and/or torque and point of application.Note: this file overrides the data source specified by the individual external forces if specified.-->
		<datafile>D:\Reginaldo\OpenSim\Mod&amp;Sim2018\ExpData\grf_run.mot</datafile>
		<!--Optional motion file (.mot) or storage file (.sto) containing the model kinematics used to transform a point expressed in ground to the body of force application.If the point is not expressed in ground, the point is not transformed-->
		<external_loads_model_kinematics_file />
		<!--Optional low-pass cut-off frequency for filtering the model kinematics corresponding used to transform the point of application. A negative value results in no filtering. The default value is -1.0, so no filtering.-->
		<lowpass_cutoff_frequency_for_load_kinematics>15</lowpass_cutoff_frequency_for_load_kinematics>
	</ExternalLoads>
</OpenSimDocument>
