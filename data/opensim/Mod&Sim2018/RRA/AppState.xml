<?xml version="1.0" encoding="UTF-8"?>
<java version="1.7.0_80" class="java.beans.XMLDecoder">
 <object class="org.opensim.utils.ApplicationState" id="ApplicationState0">
  <void property="stateObjects">
   <void method="put">
    <string>MotionsDB</string>
    <object class="org.opensim.view.motions.MotionsDBDescriptor"/>
   </void>
   <void method="put">
    <string>ViewDB</string>
    <object class="org.opensim.view.pub.ViewDBDescriptor" id="ViewDBDescriptor0">
     <void property="cameraAttributes">
      <void method="add">
       <array class="double" length="13">
        <void index="0">
         <double>4.555541174059531</double>
        </void>
        <void index="1">
         <double>0.39971328601962947</double>
        </void>
        <void index="2">
         <double>-0.23541969291558945</double>
        </void>
        <void index="3">
         <double>1.3271710300702466</double>
        </void>
        <void index="4">
         <double>0.6091751239467773</double>
        </void>
        <void index="5">
         <double>0.3219302019830872</double>
        </void>
        <void index="6">
         <double>0.0688735304761511</double>
        </void>
        <void index="7">
         <double>0.9973336627816465</double>
        </void>
        <void index="8">
         <double>0.02412471517542621</double>
        </void>
        <void index="9">
         <double>0.9834146159165577</double>
        </void>
        <void index="10">
         <double>-0.06380551910313605</double>
        </void>
        <void index="11">
         <double>-0.16977794006780875</double>
        </void>
        <void index="12">
         <double>45.91793810758514</double>
        </void>
       </array>
      </void>
     </void>
     <void property="viewNames">
      <void method="add">
       <string>View0</string>
      </void>
     </void>
    </object>
   </void>
   <void method="put">
    <string>OpenSimDB</string>
    <object class="org.opensim.view.pub.OpenSimDBDescriptor"/>
   </void>
   <void method="put">
    <string>PluginsDB</string>
    <object class="org.opensim.view.pub.PluginsDB"/>
   </void>
   <void method="put">
    <string>CoordinateViewer</string>
    <object class="org.opensim.coordinateviewer.CoordinateViewerDescriptor"/>
   </void>
  </void>
 </object>
</java>
