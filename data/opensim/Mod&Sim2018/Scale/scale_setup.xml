<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="30000">
	<ScaleTool name="subject_scale_run">
		<!--Mass of the subject in kg.  Subject-specific model generated by scaling step will have this total mass.-->
		<mass>70</mass>
		<!--Height of the subject in mm.  For informational purposes only (not used by scaling).-->
		<height>1800</height>
		<!--Age of the subject in years.  For informational purposes only (not used by scaling).-->
		<age>25</age>
		<!--Notes for the subject.-->
		<notes>Unassigned</notes>
		<!--Specifies the name of the unscaled model (.osim) and the marker set.-->
		<GenericModelMaker>
			<!--Model file (.osim) for the unscaled model.-->
			<model_file>Unassigned</model_file>
			<!--Set of model markers used to scale the model. Scaling is done based on distances between model markers compared to the same distances between the corresponding experimental markers.-->
			<marker_set_file>Unassigned</marker_set_file>
		</GenericModelMaker>
		<!--Specifies parameters for scaling the model.-->
		<ModelScaler>
			<!--Whether or not to use the model scaler during scale-->
			<apply>true</apply>
			<!--Specifies the scaling method and order. Valid options are 'measurements', 'manualScale', singly or both in any sequence.-->
			<scaling_order> measurements</scaling_order>
			<!--Specifies the measurements by which body segments are to be scaled.-->
			<MeasurementSet name="marker_measurements">
				<objects>
					<Measurement name="torso">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RASI CLAV</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LASI CLAV</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LPSI C7</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RPSI C7</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RASI RACR</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LASI LACR</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="torso">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="pelvis_X">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RASI RPSI</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LASI LPSI</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="pelvis">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="pelvis_Y">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LPSI LHJC</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RPSI RHJC</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RASI RHJC</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LASI LHJC</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="pelvis">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> Y</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="pelvis_z">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RPSI LPSI</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RASI LASI</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="pelvis">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="thigh">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LHJC LLFC</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LHJC LMFC</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RHJC RMFC</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RHJC RLFC</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="femur_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="femur_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="shank">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LLFC LLMAL</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LMFC LMMAL</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RLFC RLMAL</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RMFC RMMAL</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="tibia_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="tibia_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="foot">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LCAL LMT5</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LCAL LTOE</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RCAL RTOE</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RCAL RMT5</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="talus_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="calcn_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="toes_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="talus_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="calcn_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="toes_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="humerus">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LSJC LMEL</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LSJC LLEL</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RSJC RLEL</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RSJC RMEL</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="humerus_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="humerus_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="radius_ulna">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LLEL LFAradius</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LMEL LFAulna</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RMEL RFAulna</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RLEL RFAradius</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="ulna_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="radius_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="hand_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="ulna_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="radius_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="hand_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
				</objects>
				<groups />
			</MeasurementSet>
			<!--Scale factors to be used for manual scaling.-->
			<ScaleSet name="manual_scale">
				<objects />
				<groups />
			</ScaleSet>
			<!--TRC file (.trc) containing the marker positions used for measurement-based scaling. This is usually a static trial, but doesn't need to be.  The marker-pair distances are computed for each time step in the TRC file and averaged across the time range.-->
			<marker_file>../ExpData/static_run.trc</marker_file>
			<!--Time range over which to average marker-pair distances in the marker file (.trc) for measurement-based scaling.-->
			<time_range> 0 3.96</time_range>
			<!--Flag (true or false) indicating whether or not to preserve relative mass between segments.-->
			<preserve_mass_distribution>true</preserve_mass_distribution>
			<!--Name of OpenSim model file (.osim) to write when done scaling.-->
			<output_model_file>Unassigned</output_model_file>
			<!--Name of file to write containing the scale factors that were applied to the unscaled model (optional).-->
			<output_scale_file>scaleSet_applied_run.xml</output_scale_file>
		</ModelScaler>
		<!--Specifies parameters for placing markers on the model once a model is scaled. -->
		<MarkerPlacer>
			<!--Whether or not to use the marker placer during scale-->
			<apply>true</apply>
			<!--Task set used to specify weights used in the IK computation of the static pose.-->
			<IKTaskSet name="scale_ik_run">
				<objects>
					<IKMarkerTask name="RACR">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LACR">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="C7">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>250</weight>
					</IKMarkerTask>
					<IKMarkerTask name="CLAV">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>250</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RASH">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>10</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RPSH">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>10</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LASH">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>10</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LPSH">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>10</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RSJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>10</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RLEL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RMEL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RFAradius">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RFAulna">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LSJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>10</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LLEL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LMEL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LFAradius">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LFAulna">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RASI">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LASI">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RPSI">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LPSI">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LHJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RHJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RLFC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RMFC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RKJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RLMAL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RMMAL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RAJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RCAL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>25</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RTOE">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>25</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RMT5">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>25</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LLFC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LMFC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LKJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LLMAL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LMMAL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>50</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LAJC">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>false</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>100</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LCAL">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>25</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LTOE">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>25</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LMT5">
						<!--Whether or not this task will be used during inverse kinematics solve.-->
						<apply>true</apply>
						<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
						<weight>25</weight>
					</IKMarkerTask>
				</objects>
				<groups />
			</IKTaskSet>
			<!--TRC file (.trc) containing the time history of experimental marker positions (usually a static trial).-->
			<marker_file>../ExpData/static_run.trc</marker_file>
			<!--Name of file containing the joint angles used to set the initial configuration of the model for the purpose of placing the markers. These coordinate values can also be included in the optimization problem used to place the markers. Before the model markers are placed, a single frame of an inverse kinematics (IK) problem is solved. The IK problem can be solved simply by matching marker positions, but if the model markers are not in the correct locations, the IK solution will not be very good and neither will marker placement. Alternatively, coordinate values (specified in this file) can be specified and used to influence the IK solution. This is valuable particularly if you have high confidence in the coordinate values. For example, you know for the static trial the subject was standing will all joint angles close to zero. If the coordinate set (see the CoordinateSet property) contains non-zero weights for coordinates, the IK solution will try to match not only the marker positions, but also the coordinates in this file. Least-squared error is used to solve the IK problem. -->
			<coordinate_file>Unassigned</coordinate_file>
			<!--Time range over which the marker positions are averaged.-->
			<time_range> 0 3.96</time_range>
			<!--Name of the motion file (.mot) written after marker relocation (optional).-->
			<output_motion_file>scale_output_run.mot</output_motion_file>
			<!--Output OpenSim model file (.osim) after scaling and maker placement.-->
			<output_model_file>subject_scaled_run.osim</output_model_file>
			<!--Output marker set containing the new marker locations after markers have been placed.-->
			<output_marker_file>Unassigned</output_marker_file>
			<!--Maximum amount of movement allowed in marker data when averaging frames of the static trial. A negative value means there is not limit.-->
			<max_marker_movement>-1</max_marker_movement>
		</MarkerPlacer>
	</ScaleTool>
</OpenSimDocument>
