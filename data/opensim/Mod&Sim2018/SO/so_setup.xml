<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="30000">
	<AnalyzeTool name="subject_so">
		<!--Name of the .osim file used to construct a model.-->
		<model_file />
		<!--Replace the model's force set with sets specified in <force_set_files>? If false, the force set is appended to.-->
		<replace_force_set>false</replace_force_set>
		<!--List of xml files used to construct an force set for the model.-->
		<force_set_files> so_actuators.xml</force_set_files>
		<!--Directory used for writing results.-->
		<results_directory>results</results_directory>
		<!--Output precision.  It is 8 by default.-->
		<output_precision>8</output_precision>
		<!--Initial time for the simulation.-->
		<initial_time>0</initial_time>
		<!--Final time for the simulation.-->
		<final_time>0.72</final_time>
		<!--Flag indicating whether or not to compute equilibrium values for states other than the coordinates or speeds.  For example, equilibrium muscle fiber lengths or muscle forces.-->
		<solve_for_equilibrium_for_auxiliary_states>false</solve_for_equilibrium_for_auxiliary_states>
		<!--Maximum number of integrator steps.-->
		<maximum_number_of_integrator_steps>20000</maximum_number_of_integrator_steps>
		<!--Maximum integration step size.-->
		<maximum_integrator_step_size>1</maximum_integrator_step_size>
		<!--Minimum integration step size.-->
		<minimum_integrator_step_size>1e-008</minimum_integrator_step_size>
		<!--Integrator error tolerance. When the error is greater, the integrator step size is decreased.-->
		<integrator_error_tolerance>1e-005</integrator_error_tolerance>
		<!--Set of analyses to be run during the investigation.-->
		<AnalysisSet name="Analyses">
			<objects>
				<StaticOptimization name="StaticOptimization">
					<!--Flag (true or false) specifying whether whether on. True by default.-->
					<on>true</on>
					<!--Start time.-->
					<start_time>0</start_time>
					<!--End time.-->
					<end_time>0.72</end_time>
					<!--Specifies how often to store results during a simulation. More specifically, the interval (a positive integer) specifies how many successful integration steps should be taken before results are recorded again.-->
					<step_interval>1</step_interval>
					<!--Flag (true or false) indicating whether the results are in degrees or not.-->
					<in_degrees>true</in_degrees>
					<!--If true, the model's own force set will be used in the static optimization computation.  Otherwise, inverse dynamics for coordinate actuators will be computed for all unconstrained degrees of freedom.-->
					<use_model_force_set>true</use_model_force_set>
					<!--A double indicating the exponent to raise activations to when solving static optimization.  -->
					<activation_exponent>2</activation_exponent>
					<!--If true muscle force-length curve is observed while running optimization.-->
					<use_muscle_physiology>true</use_muscle_physiology>
					<!--Value used to determine when the optimization solution has converged-->
					<optimizer_convergence_criterion>0.0001</optimizer_convergence_criterion>
					<!--An integer for setting the maximum number of iterations the optimizer can use at each time.  -->
					<optimizer_max_iterations>100</optimizer_max_iterations>
				</StaticOptimization>
			</objects>
			<groups />
		</AnalysisSet>
		<!--Controller objects in the model.-->
		<ControllerSet name="Controllers">
			<objects />
			<groups />
		</ControllerSet>
		<!--XML file (.xml) containing the forces applied to the model as ExternalLoads.-->
		<external_loads_file>grf_setup.xml</external_loads_file>
		<!--Storage file (.sto) containing the time history of states for the model. This file often contains multiple rows of data, each row being a time-stamped array of states. The first column contains the time.  The rest of the columns contain the states in the order appropriate for the model. In a storage file, unlike a motion file (.mot), non-uniform time spacing is allowed.  If the user-specified initial time for a simulation does not correspond exactly to one of the time stamps in this file, inerpolation is NOT used because it is sometimes necessary to an exact set of states for analyses.  Instead, the closest earlier set of states is used.-->
		<states_file />
		<!--Motion file (.mot) or storage file (.sto) containing the time history of the generalized coordinates for the model. These can be specified in place of the states file.-->
		<coordinates_file>D:/Reginaldo/OpenSim/Mod&amp;Sim2018/SO/../IK/results/ik_output_run.mot</coordinates_file>
		<!--Storage file (.sto) containing the time history of the generalized speeds for the model. If coordinates_file is used in place of states_file, these can be optionally set as well to give the speeds. If not specified, speeds will be computed from coordinates by differentiation.-->
		<speeds_file />
		<!--Low-pass cut-off frequency for filtering the coordinates_file data (currently does not apply to states_file or speeds_file). A negative value results in no filtering. The default value is -1.0, so no filtering.-->
		<lowpass_cutoff_frequency_for_coordinates>6</lowpass_cutoff_frequency_for_coordinates>
	</AnalyzeTool>
</OpenSimDocument>
