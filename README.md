# Notes on Scientific Computing for Biomechanics and Motor Control

This repository is a collection of lecture notes and code on scientific computing and data analysis for Biomechanics and Motor Control.  
These notes (notebooks) are written using [Jupyter Notebook](http://org/), part of the [Python ecosystem for scientific computing]( http://scipy.org/).

[![Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/BMClab/BMC/blob/master/README.ipynb)  
[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/BMClab/BMC/master?filepath=README.ipynb) Classic Notebook  
[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/BMClab/BMC/master?labpath=README.ipynb) JupyterLab  
[![DOI](https://zenodo.org/badge/DOI/10.5281/zenodo.4599319.svg)](https://doi.org/10.5281/zenodo.4599319)  

## Introduction

* [Biomechanics](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Biomechanics.ipynb)  
* [The Biomechanics and Motor Control Laboratory @ UFABC](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/BMClab.ipynb)  

## Scientific programming

* [Python for scientific computing](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/PythonForScientificComputing.ipynb)  
* [Python tutorial](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/PythonTutorial.ipynb)
* [Version control with Git and GitHub](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/VersionControlGitGitHub.ipynb)  
* [Code structure for data analysis](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/CodeStructure.ipynb)  

## Numerical data analysis

* [Scalar and vector](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/ScalarVector.ipynb)  
* [Basic trigonometry](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/TrigonometryBasics.ipynb)  
* [Matrix](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Matrix.ipynb)  
* [Descriptive statistics](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Statistics-Descriptive.ipynb)  
* [Bayesian Statistics](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/statistics_bayesian.ipynb)
* Confidence and prediction intervals  
  * [One-dimensional intervals](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/ConfidencePredictionIntervals.ipynb)  
  * [Prediction ellipse (2D) and prediction ellipsoid (3D)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/PredictionEllipseEllipsoid.ipynb)  
* Curve fitting  
  * [Fundamentals of curve fitting](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/CurveFitting.ipynb)  
  * [Polynomial fitting](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/PolynomialFitting.ipynb)  
* [Propagation of uncertainty](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/PropagationUncertainty.ipynb)  
* Frequency analysis  
  * [Basic properties of signals](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/SignalBasicProperties.ipynb)  
  * [Fourier series](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/FourierSeries.ipynb)
  * [Fourier transform](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/FourierTransform.ipynb)
* Signal processing  
  * [Data filtering](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DataFiltering.ipynb)  
  * [Residual analysis for the optimal cutoff frequency](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/ResidualAnalysis.ipynb)  
* [Ordinary Differential Equation](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/OrdinaryDifferentialEquation.ipynb)  
* [Optimization](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Optimization.ipynb)  
* Change detection  
  * [Detection of peaks](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DetectPeaks.ipynb)  
  * [Detection of onset](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DetectOnset.ipynb)  
  * [Detection of changes using the Cumulative Sum (CUSUM)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DetectCUSUM.ipynb)  
  * [Detection of sequential data](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/detect_seq.ipynb)  
* [Time normalization of data](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/TimeNormalization.ipynb)  
* [Ensemble average](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/EnsembleAverage.ipynb)  
* [Select data vectors by similarity using a metric score](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Similarity.ipynb)  
* [Open files in C3D format](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/OpenC3Dfile.ipynb)  
* [Camera calibration and point reconstruction based on direct linear transformation (DLT)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DLT.ipynb)  

## Mechanics

### Kinematics

* [Frame of reference](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/ReferenceFrame.ipynb)  
* [Time-varying frame of reference](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/PathFrame.ipynb)
* [Polar and cylindrical frame of reference](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/PolarBasis.ipynb)
* Kinematics of particle  
  * [One-dimensional motion](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/KinematicsParticle.ipynb)  
  * [Projectile motion](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/ProjectileMotion.ipynb)  
  * [Spatial and temporal characteristics](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/SpatialTemporalCharacteristcs.ipynb)  
  * [Minimum jerk hypothesis](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/MinimumJerkHypothesis.ipynb)  
* Kinematics of Rigid Body  
  * [Angular kinematics (2D)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/KinematicsAngular2D.ipynb)  
  * [Kinematic chain](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/KinematicChain.ipynb)  
  * [Velocity and Acceleration of a point of a rigid body](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/KinematicsOfRigidBody.ipynb)  
  * [Rigid-body transformations (2D)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Transformation2D.ipynb)  
  * [Rigid-body transformations (3D)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Transformation3D.ipynb)  
  * [Determining rigid body transformation using the SVD algorithm](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/SVDalgorithm.ipynb)  
  * [Angular velocity in three-dimension movements](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/AngularVelocity3D.ipynb)

### Kinetics

* [Fundamental concepts](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/KineticsFundamentalConcepts.ipynb)  
* [Center of Mass and Moment of Inertia](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/CenterOfMassAndMomentOfInertia.ipynb) 
* [Newton Laws for particles](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/newtonLawForParticles.ipynb)
* [Newton-Euler Laws](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/newton_euler_equations.ipynb)
* [Free body diagram](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/FreeBodyDiagram.ipynb)
  * [Free body diagram for particles](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/FBDParticles.ipynb)
  * [Free body diagram for rigid bodies](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/FreeBodyDiagramForRigidBodies.ipynb)
* [3D Rigid body kinetics](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/Kinetics3dRigidBody.ipynb)
* [Matrix formalism of the Equations of Motion](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/MatrixFormalism.ipynb)  
* [Lagrangian Mechanics](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/lagrangian_mechanics.ipynb)  

## Modeling and simulation of human movement

* [Body segment parameters](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/BodySegmentParameters.ipynb)
* [Muscle modeling](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/MuscleModeling.ipynb)  
* [Muscle simulation](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/MuscleSimulation.ipynb)  
* [Musculoskeletal modeling and simulation](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/MusculoskeletaModelingSimulation.ipynb)  
* [Multibody dynamics of simple biomechanical models](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/MultibodyDynamics.ipynb)  

## Biomechanical tasks Analysis

* [The inverted pendulum model of the human standing posture](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/IP_Model.ipynb)
* [Measurements in stabilography](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Stabilography.ipynb)  
* [Rambling and Trembling decomposition of the COP](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/IEP.ipynb)  
* [Biomechanical analysis of vertical jumps](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/VerticalJump.ipynb)  
* [Gait analysis (2D)](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/GaitAnalysis2D.ipynb)  
* Force plates  
  * [Kistler force plate calculation](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/KistlerForcePlateCalculation.ipynb)  
  * [Zebris pressure platform](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/ReadZebrisPressurePlatformASCIIfiles.ipynb)  

## Electromyography

* [Introduction to data analysis in electromyography](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Electromyography.ipynb)  

## How to cite this work

Here is a suggestion to cite this GitHub repository:

> Marcos Duarte and Renato Naville Watanabe. (2021). Notes on Scientific Computing for Biomechanics and Motor Control (Version v0.0.2). Zenodo. http://doi.org/10.5281/zenodo.4599319

And a BibTeX entry:

```tex
@misc{marcos_duarte_2021_4599319,
  author       = {Marcos Duarte and Renato Naville Watanabe},
  title        = {{Notes on Scientific Computing for Biomechanics and Motor Control}},
  month        = mar,
  year         = 2021,
  publisher    = {Zenodo},
  version      = {v0.0.2},
  doi          = {10.5281/zenodo.4599319},
  url          = {https://doi.org/10.5281/zenodo.4599319}
}
```

## License

The non-software content of this project is licensed under a [Creative Commons Attribution 4.0 International License](http://creativecommons.org/licenses/by/4.0/), and the software code is licensed under the [MIT license](https://opensource.org/licenses/mit-license.php).
