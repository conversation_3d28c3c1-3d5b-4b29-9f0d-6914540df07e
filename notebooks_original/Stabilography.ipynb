{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Measurements in stabilography\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Posturography is a general term for all techniques concerned with quantifying postural sway of a standing person.  \n", "\n", "Typically in posturography, instead of measuring the actual sway of each segment, measurements of the whole body sway have been used. The displacements of center of pressure (COP) and of the vertical projection (on the horizontal plane) of the center of gravity (COGv) are the most common measures of body sway (however, keep in mind that the COP displacement is not an actual measurement of postural sway of the body or of any of its segments). While the COP displacement can be easily measured with a force plate, the direct measurement of the COGv is more complicated and typically subject to a larger error. The measurement of the COGv is computed by measuring the position of each body segment combined with the mass of each segment. More commonly, the COGv displacement is determined indirectly from the COP displacement and different methods are available for such, which produce similar results (<PERSON><PERSON><PERSON> et al. 2004).  See the notebook [The inverted pendulum model of the human standing posture](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/IP_Model.ipynb) for a description and code of one of these methods.\n", "\n", "It is possible to quantify a certain property of a phenomenon, like the postural control during upright standing, without any underlying scientific theory on what generated that property and what the implication of that measurement for the understanding of the phenomenon is. Sometimes, this is the only approach to start with. However, as our knowledge on the phenomenon advances, the need for a scientific theory or a hypothesis to interpret the results in a more meaningful way becomes evident. It is relatively easy to perform many measurements of the body sway during upright standing; far more difficult is to interpret those measurements to understand what they mean.   \n", "\n", "The most widespread empirical interpretation of typical measurements of posture sway is that more sway means more instability which is seen as an indication of a deteriorated postural control system. This rationale is based on many experiments on aging and pathological conditions that, indeed, have observed increased sway in those conditions. However, bear in mind that it might not always be the case.  \n", "\n", "Selecting which measurement will be used to quantify a certain characteristic of the postural control depends on which experiment we design and which task the individual under evaluation is performing. In this sense, posturography has been divided into two experimental paradigms: dynamic and static posturography. In static posturography, the individual is typically trying to stay as quiet as possible while his or her sway is measured, and no perturbation is applied during the task. In dynamic posturography, a momentary perturbation is applied and how the subject responded to that perturbation is measured.   \n", "\n", "Let's show some of the most common measures of postural sway, more typically employed in static posturography.   "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:13:38.519159Z", "start_time": "2017-12-30T09:13:38.271235Z"}}, "outputs": [], "source": ["import numpy as np\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:13:41.554034Z", "start_time": "2017-12-30T09:13:39.909819Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COP shape:  (4095, 2)\n"]}], "source": ["import pandas as pd  # use Pandas to read data from a website\n", "fileUrl = 'http://www.udel.edu/biology/rosewc/kaap686/reserve/cop/copdata.txt'\n", "COP = pd.read_table(fileUrl, skipinitialspace=True, sep=None, engine='python')  # Pandas dataframe\n", "COP = COP.values / 10  # mm to cm\n", "freq = 100\n", "print('COP shape: ', COP.shape)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:13:42.942813Z", "start_time": "2017-12-30T09:13:42.868099Z"}}, "outputs": [], "source": ["def cop_plot(freq, COP, units='cm'):\n", "    '''\n", "    Plot COP data from postural sway measurement.\n", "    '''\n", "    import matplotlib.gridspec as gridspec\n", "    t = np.linspace(0, COP.shape[0]/freq, COP.shape[0])\n", "    plt.rc('axes', labelsize=16,  titlesize=16)\n", "    plt.rc('xtick', labelsize=12)\n", "    plt.rc('ytick', labelsize=12)\n", "    plt.figure(figsize=(10, 4))\n", "    gs = gridspec.GridSpec(1, 2, width_ratios=[2, 1]) \n", "    ax1 = plt.subplot(gs[0])\n", "    ax1.plot(t, COP[:,0], lw=2, color=[0, 0, 1, 1], label='ap')\n", "    ax1.plot(t, COP[:,1], lw=2, color=[1, 0, 0, 1], label='ml')\n", "    ax1.set_xlim([t[0], t[-1]])\n", "    ax1.grid()\n", "    ax1.locator_params(axis='both', nbins=5)\n", "    ax1.set_xlabel('Time [s]')\n", "    ax1.set_ylabel('COP [%s]' %units)\n", "    ax1.set_title('Stabilogram')\n", "    ax1.legend(fontsize=12, loc='best', framealpha=.5)\n", "    ax2 = plt.subplot(gs[1])\n", "    ax2.plot(COP[:,1], COP[:,0], lw=2, color='g')\n", "    ax2.set_xlabel('COP ml [%s]' %units)\n", "    ax2.set_ylabel('COP ap [%s]' %units)\n", "    ax2.set_title('Statokinesigram')\n", "    if 0:  # plot the axes with the same colors of the COP data\n", "        ax2.xaxis.label.set_color('red')\n", "        ax2.spines['bottom'].set_color('red')\n", "        ax2.tick_params(axis='x', colors='red')\n", "        ax2.yaxis.label.set_color('blue')\n", "        ax2.spines['left'].set_color('blue')\n", "        ax2.tick_params(axis='y', colors='blue')\n", "    ax2.grid()\n", "    ax2.locator_params(axis='both', nbins=5)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:13:45.533020Z", "start_time": "2017-12-30T09:13:45.210758Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7ffadfbe5eb8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "cop_plot(freq, COP)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data detrend\n", "\n", "The mean value of the COP (or COGv) displacement is dependent where the individual stood on the force plate or in the space and usually has no particular interest to the understanding of postural sway. So, a typical procedure in the analysis of postural sway is to remove the mean value of the data. Related to that, the presence of a trend (a slow fluctuation) in the signal might also affect some of the measurements. Someone might argue that the trend itself could give valuable information about the signal (see <PERSON><PERSON> and <PERSON>, 2000), but the problem is that most of the measurements one uses to describe a signal assumes that the signal is stationary. A signal is stationary if its statistical properties (such as mean and variance) do not change across time. So, to detrend the data might be necessary in certain cases. Another way to remove a trend in the data is to apply a high pass filter to the data with a cut-off frequency related to the period of data acquisition (<PERSON> et al., 1998).   \n", "\n", "Let's see a simple function for data detrend in Python and use it to remove the mean of the data."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:13:55.339446Z", "start_time": "2017-12-30T09:13:55.154731Z"}}, "outputs": [], "source": ["from scipy.signal import detrend\n", "COP = detrend(COP, axis=0, type='constant')  # use 'linear' to remove a linear trend"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Measurements of spatial variability \n", "\n", "Standard deviation, RMS, range (or amplitude), and total path (or total displacement) are commonly employed to describe the spatial variability of some measurement of postural sway. For the data represented by $x$, these variables are defined as:\n", "\n", "$$ \\bar{x} \\; (mean) = \\frac{1}{N}\\sum_{i=1}^{N} x_i $$\n", "\n", "$$ SD = \\sqrt{\\frac{1}{N-1}\\sum_{i=1}^{N} (x_i - \\bar{x})^2} $$\n", "\n", "$$ RMS = \\sqrt{\\frac{1}{N}\\sum_{i=1}^{N} x_i^2} $$\n", "\n", "$$ Range = max[x] - min[x] $$\n", "\n", "$$ Total \\: path = \\sum_{i=1}^{N-1} | x_{i+i}-x_i | $$"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:14:00.488898Z", "start_time": "2017-12-30T09:14:00.458627Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Measurements of spatial variability\n", "Variable        Direction    \n", "                ap     ml  \n", "Mean:         -0.00  -0.00  cm\n", "SD:            0.61   0.33  cm\n", "RMS:           0.61   0.33  cm\n", "Range:         3.56   2.02  cm\n", "Total path:   28.61  44.32  cm\n"]}], "source": ["m = np.mean(COP, axis=0)  # mean\n", "sd = np.std(COP, axis=0)  # standard deviation\n", "rms = np.sqrt(np.mean(COP ** 2, axis=0))  # root-mean square\n", "rang = np.max(COP, axis=0) - np.min(COP, axis=0)  # range (maximum - minimum)\n", "tpath = np.sum(np.abs(np.diff(COP, axis=0)), axis=0) # total path (length of the COP displacement)\n", "\n", "unit = 'cm'\n", "print('Measurements of spatial variability')\n", "print('{0:12} {1:^16}'.format('Variable', 'Direction'))\n", "print('{0:12} {1:^8} {2:^5}'.format('', 'ap', 'ml'))\n", "print('{0:12} {1:>6.2f} {2:>6.2f} {3:>3}'.format('Mean:', m[0], m[1], unit))\n", "print('{0:12} {1:>6.2f} {2:>6.2f} {3:>3}'.format('SD:', sd[0], sd[1], unit))\n", "print('{0:12} {1:>6.2f} {2:>6.2f} {3:>3}'.format('RMS:', rms[0], rms[1], unit))\n", "print('{0:12} {1:>6.2f} {2:>6.2f} {3:>3}'.format('Range:', rang[0], rang[1], unit))\n", "print('{0:12} {1:>6.2f} {2:>6.2f} {3:>3}'.format('Total path:', tpath[0], tpath[1], unit))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mean velocity (or mean speed)\n", "\n", "The mean speed variable expresses the the average velocity of the COP displacement computed simply as the total path variable (the total displacement) divided by the total period. This variable is usually referred as velocity but as it a scalar, it should in fact be named speed.   \n", "The mean resultant speed is the speed calculated in the vectorial form considering each direction (the square root of the sum of the squared speed in each direction). For the data represented by *x* and *y*, these variables are defined as:\n", "$$ Mean \\: speed = \\frac{1}{T}\\sum_{i=1}^{N-1} | x_{i+i}-x_i | $$\n", "\n", "$$ Mean \\: resultant \\: speed = \\frac{1}{T}\\sum_{i=1}^{N-1} \\sqrt{( x_{i+i}-x_i )^2 + ( y_{i+i}-y_i )^2} $$"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:14:23.775411Z", "start_time": "2017-12-30T09:14:23.760364Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Variable           Direction    \n", "                   ap     ml  \n", "Mean velocity:    0.95   1.48  cm/s\n", "\n", "Mean resultant velocity:   1.93  cm/s\n"]}], "source": ["mvel = np.sum(np.abs(np.diff(COP, axis=0)), axis=0) / 30\n", "mvelr = np.sum(np.abs(np.sqrt(np.sum(np.diff(COP, axis=0) ** 2, axis=1))), axis=0) / 30\n", "print('{0:15} {1:^16}'.format('Variable', 'Direction'))\n", "print('{0:15} {1:^8} {2:^5}'.format('', 'ap', 'ml'))\n", "print('{0:15} {1:>6.2f} {2:>6.2f} {3:>5}'.format('Mean velocity:', mvel[0], mvel[1], unit+'/s'))\n", "print('')\n", "print('{0:22} {1:>6.2f} {2:>5}'.format('Mean resultant velocity:', mvelr, unit+'/s'))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:14:27.001014Z", "start_time": "2017-12-30T09:14:26.994463Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Squared sum of the mean speeds:\n", "  1.76  cm/s\n"]}], "source": ["print('Squared sum of the mean speeds:')\n", "print('{0:>6.2f} {1:>5}'.format(np.sqrt(np.sum(mvel ** 2)), unit+'/s'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Area\n", "\n", "Another measurement of postural sway is to compute the area that encompasses the COPap versus COPml data using a curve in a plane (e.g., a circle or ellipse) or a polygon (e.g., a rectangle). A common method for such is to compute a prediction ellipse, which is found by fitting an ellipse to the data using concepts from the statistical procedure known as principal component analysis. A 95% prediction ellipse is a prediction interval for the COP data (considered to be a bivariate random variable) such that there is 95% of probability that a new observation will lie inside the ellipse. For more details, see [Prediction ellipse and ellipsoid](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/PredictionEllipseEllipsoid.ipynb).\n", "\n", "The function `ellipseoid.py` (see [Prediction ellipse and ellipsoid](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/PredictionEllipseEllipsoid.ipynb)) calculates the ellipse area, some related parameters, and plots the results for a given multivariate random variable."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:14:36.991189Z", "start_time": "2017-12-30T09:14:36.969966Z"}}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')\n", "from hyperellipsoid import hyperellipsoid"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:14:38.106776Z", "start_time": "2017-12-30T09:14:37.716026Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7ffadcfc1e48>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["area, axes, angles, center, R = hyperellipsoid(COP[:, 1], COP[:, 0], units='cm')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Frequency analysis\n", "\n", "Frequency analysis refers to estimate the frequency content of a signal. Let's use standard Fourier analysis and the related power spectral analysis to estimate some frequency characteristics of the COP displacement. The function `psd.py` (see its code in the notebook [Fast Fourier Transform and Power Spectral Density](https://nbviewer.jupyter.org/github/BMClab/BMC/blob/master/notebooks/FourierTransform.ipynb)) estimates power spectral density characteristics using <PERSON>'s method using the `scipy.signal.welch` function, estimates some frequency characteristics, and a plots the results. \n", "\n", "Let's use `psd.py` to estimate the frequency characteristics of the COP data."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:15:04.282482Z", "start_time": "2017-12-30T09:15:04.052090Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7ffacaa08dd8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from psd import psd\n", "fp_ap, mf_ap, fmax_ap, Ptot_ap, F, P_ap = psd(COP[:, 0], fs=freq, scales='linear', xlim=[0, 2], units='cm')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T09:15:08.416672Z", "start_time": "2017-12-30T09:15:08.123628Z"}}, "outputs": [{"data": {"image/png": "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**************************************************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********************************/X26iJwV2NACKyuIu7B5NLZ3Y8uoy7uxKaWUCgv+1AO/GXgZWAecaZ/OBR4KYFwBV1oLPAibuHh4utDr7CS2f/zDOpRSSjnOn2x2K9DfGLNZRO6yz/0GdAhcWIHnzrfGwIO1BhwOt8Dr7DKy7793OgKlLJ6JqDqJTUUwf7rQk7AKmIBVGxwgBgjrrBWSLvTSFrh2oSullAoufxL4t8DdZc6NA76ueTjBE4pJbJ5lZJm6H7pSSqkg8yeB3wxcJCKbgSQR+R24FLjN3yBEJEVEZotItohsEZHhFVx3q4hsFJFDIrJTRJ4REZ+a1KFogcfFRJEYG01hseGQ/TyllFIqGPypB75LRE4BTgFaEZh64C9idcE3BboCH4vIj8aYNWWumwdMNcYcEJEUYBZW6//pqh7gGQMPZgIHqxXuzi8iw51Pg/p1bD/05s2djkAppZTN33rgBlhqHzUiIgnAUKCTMcYNLBSRD4ERlOmqN8Zs8P5RoARI8+U57rzglRL11jixHlszc8jMLqBtk6A+KvTeesvpCJRSStl8ymYiMsmX64wx//QjhhOAYmPMWq9zPwJ/riCW4cArWJPp9gF/r+C60cBogJYtW3p1oQe3VawFTZRSSoWCr83RFl5fx2G1mH8AtgAtgVMBf7fpSgQOljl3ECtBH8UY8zbwtoi0B64Gdldw3WRgMkB6errJyg9RCzyhDhc0ufVW6/XZZ52NQymllG8J3BhznedrEXkXuMIY877XuYuxJrL5ww0klzmXDGRVEdM6EVkDvARcXNVDQjGJDbz2Q6+LBU1WrXI6AqWUUjZ/ZqGfB8wpc24uMNDPGNYC0XaL2qMLUHYCW3migXa+PCSUk9igDhc0UUopFRb8SeDrgb+WOTcW2FDOtVUyxmQDHwCTRCRBRHoDg4HpZa8VkZEicoz99UnAP4AvfXlOVogmsaUmevZD1wSulFIqePxJ4COB20Rku4gsEZHtWBPJRtYgjrFAfWAP8A4wxhizRkT6iIjb67rewE8ikg18Yh/3+PKA0lnooWqB18UudKWUUmHDn3XgK+3u7p7A8cAu4HtjTKG/QRhjMoEh5Zz/DmuSm+f768pe49v9oajEUC/aRWx0lL9h+qROFzQ54QSnI1DKovXAlfJ7HXgh8F2AYwmaYntb0+Qgt76hjhc0mTzZ6QiUsmg9cKWqn8ArWxPu5zrwoCsusRJ4sMe/4XAX+v6cAkpKDC6XBP2ZSimlIo8/Ga1Fme+Pxdp0ZXbNwwmOEjuBB3sTF4B60S6S4qLJyiviYG4hjeyEXieMHm29aktcKaUc588Y+FHj0CJyLnBFQCIKAk8Xeiha4GDNRM/KKyIju6BuJfC1a6u+RqlQ0HrgSvk1C708n1POJLRw4WmBB3sGukdjnYmulFIqyPwZA29b5lQ8MByrKllY8rTAg72Ji4du5qJUkGnLWym/xsDXAwarGhhADrASa1/ysOSZxJYUoi70xrqZi1JKqSDzpwv9LmNMlDHGZR+Jxpg+QN8AxxYwdv4OySQ2qMNd6F27WodSSinH+ZPAJ1Rw/r6aBBJMxaEeA6+rm7k8+6xWIlPhoUcP61Aqgvmc0UTkLM/PiEg/DnehA7SliuphTioJ8Sz0w9up1rEErlS4WLHC6QiUclx1Mtrr9mss8IbXeQP8AdwcqKACrXQMPEQt8MMFTepYF/pVV1mvb73lbBxKKaV8T+DGmDYAIvKmMSZsJ6yVpyTECbzOtsC3b3c6AqWUUrZqj4HXtuQN3svIQjSJra6OgSullAobPjVJReRMY8y39tdnVXSdMearQAUWSCUl1muoxsAbxdsJPKeA4hJDlO6HrpRSKsB8zWgvAZ3sr1+v4BqDNZkt7IR6K9WYKBcN42M4kFPIgZyC0nXhSimlVKD4lNGMMZ28vm4TvHCCwzOJLTlEXehgrQU/kFNIRnYdSuC9ejkdgVJKKZs/W6nWA64FugKJ3u+F6/i4ZxlZQmxUyJ7ZOCGWDXuz2efO54SmSSF7blA9+qjTESillLL506f8JtAZmAfsDmw4wRNfL4roqEDVbqmaTmRTSikVTP4k8L8AbYwxBwIdTDCFavzbw7MWfPehOrQWfOhQ6/X9952NQymllF8JfCvWZi61SqjWgHu0ahwPwJaM7JA+N6gyMpyOQCmllM3fLvS5IvIcZbrQw3UZGUBiCCewAbRJTQBg0746lMCVUkqFDX8S+N/s10fKnA/bZWQQulKiHprAlQoirQeuVPUTeG1cRgah70JvkRJPlEvYcSCXvMJi4mJCNwNeKaVU3RfarOagUE9ii4ly0aJRfTZn5LA1M6duLCXr39/pCJRSStn8WQc+qYK38oHtwKfGmLBbXhaqfdC9tUlNYHNGDhv3ZteNBD6holLwSoWYpxb48uXOxqGUg/xZGH0CcBfQD0izX+8CugFjgI0icm7AIgyQxBB3oQO0SbX2udFxcKUCbMUKrQmuIp4/Wc0FXG6Mme05ISKDgeHGmJ4icg3wGPBpgGIMiFBPYgNo08Qzkc0d8mcHxXnnWa/z5zsbh1LLljkdgVKO83cjlyvKnPsImG5//RbwQk2CCoZQT2IDaFvXZqLn5jodgVIWTxe6UhHMny70DVhd5d5uss8DpAJhl7Gc6UKvYwlcKaVU2PAnq40EPhCRu4AdQHOgCLjYfr8DEHaznZyYxHZschxxMS72uQs4mFtIg/qhj0GpOmn0aOt18mRn41DKQdVugRtjVgDtgeHAM/Zre/s8xphvjTGvVeeeIpIiIrNFJFtEtojI8Aquu0NEfhaRLBHZJCJ3+PqMUC8jA3C5hNaNrVb4Zm2FKxU4r71mHUpFMH+zWgrQEIjHmomeJiIYY97w834vAgVAU6wypR+LyI/GmDVlrhPgamA10A74XES2GWPereoBToyBA7RtksBvf2SxaV82XVo0dCSGgLngAqcjUEopZfNnHfgQrIlq64COwBqgE7AQqHYCF5EEYCjQyRjjBhaKyIfACOBu72uNMf/y+vZ3EZkL9AbCNoHXqXHw2293OgKllFI2fyaxPQRcZ4zpBmTbr6MBf3dUOAEoNsas9Tr3I9aHgwqJiAB9sD5AlPf+aBFZJiLLwJkudNC14EoppYLDnwTe0hjzXplz07C6tv2RCBwsc+4gUNXWZROx4v+/8t40xkw2xqQbY9IBEuppC7zG+va1DqWUUo7zJ4HvEZGm9tebRaQX1ni0v9U63EBymXPJQFZFPyAif8P6wHC+MSa/qge4RHC5xM/wasY7gRutoKSUUipA/EngrwFn2F8/A3yN1eX9kp8xrAWiRaS917kuVNw1fj3W2Hh/Y8x2Xx4Q5VDyBmgUH0OD+jG484vY667ys4ZSSinlE3/KiT7u9fWbIrIASDDG/OpPAMaYbBH5AJgkIiOxZqEPBk4ve62IXIlVh7yfMWajr89wMH8jIrRJTWDVtgNs2pvNMUlxzgWjlFKqzvC5BS4iLcs77Lezvb72x1igPrAHeAcYY4xZIyJ9RMR7I/GHgMbADyLito9Xqrq5ky1wqINbqiqllHJcdVrgmwHPIG55GdHg5zi4MSYTGFLO+e+wJrl5vm/jz/2jxNkEXmcmsl12mdMRKKWUslUnga8G4rBmnL8F7AxKREHg1AQ2D09Vso21PYGPHet0BEoppWw+d6EbY7oCl2DtwrYQ+AS4HKhnjCk2xhQHJ8Saa+rwuHOdaYHn5FiHUkopx1VrFrox5mdjzB1AG+Bp4AJgl4h0D0ZwgRIb489k+8Dx7Ie+NSOH4pJavJRs4EDrUMppy5ZpTXAV8fzd3aQ98GegF7AS2B+wiOqghNhojk2O449Deew8kEuLlHinQ1KqdtN64EpVaxZ6ioj8VUSWAnOwNmA50xjTzxizKWgR1hGebvRaPw6ulFIqLFSnBb4T2ARMBxbb59JEJM1zgTHmqwDGVqe0aZLA9xsz2LTXzZ9PaOJ0OErVbloPXKlqJfA/sGahj7KPsgzQNhBB1UVtGteRiWxKhQNPLXBN4CqC+ZzAjTGtgxhHnVcnutCvvdbpCJSyvPqq0xEo5ThnSnRFIM9a8FrdAtcErsKFpwtdqQjm7PqqCNKiUTxRLmHHgVzyCsN2yXzl9u2zDqWUUo7TBB4i9aJdtGhUH2Nga2Yt3QzlkkusQymnTZ6s498q4mkCD6HScfC9tbgbXalwcOON1qFUBNMEHkJtUq26LLV6HFwppVRY0AQeQp6JbJs1gSullKohTeAhpHXBlVJKBYouIwuhWr8WfMwYpyNQSill0wQeQscmxxEX42KfO59DeYUkx8U4HVL1DBvmdARKKaVs2oUeQi6XlJYWrZXj4Nu2WYdSSinHaQIPsTa1eRx8xAjrUEop5ThN4CGma8Gdt3Gvmz1ZeU6HoZRSNaIJPMRqdQu8DlixdT/nPPMtw15dTEmJcTocpZTymybwEGtbF4qa1FLZ+UWMn7mKohLDpn3ZfL8xw+mQlFLKb5rAQ8x7NzZjtAUYSpPm/cKWjByiXALArOXbHY5IKaX8pwk8xBrFx9Cgfgzu/CL2uQucDqd6/v5366iFPv35D2Yu20a9aBevXtUDgPk/7yIrr9DhyJRfXn1Va4KriKcJPMREpPaOgw8aZB21zO5Defzjg9UA/OO8P3H2SU05rU0KeYUlzP/pD4ejU34ZPVprgquIpwncAYe3VHU7HEk1/f67ddQiJSWG29/7kf05hfRpn8o1vVoDcEmP5oB2oyulai9N4A6otVuq1sISjtO+38x36/bRKD6Gpy7tgsse/x548nHE14ti6ebM2rmpTqTTeuBKaQJ3gqcq2SZdCx5Ua3dn8ej83wB49OLOHJMcV/peQmw053U6DoAPVmgrvNaphR8mlQo0TeAOqLVj4LVIflEx495ZSUFRCcPSW3Bup2OPumZoj2YAvL9ih64Jr21GjbIOpSKYFjNxgGc/9C0ZORSXGKIcjqcueurztfz2RxatGsfzz0EnlXtNzzaNadawPjsO5LJ4Ywanp6WGOErlN+0+Vyo8WuAikiIis0UkW0S2iMjwCq7rJyJfi8hBEdkc4jADJiE2mqbJsRQUl7DzQK7T4dQ5i9bv47XvNhLlEp4d1pWE2PI/p7pcwlCdzKaUqqXCIoEDLwIFQFPgSuBlEelYznXZwBvAHSGMLShq5US2++6zjjB2MKeQ2/7zI8bAuLPa061lo0qvH9rd6kaf//MfuPOLQhGiCoTly61DqQjmeAIXkQRgKDDBGOM2xiwEPgSOKntljFlqjJkObAxxmAFXuiPb3lq0lOzss60jTBljuGfOT/xxKI/uLRvy137tqvyZVo0TOLVNCrmFxXzy064QRKkCIj3dOpSKYI4ncOAEoNgYs9br3I9AeS3wOqNtbZzItmqVdYSp2St38PHqXSTUi+KZYV2JjvLtf96XdNdudKVU7RMOCTwROFjm3EEgqSY3FZHRIrJMRJbt3bu3JrcKitKZ6Bk5DkdSDbfeah1haPv+HP45dw0A91/YkVb2REFfDOx8HPVjoli6KZMtGbXoA5VSKqKFQwJ3A8llziUDWTW5qTFmsjEm3RiT3qRJk5QeL2UAACAASURBVJrcKihK14LXtt3YwtTTX6zFnV/EXzo25VJ7YpqvEmOjOc9eZvb+ih3BCC9ovlu3lxlLtmhhHKUiUDgk8LVAtIi09zrXBVjjUDwh0aJRPFEuYfv+XEr0H98aWbs7i9krdxATJdx3/kmISLXv4dla9f3l22vNmvCvftvNNW8s5d7ZP/PWkq1Oh6OUCjHHE7gxJhv4AJgkIgki0hsYDEwve62IuEQkDoixvpU4EakX2ogDo160ixaN6mMM5BWWOB1Orfb052sxBi4/pSUtUuL9ukfPtl5rwjeFf53wn3cc5G9vr8TzWeORj3+tXfMplFI15ngCt40F6gN7gHeAMcaYNSLSR0S8+5jPBHKBT4CW9tefhzrYQPGMg+cVFjscSe3147YDfLrmD+JiXNx8Vprf93G5hIvtJWXhPplt54Fcrp/6AzkFxVzUrRlDuh5PbmEx42euoqhYPwwqFSnCIoEbYzKNMUOMMQnGmJbGmLft898ZYxK9rltgjJEyR1/HAq8hz1KyWpPAH3nEOipQXGJ48rPfeXdp6Lpzn/zcqo52zemtj9jr3B9D7dnon/78B9lhuib8UF4h1/3fD+zJyue0Nik8NvRkHhjcieMaxLFq2wFeXrDB6RCVUiESFgk8UrVJtbp7c2tLF/rpp1tHBaYt2swLX6/nntk/8fsfNZqD6JPFGzP4bt0+kmKjuenMqtd8V6V1agKntG5ETkF4rgkvLC7hrzNW8PvuLNo1SWDyiHRio6NoUD+GJy/tAsBzX67jp+1lF3UopeoiTeAOqnUt8EWLrKMcm/dl86/PrMpfJQYenf9rUEMxxmrtA4w6sy2NEgIzFSJc64QbY7hv9s98t24fqYn1mHrdqTSIjyl9v3daKtee3pqiEsP4/6yqPf+bUkr5TRO4gzxLyQL9j21eYTF7s/IDek8A7rnHOsooKTHc+f5q8gpLOPvEY0iKjWbB73v5bl3w1t8v+H0vy7bsJyWhHtef0SZg9x148nHExbhYsimTrWG0Rv+lBRuYuWwbcTEuplxzSrmT9e4+70+0a5LA+j1u/vXp7w5EqZQKJU3gDjouOY7YaBeFxSUUBWjp0u5DeQx87jvO/NfX7AhRoZS3lmxh6aZMUhPr8cQlXRjbz5pM9vDHv1IchCVZJSWGJ+zW99i+7UisoFiJP5LiYkrrhL8fJnXC567awROf/Y4IPDusG11bNCz3urgYewc6l/DG/zaxaP2+EEeqlAolTeAOcrkkoDPR9xzK44rJi9m4L5vcwmLeW7atxvesyrbMHB6bb3WdPzSkE40S6nFd79Y0a1if3/7I4oMgJMFPft7FL7sOcWxyHFf1bBXw+3sms72/wvk14Us3ZXLHe6sBuHfgieXWNffWuXlDbj7L2lLh9vd+5GBuYdBjdITWA1dKE7jTApXA92TlcflrVvJumhwLWOO4wUxAxhjuen81OQXFnN/5OM61W65xMVHc8ZcOgDVLPLcgcEMERcUlPP25tW3+uP7tiYsJfDX1Xu0ac3yDOLbvz2XJpsyA399XG/a6GT19GQXFJVzTqxU3+DhU8Nd+7ejSoiE7D+bxwId1dD+kyZO1JriKeJrAHXY4gfs/E31vVr7V8t6bzZ+OTeKTcX1o1rA+2/fnsnhj8DYleXvpVhZtyCAloR6TLjyy9syFXY7n5GYN2H0onynfBa543AcrdrBxXzatGsdzaXr1tkz1VZRLuNjhAicZ7v9v787Do6rOB45/31myJwQICYsg+75ExA3QqoDiQsW9ilvr0rovVVu1Wmtr9ae1aq27UrcKogJKrVqoG6JAQUAEUSDsSxIC2TPJLOf3x72BYUhgJgyZmeT9PM99ZubeM2fOycmdd+69555Ty8//8T9Kq72MHZDLfRMGhT3CnMvp4PELhpHidjB9yRY+jMMe9Uqpg6cBPMZ6dbB6om8v9zBr2daIx7TeUVnLxS/OZ60dvN+8+ljaZyRzrt2b+u1oBqAnnrAWrMlD/vyB1dP8Dz8dRPuM5L2SOhzC3acPAODZz9dSVOE56I+v9fl58r+rAbhtXF/cYc421hT1f79Zy7Yy6aX53P/+Ct6Yv4H5BSWUVB6CDoK24opapi7cyKSXFrBxZzVDurThbxcdgdMR2fCwPTtk7P773z1jOUXlB//3jys6H7hSRK/3j2qS04d0oiDFTYXHy41TlvDuN5v541mDwxoStMQO3quLKumbl8E/rzqGdvbtVOcfeRh/++9q/r18G384axBZKe4D5BaG/HzAOnV+1/TlVNX5GT+oI2cO7dRg8uN6tWfsgDzmfF/IE3NW8+ezhxzUx09ZsJEtpTX075jJhKGdDyqvA+mRk864gXnMXlnIvDUlzFuz95mMdulJ9O6QQe+8DPrkZtA7N4M+uZnkZSVHPBb7mqJKZq8sZPbK7SzZVEr9b7gu2am8fPkI0pKatpteeuzhzF5ZyNzVO/jNu98y+YqjmjROfFyqnwtc5xFQrZgG8BhLTXIysHMWRRW1ZKVYt1+d8vgX3DauLz8f1b3ROa13VtUx6aUF/FhYSZ/cjN1H3vW6tkvjuJ7t+bqghH8t28bFx3Q7+MLOmQPAtDZ9mbt6B9lpbv44cfB+g8JvT+vPpz8UMXXhRn4+sjt98po2S2x1nY+/f7oGgF+f0g9HhEekTfHCpUeyaWcNq4sqWF1UyZqiSuuxsIKdVXUsrNrJwvV7XyPPTHbRKzcoqOdl0LtDJoe1Td1dZn/A8M3GXcxeWciclYUUBI1hnuRyMKpXe8YN7MgZQzrtda93pESER88bximPf86nPxQzZeGm6PwfxIPhw2NdAqViTlrDNIQjRowwixYtinUxGnfiiQAUzfqIB2at5F/fWtcsB3XO4uFzhjLksDZ7Jd9VVcdFL85n1fYKeudmMOXqY+mQmRyaKzOWbObWt5aR3zWbmdePiko5a30BRoy5h4paH09cmM/EI7oc8G33zvyO1+dv4OT+uUy+4qgmffTTn67h0Y9/YFjXbGZeNzKmR5LGGLaXe1hdWLknsBdWsKa4ktLqhnt9p7gd9MzJoHN2Kks27qKkqm73tuw0Nyf3z2XcgDxO6NuB9CjeFgfWbWg3T11KWpKTf904mp4dMg78pijy+QN4fIGo3u6nVCISkcXGmBHRyk/3qDiSm5nC3y8ezrlHFvG7Gd+xYms5Zz39JVeM7MGvT+lLerKLXfaR96rtFfTskM6bVx/TYPAGGD+oE/clr2DpplJWF1Y0+ei3ngHW7aiiotbH2AG5nJUf3mnsm8f2YcaSLXyyqoh5a3YwqndORJ9bVu3l+c+tMb7vPLVfzE8Diwid2qTSqU0qJ/TdM9e8MYaSqjpWF1aypriStUWVrC6qYE1RJYXltazcVs7KbeUAdGuXxriBeYwbmMeIw9s2eqYlGs7K78Kc74uYtWwr17y+mOnXjYzOJZUwrCmq4BevLKLc4+Xda0fu7vOhlDp4GsDj0En9cpl92wk8PvtHXv5yHZPnreOj77bx29MH8Pzna1m5rZyeOelMvfpYcjMbn8AjNcnJmcM6M2XhRt5evHl3p6am2lFRS2l1HVkpLh48e0jYgTQnI5lrT+zFox//wIMffM+/bhwd0SnwF+aupdzjY2Sv9hEH/+YkIuRkJJOTkcxxvdrvta2sxsva4ko27aymf8cs+uZlNOsPkYfOGcIP28v5sbCSW6Yu5cXLRkTcMS5SX63ZwS/fWEyFx5oY5papS3n32pEkubTvrFLRoHtSnEpLcnHPGQN5/4bRDOnShq1lHm6asoQVW8vpkZPOlGuODWv2rfpbraZ/swXvQUw1WVjuYX2Jda32vgmDyItw5q8rR/egU5sUVm4rZ8aSLWG/r7iilslfrgfgdvve8kTUJtXN8G5tOSu/C/06Zjb7WYSMZBcvXXYU2WluPllVtHsku0Nl2qJNXDZ5IRUeH6cMzOOwtqks31LGY7Oj9Lki1qJUK6YBPM4N7tKGGdeN5N4zB5KW5KRnTjpTrj427AB6RNdseudmsKOyls9+aNrY5MYY7pmxHH/AkJ2WxLnDD3zdO1Skg7us31HFIx+t4syn5lLj9TN2QB7Du7VtUvmVpVv7NJ6ZNBynQ3ju87XMjOCHVLgC9pSyd77zLb6A4ZoTevLcJUfy5M/ycTqEF74o0CFelYoSDeAJwOV0cOXoHiz63Vg+vOV4OrYJ/+hXRDjfvqd5WhOHVp2xZAtzvi/iwQk30+6NyU0+epyY34VBnbPYVuZh8rx1+2yvqfPz7uLNXPj815z4l8945rO1FJbX0js3g9+dcXCn/5VlZK8c7p8wEIA73/2WZZtKo5a3x+vn5reW8vdP1+B0CH+aOJi7Tx+AwyEceXg7bjy5N8bArdOWsiuoE59Sqmk0gCeQtCQXya7Ihw49e3gXnA7h01VFEc9StrW0ht/bw3FeesWp5I4YFvHn13M4hHvs6/DPfLqG4opajDEs21TK3TOWc/SDc/j128tYsG4nqW4n5w4/jGm/PI7Zt55Ad3vEOnXwLjn2cC4+pht1vgBXv7aIwigM8lJ/W+OsZVvJSHYx+Yqj9hmn/oaTejPi8LYUltfy2+nfRjxokVJqbxrAW4HczBRO6tcBX8BEdNo0EDDc8c4yKjxWr/Pzty2BWbMOqiwje+cwpn8uVXV+bpzyDac9OZeznp7Hmws2UlHrI79rNg+dM4SF94zhsQuGcXSPdjHvdd7SiAj3TxjE0T3aUVRRyzWvLTqosfgLiis5+5l5LN6wi85tUnjn2uP4SVDv/Houp4PHL8wnM9nFxysKeet/h36yHaVaMg3grcT5I7oC1mn0cI98Xp+/gXlrrLHOHzpnKPLXv8Jjjx10We46vT9OhzC/YCertlfQLj2JK0f34D+3nsDM60dx0dHdyGym25xaqySXg2cnDadLdirLNpdx1/TlTToiXlBQwtnPfMWGEmvY15nXj6J/x6xG03dtl8YfJw4G4A+zVrK2uLLJdVCqtdMA3kqc3D+X9ulJrC6qZNnmsgOmX1tcyUMfWmOdPzhxcKP3mjdF79xM7p8wkDOGdOLZScOZf9cY7j1zIH0P8j51FZn2Gcm8dPkI0pKczFiyhRe+CH/SGY/Xz5sLNnLJywsoq/EybmAeb/0yvDsjJh7RhYn5nanx+rll6lLqfE2/O0Kp1kwDeCvhdjo42x417UDzhPv8AW6btgyPN8A5R3ThtCENj3V+MC49rjtPTxrOaUM66X3BMTSgUxZ/vcDq1/DwR6v4dFVRo2mLyj1MWbiRq15dRP4D/+HuGcvx+g1Xju7Bc5ccGdGY7Q9MHNykW8u2ltZw94zlu1+/NLeAbWU1Yb9fqZZEvzlbkfrT6O8v27rfa57PfraWZZtK6dQmhd+HTBOqWp7xgztx69i+GAM3TVnCmqIKwLp98LstZTw5ZzU//fuXHP3n/3LX9OXM+b4QjzfA4C5ZPHreUO49c2DEg8JkpbgjurWssNzD79/7jhMf/Yw3F2zcvf5PH3zPcQ99wvnPfcVrX6+PuJOmUolMR2JrRfp1zGTYYW1YtrmMj1ds56z8fe/n/m5L2e4pOx89bxhtUvVadGtw05je/FhYwQfLt3HVq4sY1TuHT1YVsa1sTw/1ZJeD0b1zGDMgj5P750Z0O2ND6m8te2LOam6dtpSPbj6BtvZsevWKK2p59rO1/HPBBmp9AURgwrA9Q/ieMaQTc74v5H/rd/G/9bu4//0VHNerPROGdmb84I5kpyWFfqxSLYZOZhIP7MlM+OyzQ/5Rr8/fwL0zv2NU7/b886pj99rm8fqZ8NSXrC6q5IqR3bk/9Oh7k33qvWvXQ15O1fyq63yc9+zXu8drB8jLSubk/nmMHZDLyF45pCZFfhvj/vj8AX72wnwWbdjFqYPyeO6SIxERdlbV8fzna3n16/V4vNY18tMGd+SWsX3p1zFzzyhsxlBZ6+O/3xcya9lWPv+xGK/f+k5zOYTj++Rw2cjunNQvN6rlVqopoj2ZiQbweNCMAbysxsvRD86hzh/giztO2mve8Qc/WMmLc9fRMyedD246Pupf1ir+bS2t4ZGPVnF4+3TGDshjcJesQ34b36ad1Zz+5Fwqan3cc/oASmvqeGXeeqrs0frGDczjlrF9GNQ5aFa+oAAerKzay8crtzNr2Va+WluCP2BtP+/Iw7hvwsBmm8RFqYZoAG8CDeB7u3nqEt5bupVbxvbhlrF9AZhfUMJFL87HIcK7144kv2v2vm986y3r8cILm6WcqvWon/I02En9OnDruL4MPayB/8Ujj7QeFy9uNM8dlbVMW7SJJ+esptYXoFObFB49bxij+8TvhDiqZYt2ANdObK3Q+Udap8DfXrSZQMBQ4fFy+9vLMAauP7FXw8Eb4NlnrUWpKDsrv8vuIX+P75PD9OtG8o+fH91w8AYrcO8neIM1C951J/bmg5uOZ1jXbLaVebjk5QXcO/M7qmp90a6CUs1OO7G1QiN7tadLdipbSmuYX1DCe0u3snlXDYO7ZHHjmD6xLp5qpR45byi3n9ov4pnuDqR3bgbv/uo4nv+igCfm/Mjr8zfwxepi/nL+MI7q3i6qn6VUc9Ij8FbI4RDOtY927nt/BW8t2kSSy8HjF+Tjduq/hIoNEYl68K7ncjq4/qTevHf9aPp3zGRDSTUXPP81f/739wc1jKxSsaTf1q1U/enKNUXWUJZ3ntqPPjoSmkoUTZwPfGDnLN6/YTQ3nNQbAV74ooAzn/qSbzdHb1Y2pZqLnkJvpbq2S+O4nu35uqCEY3q04xejesS6SEo1iySXg9tP7ceYAbn8+u1lrCmq5OxnvmJifhfSk534A4aAMQQC4DeGQMDgN2b3eqfDQcesZLpkp9LZXrpkp5Kd5o64x77XH6DC48Pj9ePzG7yBAF5/wHruD+D1G3z+AN6AwesLYLBuj3M5BadDcDsdOB1irXM4cDmt56lJTvIyU3BEOMCOSixx0QtdRNoBLwOnADuAu4wxbzaQToCHgavsVS8DvzEHqIT2Qm/Yiq1lvDF/AzeP6RveoBw77NGycrQXr2oZPF4/j378A5PnrQu9Iy1iaUnOoICeQl5WCl5/gPIaH+UeL+U1Xso9Pio83t3rqusO3en7ZJeDHjnp9OyQTs+cDHp2SLdfZxySAZpqfX5Kq714/QGy05JIT3Ie1C2IfruDbYXHh8sppLldpCY5cTslrHyNPUZAabXXWmrq7EcvGENuVgods6x2yslIwtUMlw+j3Qs9Xo7AnwbqgDwgH/hARJYZY1aEpLsGmAgMAwwwGygAnmvGsrYYgzq34aFzhob/Bg3cqoVJcTu598yB/HRYZxZt2IVTwOkQHA7BKYJD7OcOcIh11Ov1B9hW5mFraQ1bSz1s2VXDltIaKmt9rCmq3H1ZKhwOgcwUN6luJ26X4LaPot1OBy6nA7d9tO12OnA7HQjgC1hnA7z+gPUYMPgD1lF7/bYKj5cdlXWs2l7Bqu0V+3xuTkYSPXMy6J6TRkaymySXg6T6z3E5SNr9uOezq+t87Kzysqu6jpLKOuuxqo5d9lIR0rPf5RDapLppk+YmO9VNm1Q32WlJ9qNV53KPFVzLaqyl/nlptZVfQz+qnA4hze0kJclJWpKTVLeTVPux1hegtHpPoK4fByCcdsjJSCbPDuh5Wcl0zEohO81NrS9ArS9ATZ0fj9dPjb3UegPUeK11tb4ALoeQ4naS4nZYj649z5PdVvmiLeYBXETSgXOBwcaYSuBLEXkfuBT4bUjyy4HHjDGb7fc+BlyNBvDm8cor1uMVV8SyFEpF3bCu2Qxr7PbJMJV7vGwtrWHLrhq2ltawvdxDistJVqqbrFQXWSluslLdZKbseX6wR6kHKs+64ioKdlRSUFxFQXEVa4srWV9SxY7KOnZU7mTh+p1R+zynQ2ibloTLIZTVeKnx+impsoJ8U2WluMhMceMLBKiu81NT58cXMFTU+vb5wdCQ9CTnXj8a2qYl0SbNjTGGwvJaCss9FJbXUlJVS1GFtSzfcuDZGuNFzAM40BfwG2N+DFq3DPhJA2kH2duC0zU424aIXIN1xE63bt2iU9LWTgO4ihdhDOTS3LJS3GR1dO93PvTmlJXibvCHSSBg2FbuoaC4kg0l1Xi8fur8Aep8gd3X3fc83/M6NclJu/Qka0lLom39c3vJSnHt9WOk1ue3jqyr9xxdl9pH2mXVdVTX+Xcfoe91dG4H28wUd4OT5Hj9VjD3eP1U1/mprvPtfp7sctI2bU+eya7wjnq9/gDFFfUB3Qrq28s9lNV4SXY5SHU7SbGPoncfYe9+7STJ5cDnD+Dx+fF4A3i8QY/2ulqvn7v/7+DaNFQ8BPAMIPQnTxnQUJfo0LRlQIaISOh1cGPMC8ALYF0Dj15xlVIx9803sS5BwnI4hC52x7vjD+GwD8kuJ7mZTnIzo3troNvpoE2qI6rX8d1Ox+7+C4fS3VHOLx5uI6sEQn+yZgH7XrjZN20WUHmgTmxKKaVUSxMPAfxHwCUiwb8FhwGhHdiw1w0LI51SSinVosU8gBtjqoDpwAMiki4io4CzgNcbSP4acJuIdBGRzsCvgVearbBKKaVUnIiHa+AA1wGTgSKgBLjWGLNCRI4HPjTGZNjpngd6Asvt1y/Z61Rz+Pe/Y10CpZRStrgI4MaYnVj3d4eun4vVca3+tQHutBfV3NLSDpxGKaVUs4j5KXSVQJ55xlqUUkrFnAZwFb5p06xFKaVUzGkAV0oppRKQBnCllFIqAWkAV0oppRKQBnCllFIqAcXFfOCHmohUAD/EuhxRlIM1b3pL0tLq1NLqAy2vTlqf+NfS6tTPGNPQPB9NEhf3gTeDH6I5iXqsiciillQfaHl1amn1gZZXJ61P/GtpdRKRRdHMT0+hK6WUUglIA7hSSimVgFpLAH8h1gWIspZWH2h5dWpp9YGWVyetT/xraXWKan1aRSc2pZRSqqVpLUfgSimlVIuiAVwppZRKQBrAlVJKqQSUkAFcRNqJyAwRqRKRDSJycSPpRET+T0RK7OUREZGg7fkislhEqu3H/OarxT5lDbdOd4jIdyJSISLrROSOkO3rRaRGRCrt5T/NU4N9yhlufe4XEW9QeStFpGfQ9kRsow9D6lMnIsuDtsdLG90gIotEpFZEXjlA2ltFZLuIlInIZBFJDtrWXUQ+tdtolYiMPeSFb7iMYdVHRC63/5fKRWSz/b3gCtr+mYh4gtonZoNARVCnK0TEH/J/d2LQ9kRro+dC6lIr1oBc9dvjoo1EJFlEXra/DypEZImInLaf9FHdjxIygANPA3VAHjAJeFZEBjWQ7hpgIjAMGAqcCfwSQESSgPeAN4C2wKvAe/b6WAi3TgJchlXm8cANIvKzkDQTjDEZ9nLKoSz0foRbH4C3gsqbYYwpgMRtI2PMacH1Ab4C3g5JFg9ttBX4EzB5f4lE5FTgt8AYoDvQE/hDUJIpwBKgPXAP8I6IdDgE5T2QsOoDpAG3YI3ydQxWvW4PSXNDUPv0i3pJwxdunQC+DtmPPgvallBtZIz5Vcg+NIV996F4aCMXsAn4CdAGuBeYJiLdQxMekv3IGJNQC5CO9SXaN2jd68DDDaT9Crgm6PWVwHz7+SnAFuye+Pa6jcD4eK5TA+/9G/BU0Ov1wNgEaqP7gTcaySfh28jeUf1Aj3hqo5Ay/gl4ZT/b3wT+HPR6DLDdft4XqAUyg7bPBX4Vr/VpIP1twKyg158BV8W6XSJsoyuALxvZltBtZO97FcBP4rmNgsr2LXBuA+ujvh8l4hF4X8BvjPkxaN0yoKGju0H2tobSDQK+NfZfyvZtI/kcapHUaTcREeB4YEXIpn+KSLGI/EdEhkW3qGGJtD4TRGSniKwQkWuD1id8G2GdLZlrjFkXsj7WbRSJhvajPBFpb28rMMZUhGyPRRs11Qnsuw89JCI7RGRe8KnoOHeEXeYfReTeoMsCid5G5wLFwBch6+OujUQkD+u7IvT/CQ7BfpSIATwDKAtZVwY0NEB8aNoyIMMOfJHkc6g1tSz3Y7XhP4LWTcI66jsc+BT4WESyo1LK8EVSn2nAAKADcDVwn4hc1IR8DrWmluUy4JWQdfHQRpFoaD8Cq+7x1EYRE5GfAyOAvwSt/g3W6c0uWANvzBKRXjEoXiS+AAYDuVgB7yKgvn9MQrcRcDnwWsgP+bhrIxFxA/8EXjXGrGogSdT3o0QM4JVAVsi6LKxTLAdKmwVU2v8IkeRzqEVcFhG5ASs4nGGMqa1fb4yZZ4ypMcZUG2MeAkqxjtKbU9j1McasNMZsNcb4jTFfAU8C50WaTzNoShuNBjoC7wSvj5M2ikRD+xFYdY+nNoqIiEwEHgZOM8bsnvHKGLPAGFNhjKk1xrwKzANOj1U5w2GMKTDGrDPGBIwxy4EHiM/9KCIi0hXr+vJrwevjrY1ExIF1Sa0OuKGRZFHfjxIxgP8IuESkT9C6YTR8ymKFva2hdCuAofbReL2hjeRzqEVSJ0TkF9idIYwxmw+Qt8Hq+NacIqpPiODyJmwb2S4HphtjKg+QdyzaKBIN7UeFxpgSe1tPEckM2R6LNgqbiIwHXsTqTLj8AMnjvX0aErofJVwb2S4DvjJ2x9b9iFkb2d9PL2N1bj3XGONtJGn096NYX/BvYieBqVg99tKBUVinGgY1kO5XwPdYp1k623+MX9nbkoANwM1AMtavpg1AUpzXaRKwHRjQwLZu9nuTgBSsU2jFQPs4rs9ZWD3MBTgaq9Pa5YncRnbaVKwj65PjuI1cdhkewjp6SAFcDaQbb//PDbTb6hOCOu8B87FOQacAZ9v17hDH9TkZKAFOaGBbNnBq/Xvt/a0Kax7nWPzPhVun04A8+3l/4Dvg94naRkHpfwB+Eedt9Jz99804QLqo70fNXtko/cHaATPtRtsIXGyvPx7rFHl9OgEeAXbayyPs3aP5CGAxUAN8AxyRAHVaB3ixTrnUL8/Zcv1LRAAABPBJREFU2wZhdfKqsr+g/guMiPP6TLHLWgmsAm4KySfh2shedxHWjw0JWR9PbXQ/1pFL8HI/1o+MSqBbUNrbgEKgHKvPRXLQtu5YvYJrsL5wY9LDPtz6YPU78IXsQx/a2zoA/8M6dVmK9aU6Lob/c+HW6S92+1QBBVin0N2J2kZ22uPs+mSG5BE3bYTVj8UAnpD/p0nNsR/pZCZKKaVUAkrEa+BKKaVUq6cBXCmllEpAGsCVUkqpBKQBXCmllEpAGsCVUkqpBKQBXCmllEpAGsCVUkqpBKQBXCmllEpAGsCVUjEjIkZEqkTkwSjl94mIeETky2jkp1Q80wCuVAyIyHoRqRGRyqClc6zLFSPDjDH31L8QkbZ2YO8RnEhEnhKRF/eXkTHmZKw5EJRq8TSAKxU7E4wxGUHL1uCNIuKKVcFiLB9rDOn1IesHA0ubvTRKxSkN4ErFEfvI/Dci8i1QJSIuEeksIu+KSLGIrBORm0Lec4SIfCMiFSLylohMFZE/2duMiPQOSvtK/Tb7daN522W5XUS+FZEyO++UoO1dRWS6/d4SEfm7vf4OEXk3pIxPicgTYf4Z8oGVZt+JGgYBS+z8poWcvTAi0tg8zEq1SBrAlYo/FwFnYE2bGABmAcuwpsUdA9wiIqcCiEgS1gxpr2PNlvY2cG44HyIijv3lbbsAaxrEHlhzsV9hv9cJ/AtrxrXu9vun2u95AxgvItl2WhdwoV3GcByBNR1mcFnzgBysmdwwxlxQf+YCuA/ryPzNMPNXqkXQAK5U7MwUkVJ7mRm0/m/GmE3GmBrgKKw5gR8wxtQZYwqAF4Gf2WmPBdzAE8YYrzHmHaypFsNxoLzry7LVGLMTK9jn2+uPBjoDdxhjqowxHmPMlwDGmG3AF8D5dtrxwA5jzOIwy5UPXCQiO+oXrKlmVxtjKoMTisjNwGVYUy/uDDN/pVqE1nqNTal4MNEYM6eB9ZuCnh8OdBaR0qB1TmCu/bwzsCXkdPOGMD//QHkDbA96Xm1/HkBXYIMxxtdI3q8C12L9ILiEMI++RSQZGIB15B/8Q+Q6oFdI2huAK4GTjTEl4eSvVEuiAVyp+BMcjDcB64wxfRpJuw3oIiISFMS7AWvt59VAWlD6jsDmMPPen01ANxFxNRLEZwLPishg4EzgzjDzHYx1ZnB28NG2iPQBFge9vhb4JVbw3tGE8iuV8PQUulLxbSFQbndsSxURp4gMFpGj7O1fAz7gJrvD2zlYp7frLQUutt83HvhJBHkfqFzbgIdFJF1EUkRkVP1GY4wHeAfruvRCY8zGMOt7BFYHtsqQ9UfZdUFErgFuwDptXhxmvkq1OBrAlYpjxhg/MAHruvA6YAfwEtDG3l4HnIPVuWwXVmex6UFZ3Gy/vxSYhHVkHFbeYZarN7AR66j+wpBkrwJDCL/zGnZZ9rqGLyIdsE73199C9gjW6fS1Qb3QL43gM5RqEWTfOzWUUolMRF4BNhtjfhfjcnTD6nzW0RhT3kgaD1CL1Vnu3ih85mysjn0LjTFjDjY/peKZXgNXSkWdfYvabcDUxoI3gDEmpbFtTWGMGRfN/JSKZxrAlVJRJSLpQCFWb/jxMS6OUi2WnkJXSimlEpB2YlNKKaUSkAZwpZRSKgFpAFdKKaUSkAZwpZRSKgFpAFdKKaUSkAZwpZRSKgFpAFdKKaUS0P8DQd62U/nMlI8AAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7ffaca944d68>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fp_ml, mf_ml, fmax_ml, Ptot_ml, F, P_ml = psd(COP[:, 1], fs=freq, xlim=[0, 2], units='cm')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Other analyses\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (2000) [On the fractal properties of natural human standing](http://www.ncbi.nlm.nih.gov/pubmed/10754215). Neuroscience Letters, 283, 173-176.  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. (2004). [Comparison of three methods to estimate the center of mass during balance assessment](http://www.ncbi.nlm.nih.gov/pubmed/15275850). J. Biomech. 37(9): 1421-1426.   \n", "- <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (1998) [Testing stationarity in time series](http://journals.aps.org/pre/abstract/10.1103/PhysRevE.58.1800). Physical Review E, 58, 1800-1810."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}