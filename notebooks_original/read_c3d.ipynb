{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Read c3D file using EZC3D library\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://demotu.org/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> EZC3D is an easy to use reader, modifier and writer for C3D format files.  \n", "> https://github.com/pyomeca/ezc3d"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T21:51:29.318290Z", "start_time": "2019-11-07T21:51:28.979171Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "#%matplotlib notebook\n", "%matplotlib widget\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import sys, os\n", "import ezc3d\n", "\n", "sys.path.insert(1, r'./../functions')\n", "from dfmlevel import dfmlevel\n", "from read_c3d import read_c3d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T21:51:47.947802Z", "start_time": "2019-11-07T21:51:47.942638Z"}}, "outputs": [], "source": ["path2 = '/mnt/A/BMClab/Projects/FapespRunAge/Data/Cadence/s20/'\n", "fname = 'run100c.c3d'\n", "fname = os.path.join(path2, fname)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["an, pt = read_c3d(fname, analog='all', point='all', short_label=True)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>F1X</th>\n", "      <th>F1Y</th>\n", "      <th>F1Z</th>\n", "      <th>M1X</th>\n", "      <th>M1Y</th>\n", "      <th>M1Z</th>\n", "      <th>F2X</th>\n", "      <th>F2Y</th>\n", "      <th>F2Z</th>\n", "      <th>M2X</th>\n", "      <th>...</th>\n", "      <th>Sensor 9EMG</th>\n", "      <th>Sensor 9IM ACC Pitch</th>\n", "      <th>Sensor 9IM ACC Roll</th>\n", "      <th>Sensor 9IM ACC Yaw</th>\n", "      <th>Sensor 9IM GYR Pitch</th>\n", "      <th>Sensor 9IM GYR Roll</th>\n", "      <th>Sensor 9IM GYR Yaw</th>\n", "      <th>Sensor 9IM MAG Pitch</th>\n", "      <th>Sensor 9IM MAG Roll</th>\n", "      <th>Sensor 9IM MAG Yaw</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>-0.002441</td>\n", "      <td>0.001221</td>\n", "      <td>0.001831</td>\n", "      <td>-0.001373</td>\n", "      <td>0.000153</td>\n", "      <td>0.004730</td>\n", "      <td>-0.160675</td>\n", "      <td>-0.284424</td>\n", "      <td>-1.747894</td>\n", "      <td>-2.335968</td>\n", "      <td>...</td>\n", "      <td>-0.000101</td>\n", "      <td>1.189453</td>\n", "      <td>-3.801758</td>\n", "      <td>0.761230</td>\n", "      <td>239.817078</td>\n", "      <td>110.853661</td>\n", "      <td>-47.134148</td>\n", "      <td>17.125525</td>\n", "      <td>-145.024765</td>\n", "      <td>-359.325287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.002222</th>\n", "      <td>-0.000000</td>\n", "      <td>0.000763</td>\n", "      <td>-0.002899</td>\n", "      <td>-0.006256</td>\n", "      <td>-0.002136</td>\n", "      <td>0.002899</td>\n", "      <td>-0.153503</td>\n", "      <td>-0.305176</td>\n", "      <td>-1.794128</td>\n", "      <td>-2.371979</td>\n", "      <td>...</td>\n", "      <td>-0.000079</td>\n", "      <td>1.189453</td>\n", "      <td>-3.801758</td>\n", "      <td>0.761230</td>\n", "      <td>239.817078</td>\n", "      <td>110.853661</td>\n", "      <td>-47.134148</td>\n", "      <td>17.125525</td>\n", "      <td>-145.024765</td>\n", "      <td>-359.325287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004444</th>\n", "      <td>-0.004730</td>\n", "      <td>-0.003815</td>\n", "      <td>0.003967</td>\n", "      <td>0.004272</td>\n", "      <td>-0.000153</td>\n", "      <td>0.009308</td>\n", "      <td>-0.237579</td>\n", "      <td>-0.264740</td>\n", "      <td>-1.803741</td>\n", "      <td>-2.336426</td>\n", "      <td>...</td>\n", "      <td>0.000009</td>\n", "      <td>1.409999</td>\n", "      <td>-4.366765</td>\n", "      <td>0.746142</td>\n", "      <td>253.246964</td>\n", "      <td>121.757927</td>\n", "      <td>-38.114075</td>\n", "      <td>16.998919</td>\n", "      <td>-144.840210</td>\n", "      <td>-360.182739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.006667</th>\n", "      <td>0.003662</td>\n", "      <td>0.002594</td>\n", "      <td>-0.002136</td>\n", "      <td>-0.005646</td>\n", "      <td>-0.000763</td>\n", "      <td>-0.003662</td>\n", "      <td>-0.255737</td>\n", "      <td>-0.204773</td>\n", "      <td>-1.804047</td>\n", "      <td>-2.355499</td>\n", "      <td>...</td>\n", "      <td>0.000057</td>\n", "      <td>1.520271</td>\n", "      <td>-4.649269</td>\n", "      <td>0.738598</td>\n", "      <td>259.961884</td>\n", "      <td>127.210060</td>\n", "      <td>-33.604038</td>\n", "      <td>16.935616</td>\n", "      <td>-144.747940</td>\n", "      <td>-360.611450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.008889</th>\n", "      <td>-0.002747</td>\n", "      <td>-0.002441</td>\n", "      <td>0.005341</td>\n", "      <td>0.004578</td>\n", "      <td>0.005646</td>\n", "      <td>0.007477</td>\n", "      <td>-0.222321</td>\n", "      <td>-0.244141</td>\n", "      <td>-1.825104</td>\n", "      <td>-2.377777</td>\n", "      <td>...</td>\n", "      <td>0.000047</td>\n", "      <td>1.524902</td>\n", "      <td>-4.661133</td>\n", "      <td>0.738281</td>\n", "      <td>260.243896</td>\n", "      <td>127.439026</td>\n", "      <td>-33.414635</td>\n", "      <td>16.932957</td>\n", "      <td>-144.744064</td>\n", "      <td>-360.629456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.928889</th>\n", "      <td>0.000610</td>\n", "      <td>0.001831</td>\n", "      <td>0.001068</td>\n", "      <td>-0.003052</td>\n", "      <td>0.002594</td>\n", "      <td>0.000916</td>\n", "      <td>0.266876</td>\n", "      <td>-0.237122</td>\n", "      <td>-1.859894</td>\n", "      <td>-2.814178</td>\n", "      <td>...</td>\n", "      <td>0.000985</td>\n", "      <td>-0.333984</td>\n", "      <td>-3.725586</td>\n", "      <td>1.123047</td>\n", "      <td>-50.365856</td>\n", "      <td>217.682938</td>\n", "      <td>33.475609</td>\n", "      <td>18.022522</td>\n", "      <td>-130.952713</td>\n", "      <td>-372.479218</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.931111</th>\n", "      <td>-0.001068</td>\n", "      <td>0.000153</td>\n", "      <td>-0.000610</td>\n", "      <td>-0.003662</td>\n", "      <td>-0.000000</td>\n", "      <td>0.004578</td>\n", "      <td>0.295715</td>\n", "      <td>-0.170898</td>\n", "      <td>-1.858826</td>\n", "      <td>-2.845459</td>\n", "      <td>...</td>\n", "      <td>-0.000204</td>\n", "      <td>1.894513</td>\n", "      <td>-5.409503</td>\n", "      <td>0.189099</td>\n", "      <td>158.874084</td>\n", "      <td>251.602448</td>\n", "      <td>66.935623</td>\n", "      <td>17.918554</td>\n", "      <td>-131.036682</td>\n", "      <td>-371.474365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.933333</th>\n", "      <td>0.001984</td>\n", "      <td>0.004730</td>\n", "      <td>-0.006714</td>\n", "      <td>-0.007935</td>\n", "      <td>-0.003510</td>\n", "      <td>-0.004883</td>\n", "      <td>0.305481</td>\n", "      <td>-0.218201</td>\n", "      <td>-1.858368</td>\n", "      <td>-2.812653</td>\n", "      <td>...</td>\n", "      <td>0.000778</td>\n", "      <td>2.918945</td>\n", "      <td>-6.183594</td>\n", "      <td>-0.240234</td>\n", "      <td>255.060974</td>\n", "      <td>267.195129</td>\n", "      <td>82.317078</td>\n", "      <td>17.870762</td>\n", "      <td>-131.075272</td>\n", "      <td>-371.012421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.935556</th>\n", "      <td>-0.003204</td>\n", "      <td>0.001221</td>\n", "      <td>0.000763</td>\n", "      <td>0.000916</td>\n", "      <td>0.001068</td>\n", "      <td>0.003967</td>\n", "      <td>0.343933</td>\n", "      <td>-0.170288</td>\n", "      <td>-1.893311</td>\n", "      <td>-2.860107</td>\n", "      <td>...</td>\n", "      <td>0.000805</td>\n", "      <td>2.918945</td>\n", "      <td>-6.183594</td>\n", "      <td>-0.240234</td>\n", "      <td>255.060974</td>\n", "      <td>267.195129</td>\n", "      <td>82.317078</td>\n", "      <td>17.870762</td>\n", "      <td>-131.075272</td>\n", "      <td>-371.012421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.937778</th>\n", "      <td>-0.001221</td>\n", "      <td>0.003204</td>\n", "      <td>-0.000458</td>\n", "      <td>-0.002747</td>\n", "      <td>0.001678</td>\n", "      <td>0.001984</td>\n", "      <td>0.312500</td>\n", "      <td>-0.135345</td>\n", "      <td>-1.875458</td>\n", "      <td>-2.801056</td>\n", "      <td>...</td>\n", "      <td>-0.001439</td>\n", "      <td>2.918945</td>\n", "      <td>-6.183594</td>\n", "      <td>-0.240234</td>\n", "      <td>255.060974</td>\n", "      <td>267.195129</td>\n", "      <td>82.317078</td>\n", "      <td>17.870762</td>\n", "      <td>-131.075272</td>\n", "      <td>-371.012421</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13473 rows × 130 columns</p>\n", "</div>"], "text/plain": ["                F1X       F1Y       F1Z       M1X       M1Y       M1Z  \\\n", "Time                                                                    \n", "0.000000  -0.002441  0.001221  0.001831 -0.001373  0.000153  0.004730   \n", "0.002222  -0.000000  0.000763 -0.002899 -0.006256 -0.002136  0.002899   \n", "0.004444  -0.004730 -0.003815  0.003967  0.004272 -0.000153  0.009308   \n", "0.006667   0.003662  0.002594 -0.002136 -0.005646 -0.000763 -0.003662   \n", "0.008889  -0.002747 -0.002441  0.005341  0.004578  0.005646  0.007477   \n", "...             ...       ...       ...       ...       ...       ...   \n", "29.928889  0.000610  0.001831  0.001068 -0.003052  0.002594  0.000916   \n", "29.931111 -0.001068  0.000153 -0.000610 -0.003662 -0.000000  0.004578   \n", "29.933333  0.001984  0.004730 -0.006714 -0.007935 -0.003510 -0.004883   \n", "29.935556 -0.003204  0.001221  0.000763  0.000916  0.001068  0.003967   \n", "29.937778 -0.001221  0.003204 -0.000458 -0.002747  0.001678  0.001984   \n", "\n", "                F2X       F2Y       F2Z       M2X  ...  Sensor 9EMG  \\\n", "Time                                               ...                \n", "0.000000  -0.160675 -0.284424 -1.747894 -2.335968  ...    -0.000101   \n", "0.002222  -0.153503 -0.305176 -1.794128 -2.371979  ...    -0.000079   \n", "0.004444  -0.237579 -0.264740 -1.803741 -2.336426  ...     0.000009   \n", "0.006667  -0.255737 -0.204773 -1.804047 -2.355499  ...     0.000057   \n", "0.008889  -0.222321 -0.244141 -1.825104 -2.377777  ...     0.000047   \n", "...             ...       ...       ...       ...  ...          ...   \n", "29.928889  0.266876 -0.237122 -1.859894 -2.814178  ...     0.000985   \n", "29.931111  0.295715 -0.170898 -1.858826 -2.845459  ...    -0.000204   \n", "29.933333  0.305481 -0.218201 -1.858368 -2.812653  ...     0.000778   \n", "29.935556  0.343933 -0.170288 -1.893311 -2.860107  ...     0.000805   \n", "29.937778  0.312500 -0.135345 -1.875458 -2.801056  ...    -0.001439   \n", "\n", "           Sensor 9IM ACC Pitch  Sensor 9IM ACC Roll  Sensor 9IM ACC Yaw  \\\n", "Time                                                                       \n", "0.000000               1.189453            -3.801758            0.761230   \n", "0.002222               1.189453            -3.801758            0.761230   \n", "0.004444               1.409999            -4.366765            0.746142   \n", "0.006667               1.520271            -4.649269            0.738598   \n", "0.008889               1.524902            -4.661133            0.738281   \n", "...                         ...                  ...                 ...   \n", "29.928889             -0.333984            -3.725586            1.123047   \n", "29.931111              1.894513            -5.409503            0.189099   \n", "29.933333              2.918945            -6.183594           -0.240234   \n", "29.935556              2.918945            -6.183594           -0.240234   \n", "29.937778              2.918945            -6.183594           -0.240234   \n", "\n", "           Sensor 9IM GYR Pitch  Sensor 9IM GYR Roll  Sensor 9IM GYR Yaw  \\\n", "Time                                                                       \n", "0.000000             239.817078           110.853661          -47.134148   \n", "0.002222             239.817078           110.853661          -47.134148   \n", "0.004444             253.246964           121.757927          -38.114075   \n", "0.006667             259.961884           127.210060          -33.604038   \n", "0.008889             260.243896           127.439026          -33.414635   \n", "...                         ...                  ...                 ...   \n", "29.928889            -50.365856           217.682938           33.475609   \n", "29.931111            158.874084           251.602448           66.935623   \n", "29.933333            255.060974           267.195129           82.317078   \n", "29.935556            255.060974           267.195129           82.317078   \n", "29.937778            255.060974           267.195129           82.317078   \n", "\n", "           Sensor 9IM MAG Pitch  Sensor 9IM MAG Roll  Sensor 9IM MAG Yaw  \n", "Time                                                                      \n", "0.000000              17.125525          -145.024765         -359.325287  \n", "0.002222              17.125525          -145.024765         -359.325287  \n", "0.004444              16.998919          -144.840210         -360.182739  \n", "0.006667              16.935616          -144.747940         -360.611450  \n", "0.008889              16.932957          -144.744064         -360.629456  \n", "...                         ...                  ...                 ...  \n", "29.928889             18.022522          -130.952713         -372.479218  \n", "29.931111             17.918554          -131.036682         -371.474365  \n", "29.933333             17.870762          -131.075272         -371.012421  \n", "29.935556             17.870762          -131.075272         -371.012421  \n", "29.937778             17.870762          -131.075272         -371.012421  \n", "\n", "[13473 rows x 130 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["an"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">RACR</th>\n", "      <th colspan=\"3\" halign=\"left\">LACR</th>\n", "      <th colspan=\"3\" halign=\"left\">C7</th>\n", "      <th>CLAV</th>\n", "      <th>...</th>\n", "      <th>LMMAL</th>\n", "      <th colspan=\"3\" halign=\"left\">LCAL</th>\n", "      <th colspan=\"3\" halign=\"left\">LTOE</th>\n", "      <th colspan=\"3\" halign=\"left\">LMT5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>...</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "      <th>X3</th>\n", "      <th>Y3</th>\n", "      <th>Z3</th>\n", "      <th>X4</th>\n", "      <th>...</th>\n", "      <th>Z51</th>\n", "      <th>X52</th>\n", "      <th>Y52</th>\n", "      <th>Z52</th>\n", "      <th>X53</th>\n", "      <th>Y53</th>\n", "      <th>Z53</th>\n", "      <th>X54</th>\n", "      <th>Y54</th>\n", "      <th>Z54</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>2213.544434</td>\n", "      <td>1420.947998</td>\n", "      <td>1571.324829</td>\n", "      <td>2194.909180</td>\n", "      <td>1416.853516</td>\n", "      <td>1215.808472</td>\n", "      <td>2195.543457</td>\n", "      <td>1465.110840</td>\n", "      <td>1394.859375</td>\n", "      <td>2328.635498</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2138.386230</td>\n", "      <td>68.125816</td>\n", "      <td>1379.954468</td>\n", "      <td>2355.536621</td>\n", "      <td>58.527267</td>\n", "      <td>1366.957153</td>\n", "      <td>2319.052002</td>\n", "      <td>44.644505</td>\n", "      <td>1313.224976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.006667</th>\n", "      <td>2212.271729</td>\n", "      <td>1419.337158</td>\n", "      <td>1570.394531</td>\n", "      <td>2197.858887</td>\n", "      <td>1413.389404</td>\n", "      <td>1214.322266</td>\n", "      <td>2195.075928</td>\n", "      <td>1462.475098</td>\n", "      <td>1393.838501</td>\n", "      <td>2329.151367</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2118.484619</td>\n", "      <td>68.607948</td>\n", "      <td>1379.212769</td>\n", "      <td>2335.351318</td>\n", "      <td>58.051247</td>\n", "      <td>1366.094116</td>\n", "      <td>2298.880127</td>\n", "      <td>44.152187</td>\n", "      <td>1312.532349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013333</th>\n", "      <td>2210.282471</td>\n", "      <td>1418.303955</td>\n", "      <td>1569.193481</td>\n", "      <td>2201.142578</td>\n", "      <td>1410.884766</td>\n", "      <td>1213.318604</td>\n", "      <td>2194.371338</td>\n", "      <td>1460.500854</td>\n", "      <td>1392.986816</td>\n", "      <td>2328.939941</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2098.407471</td>\n", "      <td>69.392067</td>\n", "      <td>1378.596313</td>\n", "      <td>2314.714111</td>\n", "      <td>57.271126</td>\n", "      <td>1365.204224</td>\n", "      <td>2278.539551</td>\n", "      <td>43.632252</td>\n", "      <td>1311.776001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020000</th>\n", "      <td>2207.747803</td>\n", "      <td>1417.928345</td>\n", "      <td>1568.012329</td>\n", "      <td>2204.202637</td>\n", "      <td>1409.422607</td>\n", "      <td>1212.401733</td>\n", "      <td>2193.368896</td>\n", "      <td>1459.449097</td>\n", "      <td>1392.110962</td>\n", "      <td>2327.990234</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2078.121338</td>\n", "      <td>70.368813</td>\n", "      <td>1378.016479</td>\n", "      <td>2294.337158</td>\n", "      <td>57.032829</td>\n", "      <td>1364.186890</td>\n", "      <td>2258.052002</td>\n", "      <td>43.281223</td>\n", "      <td>1311.087036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.026667</th>\n", "      <td>2204.928955</td>\n", "      <td>1418.094360</td>\n", "      <td>1566.952759</td>\n", "      <td>2207.516357</td>\n", "      <td>1408.732910</td>\n", "      <td>1211.450562</td>\n", "      <td>2192.766113</td>\n", "      <td>1458.669678</td>\n", "      <td>1391.343506</td>\n", "      <td>2326.735352</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2057.793945</td>\n", "      <td>71.495544</td>\n", "      <td>1377.419800</td>\n", "      <td>2273.762207</td>\n", "      <td>56.897720</td>\n", "      <td>1363.448120</td>\n", "      <td>2237.523193</td>\n", "      <td>42.831345</td>\n", "      <td>1310.343018</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.906667</th>\n", "      <td>2409.503906</td>\n", "      <td>1409.329224</td>\n", "      <td>1616.354370</td>\n", "      <td>2437.631348</td>\n", "      <td>1428.756226</td>\n", "      <td>1253.974243</td>\n", "      <td>2395.374512</td>\n", "      <td>1458.581787</td>\n", "      <td>1432.489990</td>\n", "      <td>2531.663818</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2048.218018</td>\n", "      <td>541.099243</td>\n", "      <td>1422.988037</td>\n", "      <td>1951.526611</td>\n", "      <td>352.146423</td>\n", "      <td>1385.853271</td>\n", "      <td>1942.871216</td>\n", "      <td>400.749084</td>\n", "      <td>1342.777222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.913333</th>\n", "      <td>2410.107178</td>\n", "      <td>1404.970459</td>\n", "      <td>1618.451416</td>\n", "      <td>2436.678223</td>\n", "      <td>1425.311279</td>\n", "      <td>1254.532104</td>\n", "      <td>2396.526611</td>\n", "      <td>1456.373657</td>\n", "      <td>1432.407837</td>\n", "      <td>2533.387451</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2061.593750</td>\n", "      <td>546.014771</td>\n", "      <td>1421.315430</td>\n", "      <td>1968.394287</td>\n", "      <td>354.885895</td>\n", "      <td>1387.186768</td>\n", "      <td>1960.520386</td>\n", "      <td>401.953033</td>\n", "      <td>1342.269287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.920000</th>\n", "      <td>2411.272217</td>\n", "      <td>1401.923462</td>\n", "      <td>1618.552368</td>\n", "      <td>2435.437988</td>\n", "      <td>1422.631104</td>\n", "      <td>1255.395752</td>\n", "      <td>2397.616699</td>\n", "      <td>1454.628418</td>\n", "      <td>1432.525391</td>\n", "      <td>2533.692627</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2074.715820</td>\n", "      <td>549.933594</td>\n", "      <td>1419.789429</td>\n", "      <td>1985.499146</td>\n", "      <td>355.914154</td>\n", "      <td>1389.160400</td>\n", "      <td>1978.374634</td>\n", "      <td>401.322357</td>\n", "      <td>1342.066528</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.926667</th>\n", "      <td>2412.643066</td>\n", "      <td>1400.000366</td>\n", "      <td>1618.078979</td>\n", "      <td>2433.286133</td>\n", "      <td>1420.706665</td>\n", "      <td>1256.044922</td>\n", "      <td>2398.005615</td>\n", "      <td>1453.223389</td>\n", "      <td>1432.965454</td>\n", "      <td>2533.084961</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2087.037354</td>\n", "      <td>552.726379</td>\n", "      <td>1416.801147</td>\n", "      <td>2003.463379</td>\n", "      <td>355.632355</td>\n", "      <td>1391.797119</td>\n", "      <td>1996.600342</td>\n", "      <td>399.063965</td>\n", "      <td>1342.302002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.933333</th>\n", "      <td>2413.520020</td>\n", "      <td>1398.615845</td>\n", "      <td>1617.448486</td>\n", "      <td>2430.549072</td>\n", "      <td>1419.565186</td>\n", "      <td>1256.604492</td>\n", "      <td>2397.479736</td>\n", "      <td>1452.064697</td>\n", "      <td>1433.602539</td>\n", "      <td>2532.042480</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2098.605225</td>\n", "      <td>554.406067</td>\n", "      <td>1413.652832</td>\n", "      <td>2021.544800</td>\n", "      <td>354.294006</td>\n", "      <td>1394.230713</td>\n", "      <td>2014.322510</td>\n", "      <td>395.487213</td>\n", "      <td>1343.530518</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4491 rows × 162 columns</p>\n", "</div>"], "text/plain": ["Marker             RACR                                   LACR               \\\n", "Coordinate            X            Y            Z            X            Y   \n", "XYZ                  X1           Y1           Z1           X2           Y2   \n", "Time                                                                          \n", "0.000000    2213.544434  1420.947998  1571.324829  2194.909180  1416.853516   \n", "0.006667    2212.271729  1419.337158  1570.394531  2197.858887  1413.389404   \n", "0.013333    2210.282471  1418.303955  1569.193481  2201.142578  1410.884766   \n", "0.020000    2207.747803  1417.928345  1568.012329  2204.202637  1409.422607   \n", "0.026667    2204.928955  1418.094360  1566.952759  2207.516357  1408.732910   \n", "...                 ...          ...          ...          ...          ...   \n", "29.906667   2409.503906  1409.329224  1616.354370  2437.631348  1428.756226   \n", "29.913333   2410.107178  1404.970459  1618.451416  2436.678223  1425.311279   \n", "29.920000   2411.272217  1401.923462  1618.552368  2435.437988  1422.631104   \n", "29.926667   2412.643066  1400.000366  1618.078979  2433.286133  1420.706665   \n", "29.933333   2413.520020  1398.615845  1617.448486  2430.549072  1419.565186   \n", "\n", "Marker                            C7                                   CLAV  \\\n", "Coordinate            Z            X            Y            Z            X   \n", "XYZ                  Z2           X3           Y3           Z3           X4   \n", "Time                                                                          \n", "0.000000    1215.808472  2195.543457  1465.110840  1394.859375  2328.635498   \n", "0.006667    1214.322266  2195.075928  1462.475098  1393.838501  2329.151367   \n", "0.013333    1213.318604  2194.371338  1460.500854  1392.986816  2328.939941   \n", "0.020000    1212.401733  2193.368896  1459.449097  1392.110962  2327.990234   \n", "0.026667    1211.450562  2192.766113  1458.669678  1391.343506  2326.735352   \n", "...                 ...          ...          ...          ...          ...   \n", "29.906667   1253.974243  2395.374512  1458.581787  1432.489990  2531.663818   \n", "29.913333   1254.532104  2396.526611  1456.373657  1432.407837  2533.387451   \n", "29.920000   1255.395752  2397.616699  1454.628418  1432.525391  2533.692627   \n", "29.926667   1256.044922  2398.005615  1453.223389  1432.965454  2533.084961   \n", "29.933333   1256.604492  2397.479736  1452.064697  1433.602539  2532.042480   \n", "\n", "Marker      ... LMMAL         LCAL                                  LTOE  \\\n", "Coordinate  ...     Z            X           Y            Z            X   \n", "XYZ         ...   Z51          X52         Y52          Z52          X53   \n", "Time        ...                                                            \n", "0.000000    ...   NaN  2138.386230   68.125816  1379.954468  2355.536621   \n", "0.006667    ...   NaN  2118.484619   68.607948  1379.212769  2335.351318   \n", "0.013333    ...   NaN  2098.407471   69.392067  1378.596313  2314.714111   \n", "0.020000    ...   NaN  2078.121338   70.368813  1378.016479  2294.337158   \n", "0.026667    ...   NaN  2057.793945   71.495544  1377.419800  2273.762207   \n", "...         ...   ...          ...         ...          ...          ...   \n", "29.906667   ...   NaN  2048.218018  541.099243  1422.988037  1951.526611   \n", "29.913333   ...   NaN  2061.593750  546.014771  1421.315430  1968.394287   \n", "29.920000   ...   NaN  2074.715820  549.933594  1419.789429  1985.499146   \n", "29.926667   ...   NaN  2087.037354  552.726379  1416.801147  2003.463379   \n", "29.933333   ...   NaN  2098.605225  554.406067  1413.652832  2021.544800   \n", "\n", "Marker                                      LMT5                           \n", "Coordinate           Y            Z            X           Y            Z  \n", "XYZ                Y53          Z53          X54         Y54          Z54  \n", "Time                                                                       \n", "0.000000     58.527267  1366.957153  2319.052002   44.644505  1313.224976  \n", "0.006667     58.051247  1366.094116  2298.880127   44.152187  1312.532349  \n", "0.013333     57.271126  1365.204224  2278.539551   43.632252  1311.776001  \n", "0.020000     57.032829  1364.186890  2258.052002   43.281223  1311.087036  \n", "0.026667     56.897720  1363.448120  2237.523193   42.831345  1310.343018  \n", "...                ...          ...          ...         ...          ...  \n", "29.906667   352.146423  1385.853271  1942.871216  400.749084  1342.777222  \n", "29.913333   354.885895  1387.186768  1960.520386  401.953033  1342.269287  \n", "29.920000   355.914154  1389.160400  1978.374634  401.322357  1342.066528  \n", "29.926667   355.632355  1391.797119  1996.600342  399.063965  1342.302002  \n", "29.933333   354.294006  1394.230713  2014.322510  395.487213  1343.530518  \n", "\n", "[4491 rows x 162 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}