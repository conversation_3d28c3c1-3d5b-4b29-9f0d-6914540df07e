{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to  previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overriden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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********************************************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\" width=\"1000\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[[ 1.         0.         0.       ]\n", " [ 0.         0.5       -0.8660254]\n", " [ 0.         0.8660254  0.5      ]]\n"]}], "source": ["from mpl_toolkits.mplot3d import Axes3D\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "%matplotlib notebook\n", "plt.rcParams['figure.figsize']=10,10\n", "fig = plt.figure(figsize=plt.figaspect(1))  # Square figure\n", "ax = fig.add_subplot(111, projection='3d')\n", "\n", "coefs = (1, 3, 15)  # Coefficients in a0/c x**2 + a1/c y**2 + a2/c z**2 = 1 \n", "# Radii corresponding to the coefficients:\n", "rx, ry, rz = 1/np.sqrt(coefs)\n", "\n", "# Set of all spherical angles:\n", "u = np.linspace(0, 2 * np.pi, 30)\n", "v = np.linspace(0, np.pi, 30)\n", "\n", "# Cartesian coordinates that correspond to the spherical angles:\n", "# (this is the equation of an ellipsoid):\n", "x = rx * np.outer(np.cos(u), np.sin(v))\n", "y = ry * np.outer(np.sin(u), np.sin(v))\n", "z = rz * np.outer(np.ones_like(u), np.cos(v))\n", "\n", "\n", "xr = np.reshape(x, (1,-1))\n", "yr = np.reshape(y, (1,-1))\n", "zr = np.reshape(z, (1,-1))\n", "\n", "alpha = np.pi/3\n", "\n", "RX = np.array([[1,0,0],[0,np.cos(alpha),-np.sin(alpha)],[0,np.sin(alpha),np.cos(alpha)]])\n", "print(RX)\n", "rRotx = <EMAIL>((xr,yr,zr))\n", "\n", "# Plot:\n", "ax.plot_surface(np.reshape(rRotx[0,:],(30,30)), np.reshape(rRotx[1,:],(30,30)), \n", "                np.reshape(rRotx[2,:],(30,30)), rstride=4, cstride=4, color='b')\n", "\n", "plt.xlabel('X')\n", "plt.ylabel('Y')\n", "# Adjustment of the axes, so that they all have the same span:\n", "max_radius = max(rx, ry, rz)\n", "for axis in 'xyz':\n", "    getattr(ax, 'set_{}lim'.format(axis))((-max_radius, max_radius))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["mm = np.array([2.71, 10.22, 26.52])\n", "lm = np.array([2.92, 10.10, 18.85])\n", "fh = np.array([5.05, 41.90, 15.41])\n", "mc = np.array([8.29, 41.88, 26.52])\n", "ajc = (mm + lm)/2\n", "kjc = (fh + mc)/2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.3"}}, "nbformat": 4, "nbformat_minor": 2}