{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZhJwhjmOM8ie", "slideshow": {"slide_type": "slide"}}, "source": ["# Kinetics: fundamental concepts\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Kinetics is the branch of classical mechanics that is concerned with the relationship between the motion of bodies and its causes, namely forces and torques ([Encyclopædia Britannica Online](https://www.britannica.com/science/kinetics)).  \n", "Kinetics, as used in Biomechanics, also includes the study of statics, the study of equilibrium and its relation to forces and torques (one can treat equilibrium as a special case of motion, where the velocity is zero). This is different than the nowadays most common ramification of Mechanics in Statics and Dynamics, and Dynamics in Kinematics and Kinetics ([Introduction to Biomechanics](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/Biomechanics.ipynb#On-the-branches-of-Mechanics-and-Biomechanics-I))."]}, {"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#The-development-of-the-laws-of-motion-of-bodies\" data-toc-modified-id=\"The-development-of-the-laws-of-motion-of-bodies-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>The development of the laws of motion of bodies</a></span></li><li><span><a href=\"#Newton's-laws-of-motion\" data-toc-modified-id=\"Newton's-laws-of-motion-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Newton's laws of motion</a></span></li><li><span><a href=\"#Linear-momentum\" data-toc-modified-id=\"Linear-momentum-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Linear momentum</a></span></li><li><span><a href=\"#Impulse\" data-toc-modified-id=\"Impulse-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Impulse</a></span></li><li><span><a href=\"#Force\" data-toc-modified-id=\"Force-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Force</a></span></li><li><span><a href=\"#Work\" data-toc-modified-id=\"Work-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Work</a></span></li><li><span><a href=\"#Mechanical-energy\" data-toc-modified-id=\"Mechanical-energy-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Mechanical energy</a></span><ul class=\"toc-item\"><li><span><a href=\"#Kinetic-energy\" data-toc-modified-id=\"Kinetic-energy-8.1\"><span class=\"toc-item-num\">8.1&nbsp;&nbsp;</span>Kinetic energy</a></span></li><li><span><a href=\"#Potential-energy\" data-toc-modified-id=\"Potential-energy-8.2\"><span class=\"toc-item-num\">8.2&nbsp;&nbsp;</span>Potential energy</a></span></li><li><span><a href=\"#Power\" data-toc-modified-id=\"Power-8.3\"><span class=\"toc-item-num\">8.3&nbsp;&nbsp;</span>Power</a></span></li></ul></li><li><span><a href=\"#Angular-momentum\" data-toc-modified-id=\"Angular-momentum-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Angular momentum</a></span></li><li><span><a href=\"#Torque-(moment-of-force)\" data-toc-modified-id=\"Torque-(moment-of-force)-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>Torque (moment of force)</a></span></li><li><span><a href=\"#Mechanical-energy-for-angular-motion\" data-toc-modified-id=\"Mechanical-energy-for-angular-motion-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>Mechanical energy for angular motion</a></span><ul class=\"toc-item\"><li><span><a href=\"#Kinetic-energy\" data-toc-modified-id=\"Kinetic-energy-11.1\"><span class=\"toc-item-num\">11.1&nbsp;&nbsp;</span>Kinetic energy</a></span></li><li><span><a href=\"#Work\" data-toc-modified-id=\"Work-11.2\"><span class=\"toc-item-num\">11.2&nbsp;&nbsp;</span>Work</a></span></li><li><span><a href=\"#Power\" data-toc-modified-id=\"Power-11.3\"><span class=\"toc-item-num\">11.3&nbsp;&nbsp;</span>Power</a></span></li></ul></li><li><span><a href=\"#Principles-of-conservation\" data-toc-modified-id=\"Principles-of-conservation-12\"><span class=\"toc-item-num\">12&nbsp;&nbsp;</span>Principles of conservation</a></span><ul class=\"toc-item\"><li><span><a href=\"#Principle-of-conservation-of-linear-momentum\" data-toc-modified-id=\"Principle-of-conservation-of-linear-momentum-12.1\"><span class=\"toc-item-num\">12.1&nbsp;&nbsp;</span>Principle of conservation of linear momentum</a></span></li><li><span><a href=\"#Principle-of-conservation-of-angular-momentum\" data-toc-modified-id=\"Principle-of-conservation-of-angular-momentum-12.2\"><span class=\"toc-item-num\">12.2&nbsp;&nbsp;</span>Principle of conservation of angular momentum</a></span></li><li><span><a href=\"#Principle-of-conservation-of-mechanical-energy\" data-toc-modified-id=\"Principle-of-conservation-of-mechanical-energy-12.3\"><span class=\"toc-item-num\">12.3&nbsp;&nbsp;</span>Principle of conservation of mechanical energy</a></span><ul class=\"toc-item\"><li><span><a href=\"#Conservative-forces\" data-toc-modified-id=\"Conservative-forces-12.3.1\"><span class=\"toc-item-num\">12.3.1&nbsp;&nbsp;</span>Conservative forces</a></span></li></ul></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-13\"><span class=\"toc-item-num\">13&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-internet\" data-toc-modified-id=\"Video-lectures-on-the-internet-14\"><span class=\"toc-item-num\">14&nbsp;&nbsp;</span>Video lectures on the internet</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-15\"><span class=\"toc-item-num\">15&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-02-03T11:34:44.530557Z", "start_time": "2021-02-03T11:34:44.104775Z"}}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "%matplotlib inline\n", "sns.set_context('notebook', font_scale=1.2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The development of the laws of motion of bodies  \n", "\n", "\"The theoretical development of the laws of motion of bodies is a problem of such interest and importance that it has engaged the attention of all the most eminent mathematicians since the invention of dynamics as a mathematical science by <PERSON>, and especially since the wonderful extension which was given to that science by <PERSON>.\"\n", "\n", "&#8212; <PERSON>, 1834 (<PERSON><PERSON><PERSON>, 2005).  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Newton's laws of motion\n", "\n", "The Newton's laws of motion describe the relationship between the forces acting on a body and the resultant linear motion due to those forces:\n", "\n", "- **First law**: An object will remain at rest or in uniform motion in a straight line unless an external force acts on the body.\n", "- **Second law**: The acceleration of an object is directly proportional to the net force acting on the object and inversely proportional to the mass of the object: $\\vec{F} = m \\vec{a}$.\n", "- **Third law**: Whenever an object exerts a force $\\vec{F}_1$ (action) on a second object, this second object simultaneously exerts a force $\\vec{F}_2$ on the first object with the same magnitude but opposite direction (reaction): $\\vec{F}_2 = −\\vec{F}_1.$\n", "\n", "These three statements are astonishing in their simplicity and how much of knowledge they empower.   \n", "<PERSON> was born in 1943 and his works that resulted in these equations and other discoveries were mostly done in the years of 1666 and 1667, when he was only 24 years old!  \n", "\n", "Here are these three laws in <PERSON>'s own words (from page 83 of Book I in the first American edition of the [*Philosophiæ Naturalis Principia Mathematica*](http://archive.org/details/newtonspmathema00newtrich):\n", "\n", "> LAW I.    \n", "> *Every body perseveres in its state of rest, or of uniform motion in a right line, unless it is compelled to change that state by forces impressed thereon.*   \n", "> LAW II.    \n", "> *The alteration of motion is ever proportional to the motive force impressed; and is made in the direction of the right line in which that force is impressed.*   \n", "> LAW III.   \n", "> *To every action there is always opposed an equal reaction: or the mutual actions of two bodies upon each other are always equal, and directed to contrary parts.*   \n", "\n", "And <PERSON> carefully defined mass, motion, and force in the first page of the book I (page 73 of the [*Principia*](http://archive.org/details/newtonspmathema00newtrich)):  \n", "\n", "> DEFINITION I.   \n", "> *The quantity of matter is the measure of the same, arising from its density and bulk conjunctly.*   \n", "> ...It is this quantity that I mean hereafter everywhere under the name of body or mass.   \n", "> DEFINITION II.   \n", "> *The quantity of motion is the measure of the same, arising from the velocity and quantity of matter conjunctly.*    \n", "> The motion of the whole is the sum of the motions of all the parts; and therefore in a body double in quantity, with equal velocity, the motion is double; with twice the velocity, it is quadruple.   \n", "> DEFINITION IV.   \n", "> *An impressed force is an action exerted upon a body, in order to change its state, either of rest, or of moving uniformly forward in a right line.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Linear momentum\n", "\n", "From Definition II above, we can see that Newton defined as motion what we know today as linear momentum, the product between mass and velocity:\n", "\n", "$$ \\vec{p} = m\\vec{v} $$\n", "\n", "So, in his second law, *alteration of motion is ever proportional to the motive force impressed*, if we understand that it was implicit that the *alteration* occurs in a certain time (or we can understand *force impressed* as force during a certain  time), <PERSON> actually stated:\n", "\n", "$$ \\vec{F} = \\frac{\\Delta\\vec{p}}{\\Delta t} \\;\\;\\;\\;\\;\\; \\text{or}\\;\\;\\;\\;\\;\\; \\vec{F}\\Delta t = \\Delta\\vec{p}$$\n", "\n", "What is equivalent to $\\vec{F} = m\\vec{a} \\; $ if mass is constant."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Impulse\n", "\n", "The mechanical linear impulse is a related concept and it can be derived from the second law of motion:\n", "\n", "$$ \\vec{Imp} = \\vec{F}\\Delta t = m\\Delta\\vec{v} $$\n", "\n", "And if the force varies with time:\n", "\n", "$$ \\vec{Imp} = \\sum_t \\vec{F}(t)\\Delta t $$\n", "\n", "or using [infinitesimal calculus](http://en.wikipedia.org/wiki/Infinitesimal_calculus) (that it was independently developed by <PERSON> himself and <PERSON><PERSON><PERSON><PERSON>):\n", "\n", "$$ \\vec{Imp} = \\int_t \\vec{F}(t)dt $$\n", "\n", "The concept of impulse due to a force that varies with time is often applied in biomechanics because it is common to measure forces (for example, with force plates) during human movement.  \n", "When such varying force is measured, the impulse can be calculated as the area under the force-versus-time curve:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-02-03T11:34:44.747485Z", "start_time": "2021-02-03T11:34:44.531765Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# simulate some data:\n", "t = np.arange(0, 1.01, 0.01)\n", "f = 1000*(-t**3+t**2)\n", "# plot:\n", "plt.rc('axes',  labelsize=16) \n", "plt.rc('xtick', labelsize=14) \n", "plt.rc('ytick', labelsize=14)\n", "hfig, hax = plt.subplots(1,1, figsize=(8,5))\n", "hax.plot(t, f, linewidth=3)\n", "hax.set_xlim(-.1, 1.1)\n", "hax.grid()\n", "hax.set_ylabel('Force [N]')\n", "hax.set_xlabel('Time [s]')\n", "plt.fill(t, f, 'b', alpha=0.3)\n", "# area (impulse) with the trapz numerical integration method:\n", "from scipy.integrate import trapz\n", "imp = trapz(f, t)\n", "# plot a rectangle for the mean impulse value:\n", "plt.fill(np.array([t[0], t[0], t[-1], t[-1]]),\n", "         np.array([0, imp, imp, 0]/(t[-1]-t[0])), 'r', alpha=0.3)\n", "s = '$i=F\\Delta t = %.1f Ns$'%imp \n", "plt.text(.4, 40, s, fontsize=18,\n", "         bbox=dict(facecolor='white', edgecolor='white'));"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Force\n", "\n", "There are many manifestations of force we may experience during movement: gravitational, friction, ground reaction force, muscle force, buoyancy, elastic force, and other less visible such as electromagnetic, nuclear, etc. But in reality, all these different forces can be grouped in only four fundamental forces:\n", "\n", "- Strong force: hold the nucleus of an atom together. Range of action is $10^{-15}$ m. \n", "- Weak force: force acting between particles of the nucleus. Range of action is $10^{-18}$ m.\n", "- Electromagnetic force: forces between electrical charges and the magnetic forces.\n", "- Gravity force: forces between masses; is the weakest of the four fundamental forces.\n", "\n", "In mechanics, forces can be classified as either contact or body forces. The contact force acts at the point of contact between two bodies. The body force acts on the entire body with no contact (e.g., gravity and electromagnetic forces).   \n", "In biomechanics, another useful classification is to divide the forces in either external or internal in relation to the human body. External forces result from interactions with an external body or environment (e.g., gravity and ground reaction forces). Internal forces result from interactions inside the body (e.g., the forces between bones)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Work\n", "\n", "The mechanical work of a force done on a body is the product between the component of the force in the direction of the resultant motion and the displacement: \n", "\n", "$$ W = \\vec{F} \\cdot \\Delta\\vec{x} $$\n", "\n", "Where the symbol $\\cdot$ stands for the [scalar product](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ScalarVector.ipynb) mathematical function.\n", "\n", "Mechanical work can also be understood as the amount of mechanical energy transferred into or out of a system."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mechanical energy\n", "\n", "Mechanical energy is the sum of kinetic and potential energies.\n", "\n", "### Kinetic energy\n", "\n", "$$ E_k = \\frac{1}{2}mv^2 $$\n", "\n", "The linear momentum and the kinetic energy are related by:\n", "\n", "$$ \\vec{p} = \\frac{\\partial E_k}{\\partial\\vec{v}} $$\n", "\n", "### Potential energy\n", "\n", "The potential energy due to the gravitational force at the Earth's surface is:\n", "\n", "$$ E_p = mgh $$\n", "\n", "The potential energy stored in a spring is:\n", "\n", "$$ E_p = \\frac{1}{2}Kx^2 $$\n", "\n", "### Power \n", " \n", "$$ P = \\frac{\\Delta E}{\\Delta t} \\quad \\text{and} \\quad P = \\vec{F} \\cdot \\vec{v} $$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Angular momentum\n", "\n", "In analogy to the linear momentum, the angular momentum is the quantity of movement of a particle rotating around an axis at a distance $\\vec{r}$:\n", "\n", "$$ \\vec{L} = \\vec{r} \\times \\vec{p} $$\n", "\n", "For a particle rotating around an axis, the angular momentum can be expressed as:\n", "\n", "$$ \\vec{L} = I \\vec{\\omega} $$\n", "\n", "Where $I$ is the rotational inertia or moment of inertia of the particle around the axis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tor<PERSON> (moment of force)\n", "\n", "In analogy to the second Newton's law for the linear case, torque or moment of force (or simply moment) is the time derivative of angular momentum:\n", "\n", "$$ \\vec{M} = \\frac{d\\vec{L}}{dt} = \\frac{d}{dt}(\\vec{r} \\times \\vec{p}) = \\frac{d\\vec{r}}{dt} \\times \\vec{p} + \\vec{r} \\times \\frac{d\\vec{p}}{dt} = 0 + \\vec{r} \\times \\vec{F} $$\n", "\n", "$$ \\vec{M} = \\vec{r} \\times \\vec{F} $$\n", "\n", "$$ \\vec{M} = (r_x\\:\\mathbf{\\hat{i}}+r_y\\:\\mathbf{\\hat{j}}+r_z\\:\\mathbf{\\hat{k}}) \\times  (F_x\\:\\mathbf{\\hat{i}}+F_y\\:\\mathbf{\\hat{j}}+F_z\\:\\mathbf{\\hat{k}}) $$\n", "\n", "Where the symbol $\\times$ stands for the [cross product](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ScalarVector.ipynb) mathematical function.   \n", "The moment of force can be calculated as the determinant of the following matrix:\n", "\n", "$$ \\vec{M} = \\begin{bmatrix}\n", "\\mathbf{\\hat{i}} & \\mathbf{\\hat{j}} & \\mathbf{\\hat{k}} \\\\ \n", "r_x & r_y & r_z \\\\\n", "F_x & F_y & F_z \n", "\\end{bmatrix} $$\n", "\n", "$$ \\vec{M} = (r_yF_z-r_zF_y)\\mathbf{\\hat{i}}+(r_zF_x-r_xF_z)\\mathbf{\\hat{j}}+(r_xF_y-r_yF_x)\\mathbf{\\hat{k}} $$\n", "\n", "The moment of force can also be calculated by the geometric equivalent formula:\n", "\n", "$$ \\vec{M} = \\vec{r} \\times \\vec{F} = ||\\vec{r}||\\:||\\vec{F}||\\:sin(\\theta) $$\n", "\n", "Where $\\theta$ is the angle between the vectors $\\vec{r}$ and $\\vec{F}$. \n", "\n", "The animation below (from [Wikipedia](http://en.wikipedia.org/wiki/File:Torque_animation.gif)) illustrates the relationship between force ($\\vec{F}$), torque ($\\tau$), and momentum vectors ($\\mathbf{p}$ and $\\vec{L}$):   \n", "\n", "<figure><img src=\"http://upload.wikimedia.org/wikipedia/commons/0/09/Torque_animation.gif\" alt=\"Torque animation\" width=\"300\"/><figcaption><center><i>Figure. Relationship between force ($\\mathbf{F}$), torque ($\\tau$), and momentum vectors ($\\mathbf{p}$ and $\\mathbf{L}$) (from [Wikipedia](http://en.wikipedia.org/wiki/File:Torque_animation.gif)).</i></center></figcaption></figure>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mechanical energy for angular motion\n", " \n", "### Kinetic energy\n", " \n", "$$ E_k = \\frac{1}{2}I\\omega^2 $$\n", "\n", "### Work\n", " \n", "$$ W = \\vec{M} \\cdot \\Delta\\vec{\\theta} $$\n", " \n", "### Power\n", " \n", "$$ P = \\frac{\\Delta E}{\\Delta t} \\quad \\text{and} \\quad P = \\vec{M} \\cdot \\vec{\\omega} $$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Principles of conservation\n", "\n", "### Principle of conservation of linear momentum\n", "\n", "> *In a closed system with no external forces acting upon it, the total linear momentum of this system is constant.*\n", "\n", "### Principle of conservation of angular momentum\n", "\n", "> *In a closed system with no external forces acting upon it, the total angular momentum of this system is constant.*\n", "\n", "### Principle of conservation of mechanical energy\n", "\n", "> *In a closed system with no external forces acting upon it, the mechanical energy of this system is constant if only conservative forces act in this system.*\n", "\n", "\n", "#### Conservative forces\n", "\n", "A force is said to be conservative if this force produces the same work regardless of its trajectory between two points, if not the force is said to be non-conservative. Mathematically, the force $\\vec{F}$ is conservative if:\n", "\n", "$$ \\oint \\vec{F} \\cdot d\\vec{s} = 0 $$\n", "\n", "The gravitational force and the elastic force of an ideal spring are examples of conservative forces but friction force is not conservative. The forces generated by our muscles are also not conservative."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Read chapter 0, What is mechanics, from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the internet\n", "\n", "- MIT OpenCourseWare: [History of Dynamics; Motion in Moving Reference Frames](https://youtu.be/GUvoVvXwoOQ)  \n", "- Fisica Universitária: [O que é a Mecânica](https://youtu.be/T10lXTek_JE)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "3l-W0R08M8jb"}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON> (2010) Engineering Mechanics: Dynamics. 12th edition. (<PERSON><PERSON><PERSON> (2011) Dinâmica: Mecânica para Engenharia. 12a edição).\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  \n", "- <PERSON> (2005) [Classical Mechanics](https://books.google.com.br/books?id=P1kCtNr-pJsC). University Science Books."]}], "metadata": {"colab": {"collapsed_sections": [], "name": "Copy of newtonLawForParticles.ipynb", "provenance": [], "version": "0.3.2"}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}