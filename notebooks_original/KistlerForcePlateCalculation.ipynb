{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Kistler force plate calculation\n", "\n", "<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In order to get proper signals of a <PERSON><PERSON><PERSON> force plate, we have to calibrate the acquired data according to the factory calibration (i.e., transform the readed values in volts of the 8 channels to values in newtons for fx12, fx34, fy14, fy23, fz1, fz2, fz3, fz4) and then calculate the resultant forces (Fx, Fy, Fz), resultant moments of force (Mx, My, Mz), and center of pressure (COPx, COPy) quantities (see figure below for the convention used). \n", "\n", "<div class='center-align'><figure><img src=\"./../images/KistlerForcePlate.png\"alt=\"Kistler force plate and its coordinate system convention\"/><figcaption><center><i>Figure. Kistler force plate and its coordinate system convention (from [Kistler Force Plate Formulae](http://isbweb.org/software/movanal/vaughan/kistler.pdf)).</i></center></figcaption></figure></div>\n", "\n", "The function `kistler_fp_cal.py` performs such calculations for the Ki<PERSON>ler force plates of the BMClab, but it will work with other Kistler force plates. The function signature is:   \n", "```python\n", "grf, cop = kistler_fp_cal(data, S_matrix=None, fxfy_range=0, fz_range=0, origin=None,\n", "                          fp_type=3, show=False, axs=None):\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["from __future__ import division, print_function\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["(2000, 9)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# load data file\n", "data = np.loadtxt('./../data/kistler_fp_data.txt', delimiter='\\t', skiprows=1)\n", "data.shape"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [], "source": ["from kistler_fp_cal import kistler_fp_cal"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAswAAAG9CAYAAAABRyDjAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nkAqmc8rrpFVBd/b62uTU6w2gNTLn8gbQAqCFQV/*************************************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\nQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x73fb4e0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["grf, cop = kistler_fp_cal(data[:, 1:], fxfy_range=0, fz_range=1, show=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- [Kistler Force Plate Formulae](http://isbweb.org/software/movanal/vaughan/kistler.pdf)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function kistler_fp_cal.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["# %load ./../functions/kistler_fp_cal.py\n", "\"\"\"Ki<PERSON><PERSON> force plate calibration.\"\"\"\n", "\n", "from __future__ import division, print_function\n", "import numpy as np\n", "    \n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = 'kistler_fp_cal.py v.1 2014/12/12'\n", "\n", "\n", "def kistler_fp_cal(data, S_matrix=None, fxfy_range=0, fz_range=0, origin=None,\n", "                   fp_type=3, show=False, axs=None):\n", "    \"\"\"<PERSON><PERSON><PERSON> force plate calibration\n", "\n", "    In order to get proper signals of a <PERSON><PERSON><PERSON> force plate, we have to\n", "    calibrate the acquired data according to the factory calibration (i.e.,\n", "    transform the readed values in volts of the 8 channels to values in newtons\n", "    for fx12, fx34, fy14, fy23, fz1, fz2, fz3, fz4) and then calculate the\n", "    resultant forces (Fx, <PERSON>y, Fz), resultant moments of force (<PERSON>x, <PERSON>, Mz),\n", "    and center of pressure (COPx, COPy) quantities. See [1]_ and [2]_\n", "\n", "    Parameters\n", "    ----------\n", "    data  : array_like [fx12, fx34, fy14, fy23, fz1, fz2, fz3, fz4]\n", "        Kistler force plate data (8 columns, in Volts)\n", "    S_matrix  : array_like\n", "        sensitivity matrix for <PERSON><PERSON><PERSON> force plate (8 columns) and at least\n", "        one row\n", "    fxfy_range  : number [0, 1, 2, 3], optional\n", "        Fx/Fy amplifier range used in the data acquisition\n", "        Fx/Fy [kN]: 0.25, 1.25, 2.5, 5\n", "        This is only used if the sensitivity matrix has more than one row\n", "        and the first four columns of the corresponding row are selected.\n", "    fz_range  : number [0, 1, 2, 3], optional\n", "        Fz amplifier range used in the data acquisition\n", "        Fz [kN]: 1, 5, 10, 20\n", "        This is only used if the sensitivity matrix has more than one row\n", "        and the last four columns of the corresponding row are selected.\n", "    origin  : array_like [a, b, az0]\n", "        coordinates of the force plate origin (az0 is negative, in meters)\n", "    fp_type  : number [1, 2, 3, 4]\n", "        type of force plate to be used in the COP correction  \n", "        1: type 9281E11\n", "        2: type 9281E and 9281EA (SN: <= 616901)\n", "        3: type 9281E and 9281EA (SN: >= 616902)\n", "        4: type 9287C, 9287CA\n", "    show  : bool, optional (default = False)\n", "        if True (1), plot data in matplotlib figure.\n", "    axs  : a matplotlib.axes.Axes instance, optional (default = None).\n", "    \n", "    Returns\n", "    -------\n", "    grf    : numpy array [Fx, <PERSON>y, <PERSON>z, <PERSON>x, My, Mz]\n", "            ground reaction force data\n", "    cop    : numpy array [COPx, COPy]\n", "            center of pressure data\n", "\n", "    References\n", "    ----------\n", "    .. [1] http://isbweb.org/software/movanal/vaughan/kistler.pdf\n", "    .. [2] http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/KistlerForcePlateCalculation.ipynb\n", "\n", "    \"\"\"\n", "\n", "    if not S_matrix:\n", "        # sensitivity matrix for Kistler force plate 9281EA Serial no. 4402018\n", "        # calibrated range\n", "        # Fx/Fy [kN]: 0.25, 1.25, 2.5, 5\n", "        # Fz [kN]:    1, 5, 10, 20\n", "        S_matrix = np.array([[38.117, 37.723, 38.062, 38.008, 19.492, 19.445, 19.426, 19.457],\n", "                             [ 7.623,  7.545,  7.212,  7.602,  3.898,  3.889,  3.885,  3.891],\n", "                             [ 3.803,  3.761,  3.800,  3.796,  1.951,  1.945,  1.944,  1.948],\n", "                             [ 1.901,  1.881,  1.900,  1.898,  0.976,  0.973,  0.972,  0.974]])\n", "    S_matrix = np.atleast_2d(S_matrix)\n", "    S_matrix = np.hstack((S_matrix[fxfy_range, 0:4], S_matrix[fz_range, 4:9]))\n", "\n", "    if not origin:\n", "        # origin for Kistler force plate 9281EA Serial no. 4402018\n", "        origin = np.array([0.120, 0.200, -0.048])\n", "    a, b, az0 = np.hsplit(np.asarray(origin), 3)\n", "\n", "    # COP correction coefficients\n", "    if fp_type == 1:    # Type 9281E11\n", "        Px = [2.51997E-15, -2.18826E-10, -2.69254E-07, -4.85912E-11, 4.55731E-6, -4.18892E-2]\n", "        Py = [2.83750E-15, -1.00051E-10, -2.05349E-06, -1.16374E-10, 4.71553E-6, 6.89265E-2]\n", "    elif fp_type == 2:  # Type 9281E and 9281EA (SN: <= 616901)\n", "        Px = [1.1604E-14, -8.39091E-10, -1.44293E-6, -2.85927E-10, 2.05575E-5, -0.113525]\n", "        Py = [1.27251E-14, -3.13238E-10, -3.33888E-6, -6.49641E-10, 1.53937E-5, 1.12624E-1]\n", "    elif fp_type == 3:  # Type 9281E and 9281EA (SN: >= 616902)\n", "        Px = [7.92063E-15, -5.821E-10, -2.77102E-6, -1.76083E-10, 1.29695E-5, -0.0413979]\n", "        Py = [8.82869E-15, -2.48554E-10, -1.76282E-6, -4.22186E-10, 1.2091E-5, 5.16279E-2]\n", "    elif fp_type == 4:  # Type 9287C, 9287CA\n", "        Px = [1.72454E-16, -4.82275E-11, 3.30016E-7, -9.46569E-12, 2.78736E-6, -8.20399E-3]\n", "        Py = [2.20428E-16, -1.80864E-11, -7.30249E-7, -3.03080E-11, 2.64974E-6, 5.41166E-2]\n", "    else:\n", "        Px = []\n", "        Py = []\n", "\n", "    # Calibration\n", "    data = 1000*data/S_matrix\n", "\n", "    fx12, fx34, fy14, fy23, fz1, fz2, fz3, fz4 = np.hsplit(data, 8)\n", "\n", "    # Medio-lateral force\n", "    Fx = fx12 + fx34\n", "    # Anterior-posterior force\n", "    Fy = fy14 + fy23\n", "    # Vertical force\n", "    Fz = fz1 + fz2 + fz3 + fz4\n", "    # Plate moment about X-axis\n", "    Mx = b * (fz1 + fz2 - fz3 - fz4)\n", "    # Plate moment about Y-axis\n", "    My = a * (-fz1 + fz2 + fz3 - fz4)\n", "    # Plate moment about Z-axis\n", "    Mz = b * (-fx12 + fx34) + a * (fy14 - fy23)\n", "    # Plate moment about top plate surface\n", "    Mx = Mx + Fy*az0\n", "    # Plate moment about top plate surface\n", "    My = My - Fx*az0\n", "    # X-Coordinate of force application point (COP)\n", "    ax = -My / Fz\n", "    # Y-Coordinate of force application point (COP)\n", "    ay = Mx / Fz\n", "    # Coefficient of Friction x-component\n", "    #COFx = Fx / Fz\n", "    # Coefficient of Friction y-component\n", "    #COFy = Fy / Fz\n", "    # COP correction\n", "    if Px:\n", "        Dax = (Px[0]*ay**4 + Px[1]*ay**2 + Px[2])*ax**3 + (Px[3]*ay**4 + Px[4]*ay**2 + Px[5])*ax\n", "        ax = ax - Dax\n", "    if Py:\n", "        Day = (Py[0]*ax**4 + Py[1]*ax**2 + Py[2])*ay**3 + (Py[3]*ax**4 + Py[4]*ax**2 + Py[5])*ay\n", "        ay = ay - Day\n", "    # Free moment\n", "    Mz = Mz - Fy*ax + Fx*ay\n", "\n", "    grf = np.hstack((Fx, Fy, Fz, Mx, My, Mz))\n", "    cop = np.hstack((ax, ay))\n", "\n", "    if show:\n", "        _plot(grf, cop, axs)\n", "\n", "    return grf, cop\n", "\n", "\n", "def _plot(grf, cop, axs):\n", "    \"\"\"Plot results of the detect_peaks function, see its help.\"\"\"\n", "    try:\n", "        import matplotlib.pyplot as plt\n", "    except ImportError:\n", "        print('matplotlib is not available.')\n", "        return\n", "\n", "    grf = np.hstack((grf, cop))\n", "\n", "    if axs is None:\n", "        _, axs = plt.subplots(4, 2, figsize=(10, 6), sharex=True)\n", "\n", "    axs = axs.flatten()\n", "    ylabel = ['Fx [N]', 'Fy [N]', 'Fz [N]',\n", "              'Mx [Nm]', 'My [Nm]', 'Mz [Nm]', 'COPx [m]', 'COPy [m]']\n", "    for i, axi in enumerate(axs):\n", "        axi.plot(grf[:, i], 'b', lw=1)\n", "        axi.set_ylabel(ylabel[i], fontsize=14)\n", "        axi.yaxis.set_major_locator(plt.MaxNLocator(4))\n", "        axi.yaxis.set_label_coords(-.2, 0.5)\n", "\n", "    axs[6].set_xlabel('Sample #', fontsize=14)\n", "    axs[7].set_xlabel('Sample #', fontsize=14)\n", "    plt.suptitle('Ground reaction force data', y=1.02, fontsize=16)\n", "    plt.tight_layout(h_pad=.1)\n", "    # plt.grid()\n", "    plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.4.3"}}, "nbformat": 4, "nbformat_minor": 0}