{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Descriptive statistics of data\n", "\n", "<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is a function for the calculation of descriptive statistics which might be useful for the initial characterization and visualization of numerical data.  \n", "The function signature is:\n", "```python\n", "stats = statdesc(data, missing='NaN', labels=[], alpha=.05, show=2)\n", "```\n", "And the function help:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function statdesc in module statdesc:\n", "\n", "statdesc(data, missing='NaN', labels=[], alpha=0.05, show=2)\n", "    Descriptive statistics of data.\n", "    \n", "    This function calculates the following statistics for each column\n", "    (variable) of the input: mean and unbiased standard deviation [1]_, 95%\n", "    confidence interval (confidence limits for the mean) with unknown\n", "    population STD [2]_, minimum and maximum, median, 25th and 75th percentiles\n", "    [3]_, test for normality (<PERSON><PERSON><PERSON><PERSON><PERSON>'s test) [4]_, and a test for\n", "    equality of variances for all columns (<PERSON><PERSON>'s or <PERSON>'s test) [5]_.\n", "    \n", "    This function also generates plots (if matplotlib is available) to\n", "    visualize the data and shows the calculated statistics on screen.\n", "    \n", "    Parameters\n", "    ----------\n", "    data : array_like\n", "        1D or 2D (column oriented) numerical data with possible missing values\n", "    \n", "    missing : string ('nan') or number (int or float), optional\n", "        option to enter a number representing missing values (default = 'nan')\n", "    \n", "    labels : list of strings, optional\n", "        labels for each column (variable) in data\n", "    \n", "    alpha : float, optional\n", "        statistical significance level (to decide which test for equality of\n", "        variances to use)\n", "    \n", "    show : integer (0 or 1 or 2), optional\n", "        option to show plots with some descritive statistics (0: don't show\n", "        any plot; 1: show plots only for the grouped data; 2: show plots for\n", "        individual data as well as for the grouped data (default))\n", "    \n", "    Returns\n", "    -------\n", "    m_sd : array\n", "        mean and unbiased standard deviation of each column (variable) in data\n", "    \n", "    ci : array\n", "        95% confidence interval (confidence limits for the mean) with unknown\n", "        population STD for each column (variable) in data\n", "    \n", "    min_max : array\n", "        minimum and maximum of each column (variable) in data\n", "    \n", "    quartiles : array\n", "        median, 25th and 75th percentiles of each column (variable) in data\n", "    \n", "    normality : array\n", "        test for normality of each column (variable) in data (<PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "        test)\n", "    \n", "    eq_var : array\n", "        test for equality of variances for all columns (variables) in data\n", "        (<PERSON><PERSON>'s or <PERSON>'s test)\n", "    \n", "    References\n", "    ----------\n", "    .. [1] http://www.itl.nist.gov/div898/handbook/eda/section3/eda356.htm\n", "    .. [2] http://www.itl.nist.gov/div898/handbook/prc/section1/prc14.htm.\n", "    .. [3] http://www.itl.nist.gov/div898/handbook/prc/section2/prc252.htm.\n", "    .. [4] http://www.itl.nist.gov/div898/handbook/prc/section2/prc213.htm.\n", "    .. [5] http://www.itl.nist.gov/div898/handbook/eda/section3/eda35a.htm.\n", "    \n", "    Examples\n", "    --------\n", "    >>> import numpy as np\n", "    >>> from statdesc import statdesc\n", "    >>> y = np.random.randn(20,3)\n", "    >>> statdesc(y)                # use the default options\n", "    >>> y[8:12,1] = np.NaN         # add a missing value\n", "    >>> y[12,1] = 2                # add another missing value\n", "    >>> statdesc(y, False, 2, ['A','B'], .01) # set arguments\n", "    >>> m_sd,ci,minmax,quartiles,normality,eq_var = statdesc(y)\n", "    \n", "    See Also\n", "    --------\n", "    scipy.stats.describe : Computes several descriptive statistics using Scipy\n", "    pandas.DataFrame.describe : Computes several descriptive statistics using Pandas\n", "\n"]}], "source": ["import sys\n", "sys.path.insert(1, r'./functions')  # add to pythonpath\n", "from statdesc import statdesc\n", "\n", "help(statdesc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test `statdesc.py`:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfEAAAFjCAYAAAAtnDI1AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nubYAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x45e1eb8>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------------------\n", "Descriptive statistics for data (100 rows, 1 column)\n", "0 missing values\n", "-----------------------------------------------------------\n", "Variable              Mean             STD\n", "-----------------------------------------------------------\n", "1                -0.031199        1.096605\n", "-----------------------------------------------------------\n", "95% confidence interval with unknown population STD\n", "Variable             Lower           Upper\n", "-----------------------------------------------------------\n", "1                -0.058260       -0.004138\n", "-----------------------------------------------------------\n", "Variable           Minimum         Maximum\n", "-----------------------------------------------------------\n", "1                -2.604322        3.143039\n", "-----------------------------------------------------------\n", "Variable            Median   25th percent.   75th percent.\n", "-----------------------------------------------------------\n", "1                -0.137626       -0.775762        0.788823\n", "-----------------------------------------------------------\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>'s test for normality\n", "Variable       W statistic         p value\n", "-----------------------------------------------------------\n", "1                 0.986654        0.414805\n", "-----------------------------------------------------------\n"]}], "source": ["stats = statdesc(np.random.randn(100, 1))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfEAAAFjCAYAAAAtnDI1AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ngg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x81a8f60>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAhAAAAIXCAYAAADNBWcxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\niTcW0AEPMHBgt/********************************/GjfM7mqT2r56XcMKWV/nWxqeUQLRB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\n", "text/plain": ["<matplotlib.figure.Figure at 0x828a780>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------------------\n", "Descriptive statistics for data (100 rows, 2 columns)\n", "0 missing values\n", "-----------------------------------------------------------\n", "Variable              Mean             STD\n", "-----------------------------------------------------------\n", "1                 0.200145        1.036444\n", "2                 0.015605        0.945703\n", "-----------------------------------------------------------\n", "95% confidence interval with unknown population STD\n", "Variable             Lower           Upper\n", "-----------------------------------------------------------\n", "1                 0.174568        0.225721\n", "2                -0.007732        0.038943\n", "-----------------------------------------------------------\n", "Variable           Minimum         Maximum\n", "-----------------------------------------------------------\n", "1                -2.688702        2.489992\n", "2                -2.123743        2.438873\n", "-----------------------------------------------------------\n", "Variable            Median   25th percent.   75th percent.\n", "-----------------------------------------------------------\n", "1                 0.208278       -0.359966        0.940224\n", "2                -0.127119       -0.640241        0.749677\n", "-----------------------------------------------------------\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>'s test for normality\n", "Variable       W statistic         p value\n", "-----------------------------------------------------------\n", "1                 0.990782        0.727672\n", "2                 0.985118        0.323867\n", "-----------------------------------------------------------\n", "<PERSON><PERSON>'s test for equality of variances\n", "               t statistic         p value\n", "-----------------------------------------------------------\n", "                  0.825739        0.363507\n", "-----------------------------------------------------------\n"]}], "source": ["stats = statdesc(np.random.randn(100, 2), show=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function cogve.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["# %load statdesc.py\n", "#!/usr/bin/env python\n", "\"\"\"Descriptive statistics of data.\"\"\"\n", "\n", "from __future__ import division, print_function\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = \"1.0.2\"\n", "__license__ = \"MIT\"\n", "\n", "import numpy as np\n", "import scipy.stats as stats\n", "try:\n", "    import matplotlib.pyplot as plt\n", "    import matplotlib.ticker as ticker\n", "except ImportError:\n", "    plt = None\n", "\n", "\n", "def statdesc(data, missing='NaN', labels=[], alpha=.05, show=2):\n", "    \"\"\"\n", "    Descriptive statistics of data.\n", "\n", "    This function calculates the following statistics for each column\n", "    (variable) of the input: mean and unbiased standard deviation [1]_, 95%\n", "    confidence interval (confidence limits for the mean) with unknown\n", "    population STD [2]_, minimum and maximum, median, 25th and 75th percentiles\n", "    [3]_, test for normality (<PERSON><PERSON><PERSON><PERSON><PERSON>'s test) [4]_, and a test for\n", "    equality of variances for all columns (<PERSON><PERSON>'s or <PERSON>'s test) [5]_.\n", "\n", "    This function also generates plots (if matplotlib is available) to\n", "    visualize the data and shows the calculated statistics on screen.\n", "\n", "    Parameters\n", "    ----------\n", "    data : array_like\n", "        1D or 2D (column oriented) numerical data with possible missing values\n", "\n", "    missing : string ('nan') or number (int or float), optional\n", "        option to enter a number representing missing values (default = 'nan')\n", "\n", "    labels : list of strings, optional\n", "        labels for each column (variable) in data\n", "\n", "    alpha : float, optional\n", "        statistical significance level (to decide which test for equality of\n", "        variances to use)\n", "\n", "    show : integer (0 or 1 or 2), optional\n", "        option to show plots with some descritive statistics (0: don't show\n", "        any plot; 1: show plots only for the grouped data; 2: show plots for\n", "        individual data as well as for the grouped data (default))\n", "\n", "    Returns\n", "    -------\n", "    m_sd : array\n", "        mean and unbiased standard deviation of each column (variable) in data\n", "\n", "    ci : array\n", "        95% confidence interval (confidence limits for the mean) with unknown\n", "        population STD for each column (variable) in data\n", "\n", "    min_max : array\n", "        minimum and maximum of each column (variable) in data\n", "\n", "    quartiles : array\n", "        median, 25th and 75th percentiles of each column (variable) in data\n", "\n", "    normality : array\n", "        test for normality of each column (variable) in data (<PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "        test)\n", "\n", "    eq_var : array\n", "        test for equality of variances for all columns (variables) in data\n", "        (<PERSON><PERSON>'s or <PERSON>'s test)\n", "\n", "    References\n", "    ----------\n", "    .. [1] http://www.itl.nist.gov/div898/handbook/eda/section3/eda356.htm\n", "    .. [2] http://www.itl.nist.gov/div898/handbook/prc/section1/prc14.htm.\n", "    .. [3] http://www.itl.nist.gov/div898/handbook/prc/section2/prc252.htm.\n", "    .. [4] http://www.itl.nist.gov/div898/handbook/prc/section2/prc213.htm.\n", "    .. [5] http://www.itl.nist.gov/div898/handbook/eda/section3/eda35a.htm.\n", "\n", "    Examples\n", "    --------\n", "    >>> import numpy as np\n", "    >>> from statdesc import statdesc\n", "    >>> y = np.random.randn(20,3)\n", "    >>> statdesc(y)                # use the default options\n", "    >>> y[8:12,1] = np.NaN         # add a missing value\n", "    >>> y[12,1] = 2                # add another missing value\n", "    >>> statdesc(y, False, 2, ['A','B'], .01) # set arguments\n", "    >>> m_sd,ci,minmax,quartiles,normality,eq_var = statdesc(y)\n", "\n", "    See Also\n", "    --------\n", "    scipy.stats.describe : Computes several descriptive statistics using Scipy\n", "    pandas.DataFrame.describe : Computes several descriptive statistics using Pandas\n", "\n", "    \"\"\"\n", "\n", "    data = np.asarray(data)  # convert the input to array\n", "    if len(data.shape) == 1:\n", "        data = data.reshape(data.shape[0], 1)\n", "    # missing data: don't use masked arrray, some functions don't handle that\n", "    if isinstance(missing, (int, float)) and ~np.isnan(missing):\n", "        # if missing option is string, must be 'NaN', then data has already NaN\n", "        data[data == missing] = np.NaN\n", "\n", "    m_sd = np.zeros((data.shape[1], 2)) * np.NaN\n", "    ci = np.zeros((data.shape[1], 2)) * np.NaN\n", "    min_max = np.zeros((data.shape[1], 2)) * np.NaN\n", "    quartiles = np.zeros((data.shape[1], 3)) * np.NaN\n", "    normality = np.zeros((data.shape[1], 2)) * np.NaN\n", "    eq_var = np.zeros((1, 2)) * np.NaN\n", "    x = []\n", "    nmiss = 0\n", "    min_len = 0\n", "\n", "    for i in range(data.shape[1]):\n", "        # due to missing data, each column can have different length;\n", "        # use list of arrays\n", "        x.append(data[~np.isnan(data[:, i]), i])\n", "        nmiss += data.shape[0] - x[i].shape[0]  # total number of missing value\n", "        # skip empty array (data column with missing data only)\n", "        if x[i].shape[0] == 0:\n", "            print('Skipping column %d, only missing data' % (i + 1))\n", "            continue\n", "        # at least 2 sets with 3 points to test for equality of variances\n", "        if x[i].shape[0] > 2:\n", "            min_len += 1\n", "        # handle labels\n", "        if len(labels) > i and labels[i]:\n", "            pass\n", "        else:\n", "            if len(labels) > i:\n", "                labels[i] = str(i+1)\n", "            else:\n", "                labels.append(str(i+1))\n", "        # summary statistics\n", "        m_sd[i], ci[i], min_max[i], quartiles[i], normality[i] = summary(x[i])\n", "        if show > 1 and plt:  # PLOT\n", "            #plot for each variable\n", "            plot1var(data[:, i], x[i], m_sd[i], min_max[i], normality[i],\n", "                     labels[i], alpha, data.shape[1])\n", "\n", "    # remove empty arrays (data columns with missing data only)\n", "    i = 0\n", "    while i < len(x):\n", "        if x[i].size == 0:\n", "            x.pop(i)\n", "        else:\n", "            i += 1\n", "\n", "    # test for equality of variances\n", "    if len(x) > 1 and min_len > 1:\n", "        # at least 2 sets with 3 points to run this function\n", "        # <PERSON><PERSON>'s test is an alternative to the <PERSON> test. The <PERSON><PERSON> test\n", "        # is less sensitive than the <PERSON> test to departures from normality\n", "        # For data with nornal distribution, <PERSON>'s test has better\n", "        # performance.\n", "        if np.all(normality[:, 1] > .05):\n", "            eq_var[0] = stats.bartlett(*x)\n", "        else:\n", "            eq_var[0] = stats.levene(*x, center='median')\n", "\n", "    if show and plt:  # PLOT\n", "        if data.shape[1] > 1:\n", "            #summary plot\n", "            plotallvar(data, x, min_max, eq_var, min_len, alpha, labels)\n", "            #scatterplot matrix\n", "            scatterplot(data, x, label=labels)\n", "\n", "    #print results on screen\n", "    statprint(m_sd, ci, min_max, quartiles, normality, eq_var,\n", "              labels, alpha, data.shape[0], data.shape[1], nmiss, len(x))\n", "\n", "    return m_sd, ci, min_max, quartiles, normality, eq_var\n", "\n", "\n", "def summary(x):\n", "    \"\"\"summary statistics\"\"\"\n", "\n", "    # mean and standard deviation (unbiased)\n", "    m_sd = np.mean(x), np.std(x, ddof=1)\n", "    # 95% confidence interval (confidence limits for the mean)\n", "    ci = np.zeros((1, 2)) * np.NaN\n", "    if x.shape[0] > 1:  # at least 2 points to run this function\n", "        ci = stats.t._pdf(.975, x.size - 1) * m_sd[1] / np.sqrt(x.size) * \\\n", "            np.array([-1, 1]) + m_sd[0]\n", "    # minimum and maximum\n", "    min_max = x.min(), x.max()\n", "    # median, and 25th and 75th percentiles\n", "    quartiles = np.median(x), np.percentile(x, 25), np.percentile(x, 75)\n", "    # test for normality\n", "    # Shapiro-<PERSON><PERSON>k function is nicer (returns an exact p value) and simpler\n", "    normality = np.zeros((1, 2)) * np.NaN\n", "    if x.shape[0] > 2:  # at least 3 points to run this function\n", "        normality = stats.s<PERSON><PERSON><PERSON>(x)  # <PERSON><PERSON><PERSON><PERSON><PERSON>'s test\n", "        #A2,critical,sig = stats.anderson(x,dist='norm') #Anderson-Darling test\n", "        #sig2 = sig[A2>critical]\n", "        #normality =  A2, ( sig2[-1] if sig2.size else sig[0] )/100\n", "\n", "    return m_sd, ci, min_max, quartiles, normality\n", "\n", "\n", "def plot1var(data, x, m_sd, min_max, normality, labels, alpha, ncol):\n", "    \"\"\"Summary plot for each variable\"\"\"\n", "\n", "    plt.figure(figsize=(7, 5))\n", "    ax1 = plt.subplot(211)\n", "    ax1.plot(data, 'bo', alpha=0.75)\n", "    ax1.plot([0, data.shape[0] - 1], [m_sd[0], m_sd[0]], 'r', linewidth=2)\n", "    ax1.plot([0, data.shape[0] - 1], [m_sd[0] + m_sd[1], m_sd[0] + m_sd[1]],\n", "             'r--', linewidth=2)\n", "    ax1.plot([0, data.shape[0] - 1], [m_sd[0] - m_sd[1], m_sd[0] - m_sd[1]],\n", "             'r--', linewidth=2)\n", "    ax1.grid(True, linestyle='-', which='major', color='lightgrey', alpha=0.5)\n", "    title = 'Variable: Mean= %f, STD= %f' % (m_sd[0], m_sd[1]) if ncol == 1 \\\n", "            else 'Variable %s: Mean= %f, STD= %f' % (labels, m_sd[0], m_sd[1])\n", "    ax1.set_title(title)\n", "    #ax1.set_xlabel('Index')\n", "    ax1.set_ylabel('Value')\n", "    if x.shape[0] > 1:\n", "        plt.xlim(xmin=-.5, xmax=data.shape[0] - .5)\n", "        plt.ylim(ymin=min_max[0] - .05*(min_max[1] - min_max[0]),\n", "                 ymax=min_max[1] + .05 * (min_max[1] - min_max[0]))\n", "    ax2 = plt.subplot(223)\n", "    h2 = ax2.boxplot(x, notch=1)\n", "    plt.setp(h2['boxes'], color='r', linewidth=2)\n", "    plt.setp(h2['medians'], color='r', linewidth=2)\n", "    plt.xticks([1], [labels])\n", "    ax2.grid(True, linestyle='-', which='major', color='lightgrey', alpha=0.5)\n", "    ax2.set_title('Boxplot')\n", "    ax2.set_xlabel('Variable')\n", "    ax2.set_ylabel('Value')\n", "    ax3 = plt.subplot(224)\n", "    nbins = 2 * np.sqrt(x.size) if x.size < 100 else np.sqrt(x.size)\n", "    n, bins, patches = ax3.hist(x, nbins, normed=1, fc='blue', alpha=0.75)\n", "    bincenters = np.linspace((bins[0] + bins[1]) / 2,\n", "                             (bins[-2] + bins[-1]) / 2, 100)\n", "    # curve for the normal PDF\n", "    y = stats.norm.pdf(bincenters, loc=m_sd[0], scale=m_sd[1])\n", "    ax3.plot(bincenters, y, 'r-', linewidth=2)\n", "    ax3.set_xlabel('Value')\n", "    #ax3.set_ylabel('Probability')\n", "    distribution = 'normal' if normality[1] > alpha else 'not normal'\n", "    ax3.set_title('Histogram (%s, p=%1.3f)' % (distribution, normality[1]))\n", "    ax3.xaxis.set_major_locator(ticker.MaxNLocator(nbins=5, prune=None))\n", "    ax3.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5, prune=None))\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "def plotallvar(data, x, min_max, eq_var, min_len, alpha, labels):\n", "    \"\"\"Summary plot for all variables\"\"\"\n", "\n", "    plt.figure(figsize=(7, 5))\n", "    ax1 = plt.subplot(211)\n", "    h1 = ax1.plot(data)\n", "    ax1.grid(True)\n", "    ax1.set_title('All variables')\n", "    #ax1.set_xlabel('Index')\n", "    ax1.set_ylabel('Value')\n", "    #ax1.legend(labels[0:data.shape[1]])\n", "    plt.xlim(xmin=-.5, xmax=data.shape[0] - .5)\n", "    if min_max.max()-min_max.min() > 0:\n", "        plt.ylim(ymin=min_max.min() - .05 * (min_max.max() - min_max.min()),\n", "                 ymax=min_max.max() + .05 * (min_max.max() - min_max.min()))\n", "    ax2 = plt.subplot(212)\n", "    h2 = ax2.boxplot(x, notch=1)\n", "    ax2.grid(True, linestyle='-', which='major', color='lightgrey', alpha=0.5)\n", "    ax2.set_title('Boxplot')\n", "    if min_len > 1:\n", "        if eq_var[0, 1] > alpha:\n", "            tit = 'Boxplot (equality of variances, p=%f)' % eq_var[0, 1]\n", "        else:\n", "            tit = 'Boxplot (no equality of variances, p=%f)' % eq_var[0, 1]\n", "        ax2.set_title(tit)\n", "    ax2.set_xlabel('Variable')\n", "    ax2.set_ylabel('Value')\n", "    rot = 0 if len(''.join(labels)) < 50 else 45\n", "    plt.xticks(range(1, data.shape[1] + 1), labels[0: data.shape[1]],\n", "               rotation=rot)\n", "    #Set boxplot color based on color of line plot\n", "    for i in range(len(h1)):\n", "        plt.setp(h2['boxes'][i], color=h1[i].get_c(), linewidth=2)\n", "        plt.setp(h2['medians'][i], color=h1[i].get_c(), linewidth=2)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "def scatterplot(data, x, label=None):\n", "    \"\"\"Scatterplot matrix for array data\n", "    data have all the data (inlcuding missing data)\n", "    x is a list of arrays without the missing data (for histogram and fitting)\n", "    \"\"\"\n", "\n", "    fig, ax = plt.subplots(data.shape[1], data.shape[1], figsize=(8, 8))\n", "    fig.suptitle('Scatterplot Matrix', fontsize=12)\n", "    fig.subplots_adjust(hspace=0.04, wspace=0.04)\n", "    nbins2 = 4 if data.shape[1] > 3 else 5\n", "    for i in range(data.shape[1]):\n", "        for j in range(data.shape[1]):\n", "            #ax1 = plt.subplot(data.shape[1],data.shape[1],data.shape[1]*i+j+1)\n", "            if i == j:\n", "                nbins = 2 * np.sqrt(x[i].size) \\\n", "                    if x[i].size < 100 else np.sqrt(x[i].size)\n", "                n, bins, patches = ax[i, j].hist(x[i], nbins, normed=1,\n", "                                                 facecolor='blue', alpha=0.75)\n", "                bincenters = np.linspace((bins[0] + bins[1]) / 2,\n", "                                         (bins[-2] + bins[-1]) / 2, 100)\n", "                y = stats.norm.pdf(bincenters, x[i].mean(), scale=x[i].std())\n", "                ax[i, j].plot(bincenters, y, 'r-', linewidth=2)\n", "                #ax[i, j].annotate(label[j], (0.05, 0.85),\n", "                #    xycoords='axes fraction',fontweight='bold')\n", "            else:\n", "                ax[i, j].plot(data[:, i], data[:, j], 'bo', alpha=0.75)\n", "                ax[i, j].grid(True, linestyle='-', which='major',\n", "                              color='lightgrey', alpha=0.5)\n", "            ax[i, j].xaxis.set_visible(False)\n", "            ax[i, j].yaxis.set_visible(False)\n", "            ax[i, j].xaxis.set_major_locator(ticker.MaxNLocator(nbins=nbins2,\n", "                                             prune='both'))\n", "            ax[i, j].yaxis.set_major_locator(ticker.MaxNLocator(nbins=nbins2,\n", "                                             prune='both'))\n", "            if ax[i, j].is_first_col():\n", "                ax[i, j].yaxis.set_ticks_position('left')\n", "                ax[i, j].yaxis.set_visible(True)\n", "                ax[i, j].set_ylabel(label[i])\n", "            if ax[i, j].is_last_col():\n", "                ax[i, j].yaxis.set_ticks_position('right')\n", "                ax[i, j].yaxis.set_visible(True)\n", "            if ax[i, j].is_first_row():\n", "                ax[i, j].xaxis.set_ticks_position('top')\n", "                ax[i, j].xaxis.set_visible(True)\n", "            if ax[i, j].is_last_row():\n", "                ax[i, j].xaxis.set_ticks_position('bottom')\n", "                ax[i, j].xaxis.set_visible(True)\n", "                ax[i, j].set_xlabel(label[j])\n", "    plt.show()\n", "\n", "\n", "def statprint(m_sd, ci, min_max, quartiles, normality, eq_var, labels, alpha,\n", "              nrow, ncol, nmiss, nx):\n", "    \"\"\"print results on screen\"\"\"\n", "\n", "    print('-----------------------------------------------------------')\n", "    str_row = 'rows' if nrow > 1 else 'row'\n", "    str_col = 'columns' if ncol > 1 else 'column'\n", "    print('Descriptive statistics for data (%d %s, %d %s)' \\\n", "          % (nrow, str_row, ncol, str_col))\n", "    print('%d missing values' % nmiss)\n", "    print('-----------------------------------------------------------')\n", "    print('%-10s %15s %15s' % ('Variable', 'Mean', 'STD'))\n", "    print('-----------------------------------------------------------')\n", "    for i in range(ncol):\n", "        print('%-10s %15f %15f' % (labels[i], m_sd[i, 0], m_sd[i, 1]))\n", "    print('-----------------------------------------------------------')\n", "    print('%s' % ('95% confidence interval with unknown population STD'))\n", "    print('%-10s %15s %15s' % ('Variable', 'Lower', 'Upper'))\n", "    print('-----------------------------------------------------------')\n", "    for i in range(ncol):\n", "        print('%-10s %15f %15f' % (labels[i], ci[i, 0], ci[i, 1]))\n", "    print('-----------------------------------------------------------')\n", "    print('%-10s %15s %15s' % ('Variable', 'Minimum', 'Maximum'))\n", "    print('-----------------------------------------------------------')\n", "    for i in range(ncol):\n", "        print('%-10s %15f %15f' % (labels[i], min_max[i, 0], min_max[i, 1]))\n", "    print('-----------------------------------------------------------')\n", "    print('%-10s %15s %15s %15s' % ('Variable', 'Median', '25th percent.',\n", "                                    '75th percent.'))\n", "    print('-----------------------------------------------------------')\n", "    for i in range(ncol):\n", "        print('%-10s %15f %15f %15f' % (labels[i], quartiles[i, 0],\n", "                                        quartiles[i, 1], quartiles[i, 2]))\n", "    print('-----------------------------------------------------------')\n", "    print('%s' % (\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s test for normality\"))\n", "    print('%-10s %15s %15s' % ('Variable', 'W statistic', 'p value'))\n", "    print('-----------------------------------------------------------')\n", "    for i in range(ncol):\n", "        print('%-10s %15f %15f' % (labels[i], normality[i, 0], normality[i, 1]))\n", "    print('-----------------------------------------------------------')\n", "    if nx > 1:\n", "        if np.all(normality[:, 1] > alpha):\n", "            print(\"<PERSON><PERSON>'s test for equality of variances\")\n", "        else:\n", "            print(\"<PERSON><PERSON>'s test for equality of variances\")\n", "        print('%26s %15s' % ('t statistic', 'p value'))\n", "        print('-----------------------------------------------------------')\n", "        print('%26f %15f' % (eq_var[0, 0], eq_var[0, 1]))\n", "        print('-----------------------------------------------------------')\n", "\n", "\n", "if __name__ == '__main__':\n", "    #import sys\n", "    #statdesc(sys.argv[1:])\n", "    y = np.random.randn(100, 3)  # ; y[5:10,1] = np.nan\n", "    statdesc(y, 1, [], ['A', 'B', 'C', 'D'], .05)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.4.1"}}, "nbformat": 4, "nbformat_minor": 0}