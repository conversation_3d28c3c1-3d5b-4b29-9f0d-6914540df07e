{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Read Cortex Motion Analysis Corporation .trc file\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://demotu.org/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Motion Analysis Corporation (MAC, http://www.motionanalysis.com/) builds motion capture systems and their software (e.g., Cortex) generates files in ASCII and binary formats for the different signals (kinematics, analog data, force plate data, etc.). Here are functions for reading most of the files saved in ASCII format. These files have headers with few lines with meta data and the signals are stored in columns and the rows for the different frames (instants of time).\n", "\n", "The \".trc\" (Track Row Column) file in ASCII contains X-Y-Z position data for the reflective markers from a motion capture trial. The position data for each marker is organized into 3 columns per marker (X, Y and Z position) with each row being a new frame. The position data is relative to the global coordinate system of the capture volume and the position values are in the units used for calibration.\n", "\n", "The \".anc\" (Analog ASCII Row Column) file contains ASCII analog data in row-column format. The data is derived from \".anb\" analog binary files. These binary \".anb\" files are generated simultaneously with video \".vc\" files if an optional analog input board is used in conjunction with video data capture.\n", "\n", "The \".cal\" file contains force plate calibration parameters. \n", "\n", "The \".forces\" file contains force plate data. The data is saved based on the \"forcepla.cal\" file of the trial and converts the raw force plate data into calibrated forces. The units used are Newtons and Newton-meters and each line in the file equates to one analog sample.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.480987Z", "start_time": "2021-11-17T19:18:21.859755Z"}}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import numpy as np\n", "%matplotlib notebook\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "from scipy import signal\n", "\n", "import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath\n", "\n", "from read_trc import read_trc"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.491921Z", "start_time": "2021-11-17T19:18:22.481872Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Read .trc file format from Cortex MAC.\n", "\n", "    This function: 1. Read trc file; 2. Can delete or replace markers (columns)\n", "    with empty data; 3. Correct number of markers in the header according to\n", "    the actual number of non-empty markers; 4. Can save a '.trc' file with\n", "    updated information and data; 5. Return header information (optional) and\n", "    data (marker position) as dataframe or dataarray.\n", "\n", "    The .trc (track row column) file in ASCII contains X-Y-Z position\n", "    data for the reflective markers from a motion capture trial. The\n", "    position data for each marker is organized into 3 columns per marker\n", "    (X, Y and Z position) with each row being a new frame. The position\n", "    data is relative to the global coordinate system of the capture volume\n", "    and the position values are in the units used for calibration.\n", "\n", "    Parameters\n", "    ----------\n", "    fname : string\n", "        Full file name of the .trc file to be opened.\n", "    fname2 : string (default = '')\n", "        Full file name of the .trc file to be saved with updated information\n", "        and data if desired.\n", "        If fname2 is '', no file is saved.\n", "        If fname2 is '=', the original file name will be used.\n", "        If fname2 is a string with length between 1 and 3 (other than '='),\n", "        e.g., '_2', this string is appended to the original file name.\n", "    units : string (default = '')\n", "        Change the units of the data if desired.\n", "        Accepted output units are 'm' or 'mm'.\n", "    dropna : bool (default = False)\n", "        True: Delete column if it has only missing or NaN values.\n", "        False: preserve column and replace column values by parameter `na`\n", "        (see below) if inputed, otherwise maintain default pandas value (NaN).\n", "    na : float or None (default = 0.0)\n", "        Value to replace (if `dropna` is False) column values if this column\n", "        has only missing or NaN values. Input None to maintain default pandas\n", "        value for this case (NaN).\n", "    fmt : string (default = 'multi')\n", "        Format of the output: 'uni', 'multi', 'xarray'\n", "        'uni': returns variable with trc header info plus pandas\n", "        dataframe with markerxyz as labels and \"Frame#\" and \"Time\" as columns;\n", "        'multi': returns variable with trc header info plus multilabel pandas\n", "        dataframe  with \"Marker\", \"Coordinate\" and \"XYZ\", as labels and \"Time\"\n", "        as index;\n", "        'xarray': returns variable as dataarray xarray and trc header info as\n", "        attributes of this dataarray.\n", "    show_msg : bool (default = True)\n", "        Whether to print messages about the execution of the intermediary steps\n", "        (True) or not (False).\n", "\n", "    Returns\n", "    -------\n", "    h : Python dictionary with .trc header info (if `fmt` = 'uni' or 'multi')\n", "        keys: header (the .trc full header), data_rate (Hz), camera_rate (Hz),\n", "        nframes, nmarkers, markers (names), xyz (X1,Y1,Z1...), units.\n", "    data : pandas dataframe or xarray dataarray\n", "        Three possible output formats according to the `fmt` option:\n", "        'uni': dataframe with shape (nframes, 2+3*nmarkers) with markerxyz as\n", "        labels and columns: Frame#, time and position data, or\n", "        'multi': fataframe with shape (nframes, 3*nmarkers) with \"Marker\",\n", "        \"Coordinate\" and \"XYZ\" as labels, \"Time\" as index, and data position\n", "        as columns, or\n", "        'xarray': dataarray with dims=['time', 'marker', 'component'] and trc\n", "        header info as attributes of this dataarray.\n", "\n", "    Examples\n", "    --------\n", "\n", "    Notes\n", "    -----\n", "\n", "    \n"]}], "source": ["print(read_trc.__doc__)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.501726Z", "start_time": "2021-11-17T19:18:22.492956Z"}}, "outputs": [], "source": ["import sys, os\n", "\n", "path2 = r'./../data/'\n", "fname = os.path.join(path2, 'arm26_elbow_flex.trc')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.522210Z", "start_time": "2021-11-17T19:18:22.502474Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/arm26_elbow_flex.trc\" ... done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Frame#</th>\n", "      <th>Time</th>\n", "      <th>r_acromionx</th>\n", "      <th>r_acromiony</th>\n", "      <th>r_acromionz</th>\n", "      <th>r_humerus_epicondylex</th>\n", "      <th>r_humerus_epicondyley</th>\n", "      <th>r_humerus_epicondylez</th>\n", "      <th>r_radius_styloidx</th>\n", "      <th>r_radius_styloidy</th>\n", "      <th>r_radius_styloidz</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>-13.054524</td>\n", "      <td>39.505476</td>\n", "      <td>169.505476</td>\n", "      <td>-12.559380</td>\n", "      <td>-297.414380</td>\n", "      <td>199.985620</td>\n", "      <td>-13.124683</td>\n", "      <td>-533.569683</td>\n", "      <td>251.420317</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.008333</td>\n", "      <td>-12.960648</td>\n", "      <td>39.599352</td>\n", "      <td>169.599352</td>\n", "      <td>-12.567324</td>\n", "      <td>-297.422324</td>\n", "      <td>199.977676</td>\n", "      <td>-12.867025</td>\n", "      <td>-533.600380</td>\n", "      <td>251.382550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.016667</td>\n", "      <td>-12.853425</td>\n", "      <td>39.706575</td>\n", "      <td>169.706575</td>\n", "      <td>-12.574394</td>\n", "      <td>-297.429394</td>\n", "      <td>199.970606</td>\n", "      <td>-12.582610</td>\n", "      <td>-533.629817</td>\n", "      <td>251.345015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0.025000</td>\n", "      <td>-12.736429</td>\n", "      <td>39.823571</td>\n", "      <td>169.823571</td>\n", "      <td>-12.580312</td>\n", "      <td>-297.435312</td>\n", "      <td>199.964688</td>\n", "      <td>-12.246998</td>\n", "      <td>-533.658334</td>\n", "      <td>251.306576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0.033333</td>\n", "      <td>-12.613556</td>\n", "      <td>39.946444</td>\n", "      <td>169.946444</td>\n", "      <td>-12.584846</td>\n", "      <td>-297.439846</td>\n", "      <td>199.960154</td>\n", "      <td>-11.837471</td>\n", "      <td>-533.686073</td>\n", "      <td>251.266170</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Frame#      Time  r_acromionx  r_acromiony  r_acromionz  \\\n", "0       1  0.000000   -13.054524    39.505476   169.505476   \n", "1       2  0.008333   -12.960648    39.599352   169.599352   \n", "2       3  0.016667   -12.853425    39.706575   169.706575   \n", "3       4  0.025000   -12.736429    39.823571   169.823571   \n", "4       5  0.033333   -12.613556    39.946444   169.946444   \n", "\n", "   r_humerus_epicondylex  r_humerus_epicondyley  r_humerus_epicondylez  \\\n", "0             -12.559380            -297.414380             199.985620   \n", "1             -12.567324            -297.422324             199.977676   \n", "2             -12.574394            -297.429394             199.970606   \n", "3             -12.580312            -297.435312             199.964688   \n", "4             -12.584846            -297.439846             199.960154   \n", "\n", "   r_radius_styloidx  r_radius_styloidy  r_radius_styloidz  \n", "0         -13.124683        -533.569683         251.420317  \n", "1         -12.867025        -533.600380         251.382550  \n", "2         -12.582610        -533.629817         251.345015  \n", "3         -12.246998        -533.658334         251.306576  \n", "4         -11.837471        -533.686073         251.266170  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["h, df = read_trc(fname, fname2='', dropna=True, na=0.0, fmt='uni')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.533474Z", "start_time": "2021-11-17T19:18:22.522962Z"}}, "outputs": [{"data": {"text/plain": ["{'header': [['PathFileType',\n", "   '4',\n", "   '(X/Y/Z)',\n", "   'arm26_elbow_flex.trc',\n", "   '',\n", "   '',\n", "   '',\n", "   '',\n", "   '',\n", "   '',\n", "   ''],\n", "  ['DataRate',\n", "   'CameraRate',\n", "   'NumFrames',\n", "   'NumMarkers',\n", "   'Units',\n", "   'OrigDataRate',\n", "   'OrigDataStartFrame',\n", "   'OrigNumFrames',\n", "   '',\n", "   '',\n", "   ''],\n", "  ['120', '120', '121', '3', 'mm', '120', '1', '121', '', '', ''],\n", "  ['Frame#',\n", "   'Time',\n", "   'r_acromion',\n", "   '',\n", "   '',\n", "   'r_humerus_epicondyle',\n", "   '',\n", "   '',\n", "   'r_radius_styloid',\n", "   '',\n", "   '',\n", "   ''],\n", "  ['', '', 'X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2', 'X3', 'Y3', 'Z3']],\n", " 'data_rate': 120.0,\n", " 'camera_rate': 120.0,\n", " 'nframes': 121,\n", " 'nmarkers': 3,\n", " 'markers': ['r_acromion', 'r_humerus_epicondyle', 'r_radius_styloid'],\n", " 'xyz': ['X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2', 'X3', 'Y3', 'Z3'],\n", " 'units': 'mm',\n", " 'fname': './../data/arm26_elbow_flex.trc',\n", " 'fname2': ''}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["h"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.556096Z", "start_time": "2021-11-17T19:18:22.534265Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/arm26_elbow_flex.trc\" ... done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">r_acromion</th>\n", "      <th colspan=\"3\" halign=\"left\">r_humerus_epicondyle</th>\n", "      <th colspan=\"3\" halign=\"left\">r_radius_styloid</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "      <th>X3</th>\n", "      <th>Y3</th>\n", "      <th>Z3</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>-13.054524</td>\n", "      <td>39.505476</td>\n", "      <td>169.505476</td>\n", "      <td>-12.559380</td>\n", "      <td>-297.414380</td>\n", "      <td>199.985620</td>\n", "      <td>-13.124683</td>\n", "      <td>-533.569683</td>\n", "      <td>251.420317</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.008333</th>\n", "      <td>-12.960648</td>\n", "      <td>39.599352</td>\n", "      <td>169.599352</td>\n", "      <td>-12.567324</td>\n", "      <td>-297.422324</td>\n", "      <td>199.977676</td>\n", "      <td>-12.867025</td>\n", "      <td>-533.600380</td>\n", "      <td>251.382550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.016667</th>\n", "      <td>-12.853425</td>\n", "      <td>39.706575</td>\n", "      <td>169.706575</td>\n", "      <td>-12.574394</td>\n", "      <td>-297.429394</td>\n", "      <td>199.970606</td>\n", "      <td>-12.582610</td>\n", "      <td>-533.629817</td>\n", "      <td>251.345015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.025000</th>\n", "      <td>-12.736429</td>\n", "      <td>39.823571</td>\n", "      <td>169.823571</td>\n", "      <td>-12.580312</td>\n", "      <td>-297.435312</td>\n", "      <td>199.964688</td>\n", "      <td>-12.246998</td>\n", "      <td>-533.658334</td>\n", "      <td>251.306576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.033333</th>\n", "      <td>-12.613556</td>\n", "      <td>39.946444</td>\n", "      <td>169.946444</td>\n", "      <td>-12.584846</td>\n", "      <td>-297.439846</td>\n", "      <td>199.960154</td>\n", "      <td>-11.837471</td>\n", "      <td>-533.686073</td>\n", "      <td>251.266170</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Marker     r_acromion                        r_humerus_epicondyle              \\\n", "Coordinate          X          Y           Z                    X           Y   \n", "XYZ                X1         Y1          Z1                   X2          Y2   \n", "Time                                                                            \n", "0.000000   -13.054524  39.505476  169.505476           -12.559380 -297.414380   \n", "0.008333   -12.960648  39.599352  169.599352           -12.567324 -297.422324   \n", "0.016667   -12.853425  39.706575  169.706575           -12.574394 -297.429394   \n", "0.025000   -12.736429  39.823571  169.823571           -12.580312 -297.435312   \n", "0.033333   -12.613556  39.946444  169.946444           -12.584846 -297.439846   \n", "\n", "Marker                 r_radius_styloid                          \n", "Coordinate           Z                X           Y           Z  \n", "XYZ                 Z2               X3          Y3          Z3  \n", "Time                                                             \n", "0.000000    199.985620       -13.124683 -533.569683  251.420317  \n", "0.008333    199.977676       -12.867025 -533.600380  251.382550  \n", "0.016667    199.970606       -12.582610 -533.629817  251.345015  \n", "0.025000    199.964688       -12.246998 -533.658334  251.306576  \n", "0.033333    199.960154       -11.837471 -533.686073  251.266170  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["h, df = read_trc(fname, fname2='', dropna=True, na=0.0, fmt='multi')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.894989Z", "start_time": "2021-11-17T19:18:22.556999Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/arm26_elbow_flex.trc\" ... done.\n"]}, {"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2 {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;Position data&#x27; (time: 121, marker: 3, component: 3)&gt;\n", "array([[[ -13.05452443,   39.50547557,  169.5054756 ],\n", "        [ -12.55937962, -297.4143796 ,  199.9856204 ],\n", "        [ -13.12468341, -533.5696827 ,  251.4203173 ]],\n", "\n", "       [[ -12.96064763,   39.59935237,  169.5993524 ],\n", "        [ -12.56732433, -297.4223243 ,  199.9776757 ],\n", "        [ -12.86702479, -533.6003803 ,  251.3825496 ]],\n", "\n", "       [[ -12.85342531,   39.70657469,  169.7065747 ],\n", "        [ -12.57439418, -297.4293942 ,  199.9706058 ],\n", "        [ -12.58261002, -533.6298174 ,  251.345015  ]],\n", "\n", "       ...,\n", "\n", "       [[ -11.91518938,   40.64481062,  170.6448106 ],\n", "        [ -12.50145015, -297.3564501 ,  200.0435499 ],\n", "        [ 231.0730594 , -300.0052994 ,  231.0669795 ]],\n", "\n", "       [[ -11.96689527,   40.59310473,  170.5931047 ],\n", "        [ -12.50375329, -297.3587533 ,  200.0412467 ],\n", "        [ 231.0449219 , -299.7324972 ,  231.0210452 ]],\n", "\n", "       [[ -12.03835742,   40.52164258,  170.5216426 ],\n", "        [ -12.50767284, -297.3626728 ,  200.0373272 ],\n", "        [ 231.0167559 , -299.4841418 ,  230.9769121 ]]])\n", "Coordinates:\n", "  * time       (time) float64 0.0 0.008333 0.01667 0.025 ... 0.9833 0.9917 1.0\n", "  * marker     (marker) &lt;U20 &#x27;r_acromion&#x27; ... &#x27;r_radius_styloid&#x27;\n", "  * component  (component) &lt;U1 &#x27;X&#x27; &#x27;Y&#x27; &#x27;Z&#x27;\n", "Attributes:\n", "    header:       [[&#x27;PathFileType&#x27;, &#x27;4&#x27;, &#x27;(X/Y/Z)&#x27;, &#x27;arm26_elbow_flex.trc&#x27;, &#x27;...\n", "    data_rate:    120.0\n", "    camera_rate:  120.0\n", "    nframes:      121\n", "    nmarkers:     3\n", "    markers:      [&#x27;r_acromion&#x27;, &#x27;r_humerus_epicondyle&#x27;, &#x27;r_radius_styloid&#x27;]\n", "    xyz:          [&#x27;X1&#x27;, &#x27;Y1&#x27;, &#x27;Z1&#x27;, &#x27;X2&#x27;, &#x27;Y2&#x27;, &#x27;Z2&#x27;, &#x27;X3&#x27;, &#x27;Y3&#x27;, &#x27;Z3&#x27;]\n", "    units:        mm\n", "    fname:        ./../data/arm26_elbow_flex.trc\n", "    fname2:       </pre><div class='xr-wrap' hidden><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'Position data'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>time</span>: 121</li><li><span class='xr-has-index'>marker</span>: 3</li><li><span class='xr-has-index'>component</span>: 3</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-526b667a-afd2-473f-9d30-e4ddfc21844f' class='xr-array-in' type='checkbox' checked><label for='section-526b667a-afd2-473f-9d30-e4ddfc21844f' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>-13.05 39.51 169.5 -12.56 -297.4 ... -297.4 200.0 231.0 -299.5 231.0</span></div><div class='xr-array-data'><pre>array([[[ -13.05452443,   39.50547557,  169.5054756 ],\n", "        [ -12.55937962, -297.4143796 ,  199.9856204 ],\n", "        [ -13.12468341, -533.5696827 ,  251.4203173 ]],\n", "\n", "       [[ -12.96064763,   39.59935237,  169.5993524 ],\n", "        [ -12.56732433, -297.4223243 ,  199.9776757 ],\n", "        [ -12.86702479, -533.6003803 ,  251.3825496 ]],\n", "\n", "       [[ -12.85342531,   39.70657469,  169.7065747 ],\n", "        [ -12.57439418, -297.4293942 ,  199.9706058 ],\n", "        [ -12.58261002, -533.6298174 ,  251.345015  ]],\n", "\n", "       ...,\n", "\n", "       [[ -11.91518938,   40.64481062,  170.6448106 ],\n", "        [ -12.50145015, -297.3564501 ,  200.0435499 ],\n", "        [ 231.0730594 , -300.0052994 ,  231.0669795 ]],\n", "\n", "       [[ -11.96689527,   40.59310473,  170.5931047 ],\n", "        [ -12.50375329, -297.3587533 ,  200.0412467 ],\n", "        [ 231.0449219 , -299.7324972 ,  231.0210452 ]],\n", "\n", "       [[ -12.03835742,   40.52164258,  170.5216426 ],\n", "        [ -12.50767284, -297.3626728 ,  200.0373272 ],\n", "        [ 231.0167559 , -299.4841418 ,  230.9769121 ]]])</pre></div></div></li><li class='xr-section-item'><input id='section-29f3afb1-2f2d-4d3f-a506-f77375fb87b8' class='xr-section-summary-in' type='checkbox'  checked><label for='section-29f3afb1-2f2d-4d3f-a506-f77375fb87b8' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>time</span></div><div class='xr-var-dims'>(time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 0.008333 0.01667 ... 0.9917 1.0</div><input id='attrs-88bfcbf7-f931-4250-b53d-e638bfd6864a' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-88bfcbf7-f931-4250-b53d-e638bfd6864a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c5159a10-a46d-45ab-aced-09fc1b58c26e' class='xr-var-data-in' type='checkbox'><label for='data-c5159a10-a46d-45ab-aced-09fc1b58c26e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>units :</span></dt><dd>s</dd></dl></div><div class='xr-var-data'><pre>array([0.      , 0.008333, 0.016667, 0.025   , 0.033333, 0.041667, 0.05    ,\n", "       0.058333, 0.066667, 0.075   , 0.083333, 0.091667, 0.1     , 0.108333,\n", "       0.116667, 0.125   , 0.133333, 0.141667, 0.15    , 0.158333, 0.166667,\n", "       0.175   , 0.183333, 0.191667, 0.2     , 0.208333, 0.216667, 0.225   ,\n", "       0.233333, 0.241667, 0.25    , 0.258333, 0.266667, 0.275   , 0.283333,\n", "       0.291667, 0.3     , 0.308333, 0.316667, 0.325   , 0.333333, 0.341667,\n", "       0.35    , 0.358333, 0.366667, 0.375   , 0.383333, 0.391667, 0.4     ,\n", "       0.408333, 0.416667, 0.425   , 0.433333, 0.441667, 0.45    , 0.458333,\n", "       0.466667, 0.475   , 0.483333, 0.491667, 0.5     , 0.508333, 0.516667,\n", "       0.525   , 0.533333, 0.541667, 0.55    , 0.558333, 0.566667, 0.575   ,\n", "       0.583333, 0.591667, 0.6     , 0.608333, 0.616667, 0.625   , 0.633333,\n", "       0.641667, 0.65    , 0.658333, 0.666667, 0.675   , 0.683333, 0.691667,\n", "       0.7     , 0.708333, 0.716667, 0.725   , 0.733333, 0.741667, 0.75    ,\n", "       0.758333, 0.766667, 0.775   , 0.783333, 0.791667, 0.8     , 0.808333,\n", "       0.816667, 0.825   , 0.833333, 0.841667, 0.85    , 0.858333, 0.866667,\n", "       0.875   , 0.883333, 0.891667, 0.9     , 0.908333, 0.916667, 0.925   ,\n", "       0.933333, 0.941667, 0.95    , 0.958333, 0.966667, 0.975   , 0.983333,\n", "       0.991667, 1.      ])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>marker</span></div><div class='xr-var-dims'>(marker)</div><div class='xr-var-dtype'>&lt;U20</div><div class='xr-var-preview xr-preview'>&#x27;r_acromion&#x27; ... &#x27;r_radius_styloid&#x27;</div><input id='attrs-7c4cd195-c467-41f7-887d-d8c4b2ae1906' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7c4cd195-c467-41f7-887d-d8c4b2ae1906' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-88a50567-62b0-4e64-8f27-cf5a018a4fa2' class='xr-var-data-in' type='checkbox'><label for='data-88a50567-62b0-4e64-8f27-cf5a018a4fa2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;r_acromion&#x27;, &#x27;r_humerus_epicondyle&#x27;, &#x27;r_radius_styloid&#x27;], dtype=&#x27;&lt;U20&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>component</span></div><div class='xr-var-dims'>(component)</div><div class='xr-var-dtype'>&lt;U1</div><div class='xr-var-preview xr-preview'>&#x27;X&#x27; &#x27;Y&#x27; &#x27;Z&#x27;</div><input id='attrs-d1728b04-9fed-4e6c-9bbd-ea4ad01bae5f' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-d1728b04-9fed-4e6c-9bbd-ea4ad01bae5f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-84db08c9-c7f8-408c-b16b-fc4957408706' class='xr-var-data-in' type='checkbox'><label for='data-84db08c9-c7f8-408c-b16b-fc4957408706' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>units :</span></dt><dd>mm</dd></dl></div><div class='xr-var-data'><pre>array([&#x27;X&#x27;, &#x27;Y&#x27;, &#x27;Z&#x27;], dtype=&#x27;&lt;U1&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-bedb3e46-4bf1-4975-b198-5de32ff4589e' class='xr-section-summary-in' type='checkbox'  ><label for='section-bedb3e46-4bf1-4975-b198-5de32ff4589e' class='xr-section-summary' >Attributes: <span>(10)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>header :</span></dt><dd>[[&#x27;PathFileType&#x27;, &#x27;4&#x27;, &#x27;(X/Y/Z)&#x27;, &#x27;arm26_elbow_flex.trc&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;DataRate&#x27;, &#x27;CameraRate&#x27;, &#x27;NumFrames&#x27;, &#x27;NumMarkers&#x27;, &#x27;Units&#x27;, &#x27;OrigDataRate&#x27;, &#x27;OrigDataStartFrame&#x27;, &#x27;OrigNumFrames&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;120&#x27;, &#x27;120&#x27;, &#x27;121&#x27;, &#x27;3&#x27;, &#x27;mm&#x27;, &#x27;120&#x27;, &#x27;1&#x27;, &#x27;121&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;Frame#&#x27;, &#x27;Time&#x27;, &#x27;r_acromion&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;r_humerus_epicondyle&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;r_radius_styloid&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;&#x27;, &#x27;&#x27;, &#x27;X1&#x27;, &#x27;Y1&#x27;, &#x27;Z1&#x27;, &#x27;X2&#x27;, &#x27;Y2&#x27;, &#x27;Z2&#x27;, &#x27;X3&#x27;, &#x27;Y3&#x27;, &#x27;Z3&#x27;]]</dd><dt><span>data_rate :</span></dt><dd>120.0</dd><dt><span>camera_rate :</span></dt><dd>120.0</dd><dt><span>nframes :</span></dt><dd>121</dd><dt><span>nmarkers :</span></dt><dd>3</dd><dt><span>markers :</span></dt><dd>[&#x27;r_acromion&#x27;, &#x27;r_humerus_epicondyle&#x27;, &#x27;r_radius_styloid&#x27;]</dd><dt><span>xyz :</span></dt><dd>[&#x27;X1&#x27;, &#x27;Y1&#x27;, &#x27;Z1&#x27;, &#x27;X2&#x27;, &#x27;Y2&#x27;, &#x27;Z2&#x27;, &#x27;X3&#x27;, &#x27;Y3&#x27;, &#x27;Z3&#x27;]</dd><dt><span>units :</span></dt><dd>mm</dd><dt><span>fname :</span></dt><dd>./../data/arm26_elbow_flex.trc</dd><dt><span>fname2 :</span></dt><dd></dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'Position data' (time: 121, marker: 3, component: 3)>\n", "array([[[ -13.05452443,   39.50547557,  169.5054756 ],\n", "        [ -12.55937962, -297.4143796 ,  199.9856204 ],\n", "        [ -13.12468341, -533.5696827 ,  251.4203173 ]],\n", "\n", "       [[ -12.96064763,   39.59935237,  169.5993524 ],\n", "        [ -12.56732433, -297.4223243 ,  199.9776757 ],\n", "        [ -12.86702479, -533.6003803 ,  251.3825496 ]],\n", "\n", "       [[ -12.85342531,   39.70657469,  169.7065747 ],\n", "        [ -12.57439418, -297.4293942 ,  199.9706058 ],\n", "        [ -12.58261002, -533.6298174 ,  251.345015  ]],\n", "\n", "       ...,\n", "\n", "       [[ -11.91518938,   40.64481062,  170.6448106 ],\n", "        [ -12.50145015, -297.3564501 ,  200.0435499 ],\n", "        [ 231.0730594 , -300.0052994 ,  231.0669795 ]],\n", "\n", "       [[ -11.96689527,   40.59310473,  170.5931047 ],\n", "        [ -12.50375329, -297.3587533 ,  200.0412467 ],\n", "        [ 231.0449219 , -299.7324972 ,  231.0210452 ]],\n", "\n", "       [[ -12.03835742,   40.52164258,  170.5216426 ],\n", "        [ -12.50767284, -297.3626728 ,  200.0373272 ],\n", "        [ 231.0167559 , -299.4841418 ,  230.9769121 ]]])\n", "Coordinates:\n", "  * time       (time) float64 0.0 0.008333 0.01667 0.025 ... 0.9833 0.9917 1.0\n", "  * marker     (marker) <U20 'r_acromion' ... 'r_radius_styloid'\n", "  * component  (component) <U1 'X' 'Y' 'Z'\n", "Attributes:\n", "    header:       [['PathFileType', '4', '(X/Y/Z)', 'arm26_elbow_flex.trc', '...\n", "    data_rate:    120.0\n", "    camera_rate:  120.0\n", "    nframes:      121\n", "    nmarkers:     3\n", "    markers:      ['r_acromion', 'r_humerus_epicondyle', 'r_radius_styloid']\n", "    xyz:          ['X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2', 'X3', 'Y3', 'Z3']\n", "    units:        mm\n", "    fname:        ./../data/arm26_elbow_flex.trc\n", "    fname2:       "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["da = read_trc(fname, fname2='', dropna=True, na=0.0, fmt='xarray')\n", "da"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.906509Z", "start_time": "2021-11-17T19:18:22.895886Z"}}, "outputs": [], "source": ["fname = os.path.join(path2, 'arm26_elbow_flex_e.trc')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.932830Z", "start_time": "2021-11-17T19:18:22.908163Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/arm26_elbow_flex_e.trc\" ... done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">r_acromion</th>\n", "      <th colspan=\"3\" halign=\"left\">r_humerus_epicondyle</th>\n", "      <th colspan=\"3\" halign=\"left\">r_radius_styloid</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "      <th>X3</th>\n", "      <th>Y3</th>\n", "      <th>Z3</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>-13.054524</td>\n", "      <td>39.505476</td>\n", "      <td>169.505476</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-13.124683</td>\n", "      <td>-533.569683</td>\n", "      <td>251.420317</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.008333</th>\n", "      <td>-12.960648</td>\n", "      <td>39.599352</td>\n", "      <td>169.599352</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-12.867025</td>\n", "      <td>-533.600380</td>\n", "      <td>251.382550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.016667</th>\n", "      <td>-12.853425</td>\n", "      <td>39.706575</td>\n", "      <td>169.706575</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-12.582610</td>\n", "      <td>-533.629817</td>\n", "      <td>251.345015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.025000</th>\n", "      <td>-12.736429</td>\n", "      <td>39.823571</td>\n", "      <td>169.823571</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-12.246998</td>\n", "      <td>-533.658334</td>\n", "      <td>251.306576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.033333</th>\n", "      <td>-12.613556</td>\n", "      <td>39.946444</td>\n", "      <td>169.946444</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-11.837471</td>\n", "      <td>-533.686073</td>\n", "      <td>251.266170</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Marker     r_acromion                        r_humerus_epicondyle            \\\n", "Coordinate          X          Y           Z                    X    Y    Z   \n", "XYZ                X1         Y1          Z1                   X2   Y2   Z2   \n", "Time                                                                          \n", "0.000000   -13.054524  39.505476  169.505476                  0.0  0.0  0.0   \n", "0.008333   -12.960648  39.599352  169.599352                  0.0  0.0  0.0   \n", "0.016667   -12.853425  39.706575  169.706575                  0.0  0.0  0.0   \n", "0.025000   -12.736429  39.823571  169.823571                  0.0  0.0  0.0   \n", "0.033333   -12.613556  39.946444  169.946444                  0.0  0.0  0.0   \n", "\n", "Marker     r_radius_styloid                          \n", "Coordinate                X           Y           Z  \n", "XYZ                      X3          Y3          Z3  \n", "Time                                                 \n", "0.000000         -13.124683 -533.569683  251.420317  \n", "0.008333         -12.867025 -533.600380  251.382550  \n", "0.016667         -12.582610 -533.629817  251.345015  \n", "0.025000         -12.246998 -533.658334  251.306576  \n", "0.033333         -11.837471 -533.686073  251.266170  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["h, df = read_trc(fname, fname2='', dropna=False, na=0.0, fmt='multi')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.956386Z", "start_time": "2021-11-17T19:18:22.933590Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/arm26_elbow_flex_e.trc\" ...  Number of markers changed from 3 to 2.\n", "done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">r_acromion</th>\n", "      <th colspan=\"3\" halign=\"left\">r_radius_styloid</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>-13.054524</td>\n", "      <td>39.505476</td>\n", "      <td>169.505476</td>\n", "      <td>-13.124683</td>\n", "      <td>-533.569683</td>\n", "      <td>251.420317</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.008333</th>\n", "      <td>-12.960648</td>\n", "      <td>39.599352</td>\n", "      <td>169.599352</td>\n", "      <td>-12.867025</td>\n", "      <td>-533.600380</td>\n", "      <td>251.382550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.016667</th>\n", "      <td>-12.853425</td>\n", "      <td>39.706575</td>\n", "      <td>169.706575</td>\n", "      <td>-12.582610</td>\n", "      <td>-533.629817</td>\n", "      <td>251.345015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.025000</th>\n", "      <td>-12.736429</td>\n", "      <td>39.823571</td>\n", "      <td>169.823571</td>\n", "      <td>-12.246998</td>\n", "      <td>-533.658334</td>\n", "      <td>251.306576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.033333</th>\n", "      <td>-12.613556</td>\n", "      <td>39.946444</td>\n", "      <td>169.946444</td>\n", "      <td>-11.837471</td>\n", "      <td>-533.686073</td>\n", "      <td>251.266170</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Marker     r_acromion                        r_radius_styloid              \\\n", "Coordinate          X          Y           Z                X           Y   \n", "XYZ                X1         Y1          Z1               X2          Y2   \n", "Time                                                                        \n", "0.000000   -13.054524  39.505476  169.505476       -13.124683 -533.569683   \n", "0.008333   -12.960648  39.599352  169.599352       -12.867025 -533.600380   \n", "0.016667   -12.853425  39.706575  169.706575       -12.582610 -533.629817   \n", "0.025000   -12.736429  39.823571  169.823571       -12.246998 -533.658334   \n", "0.033333   -12.613556  39.946444  169.946444       -11.837471 -533.686073   \n", "\n", "<PERSON><PERSON>                  \n", "Coordinate           Z  \n", "XYZ                 Z2  \n", "Time                    \n", "0.000000    251.420317  \n", "0.008333    251.382550  \n", "0.016667    251.345015  \n", "0.025000    251.306576  \n", "0.033333    251.266170  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["h, df = read_trc(fname, fname2='', dropna=True, na=0.0, fmt='multi')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:22.994412Z", "start_time": "2021-11-17T19:18:22.957454Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/arm26_elbow_flex_e.trc\" ...  Number of markers changed from 3 to 2.\n", "done.\n"]}, {"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2 {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;Position data&#x27; (time: 121, marker: 2, component: 3)&gt;\n", "array([[[ -13.05452443,   39.50547557,  169.5054756 ],\n", "        [ -13.12468341, -533.5696827 ,  251.4203173 ]],\n", "\n", "       [[ -12.96064763,   39.59935237,  169.5993524 ],\n", "        [ -12.86702479, -533.6003803 ,  251.3825496 ]],\n", "\n", "       [[ -12.85342531,   39.70657469,  169.7065747 ],\n", "        [ -12.58261002, -533.6298174 ,  251.345015  ]],\n", "\n", "       [[ -12.73642902,   39.82357098,  169.823571  ],\n", "        [ -12.24699837, -533.6583339 ,  251.3065762 ]],\n", "\n", "       [[ -12.6135559 ,   39.9464441 ,  169.9464441 ],\n", "        [ -11.83747124, -533.6860727 ,  251.2661701 ]],\n", "\n", "       [[ -12.48889884,   40.07110116,  170.0711012 ],\n", "        [ -11.33375263, -533.7129239 ,  251.2228408 ]],\n", "\n", "       [[ -12.36661015,   40.19338985,  170.1933899 ],\n", "        [ -10.71857495, -533.7384701 ,  251.1757657 ]],\n", "...\n", "       [[ -11.92946669,   40.63053331,  170.6305333 ],\n", "        [ 231.1686938 , -301.8004443 ,  231.2845456 ]],\n", "\n", "       [[ -11.89222162,   40.66777838,  170.6677784 ],\n", "        [ 231.1493615 , -301.2057996 ,  231.2236921 ]],\n", "\n", "       [[ -11.87722018,   40.68277982,  170.6827798 ],\n", "        [ 231.1262125 , -300.7204226 ,  231.1675614 ]],\n", "\n", "       [[ -11.88496206,   40.67503794,  170.6750379 ],\n", "        [ 231.1004416 , -300.3270241 ,  231.1155571 ]],\n", "\n", "       [[ -11.91518938,   40.64481062,  170.6448106 ],\n", "        [ 231.0730594 , -300.0052994 ,  231.0669795 ]],\n", "\n", "       [[ -11.96689527,   40.59310473,  170.5931047 ],\n", "        [ 231.0449219 , -299.7324972 ,  231.0210452 ]],\n", "\n", "       [[ -12.03835742,   40.52164258,  170.5216426 ],\n", "        [ 231.0167559 , -299.4841418 ,  230.9769121 ]]])\n", "Coordinates:\n", "  * time       (time) float64 0.0 0.008333 0.01667 0.025 ... 0.9833 0.9917 1.0\n", "  * marker     (marker) &lt;U16 &#x27;r_acromion&#x27; &#x27;r_radius_styloid&#x27;\n", "  * component  (component) &lt;U1 &#x27;X&#x27; &#x27;Y&#x27; &#x27;Z&#x27;\n", "Attributes:\n", "    header:       [[&#x27;PathFileType&#x27;, &#x27;4&#x27;, &#x27;(X/Y/Z)&#x27;, &#x27;arm26_elbow_flex.trc&#x27;, &#x27;...\n", "    data_rate:    120.0\n", "    camera_rate:  120.0\n", "    nframes:      121\n", "    nmarkers:     2\n", "    markers:      [&#x27;r_acromion&#x27;, &#x27;r_radius_styloid&#x27;]\n", "    xyz:          [&#x27;X1&#x27;, &#x27;Y1&#x27;, &#x27;Z1&#x27;, &#x27;X2&#x27;, &#x27;Y2&#x27;, &#x27;Z2&#x27;]\n", "    units:        mm\n", "    fname:        ./../data/arm26_elbow_flex_e.trc\n", "    fname2:       </pre><div class='xr-wrap' hidden><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'Position data'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>time</span>: 121</li><li><span class='xr-has-index'>marker</span>: 2</li><li><span class='xr-has-index'>component</span>: 3</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-852d338f-05a9-49e1-8561-2380e1ce2802' class='xr-array-in' type='checkbox' checked><label for='section-852d338f-05a9-49e1-8561-2380e1ce2802' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>-13.05 39.51 169.5 -13.12 -533.6 ... 40.52 170.5 231.0 -299.5 231.0</span></div><div class='xr-array-data'><pre>array([[[ -13.05452443,   39.50547557,  169.5054756 ],\n", "        [ -13.12468341, -533.5696827 ,  251.4203173 ]],\n", "\n", "       [[ -12.96064763,   39.59935237,  169.5993524 ],\n", "        [ -12.86702479, -533.6003803 ,  251.3825496 ]],\n", "\n", "       [[ -12.85342531,   39.70657469,  169.7065747 ],\n", "        [ -12.58261002, -533.6298174 ,  251.345015  ]],\n", "\n", "       [[ -12.73642902,   39.82357098,  169.823571  ],\n", "        [ -12.24699837, -533.6583339 ,  251.3065762 ]],\n", "\n", "       [[ -12.6135559 ,   39.9464441 ,  169.9464441 ],\n", "        [ -11.83747124, -533.6860727 ,  251.2661701 ]],\n", "\n", "       [[ -12.48889884,   40.07110116,  170.0711012 ],\n", "        [ -11.33375263, -533.7129239 ,  251.2228408 ]],\n", "\n", "       [[ -12.36661015,   40.19338985,  170.1933899 ],\n", "        [ -10.71857495, -533.7384701 ,  251.1757657 ]],\n", "...\n", "       [[ -11.92946669,   40.63053331,  170.6305333 ],\n", "        [ 231.1686938 , -301.8004443 ,  231.2845456 ]],\n", "\n", "       [[ -11.89222162,   40.66777838,  170.6677784 ],\n", "        [ 231.1493615 , -301.2057996 ,  231.2236921 ]],\n", "\n", "       [[ -11.87722018,   40.68277982,  170.6827798 ],\n", "        [ 231.1262125 , -300.7204226 ,  231.1675614 ]],\n", "\n", "       [[ -11.88496206,   40.67503794,  170.6750379 ],\n", "        [ 231.1004416 , -300.3270241 ,  231.1155571 ]],\n", "\n", "       [[ -11.91518938,   40.64481062,  170.6448106 ],\n", "        [ 231.0730594 , -300.0052994 ,  231.0669795 ]],\n", "\n", "       [[ -11.96689527,   40.59310473,  170.5931047 ],\n", "        [ 231.0449219 , -299.7324972 ,  231.0210452 ]],\n", "\n", "       [[ -12.03835742,   40.52164258,  170.5216426 ],\n", "        [ 231.0167559 , -299.4841418 ,  230.9769121 ]]])</pre></div></div></li><li class='xr-section-item'><input id='section-5f268218-cf4d-453e-bda7-3d3e49588919' class='xr-section-summary-in' type='checkbox'  checked><label for='section-5f268218-cf4d-453e-bda7-3d3e49588919' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>time</span></div><div class='xr-var-dims'>(time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 0.008333 0.01667 ... 0.9917 1.0</div><input id='attrs-4f44f135-5bf1-41cf-b5d6-edc0641efe18' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-4f44f135-5bf1-41cf-b5d6-edc0641efe18' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a2219f3f-3065-4b6d-b326-8c951e6aede1' class='xr-var-data-in' type='checkbox'><label for='data-a2219f3f-3065-4b6d-b326-8c951e6aede1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>units :</span></dt><dd>s</dd></dl></div><div class='xr-var-data'><pre>array([0.      , 0.008333, 0.016667, 0.025   , 0.033333, 0.041667, 0.05    ,\n", "       0.058333, 0.066667, 0.075   , 0.083333, 0.091667, 0.1     , 0.108333,\n", "       0.116667, 0.125   , 0.133333, 0.141667, 0.15    , 0.158333, 0.166667,\n", "       0.175   , 0.183333, 0.191667, 0.2     , 0.208333, 0.216667, 0.225   ,\n", "       0.233333, 0.241667, 0.25    , 0.258333, 0.266667, 0.275   , 0.283333,\n", "       0.291667, 0.3     , 0.308333, 0.316667, 0.325   , 0.333333, 0.341667,\n", "       0.35    , 0.358333, 0.366667, 0.375   , 0.383333, 0.391667, 0.4     ,\n", "       0.408333, 0.416667, 0.425   , 0.433333, 0.441667, 0.45    , 0.458333,\n", "       0.466667, 0.475   , 0.483333, 0.491667, 0.5     , 0.508333, 0.516667,\n", "       0.525   , 0.533333, 0.541667, 0.55    , 0.558333, 0.566667, 0.575   ,\n", "       0.583333, 0.591667, 0.6     , 0.608333, 0.616667, 0.625   , 0.633333,\n", "       0.641667, 0.65    , 0.658333, 0.666667, 0.675   , 0.683333, 0.691667,\n", "       0.7     , 0.708333, 0.716667, 0.725   , 0.733333, 0.741667, 0.75    ,\n", "       0.758333, 0.766667, 0.775   , 0.783333, 0.791667, 0.8     , 0.808333,\n", "       0.816667, 0.825   , 0.833333, 0.841667, 0.85    , 0.858333, 0.866667,\n", "       0.875   , 0.883333, 0.891667, 0.9     , 0.908333, 0.916667, 0.925   ,\n", "       0.933333, 0.941667, 0.95    , 0.958333, 0.966667, 0.975   , 0.983333,\n", "       0.991667, 1.      ])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>marker</span></div><div class='xr-var-dims'>(marker)</div><div class='xr-var-dtype'>&lt;U16</div><div class='xr-var-preview xr-preview'>&#x27;r_acromion&#x27; &#x27;r_radius_styloid&#x27;</div><input id='attrs-d88fed57-9845-49a3-8717-d6c36fce3e79' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d88fed57-9845-49a3-8717-d6c36fce3e79' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-44566d37-7bbf-4ac4-994b-b8c2bcb27f4d' class='xr-var-data-in' type='checkbox'><label for='data-44566d37-7bbf-4ac4-994b-b8c2bcb27f4d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;r_acromion&#x27;, &#x27;r_radius_styloid&#x27;], dtype=&#x27;&lt;U16&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>component</span></div><div class='xr-var-dims'>(component)</div><div class='xr-var-dtype'>&lt;U1</div><div class='xr-var-preview xr-preview'>&#x27;X&#x27; &#x27;Y&#x27; &#x27;Z&#x27;</div><input id='attrs-2f4e6d2e-03fd-4e73-8cf4-9d16ca55a159' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-2f4e6d2e-03fd-4e73-8cf4-9d16ca55a159' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-58f47f00-e7c3-41df-9502-5cc34fe018a1' class='xr-var-data-in' type='checkbox'><label for='data-58f47f00-e7c3-41df-9502-5cc34fe018a1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>units :</span></dt><dd>mm</dd></dl></div><div class='xr-var-data'><pre>array([&#x27;X&#x27;, &#x27;Y&#x27;, &#x27;Z&#x27;], dtype=&#x27;&lt;U1&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-142ba019-394d-4e51-a71c-fffabc0187cc' class='xr-section-summary-in' type='checkbox'  ><label for='section-142ba019-394d-4e51-a71c-fffabc0187cc' class='xr-section-summary' >Attributes: <span>(10)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>header :</span></dt><dd>[[&#x27;PathFileType&#x27;, &#x27;4&#x27;, &#x27;(X/Y/Z)&#x27;, &#x27;arm26_elbow_flex.trc&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;DataRate&#x27;, &#x27;CameraRate&#x27;, &#x27;NumFrames&#x27;, &#x27;NumMarkers&#x27;, &#x27;Units&#x27;, &#x27;OrigDataRate&#x27;, &#x27;OrigDataStartFrame&#x27;, &#x27;OrigNumFrames&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;120&#x27;, &#x27;120&#x27;, &#x27;121&#x27;, &#x27;2&#x27;, &#x27;mm&#x27;, &#x27;120&#x27;, &#x27;1&#x27;, &#x27;121&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;Frame#&#x27;, &#x27;Time&#x27;, &#x27;r_acromion&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;r_radius_styloid&#x27;, &#x27;&#x27;, &#x27;&#x27;, &#x27;&#x27;], [&#x27;&#x27;, &#x27;&#x27;, &#x27;X1&#x27;, &#x27;Y1&#x27;, &#x27;Z1&#x27;, &#x27;X2&#x27;, &#x27;Y2&#x27;, &#x27;Z2&#x27;]]</dd><dt><span>data_rate :</span></dt><dd>120.0</dd><dt><span>camera_rate :</span></dt><dd>120.0</dd><dt><span>nframes :</span></dt><dd>121</dd><dt><span>nmarkers :</span></dt><dd>2</dd><dt><span>markers :</span></dt><dd>[&#x27;r_acromion&#x27;, &#x27;r_radius_styloid&#x27;]</dd><dt><span>xyz :</span></dt><dd>[&#x27;X1&#x27;, &#x27;Y1&#x27;, &#x27;Z1&#x27;, &#x27;X2&#x27;, &#x27;Y2&#x27;, &#x27;Z2&#x27;]</dd><dt><span>units :</span></dt><dd>mm</dd><dt><span>fname :</span></dt><dd>./../data/arm26_elbow_flex_e.trc</dd><dt><span>fname2 :</span></dt><dd></dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'Position data' (time: 121, marker: 2, component: 3)>\n", "array([[[ -13.05452443,   39.50547557,  169.5054756 ],\n", "        [ -13.12468341, -533.5696827 ,  251.4203173 ]],\n", "\n", "       [[ -12.96064763,   39.59935237,  169.5993524 ],\n", "        [ -12.86702479, -533.6003803 ,  251.3825496 ]],\n", "\n", "       [[ -12.85342531,   39.70657469,  169.7065747 ],\n", "        [ -12.58261002, -533.6298174 ,  251.345015  ]],\n", "\n", "       [[ -12.73642902,   39.82357098,  169.823571  ],\n", "        [ -12.24699837, -533.6583339 ,  251.3065762 ]],\n", "\n", "       [[ -12.6135559 ,   39.9464441 ,  169.9464441 ],\n", "        [ -11.83747124, -533.6860727 ,  251.2661701 ]],\n", "\n", "       [[ -12.48889884,   40.07110116,  170.0711012 ],\n", "        [ -11.33375263, -533.7129239 ,  251.2228408 ]],\n", "\n", "       [[ -12.36661015,   40.19338985,  170.1933899 ],\n", "        [ -10.71857495, -533.7384701 ,  251.1757657 ]],\n", "...\n", "       [[ -11.92946669,   40.63053331,  170.6305333 ],\n", "        [ 231.1686938 , -301.8004443 ,  231.2845456 ]],\n", "\n", "       [[ -11.89222162,   40.66777838,  170.6677784 ],\n", "        [ 231.1493615 , -301.2057996 ,  231.2236921 ]],\n", "\n", "       [[ -11.87722018,   40.68277982,  170.6827798 ],\n", "        [ 231.1262125 , -300.7204226 ,  231.1675614 ]],\n", "\n", "       [[ -11.88496206,   40.67503794,  170.6750379 ],\n", "        [ 231.1004416 , -300.3270241 ,  231.1155571 ]],\n", "\n", "       [[ -11.91518938,   40.64481062,  170.6448106 ],\n", "        [ 231.0730594 , -300.0052994 ,  231.0669795 ]],\n", "\n", "       [[ -11.96689527,   40.59310473,  170.5931047 ],\n", "        [ 231.0449219 , -299.7324972 ,  231.0210452 ]],\n", "\n", "       [[ -12.03835742,   40.52164258,  170.5216426 ],\n", "        [ 231.0167559 , -299.4841418 ,  230.9769121 ]]])\n", "Coordinates:\n", "  * time       (time) float64 0.0 0.008333 0.01667 0.025 ... 0.9833 0.9917 1.0\n", "  * marker     (marker) <U16 'r_acromion' 'r_radius_styloid'\n", "  * component  (component) <U1 'X' 'Y' 'Z'\n", "Attributes:\n", "    header:       [['PathFileType', '4', '(X/Y/Z)', 'arm26_elbow_flex.trc', '...\n", "    data_rate:    120.0\n", "    camera_rate:  120.0\n", "    nframes:      121\n", "    nmarkers:     2\n", "    markers:      ['r_acromion', 'r_radius_styloid']\n", "    xyz:          ['X1', 'Y1', 'Z1', 'X2', 'Y2', 'Z2']\n", "    units:        mm\n", "    fname:        ./../data/arm26_elbow_flex_e.trc\n", "    fname2:       "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["da = read_trc(fname, fname2='', dropna=True, na=0.0, fmt='xarray')\n", "da"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:23.060372Z", "start_time": "2021-11-17T19:18:22.995178Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/walk.trc\" ...  Number of markers changed from 28 to 55.\n", "done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Frame#</th>\n", "      <th>Time</th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>L.ASISx</th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th>L.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th>V_R.TT_KJCz</th>\n", "      <th>V_L.TT_KJCx</th>\n", "      <th>V_L.TT_KJCy</th>\n", "      <th>V_L.TT_KJCz</th>\n", "      <th>V_R.MT2x</th>\n", "      <th>V_R.MT2y</th>\n", "      <th>V_R.MT2z</th>\n", "      <th>V_L.MT2x</th>\n", "      <th>V_L.MT2y</th>\n", "      <th>V_L.MT2z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>516.54236</td>\n", "      <td>966.88000</td>\n", "      <td>-306.10416</td>\n", "      <td>531.67438</td>\n", "      <td>981.34631</td>\n", "      <td>-560.16077</td>\n", "      <td>315.74045</td>\n", "      <td>977.08398</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.007</td>\n", "      <td>523.92200</td>\n", "      <td>967.96594</td>\n", "      <td>-308.23773</td>\n", "      <td>539.83044</td>\n", "      <td>982.78345</td>\n", "      <td>-561.77612</td>\n", "      <td>323.29425</td>\n", "      <td>977.64166</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.013</td>\n", "      <td>531.20807</td>\n", "      <td>968.92493</td>\n", "      <td>-310.12112</td>\n", "      <td>547.60663</td>\n", "      <td>984.00653</td>\n", "      <td>-563.42725</td>\n", "      <td>330.56866</td>\n", "      <td>978.15283</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0.020</td>\n", "      <td>538.24219</td>\n", "      <td>969.77612</td>\n", "      <td>-311.72064</td>\n", "      <td>555.40649</td>\n", "      <td>985.09637</td>\n", "      <td>-564.85162</td>\n", "      <td>337.63867</td>\n", "      <td>978.81207</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0.027</td>\n", "      <td>545.11420</td>\n", "      <td>970.81128</td>\n", "      <td>-313.07266</td>\n", "      <td>563.14301</td>\n", "      <td>986.00916</td>\n", "      <td>-566.06659</td>\n", "      <td>344.50589</td>\n", "      <td>979.21619</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>180</td>\n", "      <td>1.193</td>\n", "      <td>1875.04834</td>\n", "      <td>958.35535</td>\n", "      <td>-290.67004</td>\n", "      <td>1897.06970</td>\n", "      <td>960.27222</td>\n", "      <td>-546.90094</td>\n", "      <td>1678.10901</td>\n", "      <td>977.31805</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>181</td>\n", "      <td>1.200</td>\n", "      <td>1885.37988</td>\n", "      <td>958.09222</td>\n", "      <td>-291.67429</td>\n", "      <td>1907.52881</td>\n", "      <td>960.81549</td>\n", "      <td>-547.97144</td>\n", "      <td>1688.67249</td>\n", "      <td>977.12646</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>182</td>\n", "      <td>1.207</td>\n", "      <td>1895.50452</td>\n", "      <td>957.80798</td>\n", "      <td>-292.71875</td>\n", "      <td>1917.97290</td>\n", "      <td>961.49707</td>\n", "      <td>-548.99799</td>\n", "      <td>1699.17065</td>\n", "      <td>977.02045</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>183</td>\n", "      <td>1.213</td>\n", "      <td>1905.40540</td>\n", "      <td>957.61029</td>\n", "      <td>-293.84250</td>\n", "      <td>1928.34631</td>\n", "      <td>962.31494</td>\n", "      <td>-550.08704</td>\n", "      <td>1709.65186</td>\n", "      <td>976.93237</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>184</td>\n", "      <td>1.220</td>\n", "      <td>1916.91516</td>\n", "      <td>958.57813</td>\n", "      <td>-294.85443</td>\n", "      <td>1938.59363</td>\n", "      <td>963.29272</td>\n", "      <td>-551.26306</td>\n", "      <td>1720.05396</td>\n", "      <td>976.89636</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 167 columns</p>\n", "</div>"], "text/plain": ["     Frame#   Time     R.ASISx    R.ASISy    R.ASISz     L.ASISx    L.ASISy  \\\n", "0         1  0.000   516.54236  966.88000 -306.10416   531.67438  981.34631   \n", "1         2  0.007   523.92200  967.96594 -308.23773   539.83044  982.78345   \n", "2         3  0.013   531.20807  968.92493 -310.12112   547.60663  984.00653   \n", "3         4  0.020   538.24219  969.77612 -311.72064   555.40649  985.09637   \n", "4         5  0.027   545.11420  970.81128 -313.07266   563.14301  986.00916   \n", "..      ...    ...         ...        ...        ...         ...        ...   \n", "179     180  1.193  1875.04834  958.35535 -290.67004  1897.06970  960.27222   \n", "180     181  1.200  1885.37988  958.09222 -291.67429  1907.52881  960.81549   \n", "181     182  1.207  1895.50452  957.80798 -292.71875  1917.97290  961.49707   \n", "182     183  1.213  1905.40540  957.61029 -293.84250  1928.34631  962.31494   \n", "183     184  1.220  1916.91516  958.57813 -294.85443  1938.59363  963.29272   \n", "\n", "       L.ASISz     R.PSISx    R.PSISy  ...  V_R.TT_KJCz  V_L.TT_KJCx  \\\n", "0   -560.16077   315.74045  977.08398  ...          0.0          0.0   \n", "1   -561.77612   323.29425  977.64166  ...          0.0          0.0   \n", "2   -563.42725   330.56866  978.15283  ...          0.0          0.0   \n", "3   -564.85162   337.63867  978.81207  ...          0.0          0.0   \n", "4   -566.06659   344.50589  979.21619  ...          0.0          0.0   \n", "..         ...         ...        ...  ...          ...          ...   \n", "179 -546.90094  1678.10901  977.31805  ...          0.0          0.0   \n", "180 -547.97144  1688.67249  977.12646  ...          0.0          0.0   \n", "181 -548.99799  1699.17065  977.02045  ...          0.0          0.0   \n", "182 -550.08704  1709.65186  976.93237  ...          0.0          0.0   \n", "183 -551.26306  1720.05396  976.89636  ...          0.0          0.0   \n", "\n", "     V_L.TT_KJCy  V_L.TT_KJCz  V_R.MT2x  V_R.MT2y  V_R.MT2z  V_L.MT2x  \\\n", "0            0.0          0.0       0.0       0.0       0.0       0.0   \n", "1            0.0          0.0       0.0       0.0       0.0       0.0   \n", "2            0.0          0.0       0.0       0.0       0.0       0.0   \n", "3            0.0          0.0       0.0       0.0       0.0       0.0   \n", "4            0.0          0.0       0.0       0.0       0.0       0.0   \n", "..           ...          ...       ...       ...       ...       ...   \n", "179          0.0          0.0       0.0       0.0       0.0       0.0   \n", "180          0.0          0.0       0.0       0.0       0.0       0.0   \n", "181          0.0          0.0       0.0       0.0       0.0       0.0   \n", "182          0.0          0.0       0.0       0.0       0.0       0.0   \n", "183          0.0          0.0       0.0       0.0       0.0       0.0   \n", "\n", "     V_L.MT2y  V_L.MT2z  \n", "0         0.0       0.0  \n", "1         0.0       0.0  \n", "2         0.0       0.0  \n", "3         0.0       0.0  \n", "4         0.0       0.0  \n", "..        ...       ...  \n", "179       0.0       0.0  \n", "180       0.0       0.0  \n", "181       0.0       0.0  \n", "182       0.0       0.0  \n", "183       0.0       0.0  \n", "\n", "[184 rows x 167 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["h, data = read_trc('./../data/walk.trc', fname2='', dropna=False, na=0.0, fmt='uni')\n", "data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:23.133494Z", "start_time": "2021-11-17T19:18:23.061216Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/walk.trc\" ...  Number of markers changed from 28 to 55.\n", "done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">R_ASIS</th>\n", "      <th colspan=\"3\" halign=\"left\">L_ASIS</th>\n", "      <th colspan=\"3\" halign=\"left\">R_PSIS</th>\n", "      <th>L_PSIS</th>\n", "      <th>...</th>\n", "      <th>V_R_TT_KJC</th>\n", "      <th colspan=\"3\" halign=\"left\">V_L_TT_KJC</th>\n", "      <th colspan=\"3\" halign=\"left\">V_R_MT2</th>\n", "      <th colspan=\"3\" halign=\"left\">V_L_MT2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>...</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "      <th>X3</th>\n", "      <th>Y3</th>\n", "      <th>Z3</th>\n", "      <th>X4</th>\n", "      <th>...</th>\n", "      <th>Z52</th>\n", "      <th>X53</th>\n", "      <th>Y53</th>\n", "      <th>Z53</th>\n", "      <th>X54</th>\n", "      <th>Y54</th>\n", "      <th>Z54</th>\n", "      <th>X55</th>\n", "      <th>Y55</th>\n", "      <th>Z55</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000</th>\n", "      <td>516.54236</td>\n", "      <td>966.88000</td>\n", "      <td>-306.10416</td>\n", "      <td>531.67438</td>\n", "      <td>981.34631</td>\n", "      <td>-560.16077</td>\n", "      <td>315.74045</td>\n", "      <td>977.08398</td>\n", "      <td>-388.89532</td>\n", "      <td>312.81592</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.007</th>\n", "      <td>523.92200</td>\n", "      <td>967.96594</td>\n", "      <td>-308.23773</td>\n", "      <td>539.83044</td>\n", "      <td>982.78345</td>\n", "      <td>-561.77612</td>\n", "      <td>323.29425</td>\n", "      <td>977.64166</td>\n", "      <td>-391.11392</td>\n", "      <td>320.88770</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013</th>\n", "      <td>531.20807</td>\n", "      <td>968.92493</td>\n", "      <td>-310.12112</td>\n", "      <td>547.60663</td>\n", "      <td>984.00653</td>\n", "      <td>-563.42725</td>\n", "      <td>330.56866</td>\n", "      <td>978.15283</td>\n", "      <td>-393.34290</td>\n", "      <td>328.17276</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020</th>\n", "      <td>538.24219</td>\n", "      <td>969.77612</td>\n", "      <td>-311.72064</td>\n", "      <td>555.40649</td>\n", "      <td>985.09637</td>\n", "      <td>-564.85162</td>\n", "      <td>337.63867</td>\n", "      <td>978.81207</td>\n", "      <td>-395.60764</td>\n", "      <td>335.90649</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.027</th>\n", "      <td>545.11420</td>\n", "      <td>970.81128</td>\n", "      <td>-313.07266</td>\n", "      <td>563.14301</td>\n", "      <td>986.00916</td>\n", "      <td>-566.06659</td>\n", "      <td>344.50589</td>\n", "      <td>979.21619</td>\n", "      <td>-397.85052</td>\n", "      <td>343.31287</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.193</th>\n", "      <td>1875.04834</td>\n", "      <td>958.35535</td>\n", "      <td>-290.67004</td>\n", "      <td>1897.06970</td>\n", "      <td>960.27222</td>\n", "      <td>-546.90094</td>\n", "      <td>1678.10901</td>\n", "      <td>977.31805</td>\n", "      <td>-385.06229</td>\n", "      <td>1678.84143</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.200</th>\n", "      <td>1885.37988</td>\n", "      <td>958.09222</td>\n", "      <td>-291.67429</td>\n", "      <td>1907.52881</td>\n", "      <td>960.81549</td>\n", "      <td>-547.97144</td>\n", "      <td>1688.67249</td>\n", "      <td>977.12646</td>\n", "      <td>-386.23868</td>\n", "      <td>1689.18640</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.207</th>\n", "      <td>1895.50452</td>\n", "      <td>957.80798</td>\n", "      <td>-292.71875</td>\n", "      <td>1917.97290</td>\n", "      <td>961.49707</td>\n", "      <td>-548.99799</td>\n", "      <td>1699.17065</td>\n", "      <td>977.02045</td>\n", "      <td>-387.41364</td>\n", "      <td>1699.72668</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.213</th>\n", "      <td>1905.40540</td>\n", "      <td>957.61029</td>\n", "      <td>-293.84250</td>\n", "      <td>1928.34631</td>\n", "      <td>962.31494</td>\n", "      <td>-550.08704</td>\n", "      <td>1709.65186</td>\n", "      <td>976.93237</td>\n", "      <td>-388.50803</td>\n", "      <td>1710.00281</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.220</th>\n", "      <td>1916.91516</td>\n", "      <td>958.57813</td>\n", "      <td>-294.85443</td>\n", "      <td>1938.59363</td>\n", "      <td>963.29272</td>\n", "      <td>-551.26306</td>\n", "      <td>1720.05396</td>\n", "      <td>976.89636</td>\n", "      <td>-389.46796</td>\n", "      <td>1720.32813</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 165 columns</p>\n", "</div>"], "text/plain": ["Marker          R_ASIS                            L_ASIS             \\\n", "Coordinate           X          Y          Z           X          Y   \n", "XYZ                 X1         Y1         Z1          X2         Y2   \n", "Time                                                                  \n", "0.000        516.54236  966.88000 -306.10416   531.67438  981.34631   \n", "0.007        523.92200  967.96594 -308.23773   539.83044  982.78345   \n", "0.013        531.20807  968.92493 -310.12112   547.60663  984.00653   \n", "0.020        538.24219  969.77612 -311.72064   555.40649  985.09637   \n", "0.027        545.11420  970.81128 -313.07266   563.14301  986.00916   \n", "...                ...        ...        ...         ...        ...   \n", "1.193       1875.04834  958.35535 -290.67004  1897.06970  960.27222   \n", "1.200       1885.37988  958.09222 -291.67429  1907.52881  960.81549   \n", "1.207       1895.50452  957.80798 -292.71875  1917.97290  961.49707   \n", "1.213       1905.40540  957.61029 -293.84250  1928.34631  962.31494   \n", "1.220       1916.91516  958.57813 -294.85443  1938.59363  963.29272   \n", "\n", "Marker                     R_PSIS                            L_PSIS  ...  \\\n", "Coordinate          Z           X          Y          Z           X  ...   \n", "XYZ                Z2          X3         Y3         Z3          X4  ...   \n", "Time                                                                 ...   \n", "0.000      -560.16077   315.74045  977.08398 -388.89532   312.81592  ...   \n", "0.007      -561.77612   323.29425  977.64166 -391.11392   320.88770  ...   \n", "0.013      -563.42725   330.56866  978.15283 -393.34290   328.17276  ...   \n", "0.020      -564.85162   337.63867  978.81207 -395.60764   335.90649  ...   \n", "0.027      -566.06659   344.50589  979.21619 -397.85052   343.31287  ...   \n", "...               ...         ...        ...        ...         ...  ...   \n", "1.193      -546.90094  1678.10901  977.31805 -385.06229  1678.84143  ...   \n", "1.200      -547.97144  1688.67249  977.12646 -386.23868  1689.18640  ...   \n", "1.207      -548.99799  1699.17065  977.02045 -387.41364  1699.72668  ...   \n", "1.213      -550.08704  1709.65186  976.93237 -388.50803  1710.00281  ...   \n", "1.220      -551.26306  1720.05396  976.89636 -389.46796  1720.32813  ...   \n", "\n", "Marker     V_R_TT_KJC V_L_TT_KJC           V_R_MT2           V_L_MT2            \n", "Coordinate          Z          X    Y    Z       X    Y    Z       X    Y    Z  \n", "XYZ               Z52        X53  Y53  Z53     X54  Y54  Z54     X55  Y55  Z55  \n", "Time                                                                            \n", "0.000             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.007             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.013             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.020             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.027             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "...               ...        ...  ...  ...     ...  ...  ...     ...  ...  ...  \n", "1.193             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.200             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.207             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.213             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.220             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "\n", "[184 rows x 165 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["h, data = read_trc('./../data/walk.trc', fname2='', dropna=False, na=0.0, fmt='multi')\n", "data"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2021-11-17T19:18:23.176017Z", "start_time": "2021-11-17T19:18:23.134292Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/walk.trc\" ... done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">R_ASIS</th>\n", "      <th colspan=\"3\" halign=\"left\">L_ASIS</th>\n", "      <th colspan=\"3\" halign=\"left\">R_PSIS</th>\n", "      <th>L_PSIS</th>\n", "      <th>...</th>\n", "      <th>R_MT2</th>\n", "      <th colspan=\"3\" halign=\"left\">L_Knee_Medial</th>\n", "      <th colspan=\"3\" halign=\"left\">L_Ankle_Medial</th>\n", "      <th colspan=\"3\" halign=\"left\">L_MT2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>...</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "      <th>X3</th>\n", "      <th>Y3</th>\n", "      <th>Z3</th>\n", "      <th>X4</th>\n", "      <th>...</th>\n", "      <th>Z25</th>\n", "      <th>X26</th>\n", "      <th>Y26</th>\n", "      <th>Z26</th>\n", "      <th>X27</th>\n", "      <th>Y27</th>\n", "      <th>Z27</th>\n", "      <th>X28</th>\n", "      <th>Y28</th>\n", "      <th>Z28</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000</th>\n", "      <td>516.54236</td>\n", "      <td>966.88000</td>\n", "      <td>-306.10416</td>\n", "      <td>531.67438</td>\n", "      <td>981.34631</td>\n", "      <td>-560.16077</td>\n", "      <td>315.74045</td>\n", "      <td>977.08398</td>\n", "      <td>-388.89532</td>\n", "      <td>312.81592</td>\n", "      <td>...</td>\n", "      <td>-295.95847</td>\n", "      <td>584.31927</td>\n", "      <td>491.83475</td>\n", "      <td>-451.49405</td>\n", "      <td>640.91467</td>\n", "      <td>102.96970</td>\n", "      <td>-461.64136</td>\n", "      <td>766.49017</td>\n", "      <td>67.05472</td>\n", "      <td>-520.25671</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.007</th>\n", "      <td>523.92200</td>\n", "      <td>967.96594</td>\n", "      <td>-308.23773</td>\n", "      <td>539.83044</td>\n", "      <td>982.78345</td>\n", "      <td>-561.77612</td>\n", "      <td>323.29425</td>\n", "      <td>977.64166</td>\n", "      <td>-391.11392</td>\n", "      <td>320.88770</td>\n", "      <td>...</td>\n", "      <td>-296.16809</td>\n", "      <td>585.61047</td>\n", "      <td>491.72031</td>\n", "      <td>-452.45239</td>\n", "      <td>641.00195</td>\n", "      <td>102.78003</td>\n", "      <td>-461.30609</td>\n", "      <td>766.70880</td>\n", "      <td>66.18866</td>\n", "      <td>-519.62793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013</th>\n", "      <td>531.20807</td>\n", "      <td>968.92493</td>\n", "      <td>-310.12112</td>\n", "      <td>547.60663</td>\n", "      <td>984.00653</td>\n", "      <td>-563.42725</td>\n", "      <td>330.56866</td>\n", "      <td>978.15283</td>\n", "      <td>-393.34290</td>\n", "      <td>328.17276</td>\n", "      <td>...</td>\n", "      <td>-296.09930</td>\n", "      <td>586.44348</td>\n", "      <td>491.23099</td>\n", "      <td>-453.99197</td>\n", "      <td>641.22186</td>\n", "      <td>102.52743</td>\n", "      <td>-461.05392</td>\n", "      <td>766.92682</td>\n", "      <td>65.27229</td>\n", "      <td>-519.13348</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020</th>\n", "      <td>538.24219</td>\n", "      <td>969.77612</td>\n", "      <td>-311.72064</td>\n", "      <td>555.40649</td>\n", "      <td>985.09637</td>\n", "      <td>-564.85162</td>\n", "      <td>337.63867</td>\n", "      <td>978.81207</td>\n", "      <td>-395.60764</td>\n", "      <td>335.90649</td>\n", "      <td>...</td>\n", "      <td>-295.37305</td>\n", "      <td>588.36719</td>\n", "      <td>491.34662</td>\n", "      <td>-454.16455</td>\n", "      <td>641.47699</td>\n", "      <td>102.29733</td>\n", "      <td>-460.72614</td>\n", "      <td>767.09344</td>\n", "      <td>64.64764</td>\n", "      <td>-518.64502</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.027</th>\n", "      <td>545.11420</td>\n", "      <td>970.81128</td>\n", "      <td>-313.07266</td>\n", "      <td>563.14301</td>\n", "      <td>986.00916</td>\n", "      <td>-566.06659</td>\n", "      <td>344.50589</td>\n", "      <td>979.21619</td>\n", "      <td>-397.85052</td>\n", "      <td>343.31287</td>\n", "      <td>...</td>\n", "      <td>-294.78860</td>\n", "      <td>589.59979</td>\n", "      <td>491.36469</td>\n", "      <td>-454.49768</td>\n", "      <td>641.77759</td>\n", "      <td>102.33441</td>\n", "      <td>-460.27908</td>\n", "      <td>767.23840</td>\n", "      <td>64.11570</td>\n", "      <td>-518.32220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.193</th>\n", "      <td>1875.04834</td>\n", "      <td>958.35535</td>\n", "      <td>-290.67004</td>\n", "      <td>1897.06970</td>\n", "      <td>960.27222</td>\n", "      <td>-546.90094</td>\n", "      <td>1678.10901</td>\n", "      <td>977.31805</td>\n", "      <td>-385.06229</td>\n", "      <td>1678.84143</td>\n", "      <td>...</td>\n", "      <td>-349.12631</td>\n", "      <td>1974.25183</td>\n", "      <td>481.42581</td>\n", "      <td>-449.71994</td>\n", "      <td>2117.11304</td>\n", "      <td>115.82814</td>\n", "      <td>-464.73233</td>\n", "      <td>2238.68921</td>\n", "      <td>116.63618</td>\n", "      <td>-525.20459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.200</th>\n", "      <td>1885.37988</td>\n", "      <td>958.09222</td>\n", "      <td>-291.67429</td>\n", "      <td>1907.52881</td>\n", "      <td>960.81549</td>\n", "      <td>-547.97144</td>\n", "      <td>1688.67249</td>\n", "      <td>977.12646</td>\n", "      <td>-386.23868</td>\n", "      <td>1689.18640</td>\n", "      <td>...</td>\n", "      <td>-348.93127</td>\n", "      <td>1982.22021</td>\n", "      <td>480.87662</td>\n", "      <td>-450.35571</td>\n", "      <td>2121.40991</td>\n", "      <td>113.80779</td>\n", "      <td>-464.63849</td>\n", "      <td>2244.71509</td>\n", "      <td>110.86374</td>\n", "      <td>-523.01886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.207</th>\n", "      <td>1895.50452</td>\n", "      <td>957.80798</td>\n", "      <td>-292.71875</td>\n", "      <td>1917.97290</td>\n", "      <td>961.49707</td>\n", "      <td>-548.99799</td>\n", "      <td>1699.17065</td>\n", "      <td>977.02045</td>\n", "      <td>-387.41364</td>\n", "      <td>1699.72668</td>\n", "      <td>...</td>\n", "      <td>-348.68399</td>\n", "      <td>1989.57397</td>\n", "      <td>480.27377</td>\n", "      <td>-451.05280</td>\n", "      <td>2124.43408</td>\n", "      <td>112.07399</td>\n", "      <td>-464.43576</td>\n", "      <td>2248.97974</td>\n", "      <td>104.70275</td>\n", "      <td>-520.64160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.213</th>\n", "      <td>1905.40540</td>\n", "      <td>957.61029</td>\n", "      <td>-293.84250</td>\n", "      <td>1928.34631</td>\n", "      <td>962.31494</td>\n", "      <td>-550.08704</td>\n", "      <td>1709.65186</td>\n", "      <td>976.93237</td>\n", "      <td>-388.50803</td>\n", "      <td>1710.00281</td>\n", "      <td>...</td>\n", "      <td>-348.85641</td>\n", "      <td>1996.14453</td>\n", "      <td>479.61557</td>\n", "      <td>-451.70987</td>\n", "      <td>2127.16431</td>\n", "      <td>110.69190</td>\n", "      <td>-464.25732</td>\n", "      <td>2252.41968</td>\n", "      <td>98.64201</td>\n", "      <td>-519.56976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.220</th>\n", "      <td>1916.91516</td>\n", "      <td>958.57813</td>\n", "      <td>-294.85443</td>\n", "      <td>1938.59363</td>\n", "      <td>963.29272</td>\n", "      <td>-551.26306</td>\n", "      <td>1720.05396</td>\n", "      <td>976.89636</td>\n", "      <td>-389.46796</td>\n", "      <td>1720.32813</td>\n", "      <td>...</td>\n", "      <td>-348.74100</td>\n", "      <td>2001.50623</td>\n", "      <td>478.78607</td>\n", "      <td>-452.58780</td>\n", "      <td>2129.84302</td>\n", "      <td>109.49210</td>\n", "      <td>-464.42734</td>\n", "      <td>2255.61060</td>\n", "      <td>93.78249</td>\n", "      <td>-518.34271</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 84 columns</p>\n", "</div>"], "text/plain": ["Marker          R_ASIS                            L_ASIS             \\\n", "Coordinate           X          Y          Z           X          Y   \n", "XYZ                 X1         Y1         Z1          X2         Y2   \n", "Time                                                                  \n", "0.000        516.54236  966.88000 -306.10416   531.67438  981.34631   \n", "0.007        523.92200  967.96594 -308.23773   539.83044  982.78345   \n", "0.013        531.20807  968.92493 -310.12112   547.60663  984.00653   \n", "0.020        538.24219  969.77612 -311.72064   555.40649  985.09637   \n", "0.027        545.11420  970.81128 -313.07266   563.14301  986.00916   \n", "...                ...        ...        ...         ...        ...   \n", "1.193       1875.04834  958.35535 -290.67004  1897.06970  960.27222   \n", "1.200       1885.37988  958.09222 -291.67429  1907.52881  960.81549   \n", "1.207       1895.50452  957.80798 -292.71875  1917.97290  961.49707   \n", "1.213       1905.40540  957.61029 -293.84250  1928.34631  962.31494   \n", "1.220       1916.91516  958.57813 -294.85443  1938.59363  963.29272   \n", "\n", "Marker                     R_PSIS                            L_PSIS  ...  \\\n", "Coordinate          Z           X          Y          Z           X  ...   \n", "XYZ                Z2          X3         Y3         Z3          X4  ...   \n", "Time                                                                 ...   \n", "0.000      -560.16077   315.74045  977.08398 -388.89532   312.81592  ...   \n", "0.007      -561.77612   323.29425  977.64166 -391.11392   320.88770  ...   \n", "0.013      -563.42725   330.56866  978.15283 -393.34290   328.17276  ...   \n", "0.020      -564.85162   337.63867  978.81207 -395.60764   335.90649  ...   \n", "0.027      -566.06659   344.50589  979.21619 -397.85052   343.31287  ...   \n", "...               ...         ...        ...        ...         ...  ...   \n", "1.193      -546.90094  1678.10901  977.31805 -385.06229  1678.84143  ...   \n", "1.200      -547.97144  1688.67249  977.12646 -386.23868  1689.18640  ...   \n", "1.207      -548.99799  1699.17065  977.02045 -387.41364  1699.72668  ...   \n", "1.213      -550.08704  1709.65186  976.93237 -388.50803  1710.00281  ...   \n", "1.220      -551.26306  1720.05396  976.89636 -389.46796  1720.32813  ...   \n", "\n", "Marker          R_MT2 L_Knee_Medial                       L_Ankle_Medial  \\\n", "Coordinate          Z             X          Y          Z              X   \n", "XYZ               Z25           X26        Y26        Z26            X27   \n", "Time                                                                       \n", "0.000      -295.95847     584.31927  491.83475 -451.49405      640.91467   \n", "0.007      -296.16809     585.61047  491.72031 -452.45239      641.00195   \n", "0.013      -296.09930     586.44348  491.23099 -453.99197      641.22186   \n", "0.020      -295.37305     588.36719  491.34662 -454.16455      641.47699   \n", "0.027      -294.78860     589.59979  491.36469 -454.49768      641.77759   \n", "...               ...           ...        ...        ...            ...   \n", "1.193      -349.12631    1974.25183  481.42581 -449.71994     2117.11304   \n", "1.200      -348.93127    1982.22021  480.87662 -450.35571     2121.40991   \n", "1.207      -348.68399    1989.57397  480.27377 -451.05280     2124.43408   \n", "1.213      -348.85641    1996.14453  479.61557 -451.70987     2127.16431   \n", "1.220      -348.74100    2001.50623  478.78607 -452.58780     2129.84302   \n", "\n", "Marker                                 L_MT2                        \n", "Coordinate          Y          Z           X          Y          Z  \n", "XYZ               Y27        Z27         X28        Y28        Z28  \n", "Time                                                                \n", "0.000       102.96970 -461.64136   766.49017   67.05472 -520.25671  \n", "0.007       102.78003 -461.30609   766.70880   66.18866 -519.62793  \n", "0.013       102.52743 -461.05392   766.92682   65.27229 -519.13348  \n", "0.020       102.29733 -460.72614   767.09344   64.64764 -518.64502  \n", "0.027       102.33441 -460.27908   767.23840   64.11570 -518.32220  \n", "...               ...        ...         ...        ...        ...  \n", "1.193       115.82814 -464.73233  2238.68921  116.63618 -525.20459  \n", "1.200       113.80779 -464.63849  2244.71509  110.86374 -523.01886  \n", "1.207       112.07399 -464.43576  2248.97974  104.70275 -520.64160  \n", "1.213       110.69190 -464.25732  2252.41968   98.64201 -519.56976  \n", "1.220       109.49210 -464.42734  2255.61060   93.78249 -518.34271  \n", "\n", "[184 rows x 84 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["h, data = read_trc('./../data/walk.trc', fname2='', dropna=True, na=0.0, fmt='multi')\n", "data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": "block", "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}