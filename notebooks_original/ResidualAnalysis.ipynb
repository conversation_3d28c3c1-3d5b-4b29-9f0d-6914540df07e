{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/BMClab/BMC/blob/master/notebooks/ResidualAnalysis.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "kl40O2c6wELx"}, "source": ["# Residual analysis to determine the optimal cutoff frequency\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"id": "1dy0U3nZwELz"}, "source": ["A common problem in signal processing is to automatically determine the optimal cutoff frequency that should be employed in a low-pass filter to attenuate as much as possible the noise without compromising the signal content of the data.   \n", "\n", "Before we continue, see [this <PERSON><PERSON><PERSON> notebook](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DataFiltering.ipynb) for an overview about data filtering if needed.\n", "\n", "Unfortunately, there is no definite solution for this problem, but there are some techniques, with different degrees of success, to try to determine the optimal cutoff frequency.  \n", "\n", "<PERSON>, in his classic book *Biomechanics and motor control of human movement*, proposed a method to find the optimal cutoff frequency based on residual analysis of the difference between filtered and unfiltered signals over a range of cutoff frequencies. The optimal cutoff frequency is the one where the residual starts to change very little because it is considered that from this point, it's being filtered mostly noise and minimally signal, ideally. This concept is straightforward to implement.  \n", "\n", "The function `optcutfreq.py` from Python module `optcutfreq` is an implementation of this method and it is divided in three parts (after the help section): first, the residuals over a range of cutoff frequencies are calculated; second, an algorithm tries to find the noisy region (with a supposed linear behavior in the frequency domain) of the residuals versus cutoff frequencies plot and finds the optimal cutoff frequency; and third, the results are plotted. The code is lengthy relatively to the simplicity of the idea because of the long help section, the implementation of the automatic search and a rich plot. Here is the function signature:\n", "\n", "```python\n", "fc_opt = optcutfreq(y, freq=1, fclim=[], show=False, ax=None):\n", "```\n", "\n", "Let's test this function with benchmark data.   \n", "\n", "In 1977, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> published a paper where they investigated the effects of differentiation and filtering processes on experimental data (the angle of a bar manipulated in space). Since then, these data have became a benchmark to test new algorithms. Let's work with these data (available at [https://isbweb.org/data/pezzack/index.html](http://isbweb.org/data/pezzack/index.html)). The data have the angular displacement measured by video and the angular acceleration  directly measured by an accelerometer, which we will consider as the true acceleration."]}, {"cell_type": "markdown", "metadata": {"id": "-C31PppvwELz"}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2017-12-29T19:22:46.907564Z", "start_time": "2017-12-29T19:22:46.688392Z"}, "id": "60PI2q6kwEL0"}, "outputs": [], "source": ["# Import the necessary libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {"id": "Vd8IYiMIwEL1"}, "source": ["## Installation\n", "\n", "```bash\n", "# DEPRECATED: pip install optcutfreq\n", "```\n", "\n", "Or\n", "\n", "```bash\n", "# DEPRECATED: conda install -c duartexyz optcutfreq\n", "```"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "E2KhT_luwEL1"}, "outputs": [], "source": ["#!/usr/bin/env python\n", "\n", "\"\"\"Automatic search of optimal filter cutoff frequency based on residual analysis.\"\"\"\n", "\n", "import numpy as np\n", "from scipy.signal import butter, filtfilt\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/optcutfreq'\n", "__version__ = \"0.0.9\"\n", "__license__ = \"MIT\"\n", "\n", "\n", "def optcutfreq(y, freq=1, fclim=[], show=False, ax=None):\n", "    \"\"\" Automatic search of optimal filter cutoff frequency based on residual analysis.\n", "\n", "    This method was proposed by <PERSON> in his book [1]_.\n", "    The 'optimal' cutoff frequency (in the sense that a filter with such cutoff\n", "    frequency removes as much noise as possible without considerably affecting\n", "    the signal) is found by performing a residual analysis of the difference\n", "    between filtered and unfiltered signals over a range of cutoff frequencies.\n", "    The optimal cutoff frequency is the one where the residual starts to change\n", "    very little because it is considered that from this point, it's being\n", "    filtered mostly noise and minimally signal, ideally.\n", "\n", "    Parameters\n", "    ----------\n", "    y : 1D array_like\n", "        Data\n", "    freq : float, optional (default = 1)\n", "        sampling frequency of the signal y\n", "    fclim : list with 2 numbers, optional (default = [])\n", "        limit frequencies of the noisy part or the residuals curve\n", "    show : bool, optional (default = False)\n", "        True (1) plots data in a matplotlib figure\n", "        False (0) to not plot\n", "    ax : a matplotlib.axes.Axes instance, optional (default = None).\n", "\n", "    Returns\n", "    -------\n", "    fc_opt : float\n", "             optimal cutoff frequency (None if not found)\n", "\n", "    Notes\n", "    -----\n", "    A second-order zero-phase digital Butterworth low-pass filter is used.\n", "    # The cutoff frequency is correctyed for the number of passes:\n", "    # C = (2**(1/npasses) - 1)**0.25. C = 0.802 for a dual pass filter.\n", "\n", "    The matplotlib figure with the results will show a plot of the residual\n", "    analysis with the optimal cutoff frequency, a plot with the unfiltered and\n", "    filtered signals at this optimal cutoff frequency (with the RMSE of the\n", "    difference between these two signals), and a plot with the respective\n", "    second derivatives of these signals which should be useful to evaluate\n", "    the quality of the optimal cutoff frequency found.\n", "\n", "    Winter should not be blamed for the automatic search algorithm used here.\n", "    The algorithm implemented is just to follow as close as possible Winter's\n", "    suggestion of fitting a regression line to the noisy part of the residuals.\n", "\n", "    This function performs well with data where the signal has frequencies\n", "    considerably bellow the <PERSON>quist frequency and the noise is predominantly\n", "    white in the higher frequency region.\n", "\n", "    If the automatic search fails, the lower and upper frequencies of the noisy\n", "    part of the residuals curve cam be inputed as a parameter (fclim).\n", "    These frequencies can be chosen by viewing the plot of the residuals (enter\n", "    show=True as input parameter when calling this function).\n", "\n", "\n", "    It is known that this residual analysis algorithm results in oversmoothing\n", "    kinematic data [2]_. Use it with moderation.\n", "    This code is described elsewhere [3]_.\n", "\n", "    References\n", "    ----------\n", "    .. [1] Winter DA (2009) Biomechanics and motor control of human movement.\n", "    .. [2] http://www.clinicalgaitanalysis.com/faq/cutoff.html\n", "    .. [3] https://github.com/demotu/optcutfreq/blob/master/docs/optcutfreq.ipynb\n", "\n", "    Examples\n", "    --------\n", "    >>> y = np.cumsum(np.random.randn(1000))\n", "    >>> # optimal cutoff frequency based on residual analysis and plot:\n", "    >>> fc_opt = optcutfreq(y, freq=1000, show=True)\n", "    >>> # sane analysis but specifying the frequency limits and plot:\n", "    >>> optcutfreq(y, freq=1000, fclim=[200,400], show=True)\n", "    >>> # It's not always possible to find an optimal cutoff frequency\n", "    >>> # or the one found can be wrong (run this example many times):\n", "    >>> y = np.random.randn(100)\n", "    >>> optcutfreq(y, freq=100, show=True)\n", "\n", "    \"\"\"\n", "\n", "    from scipy.interpolate import UnivariateSpline\n", "\n", "    # Correct the cutoff frequency for the number of passes in the filter\n", "    C = 0.802  # for dual pass; C = (2**(1/npasses)-1)**0.25\n", "\n", "    # signal filtering\n", "    freqs = np.linspace((freq/2) / 100, (freq/2)*C, 101, endpoint=False)\n", "    res = []\n", "    for fc in freqs:\n", "        b, a = butter(2, (fc/C) / (freq / 2))\n", "        yf = filtfilt(b, a, y)\n", "        # residual between filtered and unfiltered signals\n", "        res = np.hstack((res, np.sqrt(np.mean((yf - y)**2))))\n", "\n", "    # find the optimal cutoff frequency by fitting an exponential curve\n", "    # y = A*exp(B*x)+C to the residual data and consider that the tail part\n", "    # of the exponential (which should be the noisy part of the residuals)\n", "    # decay starts after 3 lifetimes (exp(-3), 95% drop)\n", "    if not len(fclim) or np.any(fclim < 0) or np.any(fclim > freq/2):\n", "        fc1 = 0\n", "        fc2 = int(0.95*(len(freqs)-1))\n", "        # log of exponential turns the problem to first order polynomial fit\n", "        # make the data always greater than zero before taking the logarithm\n", "        reslog = np.log(np.abs(res[fc1:fc2 + 1] - res[fc2]) +\n", "                        1000 * np.finfo(float).eps)\n", "        Blog, Alog = np.polyfit(freqs[fc1:fc2 + 1], reslog, 1)\n", "        fcini = np.nonzero(freqs >= -3 / Blog)  # 3 lifetimes\n", "        fclim = [fcini[0][0], fc2] if np.size(fcini) else []\n", "    else:\n", "        fclim = [np.nonzero(freqs >= fclim[0])[0][0],\n", "                 np.nonzero(freqs >= fclim[1])[0][0]]\n", "\n", "    # find fc_opt with linear fit y=A+Bx of the noisy part of the residuals\n", "    if len(fclim) and fclim[0] < fclim[1]:\n", "        B, A = np.polyfit(freqs[fclim[0]:fclim[1]], res[fclim[0]:fclim[1]], 1)\n", "        # optimal cutoff frequency is the frequency where y[fc_opt] = A\n", "        roots = UnivariateSpline(freqs, res - A, s=0).roots()\n", "        fc_opt = roots[0] if len(roots) else None\n", "    else:\n", "        fc_opt = None\n", "\n", "    if show:\n", "        _plot(y, freq, freqs, res, fclim, fc_opt, B, A, ax)\n", "\n", "    return fc_opt\n", "\n", "\n", "def _plot(y, freq, freqs, res, fclim, fc_opt, B, A, ax):\n", "    \"\"\"Plot results of the optcutfreq function, see its help.\"\"\"\n", "    try:\n", "        import matplotlib.pyplot as plt\n", "    except ImportError:\n", "        print('matplotlib is not available.')\n", "    else:\n", "        if ax is None:\n", "            plt.figure(num=None, figsize=(10, 5))\n", "            ax = np.array([plt.subplot(121),\n", "                           plt.subplot(222),\n", "                           plt.subplot(224)])\n", "\n", "        plt.rc('axes', labelsize=12, titlesize=12)\n", "        plt.rc('xtick', labelsize=12)\n", "        plt.rc('ytick', labelsize=12)\n", "        ax[0].plot(freqs, res, 'b.', markersize=9)\n", "        time = np.linspace(0, len(y) / freq, len(y))\n", "        ax[1].plot(time, y, 'g', linewidth=1, label='Unfiltered')\n", "        ydd = np.diff(y, n=2) * freq ** 2\n", "        ax[2].plot(time[:-2], ydd, 'g', linewidth=1, label='Unfiltered')\n", "        if fc_opt:\n", "            ylin = np.poly1d([B, A])(freqs)\n", "            ax[0].plot(freqs, ylin, 'r--', linewidth=2)\n", "            ax[0].plot(freqs[fclim[0]], res[fclim[0]], 'r>',\n", "                       freqs[fclim[1]], res[fclim[1]], 'r<', ms=9)\n", "            ax[0].set_ylim(ymin=0, ymax=4 * A)\n", "            ax[0].plot([0, freqs[-1]], [A, A], 'r-', linewidth=2)\n", "            ax[0].plot([fc_opt, fc_opt], [0, A], 'r-', linewidth=2)\n", "            ax[0].plot(fc_opt, 0, 'ro', markersize=7, clip_on=False,\n", "                       zorder=9, label='$Fc_{opt}$ = %.1f Hz' % fc_opt)\n", "            ax[0].legend(fontsize=12, loc='best', numpoints=1, framealpha=.5)\n", "            # Correct the cutoff frequency for the number of passes\n", "            C = 0.802  # for dual pass; C = (2**(1/npasses) - 1)**0.25\n", "            b, a = butter(2, (fc_opt/C) / (freq / 2))\n", "            yf = filtfilt(b, a, y)\n", "            ax[1].plot(time, yf, color=[1, 0, 0, .5],\n", "                       linewidth=2, label='Opt. filtered')\n", "            ax[1].legend(fontsize=12, loc='best', framealpha=.5)\n", "            ax[1].set_title('Signals (RMSE = %.3g)' % A)\n", "            yfdd = np.diff(yf, n=2) * freq ** 2\n", "            ax[2].plot(time[:-2], yfdd, color=[1, 0, 0, .5],\n", "                       linewidth=2, label='Opt. filtered')\n", "            ax[2].legend(fontsize=12, loc='best', framealpha=.5)\n", "            resdd = np.sqrt(np.mean((yfdd - ydd) ** 2))\n", "            ax[2].set_title('Second derivatives (RMSE = %.3g)' % resdd)\n", "        else:\n", "            ax[0].text(.5, .5, 'Unable to find optimal cutoff frequency',\n", "                       horizontalalignment='center', color='r', zorder=9,\n", "                       transform=ax[0].transAxes, fontsize=12)\n", "            ax[1].set_title('Signal')\n", "            ax[2].set_title('Second derivative')\n", "\n", "        ax[0].set_xlabel('Cutoff frequency [Hz]')\n", "        ax[0].set_ylabel('Residual RMSE')\n", "        ax[0].set_title('Residual analysis')\n", "        ax[0].grid()\n", "        # ax2.set_xlabel('Time [s]')\n", "        ax[1].set_xlim(0, time[-1])\n", "        ax[1].grid()\n", "        ax[2].set_xlabel('Time [s]')\n", "        ax[2].set_xlim(0, time[-1])\n", "        ax[2].grid()\n", "        plt.tight_layout()\n", "        plt.show()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2017-12-29T19:22:48.275312Z", "start_time": "2017-12-29T19:22:47.904032Z"}, "id": "hpBHM6CMwEL2", "outputId": "31a503c6-5a84-4720-c1df-8f803535e9ab", "colab": {"base_uri": "https://localhost:8080/", "height": 428}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1100x400 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["# load data file\n", "# use Pandas just to read data from internet\n", "data = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data/Pezzack.txt',\n", "                   sep='\\t', header=None, skiprows=6).to_numpy()\n", "time, disp, disp2, aacc = data[:, 0], data[:, 1], data[:, 2], data[:, 3]\n", "dt = np.mean(np.diff(time))\n", "# plot data\n", "fig, (ax1,ax2) = plt.subplots(1, 2, sharex = True, figsize=(11, 4))\n", "plt.suptitle(\"Pezzack's benchmark data\", fontsize=14)\n", "ax1.plot(time, disp, 'b')\n", "ax1.set_xlabel('Time [s]'); ax1.set_ylabel('Angular displacement [rad]')\n", "ax2.plot(time, aacc, 'g')\n", "ax2.set_xlabel('Time [s]'); ax2.set_ylabel('Angular acceleration [rad/s$^2$]')\n", "plt.subplots_adjust(wspace=0.3)"]}, {"cell_type": "markdown", "metadata": {"id": "03Y3oYx5wEL3"}, "source": ["And using the residual analsysis code:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2017-12-29T19:22:53.522401Z", "start_time": "2017-12-29T19:22:52.929965Z"}, "id": "g15d5SUWwEL4", "outputId": "c791a27c-7b6e-4798-be77-3e10e3b0ac3e", "colab": {"base_uri": "https://localhost:8080/", "height": 507}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x500 with 3 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["freq = np.mean(1/np.diff(time))\n", "fc_opt = optcutfreq(disp, freq=freq, show=True)"]}, {"cell_type": "markdown", "metadata": {"id": "3MqtRyFvwEL4"}, "source": ["The optimal cutoff frequency found is 5.6 Hz. Note that the filtering process is relevant only for the derivative of the data; we cannot distinguish the unfiltered and unfiltered displacements (see that the RMSE residual is very small).   \n", "Let's employ this filter, differentiate the data twice and compare with the true acceleration as we did before:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2017-12-29T19:23:32.085684Z", "start_time": "2017-12-29T19:23:31.936581Z"}, "id": "nG95JbokwEL4", "outputId": "7215cf04-8514-464b-fce1-ee736395d91e", "colab": {"base_uri": "https://localhost:8080/", "height": 436}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1100x400 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["from scipy.signal import butter, filtfilt\n", "# Butterworth filter\n", "# Correct the cutoff frequency for the number of passes in the filter\n", "C = 0.802 # for dual pass; C = (2**(1/npasses) - 1)**0.25\n", "b, a = butter(2, (fc_opt/C)/(freq/2))\n", "dispf = filtfilt(b, a, disp)\n", "aaccBW =  np.diff(dispf, 2)*freq*freq\n", "# RMSE:\n", "rmseBW = np.sqrt(np.mean((aaccBW-aacc[1:-1])**2))\n", "# plot data\n", "fig, ax1 = plt.subplots(1, 1, figsize=(11, 4))\n", "plt.suptitle(\"Pezzack's benchmark data\", fontsize=14)\n", "ax1.plot(time[1:-1], aacc[1:-1], 'g', label='Analog acceleration:     (True value)')\n", "ax1.plot(time[1:-1], aaccBW, 'r',\n", "         label='Butterworth %.3g Hz:     RMSE = %0.2f' %(fc_opt,rmseBW))\n", "ax1.set_xlabel('Time [s]');\n", "ax1.set_ylabel('Angular acceleration [rad/s$^2$]');\n", "plt.legend(frameon=False, fontsize=12, loc='upper left');"]}, {"cell_type": "markdown", "metadata": {"id": "we_Kw0zEwEL4"}, "source": ["The performance seems satisfactory (see [this <PERSON><PERSON><PERSON> notebook](https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/DataFiltering.ipynb) for a comparison using other filters), but it is known that this residual analysis algorithm results in oversmoothing the kinematic data (see  [https://www.clinicalgaitanalysis.com/faq/cutoff.html](http://www.clinicalgaitanalysis.com/faq/cutoff.html))."]}, {"cell_type": "markdown", "metadata": {"id": "6Gknc5LowEL4"}, "source": ["To read more about the determination of the optimal cutoff frequency, see the following papers:  \n", "\n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON>, & <PERSON> (1977). An assessment of derivative determining techniques used for motion analysis. Journal of Biomechanics, 10, 377-382.\n", "- Giakas & Baltizopoulos (1997) A comparison of automatic filtering techniques applied to biomechanical walking data. J. Biomech. 30, 847-850.\n", "- <PERSON>, <PERSON>, <PERSON> & <PERSON> (2009) [Automatic smoothing of raw kinematic signals using SSA and cluster analysis](https://lim.ii.udc.es/docs/proceedings/2009_09_EUROMECH_Automatic.pdf). 7th EUROMECH Solid Mechanics Conference.\n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON> & <PERSON> (2012) Effect of low pass filtering on joint moments from inverse dynamics: Implications for injury prevention. J. Biomech. 45, 666-671."]}, {"cell_type": "markdown", "metadata": {"id": "9Utl-qxJwEL5"}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON>, & <PERSON> (1977). An assessment of derivative determining techniques used for motion analysis. Journal of Biomechanics, 10, 377-382. [PubMed](https://www.ncbi.nlm.nih.gov/pubmed/893476).\n", "- Winter DA (2009) [Biomechanics and motor control of human movement](https://books.google.com.br/books?id=_bFHL08IWfwC&printsec=frontcover&source=gbs_ge_summary_r&cad=0#v=onepage&q&f=false). 4 ed. Hoboken, EUA: Wiley."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": [], "include_colab_link": true}}, "nbformat": 4, "nbformat_minor": 0}