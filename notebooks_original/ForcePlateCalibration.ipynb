{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Algorithm for force plate calibration\n", "\n", "<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook demonstrates the algorithm for force plate calibration proposed by <PERSON><PERSON><PERSON> et al. (2008, 2009).  \n", "\n", "A force plate (FP) is an electromechanical device that measures the components of the vectors force $(\\mathbf{F} = [F_X,\\, F_Y,\\, F_Z])$ and moment of force or torque $(\\mathbf{M} = [M_X,\\, M_Y,\\, M_Z])$ applied to the it. The FP is composed by a transducer that transforms a mechanical deformation to an electrical signal usually using strain gauges or piezoelectric sensors. The transformation from electrical signals (input) to force and moment of force (output) as a function of time in a six-component FP usually is given by the following linear relationship:\n", "\n", "$$ \n", "\\mathbf{L}(t) = \\mathbf{C}\\mathbf{V}(t)\n", "$$\n", "\n", "Where $\\mathbf{L}(t)$ is the force plate output vector $([\\mathbf{F}(t), \\mathbf{M}(t)]^T)$, in N and Nm, $\\mathbf{V}(t)$ is the vector of electrical signals (six voltage signals, in V) and  $\\mathbf{C}$ is known as the six-by-six (constant) calibration matrix (in N/V or Nm/V). Note that we used the term vector here to refer to an uni-dimensional matrix (usual in scientific computing), which is different from vector/scalar concept in Mechanics.  \n", "The expansion of the former equiation at a given instant is:\n", "\n", "$$\n", "\\begin{bmatrix} \n", "F_x \\\\ F_y \\\\ F_z \\\\ M_x \\\\ M_y \\\\ M_z \n", "\\end{bmatrix}\\, = \\,\n", "\\begin{bmatrix} \n", "C_{11} && C_{12} && C_{13} && C_{14} && C_{15} && C_{16} \\\\\n", "C_{21} && C_{22} && C_{23} && C_{24} && C_{25} && C_{26} \\\\\n", "C_{31} && C_{32} && C_{33} && C_{34} && C_{35} && C_{36} \\\\\n", "C_{41} && C_{42} && C_{43} && C_{44} && C_{45} && C_{46} \\\\\n", "C_{51} && C_{52} && C_{53} && C_{54} && C_{55} && C_{56} \\\\\n", "C_{61} && <PERSON>_{62} && C_{63} && C_{64} && C_{65} && C_{66}\n", "\\end{bmatrix}\\,\n", "\\begin{bmatrix}\n", "V_1 \\\\ V_2 \\\\ V_3 \\\\ V_4 \\\\ V_5 \\\\ V_6 \n", "\\end{bmatrix}\n", "$$\n", "\n", "The terms off-diagonal are known as the crosstalk terms and represent the effect of a load applied in one direction on the other direction. For a FP with none or small crosstalk, the off-diagonal terms are zero or very small compared to the main-diagonal terms. Note that the equation above is in fact a system of six linear independent equations with six unknowns each (where $V_1 ... V_6$ are the measured inputs):\n", "\n", "\\begin{cases}\n", "    F_x &=& C_{11}V_1 + C_{12}V_2 + C_{13}V_3 + C_{14}V_4 + C_{15}V_5 + C_{16}V_6 \\\\\n", "    F_y &=& C_{21}V_1 + C_{22}V_2 + C_{23}V_3 + C_{24}V_4 + C_{25}V_5 + C_{26}V_6 \\\\\n", "    F_z &=& C_{31}V_1 + C_{32}V_2 + C_{33}V_3 + C_{34}V_4 + C_{35}V_5 + C_{36}V_6 \\\\\n", "    M_x &=& C_{41}V_1 + C_{42}V_2 + C_{43}V_3 + C_{44}V_4 + C_{45}V_5 + C_{46}V_6 \\\\\n", "    M_y &=& C_{51}V_1 + C_{52}V_2 + C_{53}V_3 + C_{54}V_4 + C_{55}V_5 + C_{56}V_6 \\\\\n", "    M_z &=& C_{61}V_1 + C_{62}V_2 + C_{63}V_3 + C_{64}V_4 + C_{65}V_5 + C_{66}V_6 \n", "\\end{cases}\n", "\n", "Of course, an important aspect of the FP functionning is that it should be calibrated, i.e., the calibration matrix must be known and accurate (it comes with the force plate when you buy one). <PERSON><PERSON><PERSON> et al. (2008) proposed a method for in situ re-calibration of FP and their algorithm is presented next."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Algorithm\n", "\n", "Consider that in a re-calibration procedure we apply on the FP known forces, $\\mathbf{F}_I = [F_{X_I},\\, F_{Y_I},\\, F_{Z_I}]^T$, at known places, $\\mathbf{COP} = [X_{COP},\\,  Y_{COP},\\,  Z_{COP}]$ (the center of pressure coordinates in the FP reference frame).  \n", "The moments of forces, $\\mathbf{M}_I = [M_{X_I},\\, M_{Y_I},\\, M_{Z_I}]^T$, due to these forces can be found using the equation $\\mathbf{M}_I = \\mathbf{COP} \\times \\mathbf{F}_I$, which can be expressed in matrix form as:\n", "\n", "$$ \n", "\\mathbf{M}_I = \n", "\\begin{bmatrix} \n", "0 && -Z_{COP} && Y_{COP} \\\\\n", "Z_{COP} && 0 && -X_{COP} \\\\\n", "-Y_{COP} && X_{COP} && 0\n", "\\end{bmatrix}\\, \\mathbf{F}_I \\, = \\, \\mathbf{A}_{COP}\\mathbf{F}_I\n", "$$\n", "\n", "$\\mathbf{A}_{COP}$ (a [skew-symmetric matrix](https://en.wikipedia.org/wiki/Skew-symmetric_matrix)) is simply the COP position in matrix form in order to calculate the [cross product with matrix multiplication](https://en.wikipedia.org/wiki/Cross_product).\n", "\n", "These known loads on the FP can also be represented as:\n", "\n", "$$ \n", "\\mathbf{L}_I = \n", "\\begin{bmatrix} \n", "\\mathbf{F}_I \\\\\n", "\\mathbf{M}_I \n", "\\end{bmatrix}\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Linear re-calibration\n", "\n", "For a linear re-calibration, the relationship between the measured FP output, $\\mathbf{L}$, and the known loads, $\\mathbf{L}_I$, is approximated by a linear equation:\n", "\n", "$$ \\mathbf{L}_I = \\mathbf{C}\\mathbf{L} + \\mathbf{E} $$\n", "\n", "Where $\\mathbf{C}$ now is the six-by-six re-calibration matrix (with dimensionless units) and $\\mathbf{E}$ is a gaussian, uncorrelated, zero mean\n", "noise six-by-one matrix.  \n", "The re-calibration matrix can be found by solving the equation above and then $\\mathbf{C}$ can be later used to re-calibrate the FP output:\n", "\n", "$$ \\mathbf{L}_C = \\mathbf{C}\\mathbf{L} $$\n", "\n", "Where $\\mathbf{L}_C$ is the re-calibrated FP output. For a perfectly calibrated FP, $\\mathbf{L}_C = \\mathbf{L}$ and $\\mathbf{C} = \\mathbf{I}$, the six-by-six identity matrix.\n", "\n", "<PERSON><PERSON><PERSON> et al. (2008, 2009) proposed to use a calibrated three-component load cell (LC) to measure the loads $\\mathbf{F}_I(t)$ applied on the FP at $k$ known measurements sites. The LC measures the loads in its own coordinate system $(xyz)$: $\\mathbf{F}_{LC}(t) = [F_x(t),\\, F_y(t),\\, F_z(t)]^T$, which is probaly rotated (by an unknown value, represented by rotation matrix $\\mathbf{R}^k$) in relation to the FP coordinate system (the coordinate systems are also translated to each other but the translation is known and given by the COP position).  \n", "For each measurement site, the equation for the determination of the re-calibration matrix will be given by:\n", "\n", "$$ \\mathbf{P}^k\\mathbf{R}^k\\mathbf{F}^k_{LC}(t)= \\mathbf{P}^k\\mathbf{F}_I^k(t) = \\mathbf{C}\\mathbf{L}^k(t) + \\mathbf{E}^k(t) \\quad k = 1, ..., n $$\n", "\n", "Where:\n", "\n", "$$ \n", "\\mathbf{P}^k = \n", "\\begin{bmatrix} \n", "\\mathbf{I}_3 \\\\\n", "\\mathbf{A}_{COP} \n", "\\end{bmatrix}\n", "$$\n", "\n", "and $I_3$ is the three-by-three identity matrix.  \n", "\n", "Using a typical load cell, with a flat bottom, on top the FP, a realistic assumption is to consider that $z$ of LC is aligned to $Z$ of FP (the vertical direction); in this case the rotation matrix is:\n", "\n", "$$ \n", "\\mathbf{R}^k = \n", "\\begin{bmatrix} \n", "\\cos\\alpha^k && -\\sin\\alpha^k && 0 \\\\\n", "\\sin\\alpha^k && \\cos\\alpha^k && 0 \\\\\n", "0 && 0 && 1\n", "\\end{bmatrix}\n", "$$\n", "\n", "\n", "<PERSON><PERSON><PERSON> et al. (2008) propose the following algorithm to estimate $\\mathbf{C}$:\n", "1. The misalignments, $\\alpha^k$, are initialized: $\\mathbf{\\alpha} = [\\alpha^1, \\cdots, \\alpha^n]$;\n", "2. $\\mathbf{C}$ is calculated by a least-squares approach;\n", "3. The residual errors are estimated as: $\\mathbf{E}^k(t) = \\mathbf{P}^k\\mathbf{R}^k\\mathbf{F}^k_{LC}(t) - \\mathbf{C}\\mathbf{L}^k(t)$;\n", "4. The increment $\\mathbf{\\Delta\\alpha}$ is calculated by minimizing the cost function $\\sum_{k,t}\\mathbf{E}^k(t)^T\\mathbf{E}^k(t)$, assuming dimensional unitary weights;\n", "5. The parameters are updated: $\\mathbf{\\alpha} = \\mathbf{\\alpha} + \\mathbf{\\Delta\\alpha}$.\n", "\n", "The iteration of steps 2–5 stops when each $\\Delta\\alpha^k < \\varepsilon_0$, where $\\varepsilon_0=10^{-10}$ is the chosen threshold."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simulation\n", "\n", "Let's simulate some data to test this calibration procedure. <PERSON><PERSON><PERSON> et al. (2008) employed sinusoids, cosenoids, and ramps as sintetic signals to simulate the calibration process:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:32.772797", "start_time": "2016-11-05T21:50:30.035501"}, "collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "from numpy.linalg import inv\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "import seaborn as sns\n", "sns.set_context(\"notebook\", font_scale=1.4,\n", "                rc={\"lines.linewidth\": 3, \"lines.markersize\": 8, \"axes.titlesize\": 'x-large'})"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:33.135262", "start_time": "2016-11-05T21:50:32.773800"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAyEAAAHuCAYAAACIzwO7AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nERERUbP6/7vIZodn4INYAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x187c2d9b4e0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# simulated forces measured by the load cell in its local coordinate system\n", "samples = np.linspace(1, 6000, 6000)\n", "ns = samples.shape[0]\n", "Flc = np.array([100*np.sin(5*2*np.pi*samples/ns) + 2*np.random.randn(6000),\n", "                100*np.cos(5*2*np.pi*samples/ns) + 2*np.random.randn(6000),\n", "                samples/15 + 200 + 5*np.random.randn(6000)])\n", "# plots\n", "fig, axs = plt.subplots(3, 1, figsize=(8, 5), sharex='all')\n", "axs[0].plot(samples, Flc[0])\n", "axs[0].set_ylabel('Fx (N)')\n", "axs[0].locator_params(axis='y', nbins=3)\n", "axs[0].yaxis.set_label_coords(-.08, 0.5)\n", "axs[1].plot(samples, Flc[1])\n", "axs[1].set_ylabel('Fy (N)')\n", "axs[1].locator_params(axis='y', nbins=3)\n", "axs[1].yaxis.set_label_coords(-.08, 0.5)\n", "axs[2].plot(samples, Flc[2])\n", "axs[2].set_ylabel('Fz (N)')\n", "axs[2].set_xlabel('Samples')\n", "axs[2].locator_params(axis='y', nbins=3)\n", "axs[2].yaxis.set_label_coords(-.08, 0.5)\n", "plt.tight_layout(pad=.5, h_pad=.025)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And <PERSON><PERSON><PERSON> et al. (2008) also proposed five measurement sites and a re-calibration matrix for the simulated re-calibration:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:34.871358", "start_time": "2016-11-05T21:50:34.860351"}}, "outputs": [], "source": ["# simulated true re-calibration matrix\n", "C = np.array([[ 1.0354, -0.0053, -0.0021, -0.0289, -0.0402,  0.0081],\n", "              [ 0.0064,  1.0309, -0.0031,  0.0211,  0.0135, -0.0001],\n", "              [ 0.0000, -0.0004,  1.0022, -0.0005, -0.0182,  0.0300],\n", "              [-0.0012, -0.0385,  0.0002,  0.9328,  0.0007,  0.0017],\n", "              [ 0.0347,  0.0003,  0.0008, -0.0002,  0.9325, -0.0024],\n", "              [-0.0004, -0.0013, -0.0003, -0.0023,  0.0035,  1.0592]])\n", "# five k measurements sites (in m)\n", "COP = np.array([[   0,  112,  112, -112, -112],\n", "                [   0,  192, -192,  192, -192],\n", "                [-124, -124, -124, -124, -124]])/1000\n", "# number of sites\n", "nk = COP.shape[1]\n", "# function for the COP skew-symmetric matrix\n", "Acop = lambda x,y,z : np.array([[.0, -z, y], [z, .0, -x], [-y, x, .0]])\n", "# same simulated forces measured by the load cell in all sites\n", "Flc = np.tile(Flc, nk)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's generate the loads measured by the FP given the re-calibration matrix and the simulated forces measured by the load cell (we will consider no rotation for now). For that we will have to solve the equation:\n", "\n", "$$ \\mathbf{L}_I = \\mathbf{C}\\mathbf{L} $$\n", "\n", "Which is:\n", "\n", "$$ \\mathbf{L} = \\mathbf{C}^{-1}\\mathbf{L}_I $$\n", "\n", "$\\mathbf{C}$ is a square (6-by-6) matrix and the computation of its inverse is straightforward."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:36.482542", "start_time": "2016-11-05T21:50:36.468031"}}, "outputs": [], "source": ["# simulated loads measured by LC\n", "Li = np.empty((6, ns*nk))\n", "P = np.empty((6, 3, nk))\n", "for k, cop in enumerate(COP.T):\n", "    P[:, :, k] = np.vstack((np.eye(3), Acop(*cop)))\n", "    Li[:, k*ns:(k+1)*ns] = P[:, :, k] @ Flc[:, k*ns:(k+1)*ns]\n", "\n", "# simulated loads applied on FP\n", "L = inv(C) @  Li"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the calculations above we took advantage of the [new operator for matrix multiplcation in Python 3](https://www.python.org/dev/peps/pep-0465/): `@` (mnemonic: `@` is `*` for mATrices).  \n", "\n", "We can now simulate the re-calibration procedure by determining the re-calibration matrix using these loads. Of course, the re-calibration matrix to be determined should be equal to the simulated re-calibration matrix we started with, but this is the fun of the simulation - we know where we want to go.\n", "\n", "The re-calibration matrix can be found by solving the following equation (considering the angles equal zero for now):\n", "\n", "$$ \\mathbf{L}_I = \\mathbf{C}\\mathbf{L} $$\n", "\n", "$$ \\mathbf{L}_I \\mathbf{L}^{-1} = \\mathbf{C}\\mathbf{L} \\mathbf{L}^{-1} = \\mathbf{C}\\mathbf{I}$$\n", "\n", "$$ \\mathbf{C} = \\mathbf{L}_I\\mathbf{L}^{-1} $$\n", "\n", "The problem is that $\\mathbf{L}$ in general is a non-square matrix and its inverse is not defined (unless you perform exactly six measurements and then $\\mathbf{L}$ would be a six-by-six square matrix, but this is too restrictive). However, we still can solve the equation with some extra manipulation:\n", "\n", "$$ \\mathbf{L}_I = \\mathbf{C}\\mathbf{L} $$\n", "\n", "$$ \\mathbf{L}_I \\mathbf{L}^T = \\mathbf{C}\\mathbf{L} \\mathbf{L}^T $$\n", "\n", "$$ \\mathbf{L}_I \\mathbf{L}^T(\\mathbf{L}\\mathbf{L}^T)^{-1} = \\mathbf{C}\\mathbf{L} \\mathbf{L}^T (\\mathbf{L}\\mathbf{L}^T)^{-1} = \\mathbf{C}\\mathbf{I} $$\n", "\n", "$$ \\mathbf{C} = \\mathbf{L}_I\\mathbf{L}^T(\\mathbf{L}\\mathbf{L}^T)^{-1} $$ \n", "\n", "Note that $\\mathbf{L} \\mathbf{L}^T$ is a square matrix and is invertible (also [nonsingular](https://en.wikipedia.org/wiki/Invertible_matrix)) if $\\mathbf{L}$ is L.I. ([linearly independent rows/columns](https://en.wikipedia.org/wiki/Linear_independence)). The matrix $\\mathbf{L}^T(\\mathbf{L}\\mathbf{L}^T)^{-1}$ is known as the [generalized inverse or Moore–Penrose pseudoinverse](https://en.wikipedia.org/wiki/Moore%E2%80%93Penrose_pseudoinverse), a generalization of the inverse matrix. If we denote this pseudoinverse matrix by $\\mathbf{L}^+$, we can state the solution of the equation simply as:\n", "\n", "$$ \\mathbf{L}_I = \\mathbf{C}\\mathbf{L} $$\n", "\n", "$$ \\mathbf{C} = \\mathbf{L}_I \\mathbf{L}^+ $$\n", "\n", "To compute the <PERSON> pseudoinverse, we could calculate it by the naive approach in Python:\n", "```python\n", "from numpy.linalg import inv\n", "Linv = L.T @ inv(L @ L.T)\n", "```\n", "But both <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> have functions to calculate the pseudoinverse, which might give greater numerical stability (but read [Inverses and pseudoinverses. Numerical issues, speed, symmetry](http://vene.ro/blog/inverses-pseudoinverses-numerical-issues-speed-symmetry.html)).  \n", "Of note, [numpy.linalg.pinv](http://docs.scipy.org/doc/numpy/reference/generated/numpy.linalg.pinv.html) calculates the pseudoinverse of a matrix using its singular-value decomposition (SVD) and including all large singular values (using the [LAPACK (Linear Algebra Package)](https://en.wikipedia.org/wiki/LAPACK) routine gesdd), whereas [scipy.linalg.pinv](http://docs.scipy.org/doc/scipy/reference/generated/scipy.linalg.pinv.html#scipy.linalg.pinv) calculates a pseudoinverse of a matrix using a least-squares solver (using the LAPACK method gelsd) and [scipy.linalg.pinv2](http://docs.scipy.org/doc/scipy/reference/generated/scipy.linalg.pinv2.html) also uses SVD to find the pseudoinverse (also using the LAPACK routine gesdd). \n", "Let's use [scipy.linalg.pinv2](http://docs.scipy.org/doc/scipy/reference/generated/scipy.linalg.pinv2.html):"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:37.856504", "start_time": "2016-11-05T21:50:37.840015"}}, "outputs": [], "source": ["from scipy.linalg import pinv2\n", "Lpinv = pinv2(L)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then, the re-calibration matrix is:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:39.025257", "start_time": "2016-11-05T21:50:39.019753"}}, "outputs": [], "source": ["C2 = Li @ Lpinv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Which is indeed the same as the initial calibration matrix:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:40.067591", "start_time": "2016-11-05T21:50:40.064088"}}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["np.allclose(C, C2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The residual error between the old loads and new loads after re-calibration is:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:41.333046", "start_time": "2016-11-05T21:50:41.327041"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average residual error between old and new loads: 8.38893490632e-21\n"]}], "source": ["E = Li - C2 @ L\n", "e = np.sum(E * E)\n", "print('Average residual error between old and new loads:', e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimization\n", "\n", "Let's now implement the full algorithm considering the likely rotation of the load cell during a re-calibration.  \n", "\n", "The idea is to guess initial values for the angles, estmate the re-calibration matrix, estimate new values for the angles that minimize the equation for the residuals and then estimate again the re-calibration matrix in an iterative approach until the estimated angles converge to the actual angles of the load cell in the different sites. This is a typical problem of [optimization](https://en.wikipedia.org/wiki/Mathematical_optimization) where the angles are the design variables and the equation for the residuals is the cost function (see this [notebook about optimization](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/Optimization.ipynb)).  \n", "\n", "Let's code the optimization in a complete function for the force plate re-calibration, named `fpcalibra.py`, with the following signature:\n", "```python\n", "   C, ang = fpcalibra(Lfp, Flc, COP, threshold=1e-10)\n", "```\n", "\n", "Let's import this function and run its example:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:50:43.308036", "start_time": "2016-11-05T21:50:43.083027"}}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath\n", "from fpcalibra import fpcalibra"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:51:58.977476", "start_time": "2016-11-05T21:50:48.155145"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Optimization finished in 70.8 s after 856 steps.\n", "\n", "Optimal calibration matrix:\n", " [[  1.03540000e+00  -5.29972332e-03  -2.10000083e-03  -2.88999943e-02\n", "   -4.01999964e-02   8.09999997e-03]\n", " [  6.39972212e-03   1.03090000e+00  -3.09999944e-03   2.11000078e-02\n", "    1.35000108e-02  -1.00002174e-04]\n", " [  5.07406617e-16  -4.00000000e-04   1.00220000e+00  -5.00000000e-04\n", "   -1.82000000e-02   3.00000000e-02]\n", " [ -1.20003446e-03  -3.84999998e-02   2.00000070e-04   9.32800001e-01\n", "    7.00001338e-04   1.69999973e-03]\n", " [  3.46999998e-02   2.99965692e-04   8.00000103e-04  -2.00000702e-04\n", "    9.32500000e-01  -2.40000000e-03]\n", " [ -4.00000323e-04  -1.30000000e-03  -2.99999999e-04  -2.30000001e-03\n", "    3.49999821e-03   1.05920000e+00]]\n", "\n", "Optimal angles:\n", " [  1.99999843e+01  -1.00000153e+01  -1.52986094e-05   1.49999847e+01\n", "  -5.00001530e+00]\n", "\n", "\n", "Residual between simulated and optimal re-calibration matrices: 4.24610474962e-08\n", "Residual between simulated and optimal rotation angles: 1.34142010205e-06\n"]}], "source": ["    >>> import numpy as np\n", "    >>> from numpy.linalg import inv\n", "    >>>\n", "    >>> # simulated true re-calibration matrix\n", "    >>> C = np.array([[ 1.0354, -0.0053, -0.0021, -0.0289, -0.0402,  0.0081],\n", "    >>>               [ 0.0064,  1.0309, -0.0031,  0.0211,  0.0135, -0.0001],\n", "    >>>               [ 0.0000, -0.0004,  1.0022, -0.0005, -0.0182,  0.0300],\n", "    >>>               [-0.0012, -0.0385,  0.0002,  0.9328,  0.0007,  0.0017],\n", "    >>>               [ 0.0347,  0.0003,  0.0008, -0.0002,  0.9325, -0.0024],\n", "    >>>               [-0.0004, -0.0013, -0.0003, -0.0023,  0.0035,  1.0592]])\n", "    >>> # simulated 5 measurements sites (in m)\n", "    >>> COP = np.array([[   0,  112,  112, -112, -112],\n", "    >>>                 [   0,  192, -192,  192, -192],\n", "    >>>                 [-124, -124, -124, -124, -124]])/1000\n", "    >>> nk = COP.shape[1]\n", "    >>> # simulated forces measured by the load cell (in N) before rotation\n", "    >>> samples = np.linspace(1, 6000, 6000)\n", "    >>> ns = samples.shape[0]\n", "    >>> Flc = np.empty((3, nk*ns))\n", "    >>> for k in range(nk):\n", "    >>>     Flc[:, k*ns:(k+1)*ns] = np.array([100*np.sin(5*2*np.pi*samples/ns) + 2*np.random.randn(ns),\n", "    >>>                                       100*np.cos(5*2*np.pi*samples/ns) + 2*np.random.randn(ns),\n", "    >>>                                       samples/15 + 200 + 5*np.random.randn(ns)])\n", "    >>> # function for the COP skew-symmetric matrix\n", "    >>> Acop = lambda x,y,z : np.array([[.0, -z, y], [z, .0, -x], [-y, x, .0]])\n", "    >>> # simulated loads measured by the force plate\n", "    >>> Li = np.empty((6, ns*nk))\n", "    >>> P = np.empty((6, 3, nk))\n", "    >>> for k, cop in enumerate(COP.T):\n", "    >>>     P[:, :, k] = np.vstack((np.eye(3), Acop(*cop)))\n", "    >>>     Li[:, k*ns:(k+1)*ns] = P[:, :, k] @ Flc[:, k*ns:(k+1)*ns]\n", "    >>> Lfp = inv(C) @  Li\n", "    >>> # simulated angles of rotaton of the measurement sites\n", "    >>> ang = np.array([20, -10, 0, 15, -5])/180*np.pi\n", "    >>> # function for the rotation matrix\n", "    >>> R = lambda a : np.array([[np.cos(a), -np.sin(a), 0], [np.sin(a), np.cos(a), 0], [ 0, 0, 1]])\n", "    >>> # simulated forces measured by the load cell after rotation\n", "    >>> for k in range(nk):\n", "    >>>     Flc[:, k*ns:(k+1)*ns] = R(ang[k]).T @ Flc[:, k*ns:(k+1)*ns]\n", "    >>> \n", "    >>> C2, ang2 = fpcalibra(Lfp, Flc, COP)\n", "    >>> \n", "    >>> e = np.sqrt(np.sum(C2-C)**2)\n", "    >>> print('Residual between simulated and optimal re-calibration matrices:', e)\n", "    >>> e = np.sqrt(np.sum(ang2-ang)**2)\n", "    >>> print('Residual between simulated and optimal rotation angles:', e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The simulation works as expected and the function was able to estimate accurately the known initial re-calibration matrix and angles of rotation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Non-linear algorithm for force plate calibration\n", "\n", "<PERSON><PERSON><PERSON> et al. (2011) extended the algorithm described earlier and proposed an algorithm for non-linear re-calibration of FPs.  \n", "The idea is that a load applied on the FP produces bending which depends on the point of force application and in turn will result in systematic errors in the COP determination. Consequently, this non-linearity could be modeled and compensated with a re-calibration which takes into account the COP coordinates measured by the FP and added to the linear re-calibration we deduced above (<PERSON><PERSON> et al., 2011).  \n", "The re-calibration equation will be (<PERSON><PERSON><PERSON> et al., 2011):\n", "\n", "$$ \\begin{array}{l l}\n", "\\mathbf{L}_C = \\mathbf{C}_0\\mathbf{L} + \\,\n", "\\begin{bmatrix} \n", "C_{x_{11}} & C_{x_{12}} & 0 & C_{x_{14}} & C_{x_{15}} & C_{x_{16}} \\\\\n", "C_{x_{21}} & C_{x_{22}} & 0 & C_{x_{24}} & C_{x_{25}} & C_{x_{26}} \\\\\n", "C_{x_{31}} & C_{x_{32}} & 0 & C_{x_{34}} & C_{x_{35}} & C_{x_{36}} \\\\\n", "C_{x_{41}} & C_{x_{42}} & 0 & C_{x_{44}} & C_{x_{45}} & C_{x_{46}} \\\\\n", "C_{x_{51}} & C_{x_{52}} & 0 & C_{x_{54}} & C_{x_{55}} & C_{x_{56}} \\\\\n", "C_{x_{61}} & C_{x_{62}} & 0 & C_{x_{64}} & C_{x_{65}} & C_{x_{66}}\n", "\\end{bmatrix}\\,\n", "\\begin{bmatrix}\n", "F_x \\\\ F_y \\\\ F_z \\\\ M_x \\\\ M_y \\\\ M_z \n", "\\end{bmatrix} COP_x + \n", "\\begin{bmatrix} \n", "C_{y_{11}} & C_{y_{12}} & 0 & 0 & C_{y_{15}} & C_{y_{16}} \\\\\n", "C_{y_{21}} & C_{y_{22}} & 0 & 0 & C_{y_{25}} & C_{y_{26}} \\\\\n", "C_{y_{31}} & C_{y_{32}} & 0 & 0 & C_{y_{35}} & C_{y_{36}} \\\\\n", "C_{y_{41}} & C_{y_{42}} & 0 & 0 & C_{y_{45}} & C_{y_{46}} \\\\\n", "C_{y_{51}} & C_{y_{52}} & 0 & 0 & C_{y_{55}} & C_{y_{56}} \\\\\n", "C_{y_{61}} & C_{y_{62}} & 0 & 0 & C_{y_{65}} & C_{y_{66}}\n", "\\end{bmatrix}\\,\n", "\\begin{bmatrix}\n", "F_x \\\\ F_y \\\\ F_z \\\\ M_x \\\\ M_y \\\\ M_z \n", "\\end{bmatrix} COP_y\n", "\\\\[6pt]\n", "\\mathbf{L}_C = (\\mathbf{C}_0 + \\mathbf{C}_x COP_x + \\mathbf{C}_y COP_y)\\mathbf{L} = \\mathbf{C}_{NL}\\mathbf{L}\n", "\\end{array} $$\n", "\n", "Where $\\mathbf{C}_0$ is the linear re-calibration matrix, $\\mathbf{L}$ is the measured FP output, $\\mathbf{C}_x$ and  $\\mathbf{C}_y$ are the non-linear re-calibration matrices.\n", "\n", "To estimate $\\mathbf{C}_{NL}$, <PERSON><PERSON><PERSON> et al. (2011) suggest to employ the algorithm proposed by <PERSON><PERSON><PERSON> et al. (2008) to estimate the linear re-calibration described earlier."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["    # number of sites\n", "    nk = COP.shape[1]\n", "    # number of samples\n", "    ns = int(Lfp.shape[1]/nk)\n", "    # function for the COP skew-symmetric matrix\n", "    Acop = lambda x,y,z : np.array([[.0, -z, y], [z, .0, -x], [-y, x, .0]])\n", "    P = np.empty((6, 3, nk))\n", "    for k, cop in enumerate(COP.T):\n", "        P[:, :, k] = np.vstack((np.eye(3), Acop(*cop)))\n", "    # function for the 2D rotation matrix\n", "    R = lambda a : np.array([[np.cos(a), -np.sin(a), 0], [np.sin(a), np.cos(a), 0], [ 0, 0, 1]])\n", "    # Pseudoiverse of the loads measured by the force plate\n", "    if method.lower() == 'svd':\n", "        Lpinv = pinv2(Lfp)\n", "    else:\n", "        Lpinv = pinv(Lfp)        \n", "    # cost function for the optimization\n", "    def costfun(ang, P, R, Flc, CLfp, nk, ns, E):\n", "        for k in range(nk):\n", "            E[:,k*ns:(k+1)*ns] = (P[:,:,k] @ R(ang[k])) @ Flc[:,k*ns:(k+1)*ns] - CLfp[:,k*ns:(k+1)*ns]\n", "        return np.sum(E * E)\n", "    # inequality constraints\n", "    bnds = [(-np.pi/2, np.pi/2) for k in range(nk)]\n", "    # some initialization\n", "    ang0 = np.zeros(nk)\n", "    E = np.empty((6, ns*nk))\n", "    da = []\n", "    delta_ang = 10*threshold\n", "    Li = np.empty((6, ns*nk))\n", "    start = time.time()\n", "    # the optimization\n", "    while np.all(delta_ang > threshold):\n", "        for k in range(nk):\n", "            Li[:,k*ns:(k+1)*ns] = (P[:,:,k] @ R(ang0[k])) @ Flc[:,k*ns:(k+1)*ns]\n", "        C = Li @ Lpinv\n", "        CLfp = C @ Lfp\n", "        res = minimize(fun=costfun, x0=ang0, args=(P, R, Flc, CLfp, nk, ns, E),\n", "                       bounds=bnds, method='TNC', options={'disp': False})\n", "        delta_ang = np.abs(res.x - ang0)\n", "        ang0 = res.x\n", "        da.append(delta_ang.sum())\n", "\n", "    tdelta = time.time() - start\n", "    print('\\nOptimization finished in %.1f s after %d steps.\\n' %(t<PERSON><PERSON>, len(da)))\n", "    print('Optimal calibration matrix:\\n', C)\n", "    print('\\nOptimal angles:\\n', res.x*180/np.pi)\n", "    print('\\n')\n", "\n", "    return C, res.x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (2008) A portable system for in-situ re-calibration of force platforms: theoretical validation. Gait & Posture, 28, 488–494](http://www.ncbi.nlm.nih.gov/pubmed/18450453).  \n", "- [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2009) A portable system for in-situ re-calibration of force platforms: experimental validation. Gait & Posture, 29, 449–453](http://www.ncbi.nlm.nih.gov/pubmed/19111467).  \n", "- [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2011) Non-linear re-calibration of force platforms. Gait & Posture, 33, 724–726](http://www.ncbi.nlm.nih.gov/pubmed/21392999)."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2016-11-05T21:57:56.164254", "start_time": "2016-11-05T21:57:56.156233"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The version_information extension is already loaded. To reload it, use:\n", "  %reload_ext version_information\n"]}, {"data": {"application/json": {"Software versions": [{"module": "Python", "version": "3.5.2 64bit [MSC v.1900 64 bit (AMD64)]"}, {"module": "IPython", "version": "5.1.0"}, {"module": "OS", "version": "Windows 10 10.0.14393 SP0"}, {"module": "numpy", "version": "1.11.2"}, {"module": "scipy", "version": "0.18.1"}, {"module": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.5.3"}, {"module": "ipython", "version": "5.1.0"}, {"module": "jup<PERSON><PERSON>", "version": "1.0.0"}, {"module": "pandas", "version": "0.19.1"}]}, "text/html": ["<table><tr><th>Software</th><th>Version</th></tr><tr><td>Python</td><td>3.5.2 64bit [MSC v.1900 64 bit (AMD64)]</td></tr><tr><td>IPython</td><td>5.1.0</td></tr><tr><td>OS</td><td>Windows 10 10.0.14393 SP0</td></tr><tr><td>numpy</td><td>1.11.2</td></tr><tr><td>scipy</td><td>0.18.1</td></tr><tr><td>matplotlib</td><td>1.5.3</td></tr><tr><td>ipython</td><td>5.1.0</td></tr><tr><td>jupyter</td><td>1.0.0</td></tr><tr><td>pandas</td><td>0.19.1</td></tr><tr><td colspan='2'>Sat Nov 05 21:57:56 2016 E. South America Daylight Time</td></tr></table>"], "text/latex": ["\\begin{tabular}{|l|l|}\\hline\n", "{\\bf Software} & {\\bf Version} \\\\ \\hline\\hline\n", "Python & 3.5.2 64bit [MSC v.1900 64 bit (AMD64)] \\\\ \\hline\n", "IPython & 5.1.0 \\\\ \\hline\n", "OS & Windows 10 10.0.14393 SP0 \\\\ \\hline\n", "numpy & 1.11.2 \\\\ \\hline\n", "scipy & 0.18.1 \\\\ \\hline\n", "matplotlib & 1.5.3 \\\\ \\hline\n", "ipython & 5.1.0 \\\\ \\hline\n", "jupyter & 1.0.0 \\\\ \\hline\n", "pandas & 0.19.1 \\\\ \\hline\n", "\\hline \\multicolumn{2}{|l|}{Sat Nov 05 21:57:56 2016 E. South America Daylight Time} \\\\ \\hline\n", "\\end{tabular}\n"], "text/plain": ["Software versions\n", "Python 3.5.2 64bit [MSC v.1900 64 bit (AMD64)]\n", "IPython 5.1.0\n", "OS Windows 10 10.0.14393 SP0\n", "numpy 1.11.2\n", "scipy 0.18.1\n", "matplotlib 1.5.3\n", "ipython 5.1.0\n", "jupyter 1.0.0\n", "pandas 0.19.1\n", "Sat Nov 05 21:57:56 2016 E. South America Daylight Time"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["%load_ext version_information\n", "%version_information numpy, scipy, matplotlib, ipython, jupyter, pandas"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function fpcalibra.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2016-08-19T19:57:45.740280", "start_time": "2016-08-19T19:57:45.734275"}, "collapsed": true}, "outputs": [], "source": ["# %load ./../functions/fpcalibra.py\n", "\"\"\"Force plate calibration algorithm.\n", "\"\"\"\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = 'fpcalibra.py v.1.0.1 2016/08/19'\n", "__license__ = \"MIT\"\n", "\n", "import numpy as np\n", "from scipy.linalg import pinv, pinv2\n", "from scipy.optimize import minimize\n", "import time\n", "\n", "\n", "def fpcalibra(Lfp, Flc, COP, threshold=1e-10, method='SVD'):\n", "    \"\"\"Force plate calibration algorithm.\n", "    \n", "    For a force plate (FP) re-calibration, the relationship between the\n", "    measured FP output (L) and the known loads (Li) is approximated by:\n", "    Li = C@L + E (@ is the operator for matrix multiplication).  \n", "    Where C is the 6-by-6 re-calibration matrix and E is a gaussian,\n", "    uncorrelated, zero mean noise six-by-one matrix.  \n", "\n", "    The re-calibration matrix can be found by solving the equation above and\n", "    then C can be later used to re-calibrate the FP output: Lc = C@L.  \n", "    Where Lc is the re-calibrated FP output.\n", "\n", "    <PERSON><PERSON><PERSON> et al. (2008) [1]_ proposed to use a calibrated three-component\n", "    load cell to measure the forces applied on the FP at known measurement\n", "    sites and an algorithm for the re-calibration.\n", "    \n", "    This code implements the re-calibration algorithm, see [2]_\n", "    \n", "    Parameters\n", "    ----------\n", "    Lfp : numpy 2-D array (6, nsamples*nksites)\n", "        loads [Fx, Fy, Fz, Mx, My, Mz] (in N and Nm) measured by the force\n", "        plate due to the corresponding forces applied at the measurement sites\n", "    Flc : numpy 2-D array (3, nsamples*nksites)\n", "        forces [Fx, Fy, Fz] (in N) measured by the load cell at the\n", "        measurement sites\n", "    COP : numpy 2-D array (3, nksites)\n", "        positions [COPx, COPy, COPz] (in m) of the load cell at the\n", "        measurement sites\n", "    threshold  : float, optional\n", "        threshold to stop the optimization (default 1e-10)\n", "    method  : string, optional\n", "        method for the pseudiinverse calculation, 'SVD' (default) or 'lstsq'\n", "        SVD is the Singular Value Decomposition and lstsq is least-squares\n", "    \n", "    Returns\n", "    -------\n", "    C   : numpy 2-D (6-by-6) array\n", "        optimal force plate re-calibration matrix (in dimensionless units)\n", "    ang : numpy 1-D array [ang0, ..., angk]\n", "        optimal angles of rotation (in rad) of the load cells at the\n", "        measurement sites\n", "\n", "    References\n", "    ----------\n", "    .. [1] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (2008) Gait & Posture, 28, 488–494. \n", "    .. [2] http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ForcePlateCalibration.ipynb\n", "\n", "    Example\n", "    -------\n", "    >>> from fpcalibra import fpcalibra\n", "    >>> import numpy as np\n", "    >>> from numpy.linalg import inv\n", "    >>>\n", "    >>> # simulated true re-calibration matrix\n", "    >>> C = np.array([[ 1.0354, -0.0053, -0.0021, -0.0289, -0.0402,  0.0081],\n", "    >>>               [ 0.0064,  1.0309, -0.0031,  0.0211,  0.0135, -0.0001],\n", "    >>>               [ 0.0000, -0.0004,  1.0022, -0.0005, -0.0182,  0.0300],\n", "    >>>               [-0.0012, -0.0385,  0.0002,  0.9328,  0.0007,  0.0017],\n", "    >>>               [ 0.0347,  0.0003,  0.0008, -0.0002,  0.9325, -0.0024],\n", "    >>>               [-0.0004, -0.0013, -0.0003, -0.0023,  0.0035,  1.0592]])\n", "    >>> # simulated 5 measurements sites (in m)\n", "    >>> COP = np.array([[   0,  112,  112, -112, -112],\n", "    >>>                 [   0,  192, -192,  192, -192],\n", "    >>>                 [-124, -124, -124, -124, -124]])/1000\n", "    >>> nk = COP.shape[1]\n", "    >>> # simulated forces measured by the load cell (in N) before rotation\n", "    >>> samples = np.linspace(1, 6000, 6000)\n", "    >>> ns = samples.shape[0]\n", "    >>> Flc = np.empty((3, nk*ns))\n", "    >>> for k in range(nk):\n", "    >>>     Flc[:, k*ns:(k+1)*ns] = np.array([100*np.sin(5*2*np.pi*samples/ns) + 2*np.random.randn(ns),\n", "    >>>                                       100*np.cos(5*2*np.pi*samples/ns) + 2*np.random.randn(ns),\n", "    >>>                                       samples/15 + 200 + 5*np.random.randn(ns)])\n", "    >>> # function for the COP skew-symmetric matrix\n", "    >>> Acop = lambda x,y,z : np.array([[.0, -z, y], [z, .0, -x], [-y, x, .0]])\n", "    >>> # simulated loads measured by the force plate\n", "    >>> Li = np.empty((6, ns*nk))\n", "    >>> P = np.empty((6, 3, nk))\n", "    >>> for k, cop in enumerate(COP.T):\n", "    >>>     P[:, :, k] = np.vstack((np.eye(3), Acop(*cop)))\n", "    >>>     Li[:, k*ns:(k+1)*ns] = P[:, :, k] @ Flc[:, k*ns:(k+1)*ns]\n", "    >>> Lfp = inv(C) @  Li\n", "    >>> # simulated angles of rotaton of the measurement sites\n", "    >>> ang = np.array([20, -10, 0, 15, -5])/180*np.pi\n", "    >>> # function for the rotation matrix\n", "    >>> R = lambda a : np.array([[np.cos(a), -np.sin(a), 0], [np.sin(a), np.cos(a), 0], [ 0, 0, 1]])\n", "    >>> # simulated forces measured by the load cell after rotation\n", "    >>> for k in range(nk):\n", "    >>>     Flc[:, k*ns:(k+1)*ns] = R(ang[k]).T @ Flc[:, k*ns:(k+1)*ns]\n", "    >>> \n", "    >>> C2, ang2 = fpcalibra(Lfp, Flc, COP)\n", "    >>> \n", "    >>> e = np.sqrt(np.sum(C2-C)**2)\n", "    >>> print('Residual between simulated and optimal re-calibration matrices:', e)\n", "    >>> e = np.sqrt(np.sum(ang2-ang)**2)\n", "    >>> print('Residual between simulated and optimal rotation angles:', e)\n", "    \"\"\"\n", "\n", "    # number of sites\n", "    nk = COP.shape[1]\n", "    # number of samples\n", "    ns = int(Lfp.shape[1]/nk)\n", "    # function for the COP skew-symmetric matrix\n", "    Acop = lambda x,y,z : np.array([[.0, -z, y], [z, .0, -x], [-y, x, .0]])\n", "    P = np.empty((6, 3, nk))\n", "    for k, cop in enumerate(COP.T):\n", "        P[:, :, k] = np.vstack((np.eye(3), Acop(*cop)))\n", "    # function for the 2D rotation matrix\n", "    R = lambda a : np.array([[np.cos(a), -np.sin(a), 0], [np.sin(a), np.cos(a), 0], [ 0, 0, 1]])\n", "    # Pseudoiverse of the loads measured by the force plate\n", "    if method.lower() == 'svd':\n", "        Lpinv = pinv2(Lfp)\n", "    else:\n", "        Lpinv = pinv(Lfp)        \n", "    # cost function for the optimization\n", "    def costfun(ang, P, R, Flc, CLfp, nk, ns, E):\n", "        for k in range(nk):\n", "            E[:,k*ns:(k+1)*ns] = (P[:,:,k] @ R(ang[k])) @ Flc[:,k*ns:(k+1)*ns] - CLfp[:,k*ns:(k+1)*ns]\n", "        return np.sum(E * E)\n", "    # inequality constraints\n", "    bnds = [(-np.pi/2, np.pi/2) for k in range(nk)]\n", "    # some initialization\n", "    ang0 = np.zeros(nk)\n", "    E = np.empty((6, ns*nk))\n", "    da = []\n", "    delta_ang = 10*threshold\n", "    Li = np.empty((6, ns*nk))\n", "    start = time.time()\n", "    # the optimization\n", "    while np.all(delta_ang > threshold):\n", "        for k in range(nk):\n", "            Li[:,k*ns:(k+1)*ns] = (P[:,:,k] @ R(ang0[k])) @ Flc[:,k*ns:(k+1)*ns]\n", "        C = Li @ Lpinv\n", "        CLfp = C @ Lfp\n", "        res = minimize(fun=costfun, x0=ang0, args=(P, R, Flc, CLfp, nk, ns, E),\n", "                       bounds=bnds, method='TNC', options={'disp': False})\n", "        delta_ang = np.abs(res.x - ang0)\n", "        ang0 = res.x\n", "        da.append(delta_ang.sum())\n", "\n", "    tdelta = time.time() - start\n", "    print('\\nOptimization finished in %.1f s after %d steps.\\n' %(t<PERSON><PERSON>, len(da)))\n", "    print('Optimal calibration matrix:\\n', C)\n", "    print('\\nOptimal angles:\\n', res.x*180/np.pi)\n", "    print('\\n')\n", "\n", "    return C, res.x"]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.10"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}