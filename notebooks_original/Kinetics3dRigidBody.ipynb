{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Three-dimensional rigid body kinetics\n", "\n", "> <PERSON><PERSON>\n", "\n", "> Laboratory of Biomechanics and Motor Control ([http://pesquisa.ufabc.edu.br/bmclab](http://pesquisa.ufabc.edu.br/bmclab))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"toc": 1}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#The-Newton-Euler-laws\" data-toc-modified-id=\"The-Newton-Euler-laws-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>The Newton-Euler laws</a></span><ul class=\"toc-item\"><li><span><a href=\"#Resultant-force-and-moments\" data-toc-modified-id=\"Resultant-force-and-moments-1.1\"><span class=\"toc-item-num\">1.1&nbsp;&nbsp;</span>Resultant force and moments</a></span></li></ul></li><li><span><a href=\"#Deduction-of-the-angular-momentum-in-three-dimensional-movement\" data-toc-modified-id=\"Deduction-of-the-angular-momentum-in-three-dimensional-movement-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Deduction of the angular momentum in three-dimensional movement</a></span><ul class=\"toc-item\"><li><span><a href=\"#Angular-momentum-in-three-dimensional-movement\" data-toc-modified-id=\"Angular-momentum-in-three-dimensional-movement-2.1\"><span class=\"toc-item-num\">2.1&nbsp;&nbsp;</span>Angular momentum in three-dimensional movement</a></span></li></ul></li><li><span><a href=\"#The-matrix-of-inertia-is-different-depending-on-the-body-orientation\" data-toc-modified-id=\"The-matrix-of-inertia-is-different-depending-on-the-body-orientation-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>The matrix of inertia is different depending on the body orientation</a></span><ul class=\"toc-item\"><li><span><a href=\"#The-solution-is-to-attach-a-frame-reference-to-the-body\" data-toc-modified-id=\"The-solution-is-to-attach-a-frame-reference-to-the-body-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>The solution is to attach a frame reference to the body</a></span></li><li><span><a href=\"#The-fixed-frame-is-chosen-in-a-way-that-the-matrix-of-inertia-is-a-diagonal-matrix\" data-toc-modified-id=\"The-fixed-frame-is-chosen-in-a-way-that-the-matrix-of-inertia-is-a-diagonal-matrix-3.2\"><span class=\"toc-item-num\">3.2&nbsp;&nbsp;</span>The fixed frame is chosen in a way that the matrix of inertia is a diagonal matrix</a></span></li></ul></li><li><span><a href=\"#The-angular-velocity-in-the-fixed-frame\" data-toc-modified-id=\"The-angular-velocity-in-the-fixed-frame-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>The angular velocity in the fixed frame</a></span><ul class=\"toc-item\"><li><span><a href=\"#The-angular-momentum-in-the-fixed-frame\" data-toc-modified-id=\"The-angular-momentum-in-the-fixed-frame-4.1\"><span class=\"toc-item-num\">4.1&nbsp;&nbsp;</span>The angular momentum in the fixed frame</a></span></li></ul></li><li><span><a href=\"#Derivative-of-the-angular-momentum\" data-toc-modified-id=\"Derivative-of-the-angular-momentum-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Derivative of the angular momentum</a></span><ul class=\"toc-item\"><li><span><a href=\"#Angular-velocity\" data-toc-modified-id=\"Angular-velocity-5.1\"><span class=\"toc-item-num\">5.1&nbsp;&nbsp;</span>Angular velocity</a></span></li><li><span><a href=\"#The-derivative-of-the-versors\" data-toc-modified-id=\"The-derivative-of-the-versors-5.2\"><span class=\"toc-item-num\">5.2&nbsp;&nbsp;</span>The derivative of the versors</a></span></li><li><span><a href=\"#The-derivative-of-the-angular-momentum\" data-toc-modified-id=\"The-derivative-of-the-angular-momentum-5.3\"><span class=\"toc-item-num\">5.3&nbsp;&nbsp;</span>The derivative of the angular momentum</a></span></li></ul></li><li><span><a href=\"#Newton-Euler-laws\" data-toc-modified-id=\"Newton-Euler-laws-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Newton-Euler laws</a></span></li><li><span><a href=\"#Examples\" data-toc-modified-id=\"Examples-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Examples</a></span><ul class=\"toc-item\"><li><span><a href=\"#3D-pendulum-bar\" data-toc-modified-id=\"3D-pendulum-bar-7.1\"><span class=\"toc-item-num\">7.1&nbsp;&nbsp;</span>3D pendulum bar</a></span></li><li><span><a href=\"#Data-from-postural-control\" data-toc-modified-id=\"Data-from-postural-control-7.2\"><span class=\"toc-item-num\">7.2&nbsp;&nbsp;</span>Data from postural control</a></span></li></ul></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## The Newton-Euler laws\n", "\n", "The Newton-Euler laws remain valid in the three-dimensional motion of a rigid body (for revision on Newton-Euler laws, [see this notebook about these laws](newton_euler_equations.ipynb)).\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{F} = m\\vec{a_{cm}}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    \\vec{<PERSON>_<PERSON>} = \\frac{d\\vec{H_O}}{dt}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Resultant force and moments\n", "\n", "The resultant force and the resultant moment around a point O are computed in the same way of the two-dimensional case. \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{F} = \\sum\\limits_{i=1}^n \\vec{F_n}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    \\vec{<PERSON>_<PERSON>} = \\sum\\limits_{i=1}^n \\vec{r_{i/O}} \\times \\vec{F_i}\n", "\\end{equation}\n", "</span>\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Deduction of the angular momentum in three-dimensional movement\n", "\n", "The major difference that appears when we are dealing with three-dimensional motions is to compute the derivative of the angular momentum. For the sake of simplicity, the analysis will only consider the point O as the center of mass of the body.\n", "\n", "We begin computing the angular momentum of a rigid body around its center of mass.\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{H_{cm}} =  \\int_B \\vec{r_{/cm}} \\times \\vec{v}\\,dm = \\int  \\vec{r_{/cm}} \\times (\\vec{\\omega}\\times\\vec{r_{/cm}})\\,dm\n", "\\end{equation}\n", "</span>\n", "where <span class=\"notranslate\">$\\vec{\\boldsymbol{r_{/cm}}}$</span> is the vector from the point O to the position of the infinitesimal mass considered in the integral. For simplicity of the notation, we will use <span class=\"notranslate\">$\\vec{\\boldsymbol{r}} = \\vec{\\boldsymbol{r_{/cm}}}$</span>. The triple vector product above can be computed using the rule:\n", "    <span class=\"notranslate\">\n", "    $$\\vec{a}\\times(\\vec{b}\\times\\vec{c}) = (\\vec{a}.\\vec{c})\\vec{b} - (\\vec{a}.\\vec{b})\\vec{c}$$.</span>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So, the angular momentum is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", " \\vec{H_{cm}} = \\int  \\vec{r} \\times (\\vec{\\omega}\\times\\vec{r})\\,dm =\n", "      \\int  (r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}}) \\times \\left[(\\omega_x\\hat{\\boldsymbol{i}} + \\omega_y\\hat{\\boldsymbol{j}}+\\omega_z\\hat{\\boldsymbol{k}})\\times(r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}})\\right]\\,dm = \\int \\left[(r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}}).(r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}})\\right](\\omega_x\\hat{\\boldsymbol{i}} + \\omega_y\\hat{\\boldsymbol{j}}+\\omega_z\\hat{\\boldsymbol{k}}) -\\left[(r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}}).(\\omega_x\\hat{\\boldsymbol{i}} + \\omega_y\\hat{\\boldsymbol{j}}+\\omega_z\\hat{\\boldsymbol{k}})\\right](r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}}) \\,dm = \\int (r_x^2 + r_y^2+r_z^2)(\\omega_x\\hat{\\boldsymbol{i}} + \\omega_y\\hat{\\boldsymbol{j}}+\\omega_z\\hat{\\boldsymbol{k}}) - (r_x\\omega_x + r_y\\omega_y+r_z\\omega_z)(r_x\\hat{\\boldsymbol{i}} + r_y\\hat{\\boldsymbol{j}}+r_z\\hat{\\boldsymbol{k}})\\,dm\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<span class=\"notranslate\">\n", "\\begin{equation}\n", " \\vec{H_{cm}} = \\int (r_x^2 + r_y^2+r_z^2)\\omega_x\\hat{\\boldsymbol{i}} - (r_x\\omega_x + r_y\\omega_y+r_z\\omega_z)r_x\\hat{\\boldsymbol{i}} + (r_x^2 + r_y^2+r_z^2)\\omega_y\\hat{\\boldsymbol{j}} - (r_x\\omega_x + r_y\\omega_y+r_z\\omega_z)r_y\\hat{\\boldsymbol{j}}+(r_x^2 + r_y^2+r_z^2)\\omega_z\\hat{\\boldsymbol{k}} - (r_x\\omega_x + r_y\\omega_y+r_z\\omega_z)r_z\\hat{\\boldsymbol{k}}\\,dm\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{H_{cm}}=\\left(\\int \\omega_x(r_y^2+r_z^2)\\,dm+  \\int-\\omega_yr_xr_y\\,dm + \\int-\\omega_z r_xr_z\\,dm\\right)\\;\\hat{\\boldsymbol{i}} + \\left(\\int-\\omega_x r_xr_y\\,dm +\\int\\omega_y (r_x^2 +r_z^2)\\,dm + \\int- \\omega_zr_yr_z\\,dm\\right)\\hat{\\boldsymbol{j}} + \\left(\\int-\\omega_x r_xr_z\\,dm  + \\int-\\omega_yr_yr_z\\,dm + \\int \\omega_z(r_x^2+r_y^2) \\,dm\\right)\\hat{\\boldsymbol{k}}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<span class=\"notranslate\">\n", "\\begin{equation}\n", "      \\vec{H_{cm}}=\\bigg(\\omega_x\\int (r_y^2+r_z^2)\\,dm+  \\omega_y\\int-r_xr_y\\,dm + \\omega_z\\int- r_xr_z\\,dm\\bigg)\\;\\hat{\\boldsymbol{i}}+\\bigg(\\omega_x\\int- r_xr_y\\,dm +\\omega_y\\int (r_x^2 +r_z^2)\\,dm +  \\omega_z\\int-r_yr_z\\,dm\\bigg)\\hat{\\boldsymbol{j}} + \\bigg(\\omega_x\\int- r_xr_z\\,dm  + \\omega_y\\int-r_yr_z\\,dm + \\omega_z\\int (r_x^2+r_y^2) \\,dm\\bigg)\\hat{\\boldsymbol{k}}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{H_{cm}}=\\left(\\omega_xI_{xx}^{cm}+  \\omega_yI_{xy}^{cm} + \\omega_zI_{xz}^{cm}\\right)\\;\\hat{\\boldsymbol{i}} + \\left(\\omega_xI_{xy}^{cm} +\\omega_yI_{yy}^{cm} +  \\omega_zI_{yz}^{cm}\\right)\\hat{\\boldsymbol{j}}+\\left(\\omega_xI_{yz}^{cm}  + \\omega_yI_{yz}^{cm} + \\omega_zI_{zz}^{cm}\\right)\\hat{\\boldsymbol{k}}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Angular momentum in three-dimensional movement\n", "\n", "\n", "\\begin{align}\n", "\\begin{split}\n", "      \\vec{H_{cm}}=\\left[\\begin{array}{ccc}I_{xx}^{cm}&I_{xy}^{cm}&I_{xz}^{cm}\\\\\n", "                                  I_{xy}^{cm}&I_{yy}^{cm}&I_{yz}^{cm}\\\\\n", "                                  I_{xz}^{cm}&I_{yz}^{cm}&I_{zz}^{cm}\\end{array}\\right]\\cdot\n", "               \\left[\\begin{array}{c}\\omega_x\\\\\\omega_y\\\\\\omega_z \\end{array}\\right] = I\\vec{\\omega}\n", "      \\end{split}\n", "      \\label{eq:angmom}\n", " \\end{align}\n", " \n", " where this matrix is the Matrix of Inertia (or more rigorously, Tensor of Inertia) as defined previously in [this notebook about moment of inertia](CenterOfMassAndMomentOfInertia.ipynb#matrixinertia)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## The matrix of inertia is different depending on the body orientation\n", "\n", "So, to compute the angular momentum of a body we have to multiply the matrix of inertia <span class=\"notranslate\">$I$</span> of the body by its angular velocity <span class=\"notranslate\">$\\vec{\\omega}$</span>. The problem on this approach is that the moments and products of inertia depends on the orientation of the body relative to the frame of reference. As they depend on the distances of each point of the body to the axes, if the body is rotating, the matrix of inertia $I$ will be different at each instant.\n", "\n", "<figure><img src=\"../images/3Dbodyref.png\" width=800 />\n", "    "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### The solution is to attach a frame reference to the body\n", "\n", "The solution to this problem is to attach a frame of reference to the body. We will denote this frame of reference as <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$</span>, <span class=\"notranslate\">$\\hat{\\boldsymbol{e_2}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span>, with origin in the center of mass of the body. As can be noted from the figure below, the frame of reference moves along the body. Now the matrix of inertia <span class=\"notranslate\">$I$</span> will be constant relative to this new basis <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$, $\\hat{\\boldsymbol{e_2}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span>.\n", "\n", "<figure><img src=\"../images/3DbodyrefMove.png\" width=800 />"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### The fixed frame is chosen in a way that the matrix of inertia is a diagonal matrix\n", "\n", "As we can choose the basis versors as we want, we can choose them so as to the products of inertia be equal to zero. This can always be done. If the body has axes of symmetry,  we can choose these axes (principal axes) as the direction of the basis to the products of inertia be equal to zero. Having the basis <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$, $\\hat{\\boldsymbol{e_2}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span>, the matrix of inertia will be a diagonal matrix:\n", "\n", "\n", "\\begin{equation}\n", "    I = \\left[\\begin{array}{ccc}I_1&0&0\\\\\n", "          0&I_2&0\\\\\n", "          0&0&I_3\\end{array}\\right]\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## The angular velocity in the fixed frame\n", "\n", "So, we can write the angular momentum vector relative to this new basis. To do this, we must write the angular velocity in this new basis:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\omega} = \\omega_1\\hat{\\boldsymbol{e_1}} + \\omega_2\\hat{\\boldsymbol{e_2}} + \\omega_3\\hat{\\boldsymbol{e_3}}  \n", "\\end{equation}\n", "</span>\n", "\n", "Note that this angular velocity is the same vector that we used previously. We are just describing it in a basis attached to the body (local basis)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### The angular momentum in the fixed frame\n", "\n", "So, using the basis in the direction of the principal axes of the body the angular momentum simplifies to:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", " \\vec{H_{cm}} = I\\vec{\\omega} = I_1\\omega_1 \\hat{\\boldsymbol{e_1}} + I_2\\omega_2 \\hat{\\boldsymbol{e_2}} +I_3\\omega_3 \\hat{\\boldsymbol{e_3}} \n", " \\label{eq:angmomprinc}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Derivative of the angular momentum\n", "\n", "For the second Newton-Euler law, we must compute the derivative of the angular momentum. So, we derive the angular momentum in Eq. \\eqref{eq:angmomprinc}. As the versors <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$, $\\hat{\\boldsymbol{e_2}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span> are varying in time, we must consider their derivatives.\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d\\vec{H_{cm}}}{dt} = I_1\\dot{\\omega_1}\\hat{\\boldsymbol{e_1}} + I_2\\dot{\\omega_2}\\hat{\\boldsymbol{e_2}}+I_3\\dot{\\omega_3}\\hat{\\boldsymbol{e_3}} + I_1\\omega_1\\frac{d\\hat{\\boldsymbol{e_1}}}{dt} + I_2\\omega_2\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}+I_3\\omega_3\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\n", "    \\label{eq:derivangmom}\n", "\\end{equation}\n", "</span>\n", "\n", "Now it only remains to find an expression for the derivative of the versors <span class=\"notranslate\">$\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}$, $\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}$</span> and <span class=\"notranslate\">$\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}$<span>. "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Angular velocity\n", "\n", "\n", "The angular velocity can be written as ([here you can learn more about the computation of the angular velocity](AngularVelocity3D.ipynb)):\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\omega} =  \\left(\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}\\cdot \\hat{\\boldsymbol{e_3}}\\right) \\hat{\\boldsymbol{e_1}} + \\left(\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot \\hat{\\boldsymbol{e_1}}\\right)\n", "    \\hat{\\boldsymbol{e_2}} + \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}\\right) \\hat{\\boldsymbol{e_3}}\n", "    \\label{eq:angvel}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### The derivative of the versors\n", "\n", "Now, we must isolate the derivative of the versors to substitute them in the Eq. \\eqref{eq:derivangmom}. To isolate the derivative of the versor <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$</span>, first we cross multiply both sides of the equation above by <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$</span>:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\omega} \\times \\hat{\\boldsymbol{e_1}} = - \\left(\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot \\hat{\\boldsymbol{e_1}}\\right) \\hat{\\boldsymbol{e_3}} + \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}\\right) \\hat{\\boldsymbol{e_2}}\n", "\\end{equation}\n", "</span>\n", "\n", "If we note that the term multipliying <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span> in the right side of the identity can be obtained by <span class=\"notranslate\">$\\frac{d\\hat{\\boldsymbol{e_1}}\\cdot\\hat{\\boldsymbol{e_3}} }{dt} = \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_3}} + \\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}} \\rightarrow 0 = \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_3}} + \\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}} \\rightarrow \\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}}  = - \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_3}} $</span> (the scalar product <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}\\cdot\\hat{\\boldsymbol{e_3}}$</span> is zero because these vectors are orthogonal), we can write the equation above becomes:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\omega} \\times \\hat{\\boldsymbol{e_1}} = \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_3}}\\right) \\hat{\\boldsymbol{e_3}} + \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}\\right) \\hat{\\boldsymbol{e_2}}\n", "\\end{equation}\n", "</span>\n", "\n", "Finally, we can note that $\\frac{d\\hat{\\boldsymbol{e_1}}\\cdot\\hat{\\boldsymbol{e_1}} }{dt} = \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}} + \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}} \\rightarrow \\frac{d(1)}{dt} = 2\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}} \\rightarrow \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}}  = 0 $. As this term is equal to zero, we can add it to the expression above:\n", "\n", "\\begin{equation}\n", "    \\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_1}} = \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot\\hat{\\boldsymbol{e_1}}\\right)\\hat{\\boldsymbol{e_1}} +  \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_3}}\\right) \\hat{\\boldsymbol{e_3}} + \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}\\right) \\hat{\\boldsymbol{e_2}} \n", "\\end{equation}\n", "\n", "Note that the expression above is just another manner to write the vector $\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}$, as any vector can be described by the sum of the projections on each of the versors forming a basis.\n", "\n", "So, the derivative of the versor $\\hat{\\boldsymbol{e_1}}$  can be written as:\n", "\n", "\\begin{equation}\n", "    \\frac{d\\hat{\\boldsymbol{e_1}}}{dt} = \\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_1}}\n", "\\end{equation}\n", "\n", "Similarly, the derivatives of the versors $\\hat{\\boldsymbol{e_2}}$ and $\\hat{\\boldsymbol{e_3}}$ can be written as:\n", "\n", "\\begin{equation}\n", "    \\frac{d\\hat{\\boldsymbol{e_2}}}{dt} = \\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_2}} ~~~~~~~~\\text{and} ~~~~~~ \\frac{d\\hat{\\boldsymbol{e_3}}}{dt} = \\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_3}}\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### The derivative of the angular momentum\n", "\n", "Now we can get back to the equation describing the derivative of the angular momentum:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "    \\begin{split}\n", "    \\frac{d\\vec{H_{cm}}}{dt} =&I_1\\dot{\\omega_1}\\hat{\\boldsymbol{e_1}} + I_2\\dot{\\omega_2}\\hat{\\boldsymbol{e_2}}+I_3\\dot{\\omega_3}\\hat{\\boldsymbol{e_3}} + I_1\\omega_1\\frac{d\\hat{\\boldsymbol{e_1}}}{dt} + I_2\\omega_2\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}+I_3\\omega_3\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}=\\\\\n", "    =& I_1\\dot{\\omega_1}\\hat{\\boldsymbol{e_1}} + I_2\\dot{\\omega_2}\\hat{\\boldsymbol{e_2}}+I_3\\dot{\\omega_3}\\hat{\\boldsymbol{e_3}} + I_1\\omega_1(\\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_1}}) + I_2\\omega_2(\\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_2}})+I_3\\omega_3(\\vec{\\boldsymbol{\\omega}} \\times \\hat{\\boldsymbol{e_3}}) = \\\\\n", "   =& I_1\\dot{\\omega_1}\\hat{\\boldsymbol{e_1}} + I_2\\dot{\\omega_2}\\hat{\\boldsymbol{e_2}}+I_3\\dot{\\omega_3}\\hat{\\boldsymbol{e_3}} + \\vec{\\boldsymbol{\\omega}} \\times I_1\\omega_1\\hat{\\boldsymbol{e_1}} + \\vec{\\boldsymbol{\\omega}} \\times I_2\\omega_2\\hat{\\boldsymbol{e_2}}+\\vec{\\boldsymbol{\\omega}} \\times I_3\\omega_3\\hat{\\boldsymbol{e_3}} = \\\\\n", "   =& I_1\\dot{\\omega_1}\\hat{\\boldsymbol{e_1}} + I_2\\dot{\\omega_2}\\hat{\\boldsymbol{e_2}}+I_3\\dot{\\omega_3}\\hat{\\boldsymbol{e_3}} + \\vec{\\boldsymbol{\\omega}} \\times (I_1\\omega_1\\hat{\\boldsymbol{e_1}} + I_2\\omega_2\\hat{\\boldsymbol{e_2}} +  I_3\\omega_3\\hat{\\boldsymbol{e_3}})=\\\\\n", "   =&I\\vec{\\dot{\\omega}} + \\vec{\\omega} \\times (I\\vec{\\omega})\n", "   \\end{split}\n", "   \\label{eq:derivangmomVec}\n", "\\end{align}\n", "</span>\n", "\n", "Performing the cross products, we can get the expressions for each of the coordinates attached to the body:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "    \\begin{split}\n", "    \\frac{d\\vec{H_{cm}}}{dt} =\\left[\\begin{array}{c}I_1\\dot{\\omega_1}\\\\I_2\\dot{\\omega_2}\\\\I_3\\dot{\\omega_3}\\end{array}\\right] + \\left[\\begin{array}{c}\\omega_1\\\\\\omega_2\\\\\\omega_3\\end{array}\\right]  \\times \\left[\\begin{array}{c}I_1\\omega_1\\\\I_2\\omega_2\\\\I_3\\omega_3\\end{array}\\right]  = \\left[\\begin{array}{c}I_1\\dot{\\omega_1} + \\omega_2\\omega_3(I_3-I_2)\\\\I_2\\dot{\\omega_2}+\\omega_1\\omega_3(I_1-I_3)\\\\I_3\\dot{\\omega_3}+\\omega_1\\omega_2(I_2-I_1)\\end{array}\\right] \n", "   \\end{split}\n", "\\end{align}\n", "</span>\n", "\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Newton-<PERSON><PERSON><PERSON> laws\n", "\n", "Having computed the derivative of the angular momentum, we have the final forms of the Newton-Euler laws:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    F_x = ma_{cm_x}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    F_y = ma_{cm_y}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    F_z = ma_{cm_z}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    M_{cm_1} = I_1\\dot{\\omega_1} + \\omega_2\\omega_3(I_3-I_2)\n", "    \\label{eq:M1}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    M_{cm_2} = I_2\\dot{\\omega_2}+\\omega_1\\omega_3(I_1-I_3)\n", "    \\label{eq:M2}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    M_{cm_3} = I_3\\dot{\\omega_3}+\\omega_1\\omega_2(I_2-I_1)\n", "    \\label{eq:M3}\n", "\\end{equation}\n", "</span>\n", "\n", "Or, in the vectorial form:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{F} = m\\vec{a_{cm}}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    \\vec{M_{cm}} = I\\dot{\\vec{\\omega}}+\\vec{\\omega}\\times(I\\vec{\\omega}) \\text{            (vectors in local frame)}\n", "\\end{equation}\n", "</span>\n", "\n", "\n", "Note that the equations of the forces are written in the global frame of reference and the equations of the moments are described in the frame of reference of the body. So, before using Eq.~\\eqref{eq:derivangmomVec} or the equations Eq.\\eqref{eq:M1},\\eqref{eq:M2} and \\eqref{eq:M3} you must transform all the forces and moment-arms to the frame of reference of the body by using rotation matrices.\n", "\n", "Below are shown some examples with three-dimensional kinematic data to find the forces and moments acting on the body."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Examples"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["###  3D pendulum bar\n", "\n", " At the file '../data/3Dpendulum.txt' there are 3 seconds of data of 3 points of the three-dimensional cylindrical pendulum, very similar to the pendulum shown in the [notebook about free-body diagrams](FreeBodyDiagramForRigidBodies.ipynb#pendulum), except that it can move in every direction. Also it has a motor at the upper part of the cylindrical bar producing torques to move the bar. It has mass <span class=\"notranslate\">$m=1$ kg</span>, length <span class=\"notranslate\">$l=1$ m</span> and radius <span class=\"notranslate\">$r=0.1$ m</span>. \n", " \n", " The point m1 is at the upper part of the cylinder and is the origin of the system. \n", " \n", " The point m2 is at the center of mass of the cylinder. \n", " \n", " The point m3 is a point at the surface of the cylinder. \n", " \n", "The free-body diagram of the 3d pendulum is depicted below. There is the gravitational force acting at the center of mass of gravity of the body and the torque <span class=\"notranslate\">$\\vec{M_1}$</span> due to the motor acting on the pendulum and the force <span class=\"notranslate\">$\\vec{F_1}$</span> due to the restraint at the upper part of the cylinder. Together with the forces, the local basis <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$, $\\hat{\\boldsymbol{e_2}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span> in the direction of the principal axes an origin at the center of mass of the body is also shown.\n", "\n", "<figure><img src=\"../images/3DpendulumFBD.png\" width=400 />"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The resultant forces acting on the cylinder is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{F} = \\vec{F_O} - mg\\hat{\\boldsymbol{k}}\n", "\\end{equation}\n", "</span>\n", "\n", "So, the first Newton-<PERSON>uler law, at each component of the global  basis, is written as:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "    \\begin{split}\n", "        F_{O_x} &=  ma_{cm_x} &\\rightarrow  F_{O_x} &=  ma_{cm_x} \\\\\n", "        F_{O_y} &= ma_{cm_y} &\\rightarrow  F_{O_y} &=  ma_{cm_y}\\\\\n", "        F_{O_z} - mg &= ma_{cm_z} &\\rightarrow  F_{O_z} &=  ma_{cm_z} + mg \n", "    \\end{split}\n", "    \\label{eq:fnependulum}\n", "\\end{align}\n", "</span >"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Now, the resultant moment applied to the body, computed relative to the center of mass, is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{M} = \\vec{M_O} + \\vec{r_{O/cm}} \\times \\vec{F_O}\n", "\\end{equation}\n", "</span>\n", "\n", "So, the second Newton-<PERSON> law, at each of the components at the local basis of the body, is written as:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "    \\begin{split}\n", "        M_{O_1} + MFocm_1 &= I_1\\dot{\\omega_1} + \\omega_2\\omega_3(I_3-I_2) \\rightarrow M_{O_1} &= I_1\\dot{\\omega_1} + \\omega_2\\omega_3(I_3-I_2) -  MFocm_1\\\\\n", "        M_{O_2} + MFocm_2 &= I_2\\dot{\\omega_2} + \\omega_1\\omega_3(I_1-I_3) \\rightarrow M_{O_2} &= I_2\\dot{\\omega_2} + \\omega_1\\omega_3(I_1-I_3) -  MFocm_2\\\\\n", "        M_{O_3} + MFocm_3 &= I_3\\dot{\\omega_3} + \\omega_1\\omega_2(I_2-I_1) \\rightarrow M_{O_3} &= I_3\\dot{\\omega_3} + \\omega_1\\omega_2(I_2-I_1) -  MFocm_3        \n", "    \\end{split}\n", "\\end{align}\n", "<span class=\"notranslate\">\n", "\n", "where <span class=\"notranslate\">$\\vec{MFocm} = \\vec{r_{O/cm}} \\times \\vec{F_O}$</span>.\n", "\n", "The moments of inertia at the directions of <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$, $\\hat{\\boldsymbol{e_2}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span> are, <span class=\"notranslate\">$I_1 = \\frac{mR^2}{12}$</span> and <span class=\"notranslate\">$I_2=I_3=\\frac{m(3R^2+l^2)}{12}$</span>. Now, to compute the moment <span class=\"notranslate\">$\\vec{M_O}$</span> and the force  <span class=\"notranslate\">$\\vec{F_O}$</span>, we will need to find the acceleration of the center of mass <span class=\"notranslate\">$\\vec{a_{cm}}$</span>, the angular velocity  <span class=\"notranslate\">$\\vec{\\omega}$</span>, the time-derivatives of each component of the angular velocity, and the moment-arm <span class=\"notranslate\">$\\vec{r_{O/cm}}$</span> to compute the torque due to the force <span class=\"notranslate\">$\\vec{F_O}$</span>. These signals will come from the kinematic data file.\n", "\n", "First,  we need to open the file with the data:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:20:23.504861Z", "start_time": "2020-05-15T23:20:23.480269Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t(s),m1_X (m),m1_Y (m),m1_Z(m),m2_X (m),m2_Y (m),m2_Z(m),m3_X (m),m3_Y (m),m3_Z(m)\n", "\n", "[[ 0.    0.    0.   ...  0.22  0.12 -0.44]\n", " [ 0.01  0.    0.   ...  0.22  0.12 -0.44]\n", " [ 0.02  0.    0.   ...  0.22  0.12 -0.44]\n", " ...\n", " [ 2.97  0.    0.   ...  0.2   0.12 -0.45]\n", " [ 2.98  0.    0.   ...  0.2   0.12 -0.45]\n", " [ 2.99  0.    0.   ...  0.2   0.12 -0.45]]\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "np.set_printoptions(precision=2)\n", "f = open('../data/3dPendulum.txt')\n", "data = np.loadtxt('../data/3dPendulum.txt', \n", "                  skiprows=1, delimiter = ',')\n", "header = f.readline()\n", "print(header)\n", "print(data)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:20:42.923156Z", "start_time": "2020-05-15T23:20:42.918456Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["m = 1 #[kg]\n", "g = 9.81 #[m/s²]\n", "l = 1 #[m]\n", "r = 0.1 #[m]\n", "\n", "I1 = m*r**2/12 #[kgm²]\n", "I2 = m*(3*r**2+l**2)/12 #[kgm²]\n", "I3 = I2 #[kgm²]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we assign the proper columns to variables:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:19:39.711797Z", "start_time": "2020-05-15T23:19:39.704325Z"}}, "outputs": [], "source": ["t = data[:,0]\n", "m1 = data[:,1:4]\n", "m2 = data[:,4:7]\n", "m3 = data[:,7:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As the center of mass is the data contained in m2, we can find the acceleration of the center of mass $\\vec{\\boldsymbol{a_{cm}}}$. This will be performed by deriving numerically the position of the center of mass twice. The numerical derivative of a function $f(t)$ with the samples $f(i)$ can be performed by taking the forward difference of the values $f(i)$:\n", "\n", "\\begin{equation}\n", "    \\frac{df}{dt}(i) = \\frac{f(i+1)-f(i)}{\\Delta t}\n", "\\end{equation}\n", "\n", "The numerical derivative could be obtained by taking the backward differences as well:\n", "\n", "\\begin{equation}\n", "    \\frac{df}{dt}(i) = \\frac{f(i)-f(i-1)}{\\Delta t}\n", "\\end{equation}\n", "\n", "A better estimation of the derivative of the time derivative of the function $f(t)$ would be obtained by the average value between the estimations using the backward and forward differences (this subject is treated [in this notebook about data filtering](DataFiltering.ipynb#numdiff)):\n", "\n", "\\begin{equation}\n", "    \\frac{df}{dt}(i) = \\frac{\\frac{f(i+1)-f(i)}{\\Delta t} + \\frac{f(i)-f(i-1)}{\\Delta t}}{2} = \\frac{f(i+1)-f(i-1)}{2\\Delta t} \n", "    \\label{eq:centralderiv}\n", "\\end{equation}\n", "\n", "\n", "So, the acceleration of the center of mass, is:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:23:24.452215Z", "start_time": "2020-05-15T23:23:24.435365Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(300, 3)\n", "(296, 3)\n"]}], "source": ["dt = t[1]-t[0]\n", "\n", "rcm = m2\n", "vcm = (rcm[2:,:]-rcm[0:-2,:])/(2*dt)\n", "acm = (vcm[2:,:]-vcm[0:-2,:])/(2*dt)\n", "print(rcm.shape)\n", "print(acm.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can find the force <span class=\"notranslate\">$\\vec{\\boldsymbol{F_O}}$</span>  using the Eq. \\eqref{eq:fnependulum}."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:26:00.505660Z", "start_time": "2020-05-15T23:26:00.428851Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["Fox = m*acm[:,0]\n", "Foy = m*acm[:,1]\n", "Foz = m*acm[:,2] + m*g\n", "Fo=np.hstack((Fox.reshape(-1,1),Foy.reshape(-1,1),Foz.reshape(-1,1)))\n", "\n", "plt.figure()\n", "plt.plot(t[0:acm.shape[0]], <PERSON>)\n", "plt.plot(t[0:acm.shape[0]], Foy, '--')\n", "plt.plot(t[0:acm.shape[0]], Foz)\n", "plt.legend(('x','y','z'))\n", "plt.title('Force (N)')\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, to find the moment being applied to the body, we need to compute a basis attached to the body"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:27:54.741369Z", "start_time": "2020-05-15T23:27:54.730654Z"}}, "outputs": [], "source": ["e1 = m2 - m1\n", "e1 = e1/np.linalg.norm(e1,axis=1,keepdims=True)\n", "\n", "e2_temp = m3-m2\n", "e2_temp = e2_temp/np.linalg.norm(e2_temp,axis=1,keepdims=True)\n", "\n", "e3 = np.cross(e1,e2_temp,axis=1)\n", "e3 = e3/np.linalg.norm(e3,axis=1,keepdims=True)\n", "\n", "e2 = np.cross(e3,e1, axis=1)\n", "e2 = e2/np.linalg.norm(e2,axis=1,keepdims=True)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": [" To compute the moment applied to the body, we need the angular velocity described in the basis attached to the body. The easiest way to find this angular velocity is to use Eq. \\eqref{eq:angvel}, repeated here. \n", "\n", "<span class=\"notranslate\">\n", " \\begin{equation}\n", "    \\vec{\\omega} =  \\left(\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}\\cdot \\hat{\\boldsymbol{e_3}}\\right) \\hat{\\boldsymbol{e_1}} + \\left(\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot \\hat{\\boldsymbol{e_1}}\\right) \\hat{\\boldsymbol{e_2}} + \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}\\right) \\hat{\\boldsymbol{e_3}}\n", "\\end{equation}\n", "</span>\n", "\n", " \n", " To do this we need the derivatives of the basis versors. This will also be performed with Eq. \\eqref{eq:centralderiv}.\n", " \n", " To perform the computation of the angular velocity remember that the scalar product between two vectors is given by:\n", " \n", " <span class=\"notranslate\">\n", " \\begin{equation}\n", "         \\vec{v}\\cdot\\vec{w} = \\left[\\begin{array}{c}v_x\\\\v_y\\\\v_z \\end{array}\\right]\\cdot \\left[\\begin{array}{c}w_x\\\\w_y\\\\w_z \\end{array}\\right] =  v_x.w_x+v_yw_y+v_zw_z\n", " \\end{equation}\n", "</span>"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:29:02.914091Z", "start_time": "2020-05-15T23:29:02.893735Z"}}, "outputs": [], "source": ["de1dt = (e1[2:,:]-e1[0:-2,:])/(2*dt)\n", "de2dt = (e2[2:,:]-e2[0:-2,:])/(2*dt)\n", "de3dt = (e3[2:,:]-e3[0:-2,:])/(2*dt)\n", "\n", "omega = np.hstack((np.sum(de2dt*e3[1:-1,:], axis=1, keepdims=True),\n", "                   np.sum(de3dt*e1[1:-1,:], axis=1, keepdims=True),\n", "                   np.sum(de1dt*e2[1:-1,:], axis=1, keepdims=True)))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the angular velocity vector we can obtain the derivatives of each component of it, also needed to compute the moment applied to the body. To do this we will use Eq. \\eqref{eq:centralderiv}:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:29:03.792527Z", "start_time": "2020-05-15T23:29:03.788591Z"}}, "outputs": [], "source": ["alpha = (omega[2:,:]-omega[0:-2,:])/(2*dt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It remains to find the moment caused by the force <span class=\"notranslate\">$\\vec{F_O}$, $\\vec{MFocm} = \\vec{r_{O/cm}} \\times \\vec{F_O}$</span>. The moment-arm <span class=\"notranslate\">$\\vec{r_{O/cm}} =-\\vec{r_{cm}}$."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:32:34.386404Z", "start_time": "2020-05-15T23:32:34.382424Z"}}, "outputs": [], "source": ["MFocm = np.cross(-rcm[2:-2], Fo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The problem is that this moment is in the global basis. We need to transform it to the local basis. This will be performed using the rotation matrix of the bar. Each row of this matrix is one of the basis versors. Note that at each instant the matrix of rotation $R$ will be different.  After the matrix is formed, we can find the components of the moment <span class=\"notranslate\">$\\vec{MFocm}$</span> in the local basis by multiplying the matrix of rotation <span class=\"notranslate\">$R$</span> by the vector <span class=\"notranslate\">$\\vec{MFocm}$</span>."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:34:32.207790Z", "start_time": "2020-05-15T23:34:32.157488Z"}}, "outputs": [], "source": ["MFocmLocal = np.zeros_like(MFocm)\n", "for i in range(MFocm.shape[0]):\n", "    R = np.vstack((e1[i,:],e2[i,:],e3[i,:]))\n", "    MFocmLocal[i,:] = R@MFocm[i,:]\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:36:47.519405Z", "start_time": "2020-05-15T23:36:47.436346Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["Mo1 = I1*alpha[:,0] + omega[0:alpha.shape[0],1]*omega[0:alpha.shape[0],2]*(I3-I2) - MFocmLocal[:,0]\n", "Mo2 = I2*alpha[:,1] + omega[0:alpha.shape[0],0]*omega[0:alpha.shape[0],2]*(I1-I3) - MFocmLocal[:,1]\n", "Mo3 = I3*alpha[:,2] + omega[0:alpha.shape[0],0]*omega[0:alpha.shape[0],1]*(I2-I1) - MFocmLocal[:,2]\n", "plt.figure()\n", "plt.plot(t[2:-2], Mo1)\n", "plt.plot(t[2:-2], Mo2)\n", "plt.plot(t[2:-2], Mo3)\n", "plt.legend(('$e_1$','$e_2$','$e_3$'))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We could also have used the vectorial form of the derivative of the angular momentum (Eq. \\eqref{eq:derivangmomVec}) and instead of writing three lines of code, write only one. The result is the same."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:35:46.183587Z", "start_time": "2020-05-15T23:35:46.066950Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["I = np.array([[I1,0,0],[0,I2,0],[0,0,I3]])\n", "Mo = (I@alpha.T).T  + np.cross(omega[0:alpha.shape[0],:], (I@omega[0:alpha.shape[0],:].T).T,axis=1) - MFocmLocal\n", "plt.figure()\n", "plt.plot(t[2:-2], <PERSON>)\n", "plt.legend(('$e_1$','$e_2$','$e_3$'))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data from postural control\n", "\n", "This example will use real data from a subject during quiet standing during 60 seconds. This data is from the database freely available at [https://github.com/demotu/datasets/tree/master/PDS](https://github.com/demotu/datasets/tree/master/PDS). The data of this subject is in the file '../data/postureData.txt'.\n", "\n", "The mass of the subject was $m = 53$ kg and her height was $h= 1.65 $ m. \n", "\n", "\n", "   The free-body diagram is very similar to the free-body diagram shown [in the notebook about free-body diagram](FreeBodyDiagramForRigidBodies.ipynb#quietstanding), except that the force <span class=\"notranslate\">$\\vec{F_A}$</span> and the moment <span class=\"notranslate\">$\\vec{M_A}$</span> have components at all 3 directions. \n", "   \n", "   So, the first Newton-<PERSON>uler law, at each component of the global  basis, is written as (note that in these data, the vertical direction is the y coordinate):\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "    \\begin{split}\n", "        F_{A_x} &=  ma_{cm_x} &\\rightarrow  F_{A_x} &=  ma_{cm_x} \\\\\n", "        F_{A_y}  - mg &= ma_{cm_y} &\\rightarrow  F_{A_y} &=  ma_{cm_y} + mg\\\\\n", "        F_{A_z} &= ma_{cm_z} &\\rightarrow  F_{A_z} &=  ma_{cm_z} \n", "    \\end{split}\n", "    \\label{eq:fnequiet}\n", "\\end{align}\n", "</span>\n", "\n", "Now, the resultant moment applied to the body, computed relative to the center of mass, is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{M} = \\vec{M_A} + \\vec{r_{A/cm}} \\times \\vec{F_A}\n", "\\end{equation}\n", "</span>\n", "\n", "So, the second Newton-<PERSON> law, at each of the components at the local basis of the body, is written as:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{align}\n", "    \\begin{split}\n", "        \\vec{M_A} + \\vec{MFacm} &= I\\left[\\begin{array}{c}\\dot{\\omega_1}\\\\\\dot{\\omega_2}\\\\\\dot{\\omega_3}\\end{array}\\right] + \\vec{\\omega}\\times  I\\vec{\\omega} \\rightarrow \\vec{M_A} = I\\left[\\begin{array}{c}\\dot{\\omega_1}\\\\\\dot{\\omega_2}\\\\\\dot{\\omega_3}\\end{array}\\right] + \\vec{\\omega} \\times  I\\vec{\\omega}- \\vec{MFacm}\n", "    \\end{split}\n", "\\end{align}\n", "</span>\n", "where  <span class=\"notranslate\">$\\vec{MFAcm} = \\vec{r_{A/cm}} \\times \\vec{F_A}$</span>.\n", "\n", "Now we open the data and assign the coordinates of each marker to a variable. "]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:49:55.405660Z", "start_time": "2020-05-15T23:49:55.231233Z"}}, "outputs": [], "source": ["data = np.loadtxt('../data/postureData.txt', skiprows=1, delimiter = ',')\n", "\n", "t = data[:,0]\n", "dt = t[2]-t[1]\n", "rcm = data[:,1:4] #center of mass\n", "rrA = data[:,4:7] # Right lateral malleolus\n", "rlA = data[:,7:] # Left lateral maleolus\n"]}, {"cell_type": "markdown", "metadata": {}, "source": [" The body will be approximated by a cylinder with the height of the subject and radius equal to half of the mean distances between the right and left medial malleoli."]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:50:55.393213Z", "start_time": "2020-05-15T23:50:55.380035Z"}}, "outputs": [], "source": ["m = 53\n", "h = 1.65\n", "r = np.mean(np.linalg.norm(rrA-rlA, axis = 1))/2\n", "I1 = m*r**2/12 # longitudnal\n", "I2 = m*(3*r**2+h**2)/12 # sagittal\n", "I3 = I2 # transversal\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:51:25.987480Z", "start_time": "2020-05-15T23:51:25.945077Z"}}, "outputs": [], "source": ["# acceleration of the center of mass by deriving the center of mass twice\n", "vcm = (rcm[2:,:]-rcm[0:-2,:])/(2*dt)\n", "acm = (vcm[2:,:]-vcm[0:-2,:])/(2*dt)\n", "\n", "FAx = m*acm[:,0]\n", "FAy = m*acm[:,1] + m*g\n", "FAz = m*acm[:,2] \n", "FA=np.hstack((FAx.reshape(-1,1),FAy.reshape(-1,1),FAz.reshape(-1,1)))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we form the basis attached to the body. The first versor <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$</span> will be a versor from the midpoint between the medial malleoli and the center of mass of the body. The second versor $\\hat{\\boldsymbol{e_2}}$ will be a versor from  the right to the left malleolus. The third versor <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span> will be a cross product between <span class=\"notranslate\">$\\hat{\\boldsymbol{e_1}}$</span> and <span class=\"notranslate\">$\\hat{\\boldsymbol{e_2}}$</span>."]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:52:51.984219Z", "start_time": "2020-05-15T23:52:51.940352Z"}}, "outputs": [], "source": ["e1 = rcm - (rlA+rrA)/2\n", "e1 = e1/np.linalg.norm(e1,axis=1,keepdims=True)\n", "\n", "e2 = rlA-rrA\n", "e2 = e2/np.linalg.norm(e2,axis=1,keepdims=True)\n", "\n", "e3 = np.cross(e1,e2,axis=1)\n", "e3 = e3/np.linalg.norm(e3,axis=1,keepdims=True)\n", "\n", "e2 = np.cross(e3,e1, axis=1)\n", "e2 = e2/np.linalg.norm(e2,axis=1,keepdims=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can find the angular velocity <span class=\"notranslate\">$\\vec{\\omega}$</span> at the basis attached to the body using Eq.\\eqref{eq:angvel} and the time derivatives of its components."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:53:10.597582Z", "start_time": "2020-05-15T23:53:10.576519Z"}}, "outputs": [], "source": ["de1dt = (e1[2:,:]-e1[0:-2,:])/(2*dt)\n", "de2dt = (e2[2:,:]-e2[0:-2,:])/(2*dt)\n", "de3dt = (e3[2:,:]-e3[0:-2,:])/(2*dt)\n", "\n", "omega = np.hstack((np.sum(de2dt*e3[1:-1,:], axis = 1).reshape(-1,1),\n", "                   np.sum(de3dt*e1[1:-1,:], axis = 1).reshape(-1,1),\n", "                   np.sum(de1dt*e2[1:-1,:], axis = 1).reshape(-1,1)))\n", "\n", "alpha = (omega[2:,:]-omega[0:-2,:])/(2*dt)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we need to find the moment caused by the force at the ankles <span class=\"notranslate\">$\\vec{F_A}$, $\\vec{MFAcm} = \\vec{r_{A/cm}} \\times \\vec{F_A}$</span>. The moment-arm <span class=\"notranslate\">$\\vec{r_{A/cm}}$</span> is the vector from the center of mass to the midpoint of the lateral malleoli. \n", "\n", "Besides the description of the moment due to the force <span class=\"notranslate\">$\\vec{F_A}$</span> in the basis attached to the body, we will also describe the force <span class=\"notranslate\">$\\vec{F_A}$</span> at the local basis. This is useful because it has an anatomical meaning. After this we can use the equations of Newton-E<PERSON>r to obtain the moment at the ankle. \n", "\n", "After having all signals described in the basis of the body, the moment being applied by the muscles of the ankle is computed using Eq. \\eqref{eq:derivangmomVec}."]}, {"cell_type": "code", "execution_count": 25, "metadata": {"ExecuteTime": {"end_time": "2020-05-15T23:56:01.294824Z", "start_time": "2020-05-15T23:56:01.037257Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["\n", "#computing the moment due to the ankle force\n", "racm = (rlA+rrA)/2-rcm\n", "MFAcm = np.cross(racm[0:FA.shape[0],:], FA)\n", "\n", "\n", "MFAcmLocal = np.zeros_like(MFAcm)\n", "FALocal = np.zeros_like(MFAcm)\n", "\n", "\n", "# rotation matrix and description of the ankle force and moment due to the ankle force \n", "for i in range(MFAcm.shape[0]):\n", "    R = np.vstack((e1[i,:],e2[i,:],e3[i,:]))\n", "    MFAcmLocal[i,:]=R@MFAcm[i,:]\n", "    FALocal[i,:]=R@FA[i,:]\n", "    \n", "# Second Newton-<PERSON><PERSON><PERSON> law to obtain the ankle moment    \n", "    \n", "I = np.diag([I1,I2,I3])\n", "MA = (I@alpha.T).T  + np.cross(omega[0:alpha.shape[0],:], \n", "                               (I@omega[0:alpha.shape[0],:].T).T,axis=1) - MFAcmLocal\n", "\n", "\n", "plt.figure()\n", "plt.plot(t[2:-2], MA)\n", "plt.legend(('longitudinal','sagittal','mediolateral'))\n", "plt.title('<PERSON><PERSON>')\n", "plt.xlabel('t (s)')\n", "plt.ylabel('M (N.m)')\n", "\n", "plt.show()\n", "\n", "plt.figure()\n", "plt.plot(t[2:-2], FALocal[:,0])\n", "plt.plot(t[2:-2], FALocal[:,1])\n", "plt.plot(t[2:-2], FALocal[:,2])\n", "plt.title('Ankle Force')\n", "plt.legend(('longitudinal','sagittal','mediolateral'))\n", "plt.xlabel('t (s)')\n", "plt.ylabel('F (N)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Problems\n", "\n", "1) Compute the derivative of the angular momentum of the foot and the leg using one of  the following data acquired during the gait of a subject: ['../data/BiomecII2018_gait_d.txt'](../data/BiomecII2018_gait_d.txt) or ['../data/BiomecII2018_gait_n.txt'](../data/BiomecII2018_gait_n.txt).\n", "\n", "2) Problem 20.2.7 from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> book."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, P. <PERSON>(2010) Vector Mechanics for Enginners: Dynamics. \n", "- <PERSON>, <PERSON><PERSON> (1985) [Dynamics: Theory and Applications](https://ecommons.cornell.edu/handle/1813/638). McGraw-Hill, Inc\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> (2005) Engineering Mechanics: Dynamics. \n", "- <PERSON>, <PERSON>, <PERSON> (2005) Classical Mechanics\n", "- Winter D. A., (2009) Biomechanics and motor control of human movement. John Wiley and Sons.\n", "- Santos D<PERSON>, <PERSON><PERSON>chi <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2017) A data set with kinematic and ground reaction forces of human balance. PeerJ Preprints.\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press. "]}], "metadata": {"celltoolbar": "<PERSON>", "cite2c": {"citations": {"5991067/3ZN8DZGT": {"author": [{"family": "<PERSON>", "given": "<PERSON>"}, {"family": "<PERSON><PERSON>", "given": "<PERSON>"}], "container-title": "McGraw-Hill series in mechanical engineering", "id": "5991067/3ZN8DZGT", "issued": {"year": 1985}, "page": "xv, 379 p", "page-first": "xv", "title": "Dynamics, theory and applications", "type": "article-journal"}, "5991067/TJ33RISS": {"author": [{"family": "<PERSON><PERSON><PERSON>", "given": "<PERSON>"}, {"family": "<PERSON><PERSON><PERSON>", "given": "<PERSON><PERSON><PERSON>"}], "id": "5991067/TJ33RISS", "issued": {"year": 2015}, "publisher": "Oxford University Press", "title": "Introduction to statics and dynamics", "type": "book"}}}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": false, "skip_h1_title": true, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "457px", "left": "367px", "top": "230px", "width": "165px"}, "toc_section_display": false, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}