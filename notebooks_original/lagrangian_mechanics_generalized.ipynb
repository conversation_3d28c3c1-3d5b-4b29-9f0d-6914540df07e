{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Lagrangian mechanics in generalized coordinates\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Lagrangian mechanics can be formulated completely independent of the Newtonian mechanics and Cartesian coordinates; <PERSON><PERSON><PERSON> developed this new formalism based on the [principle of least action](http://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/principle_of_least_action.ipynb).  \n", "In this notebook, we will take a less noble path, we will deduce the <PERSON><PERSON><PERSON>'s equation from Newtonian mechanics."]}, {"cell_type": "markdown", "metadata": {"toc": 1}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Review-on-Newton's-laws-of-motion\" data-toc-modified-id=\"Review-on-Newton's-laws-of-motion-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Review on <PERSON>'s laws of motion</a></span><ul class=\"toc-item\"><li><span><a href=\"#Mechanical-energy\" data-toc-modified-id=\"Mechanical-energy-1.1\"><span class=\"toc-item-num\">1.1&nbsp;&nbsp;</span>Mechanical energy</a></span></li></ul></li><li><span><a href=\"#Lagrange's-equation-in-Cartesian-Coordinates\" data-toc-modified-id=\"Lagrange's-equation-in-Cartesian-Coordinates-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span><PERSON><PERSON><PERSON>'s equation in Cartesian Coordinates</a></span></li><li><span><a href=\"#Generalized-coordinates\" data-toc-modified-id=\"Generalized-coordinates-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Generalized coordinates</a></span></li><li><span><a href=\"#Lagrange's-equation\" data-toc-modified-id=\"Lagrange's-equation-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Lagrange's equation</a></span><ul class=\"toc-item\"><li><span><a href=\"#Constraints\" data-toc-modified-id=\"Constraints-4.1\"><span class=\"toc-item-num\">4.1&nbsp;&nbsp;</span>Constraints</a></span></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-internet\" data-toc-modified-id=\"Video-lectures-on-the-internet-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Video lectures on the internet</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Review on Newton's laws of motion\n", "\n", "The [Newton's laws of motion](https://en.wikipedia.org/wiki/<PERSON>'s_laws_of_motion) laid the foundation for classical mechanics. They describe the relationship between the motion of a body and the possible forces acting upon it.\n", "\n", "Consider the motion of a particle in three-dimensional space, its position in time can be represented by the following vector:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{r}(t) = x(t)\\hat{i} + y(t)\\hat{j} + z(t)\\hat{k}\n", "\\label{}\n", "\\end{equation}\n", "</span>\n", "    \n", "And given its position, the particle's velocity and acceleration are:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation} \\begin{array}{l}\n", "\\vec{v}(t) = \\dfrac{\\mathrm d \\vec{r}(t)}{\\mathrm d t} = \\dfrac{d x(t)}{\\mathrm d t}\\hat{i} + \\dfrac{d y(t)}{\\mathrm d t}\\hat{j} + \\dfrac{d z(t)}{\\mathrm d t}\\hat{k} \\\\\n", "\\vec{a}(t) = \\dfrac{\\mathrm d \\vec{v}(t)}{\\mathrm d t} = \\dfrac{\\mathrm d^2 \\vec{r}(t)}{\\mathrm d t^2} = \\dfrac{d^2 x(t)}{\\mathrm d t^2}\\hat{i} + \\dfrac{d^2 y(t)}{\\mathrm d t^2}\\hat{j} + \\dfrac{d^2 z(t)}{\\mathrm d t^2}\\hat{k} \n", "\\label{}\n", "\\end{array} \\end{equation}\n", "</span>\n", "\n", "The particle's linear momentum is defined as:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{p}(t) = m\\vec{v}(t)\n", "\\label{}\n", "\\end{equation}\n", "</span>\n", "\n", "where $m$ and $\\vec{v}$ are the mass and velocity of the body.\n", "\n", "<PERSON>'s second law relates the resultant force applied on the particle to the rate of variation of its linear momentum, and if the mass is constant:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation} \\begin{array}{l}\n", "\\vec{F}(t) = \\dfrac{\\mathrm d \\vec{p}(t)}{\\mathrm d t} = \\dfrac{\\mathrm d (m\\vec{v}(t))}{\\mathrm d t} \\\\\n", "\\vec{F}(t) = m\\vec{a}(t)    \n", "\\label{}\n", "\\end{array} \\end{equation}\n", "</span>\n", "\n", "From <PERSON>'s second law, if the position of the particle at any time is known, one can determine the resultant force acting on it. If the position is not known, but the resultant force is, the position of the particle can determined solving the following second order ordinary differential equation:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{\\mathrm d^2 \\vec{r}(t)}{\\mathrm d t^2} = \\frac{\\vec{F}(t)}{m}\n", "\\label{}\n", "\\end{equation}\n", "</span>\n", "    \n", "The differential equation above is referred as the equation of motion (EOM) of the particle. For example, a system of $N$ particles will require $3N$ EOMs to describe their motion.  \n", "The EOM has the general solution:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{r}(t) = \\int\\!\\bigg(\\int\\frac{\\vec{F}(t)}{m} \\, \\mathrm{d}t\\bigg) \\, \\mathrm{d}t\n", "\\label{}\n", "\\end{equation}\n", "</span>\n", "    \n", "which requires the determination of two constants, the initial position and velocity."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mechanical energy\n", "\n", "A related physical quantity is the mechanical energy, which is the sum of kinetic and potential energies.  \n", "The kinetic energy, $T$ of a particle is given by:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "T = \\frac{1}{2}m v^2\n", "\\label{}\n", "\\end{equation}\n", "</span>\n", "    \n", "Which can be expressed in terms of its linear momentum as:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "T = \\frac{1}{2m}  p^2\n", "\\label{}\n", "\\end{equation}\n", "</span>\n", "    \n", "And for a given coordinate of the particle's motion, its linear momentum can be obtained from its kinetic energy by:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{p} = \\frac{\\partial T}{\\partial \\vec{v}}\n", "\\label{eq11}\n", "\\end{equation}\n", "</span>\n", "    \n", "The potential energy $V$ is the stored energy of a particle and its formulation is dependent on the force acting on the particle. For a conservative force dependent solely on the particle position, such as due to the gravitational field near the Earth surface or due to a linear spring, the force can be expressed in terms of the gradient of the potential energy:\n", "<p>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{F} = -\\nabla V(\\vec{r}) = -\\frac{\\partial V}{\\partial x}\\hat{i} - \\frac{\\partial V}{\\partial y}\\hat{j} - \\frac{\\partial V}{\\partial z}\\hat{k}\n", "\\label{eq12}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>'s equation in Cartesian Coordinates\n", "\n", "For simplicity, let's first deduce the <PERSON><PERSON><PERSON>'s equation for a particle in Cartesian Coordinates and from <PERSON>'s second law.\n", "\n", "Because we want to deduce the laws of motion based on the mechanical energy of the particle, one can see that the time derivative of the expression for the linear momentum as a function of the kinetic energy, cf. Eq. (\\ref{eq11}), is equal to the force acting on the particle and we can substitute the force in <PERSON>'s second law by this term:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\bigg(\\frac{\\partial T}{\\partial \\dot x}\\bigg) = m\\ddot x \n", "\\label{eq13}\n", "\\end{equation}\n", "\n", "We saw that a conservative force can also be expressed in terms of the potential energy of the particle, cf. Eq. (\\ref{eq12}); substituting the right side of the equation above by this expression, we have:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\bigg(\\frac{\\partial T}{\\partial \\dot x}\\bigg) = -\\frac{\\partial V}{\\partial x} \n", "\\label{eq14}\n", "\\end{equation}\n", "\n", "Using the fact that:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial T}{\\partial x} = 0 \\quad and \\quad \\frac{\\partial V}{\\partial \\dot x} = 0 \n", "\\label{eq15}\n", "\\end{equation}\n", "\n", "We can write:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\bigg(\\frac{\\partial (T-V)}{\\partial \\dot x}\\bigg) - \\frac{\\partial (T-V)}{\\partial x} = 0 \n", "\\label{eq16}\n", "\\end{equation}\n", "\n", "Defining the Lagrange or Lagrangian function, $\\mathcal{L}$, as the difference between the kinetic and potential energy in the system:\n", "\n", "\\begin{equation} \n", "\\mathcal{L} = T - V \n", "\\label{eq17}\n", "\\end{equation}\n", "\n", "We have the <PERSON><PERSON><PERSON>'s equation in Cartesian Coordinates for a conservative force acting on a particle:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\bigg(\\frac{\\partial \\mathcal{L}}{\\partial \\dot x}\\bigg) - \\frac{\\partial \\mathcal{L}}{\\partial x} = 0 \n", "\\label{eq18}\n", "\\end{equation}\n", "\n", "Once all derivatives of the <PERSON><PERSON><PERSON>ian function are calculated, this equation will be the equation of motion for the particle. If there are $N$ independent particles in a three-dimensional space, there will be $3N$ equations of motion for the system.  \n", "The set of equations above for a system are known as <PERSON><PERSON><PERSON><PERSON>'s equations, or <PERSON><PERSON><PERSON>'s equations of the second kind."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generalized coordinates\n", "\n", "The direct application of <PERSON>'s laws to mechanical systems results in a set of equations of motion in terms of Cartesian coordinates of each of the particles that make up the system. In many cases, this is not the most convenient coordinate system to solve the problem or describe the movement of the system. For example, for a serial chain of rigid links, such as a member of the human body or from a robot manipulator, it may be simpler to describe the positions of each link by the angles between links.  \n", "\n", "Coordinate systems such as angles of a chain of links are referred as [generalized coordinates](https://en.wikipedia.org/wiki/Generalized_coordinates). Generalized coordinates uniquely specify the positions of the particles in a system. Although there may be several generalized coordinates to describe a system, usually a judicious choice of generalized coordinates provides the minimum number of independent coordinates that define the configuration of a system (which is the number of <a href=\"https://en.wikipedia.org/wiki/Degrees_of_freedom_(mechanics)\">degrees of freedom</a> of the system), turning the problem simpler to solve. In this case, when the number of generalized coordinates equals the number of degrees of freedom, the system is referred as a holonomic system. In a non-holonomic system, the number of generalized coordinates necessary do describe the system depends on the path taken by the system. \n", "\n", "Being a little more technical, according to [Wikipedia](https://en.wikipedia.org/wiki/Configuration_space_(physics)):  \n", "\"In classical mechanics, the parameters that define the configuration of a system are called generalized coordinates, and the vector space defined by these coordinates is called the configuration space of the physical system. It is often the case that these parameters satisfy mathematical constraints, such that the set of actual configurations of the system is a manifold in the space of generalized coordinates. This manifold is called the configuration manifold of the system.\"\n", "\n", "In problems where it is desired to use generalized coordinates, one can write <PERSON>'s equations of motion in terms of Cartesian coordinates and then transform them into generalized coordinates. However, it would be desirable and convenient to have a general method that would directly establish the equations of motion in terms of a set of convenient generalized coordinates. In addition, general methods for writing, and perhaps solving, the equations of motion in terms of any coordinate system would also be desirable. The [Lagrangian mechanics](https://en.wikipedia.org/wiki/Lagrangian_mechanics) is such a method.\n", "\n", "When describing a system of particles using any set of generalized coordinates, $q_1,\\dotsc,q_{3N}$, these are related to, for example, the Cartesian coordinates by:\n", " \n", "\\begin{equation} \\begin{array}{rcl}\n", "q_i =q_i (x_1,\\dotsc,x_{3N} ) \\quad i=1,\\dotsc,3N \\\\\n", "x_i =x_i (q_1,\\dotsc,q_{3N} ) \\quad i=1,\\dotsc,3N \n", "\\label{qx}\n", "\\end{array} \\end{equation}\n", "\n", "The Cartesian components of velocity as a function of generalized coordinates are:\n", "\n", "\\begin{equation}\n", "\\dot{x}_i =\\frac{\\mathrm d x_i (q_1, q_2,\\dotsc,q_{3N} \n", ")}{\\mathrm d t}=\\sum\\limits_{j=1}^{3N} {\\frac{\\partial x_i }{\\partial q_j }} \n", "\\frac{\\mathrm d q_j }{\\mathrm d t}\n", "\\label{eq_xdotqdot}\n", "\\end{equation}\n", "\n", "where for simplicity we omitted the explicit mention of the temporal dependence of each coordinate.\n", "\n", "That is, any Cartesian component of the particle velocity as a function of generalized coordinates is a function of all the components of position and velocity in the generalized coordinates:\n", "\n", "\\begin{equation} \n", "\\dot{x}_i = \\dot{x}_i (q_1,\\dotsc,q_{3N} ,\\dot{q}_1,\\dotsc,\\dot{q}_{3N} ) \\quad i=1,\\dotsc,3N \n", "\\label{eq27}\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>'s equation\n", "\n", "In analogy to Newtonian mechanics, one can think that the equations of motion can be obtained by equating the generalized force, $F_i$, to the temporal rate of change of each generalized momentum, $p_i$:\n", "\n", "\\begin{equation} \n", "F_i =\\frac{\\partial p_i }{\\partial t} \n", "\\label{eq28}\n", "\\end{equation}\n", "\n", "In the formula above, let's substitute the quantity $p_i$ by its definition in terms of the kinetic energy:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial p_i }{\\partial t} =\\frac{\\partial }{\\partial t}\\left( {\\frac{\\partial T}{\\partial \n", "\\dot{q}_i }} \\right)=\\frac{\\partial }{\\partial t}\\left( \n", "{\\sum\\limits_{j=1}^{3N} {m_j \\dot{x}_j \\frac{\\partial \\dot{x}_j \n", "}{\\partial \\dot{q}_i }} } \\right)\n", "\\label{eq29}\n", "\\end{equation}\n", "\n", "where we used:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial T}{\\partial \\dot{q}_i }=\\sum\\limits_{j=1}^{3N} \n", "{\\frac{\\partial T}{\\partial \\dot{x}_j }\\frac{\\partial \\dot{x}_j \n", "}{\\partial \\dot{q}_i }}  \n", "\\label{eq30}\n", "\\end{equation}\n", "\n", "Using the [product rule](https://en.wikipedia.org/wiki/Product_rule), the derivative of the product in Eq. (\\ref{eq29}) is:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial p_i }{\\partial t}=\\sum\\limits_{j=1}^{3N} {m_j \n", "\\ddot{x}_j \\frac{\\partial \\dot{x}_j }{\\partial \\dot{q}_i }} \n", "+\\sum\\limits_{j=1}^{3N} {m_j \\dot{x}_j \\frac{\\mathrm d }{\\mathrm d t}\\left( \n", "{\\frac{\\partial \\dot{x}_j }{\\partial \\dot{q}_i }} \\right)}  \n", "\\label{eq31}\n", "\\end{equation}\n", "\n", "But:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial \\dot{x}_i }{\\partial \\dot{q}_j }=\\frac{\\partial x_i \n", "}{\\partial q_j } \\quad because \\quad \\frac{\\partial \n", "\\dot{x}_i }{\\partial \\dot{q}_j }=\\frac{\\partial x_i }{\\partial \n", "t}\\frac{\\partial t}{\\partial q_j }=\\frac{\\partial x_i }{\\partial q_j} \n", "\\label{eq32}\n", "\\end{equation}\n", "\n", "Then:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial p_i }{\\partial t}=\\sum\\limits_{j=1}^{3N} {m_j \n", "\\ddot{x}_j \\frac{\\partial x_j }{\\partial q_i }} \n", "+\\sum\\limits_{j=1}^{3N} {m_j \\dot{x}_j \\frac{\\mathrm d }{\\mathrm d t}\\left( \n", "{\\frac{\\partial x_j }{\\partial q_i }} \\right)} \n", "\\label{eq33}\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The first term on the right side of the equation above is proportional to $m_j \n", "\\ddot{x}_j$ and we will define as the generalized force, $Q_i$. But, different from Newtonian mechanics, the temporal variation of the generalized momentum is equal to the generalized force plus another term, which will investigate now. The last part of this second term can be derived as:\n", "\n", "\\begin{equation}\n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial x_j }{\\partial q_i }} \\right) = \n", "\\sum\\limits_{k=1}^{3N} {\\frac{\\mathrm d }{\\mathrm d q_k }\\left( {\\frac{\\partial \n", "x_j }{\\partial q_i }} \\right)\\frac{\\mathrm d q_k }{\\mathrm d t}} =\\sum\\limits_{k=1}^{3N} \n", "{\\frac{\\partial^2 x_j }{\\partial q_k \\partial q_i }\\dot{q}_k }\n", "\\label{eq34}\n", "\\end{equation}\n", "\n", "where we used the [chain rule](https://en.wikipedia.org/wiki/Chain_rule) for the differentiation:\n", "\\begin{equation}\n", "\\frac{\\mathrm d }{\\mathrm d t}\\Big( {f\\big({g(t)}\\big)}\\Big) = \\frac{\\partial f}{\\partial g}\\frac{\\partial g}{\\partial t}\n", "\\label{eq35}\n", "\\end{equation}\n", "\n", "But if we look at Eq. (\\ref{eq_xdotqdot}) we see that the term at the right side of the Eq. (\\ref{eq34}) can be obtained by:\n", "\n", "\\begin{equation}\n", "\\frac{\\partial \\dot{x}_j }{\\partial q_i } = \\frac{\\partial }{\\partial q_i }\\left(\\sum\\limits_{k=1}^{3N} \\frac{\\partial \n", "x_j }{\\partial q_i }\\dot{q}_k \\right) = \\sum\\limits_{k=1}^{3N} \n", "{\\frac{\\partial^2 x_j }{\\partial q_k \\partial q_i }\\dot{q}_k }\n", "\\label{eq36}\n", "\\end{equation}\n", "\n", "Comparing the Eq. (\\ref{eq34}) and Eq. (\\ref{eq36}) we have:\n", "\n", "\\begin{equation}\n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial x_j }{\\partial q_i }} \\right) = \n", "\\frac{\\mathrm d }{\\mathrm d q_i}\\left( {\\frac{\\partial x_j }{\\partial t }} \\right)\n", "\\label{eq_dotdxdq}\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["On the other hand, it is possible to relate the term $\\partial \\dot{x}_j / \\partial q_i$ to the derivative of kinetic energy with respect to the coordinate $q_i$:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial T}{\\partial q_i }=\\frac{\\partial }{\\partial q_i }\\left( \n", "{\\sum\\limits_{j=1}^{3N} {\\frac{1}{2}m_j \\dot{x}_j^2} } \n", "\\right)=\\sum\\limits_{j=1}^{3N} {m_j \\dot{x}_j } \\frac{\\partial \n", "\\dot{x}_j }{\\partial q_i } \n", "\\label{eq38}\n", "\\end{equation}\n", "\n", "where once again we used the chain rule for the differentiation.\n", "\n", "Using Eq. (\\ref{eq_dotdxdq}), Eq. (\\ref{eq38}) becomes\n", "\n", "\\begin{equation} \n", "\\frac{\\partial T}{\\partial q_i }=\\sum\\limits_{j=1}^{3N} {m_j \n", "\\dot{x}_j } \\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial x_j }{\\partial q_i }} \n", "\\right) \n", "\\label{eq39}\n", "\\end{equation}\n", "\n", "Returning to Eq. (\\ref{eq33}), it can be rewritten as:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial T}{\\partial \\dot{q}_i }} \\right) = Q_i + \\frac{\\partial T}{\\partial q_i } \n", "\\label{eq40}\n", "\\end{equation}\n", "\n", "and\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial T}{\\partial \\dot{q}_i }} \\right) - \\frac{\\partial T}{\\partial q_i } = Q_i \n", "\\label{eq41}\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's look at $Q_i$, the generalized force. It can be decomposed into two terms: \n", "\n", "The first term, composed of the conservative forces, i.e. forces that can be written as potential gradients:\n", "\n", "\\begin{equation} \n", "Q_C =-\\frac{\\partial V}{\\partial q_i } \\quad , \\quad V=V\\left( {q_1,\\dotsc,q_{3N} } \\right) \n", "\\label{eq42}\n", "\\end{equation}\n", "\n", "An example of conservative force is the gravitational force.\n", "\n", "And the second term, encompassing all non-conservative forces, $Q_{NC}$.  \n", "\n", "Then:\n", "\n", "\\begin{equation} Q_i =-\\frac{\\partial V}{\\partial q_i }+Q_{NCi} \\quad , \\quad V=V\\left( {q_1,\\dotsc,q_{3N} } \\right) \\end{equation}\n", "\n", "The Eq. (\\ref{eq41}) becomes\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial T}{\\partial \\dot{q}_i }} \n", "\\right)-\\frac{\\partial T}{\\partial q_i }=-\\frac{\\partial V}{\\partial q_i} + Q_{NCi}\n", "\\label{eq43}\n", "\\end{equation}\n", "\n", "Rearranging, we have:\n", "\n", "\\begin{equation} \\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial \\left( {T-V} \\right)}{\\partial \n", "\\dot{q}_i }} \\right)-\\frac{\\partial \\left( {T-V} \\right)}{\\partial q_i} = Q_{NCi} \n", "\\label{eq44}\n", "\\end{equation}\n", "\n", "This is possible because:\n", "\n", "\\begin{equation} \n", "\\frac{\\partial V}{\\partial \\dot{q}_i} = 0 \n", "\\label{eq45}\n", "\\end{equation}\n", "\n", "Defining:\n", "\n", "\\begin{equation} \n", "\\mathcal{L} \\equiv \\mathcal{L}(q_1,\\dotsc,q_{3N} ,\\dot{q}_1,\\dotsc,\\dot{q}_{3N} ) = T - V \n", "\\label{eq46}\n", "\\end{equation}\n", "\n", "as the Lagrange or Lagrangian function, we have the <PERSON><PERSON><PERSON>'s equation:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial \\mathcal{L}}{\\partial \\dot{q}_i }} \n", "\\right)-\\frac{\\partial \\mathcal{L}}{\\partial q_i } = Q_{NCi} \\quad i=1,\\dotsc,3N \n", "\\label{eq47}\n", "\\end{equation}\n", "\n", "Once all derivatives of the <PERSON><PERSON><PERSON>ian function are calculated, this equation will be the equation of motion for each particle. If there are  $N$ independent particles in a three-dimensional space, there will be $3N$ equations for the system.\n", "\n", "The set of equations above for a system are known as <PERSON><PERSON><PERSON><PERSON> equations, or <PERSON><PERSON><PERSON>'s equations of the second kind."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Constraints\n", " \n", "An important class of problems in mechanics, in which the Lagrangian equations are particularly useful, are composed of constrained systems. A constraint is a restriction on the freedom of movement of a particle or a system of particles (a constraint decreases the number of degrees of freedom of a system). A rigid body, or the movement of a pendulum, are examples of constrained systems. It can be shown, in a similar way, that the <PERSON><PERSON><PERSON> equation, deduced here for a system of free particles, is also valid for a system of particles under the action of constraints.  \n", "The <PERSON><PERSON><PERSON><PERSON> equation, for a system of $3N$ particles and with $k$ constraints, is then defined as:\n", "\n", "\\begin{equation} \n", "\\frac{\\mathrm d }{\\mathrm d t}\\left( {\\frac{\\partial \\mathcal{L}}{\\partial \\dot{q}_i}} \\right)-\\frac{\\partial \\mathcal{L}}{\\partial q_i } = Q_{NCi} \\quad i=1,\\dotsc,3N-k \n", "\\label{eq48}\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "- [The Principle of Least Action in ](https://www.feynmanlectures.caltech.edu/II_19.html)  \n", "- <PERSON><PERSON><PERSON> (MIT OpenCourseWare) [An Introduction to Lagrangian Mechanics](https://ocw.mit.edu/courses/mechanical-engineering/2-003sc-engineering-dynamics-fall-2011/lagrange-equations/MIT2_003SCF11_Lagrange.pdf)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Video lectures on the internet   \n", "\n", "- iLectureOnline: [Lectures in Lagrangian Mechanics](http://www.ilectureonline.com/lectures/subject/PHYSICS/34/245)  \n", "- MIT OpenCourseWare: [Introduction to <PERSON><PERSON><PERSON> With Examples](https://youtu.be/zhk9xLjrmi4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON> (1980) [Classical Mechanics](https://books.google.com.br/books?id=tJCuQgAACAAJ), 3rd ed., <PERSON><PERSON><PERSON>.  \n", "- <PERSON> (1970) [Classical Dynamics of particles and systems](https://books.google.com.br/books?id=Ss43BQAAQBAJ), 2nd ed., Academic Press.  \n", "- <PERSON><PERSON><PERSON> (1949) [Principles of Mechanics](https://books.google.com.br/books?id=qsYfENCRG5QC), 2nd ed., McGraw-hill.  \n", "- <PERSON> (2005) [Classical Mechanics](https://archive.org/details/JohnTaylorClassicalMechanics). University Science Books. "]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 2}