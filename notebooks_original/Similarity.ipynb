{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["> # Select data vectors by similarity using a metric score\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-11-20T20:19:47.223181Z", "start_time": "2019-11-20T20:19:47.054140Z"}, "editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Last updated: 2023-08-08 20:35:01\n", "\n", "Python implementation: CPython\n", "Python version       : 3.11.4\n", "IPython version      : 8.14.0\n", "\n", "Compiler    : GCC 12.2.0\n", "OS          : Linux\n", "Release     : 6.2.0-26-generic\n", "Machine     : x86_64\n", "Processor   : x86_64\n", "CPU cores   : 16\n", "Architecture: 64bit\n", "\n", "sys       : 3.11.4 | packaged by conda-forge | (main, Jun 10 2023, 18:08:17) [GCC 12.2.0]\n", "matplotlib: 3.7.2\n", "numpy     : 1.25.2\n", "\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import sys\n", "sys.path.insert(1, r'./../functions')\n", "from simila import similarity, mse\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "%load_ext line_profiler\n", "%load_ext watermark\n", "%watermark -u -t -d -m -v --iversions\n", "\n", "np.set_printoptions(precision=3)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function similarity in module simila:\n", "\n", "similarity(y: numpy.ndarray, axis1: int = 0, axis2: int = 1, threshold: float = 0, nmin: int = 3, repeat: bool = True, metric: Callable = <function mse at 0x7fbda7d5a3e0>, drop=True, msg: bool = True, **kwargs: Callable) -> tuple[numpy.ndarray, numpy.ndarray, numpy.ndarray, numpy.ndarray]\n", "    Select vectors in array by their similarity using a metric score.\n", "    \n", "    For example, if `y` is a 2-D numpy.ndarray, with shape (n, m), `axis1`=0\n", "    (n is the number of rows) and `axis2`=1 (m is the number of columns), this\n", "    function will select the vectors along the columns, that are more similar\n", "    to a `central` statistics of `y` or to a `target`, using a `metric` score.\n", "    The metric score can be calculated repeatedly until all selected vectors\n", "    have a `metric` score not greater than a `threshold`, but the minimum\n", "    number of vectors to keep or the maximum number of vectors to discard\n", "    can be specified with parameter `nmin`.\n", "    \n", "    The default `metric` and target are the mean squared error (`mse`) of `y`\n", "    w.r.t. the median of `y` along `axis2`. The `mse` metric is equivalent\n", "    to the squared Euclidean distance and it is prefered because it\n", "    penalizes largest differences more than the Euclidian distance. But any\n", "    other `metric` that can be calculated with a function can be used.\n", "    \n", "    A possible use of this function is to discard the time-series data from bad\n", "    trials (treated as outliers) stored in a 2-D array (each trial as a column\n", "    and instants as rows) where the criterion is the similarity of the trial\n", "    w.r.t. the median trial (the median statistics is more robust to remove\n", "    outliers than the mean in case there are very bad trials). After the bad\n", "    trials are discarded, the mean of all trials could then be calculated more\n", "    reliablly.\n", "    \n", "    Parameters\n", "    ----------\n", "    y : numpy.n<PERSON><PERSON>\n", "        Array for the calculation of similarity (defined by a `metric`) of its\n", "        vectors w.r.t. a `target` or a `central` statistics of `y`.\n", "    axis1 : integer, optional, default = 0\n", "        Axis of `y` for which the `metric` will be calculated at each value and\n", "        possibly averaged in the `metric` calculation.\n", "    axis2 : integer, optional, default = 1\n", "        Axis of `y` along which the different vectors are going to be compared\n", "        with the `threshold` for their similarity (using their `metric` score).\n", "    threshold : float, optional, default = 0\n", "        If greater than 0, vector with `metric` score above this value will be\n", "        discarded. If 0, threshold will be automatically calculated as the\n", "        minimum of [qs[2] + 1.5*(qs[2]-qs[0]), score[-2], 3], where qs are the\n", "        quantiles and score[-2] is the before-last largest score of `metric`\n", "        among vectors calculated at the first time, not updated by the `repeat`\n", "        option.\n", "    nmin : integer, optional, default = 3\n", "        If greater than 0, minumum number of vectors to keep.\n", "        If lower than 0, maximum number of vectors to discard.\n", "    repeat : bool, optional, default = True\n", "        Whether to calculate similarity `metric` repeatedly, updating the\n", "        score calculation each time a vector is discarded.\n", "        With `repeat` True, the output `scores` will contain at each row\n", "        the updated score values for the used `metric` for each data vector.\n", "        The first row will contain the calculated original scores before any\n", "        vector was discarded. On the next equent rows, the vectors discarded\n", "        are represented by NaN values and the kept vectors by their updated\n", "        scores.\n", "        The last row will contain the updated scores of the final vectors kept.\n", "        With the `repeat` False, the comparison of score values with\n", "        `threshold` are made only once and vectors are discarded accordingly at\n", "        once. In this case, the output `scores` will contain only two rows, the\n", "        first row will contain the calculated original scores before any\n", "        vectors were discarded. At the second row, the vectors discarded are\n", "        represented with NaN values and the kept vectors by their updated\n", "        scores.\n", "    metric : optional, default=mse\n", "        Function to use as metric to compute similarity.\n", "    drop : bool, optional, default = True\n", "        Whether to drop (delete) the discarded vectors from `y` in the output.\n", "        If False, the values of the vectors discarded will be replaced by nans\n", "        and the returned array will have the same dimensions as the original\n", "        array.\n", "    msg : bool, optional, default = True\n", "        Whether to print some messages.\n", "    kwargs : optional\n", "        Options for the metric function (e.g., see `mse` function).\n", "    \n", "    Returns\n", "    -------\n", "    y : numpy.n<PERSON><PERSON>\n", "        Array similar to input `y` but with vectors discarded (deleted) if\n", "        option `drop` is True or with all values of vectors discarded replaced\n", "        by nans if option `drop` is False.\n", "    ikept : numpy.n<PERSON><PERSON>\n", "        Indexes of kept vectors.\n", "    inotkept : numpy.n<PERSON><PERSON>\n", "        Indexes of not kept (discarded or replaced by nan) vectors.\n", "    scores : 2-<PERSON> numpy.ndarray\n", "        Metric score values of each vector (as columns) for each round of\n", "        vector selection (one row per round plus the final values).\n", "    \n", "    References\n", "    ----------\n", "    .. [1] https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Similarity.ipynb\n", "    \n", "    Examples\n", "    --------\n", "    >>> import matplotlib.pyplot as plt\n", "    >>> rng = np.random.default_rng()\n", "    >>> t, n = 100, 10\n", "    >>> y = rng.random((t, n)) / 2\n", "    >>> y = y + np.atleast_2d(2*np.sin(2*np.pi*np.linspace(0, 1, t))).T\n", "    >>> for i in range(0, n, 2):\n", "    >>>    j = rng.integers(t-20)\n", "    >>>    p = rng.integers(20)\n", "    >>>    y[j:j+p, i] = y[j:j+p, i] + rng.integers(10) - 5\n", "    >>>    y[:, i] += rng.integers(4) - 2\n", "    >>> ysr, ikeptr, inotkeptr, scoresr = similarity(y)\n", "    >>> ysn, ikeptn, inotkeptn, scoresn = similarity(y, repeat=False)\n", "    >>> fig, axs = plt.subplots(3, 1, sharex=True, figsize=(8, 8))\n", "    >>> axs[0].plot(y, label=list(range(n)))\n", "    >>> axs[0].legend(loc=(1.01, 0), ncol=2)\n", "    >>> axs[0].set_title(f'Original vectors (n={n})')\n", "    >>> axs[1].plot(ysr, label= ikeptr.tolist())\n", "    >>> axs[1].set_title(f'Vectors maintained with repeat selection (n={len(ikeptr)})')\n", "    >>> axs[1].legend(loc=(1.01, 0))\n", "    >>> axs[2].plot(ysn, label= ikeptn.tolist())\n", "    >>> axs[2].set_title(f'Vectors maintained without repeat selection (n={len(ikeptn)})')\n", "    >>> axs[2].legend(loc=(1.01, 0))\n", "    >>> plt.show()\n", "    \n", "    Version history\n", "    ---------------\n", "    '1.0.0':\n", "        First release version\n", "\n"]}], "source": ["help(similarity)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function mse in module simila:\n", "\n", "mse(y: numpy.ndar<PERSON>, target: numpy.ndarray | None = None, axis1: int = 0, axis2: int = 1, central: Callable = <function nanmedian at 0x7fbe32f73570>, normalization: Callable = <function nanmedian at 0x7fbe32f73570>) -> numpy.ndarray\n", "    Mean Squared Error of `y` w.r.t. `target` or `central` along `axis2` at `axis1`.\n", "    \n", "    Parameters\n", "    ----------\n", "    y : numpy.n<PERSON><PERSON>\n", "        At least a 2-D numpy.ndarray of data for the calculation of mean squared\n", "        error w.r.t. a `target` or a `central` statistics of the data.\n", "    target : 1-D numpy.ndarray of length `axis1`, optional, default = None\n", "        Reference value to calculate the mean squared error of `y` w.r.t. this\n", "        vector. If it is None, the mse value will be calculated w.r.t. a `central`\n", "        calculated along `axis2` of `y`.\n", "    axis1 : integer, optional, default = 0\n", "        Axis of `y` for which the mse will be calculated at each value.\n", "    axis2 : integer, optional, default = 1\n", "        Axis of `y` along which the `central` statistics might be calculated or\n", "        along which the target will be subtracted.\n", "    central : Python function, optional, default = np.nanmedian\n", "        Function to calculate statistics on `y` w.r.t. mse is computed.\n", "    normalization : Python function, optional, default = np.nanmedian\n", "        Function to normalize the calculated mse values\n", "    \n", "    Returns\n", "    -------\n", "    score : numpy.n<PERSON><PERSON>\n", "        Mean Squared Error values\n", "    \n", "    References\n", "    ----------\n", "    .. [1] https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Similarity.ipynb\n", "    \n", "    Examples\n", "    --------\n", "    >>> import numpy as np\n", "    >>> rng = np.random.default_rng()\n", "    >>> y = rng.random((100, 10))\n", "    >>> y = y + np.atleast_2d(np.sin(2*np.pi*np.linspace(0, 1, 100))).T\n", "    >>> mse(y, axis1=0, axis2=1, central=np.nanmedian, normalization=np.nanmedian)\n", "    \n", "    Version history\n", "    ---------------\n", "    '1.0.0':\n", "        First release version\n", "\n"]}], "source": ["help(mse)"]}, {"cell_type": "markdown", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["### Example"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculated threshold: 3.0\n", "Vectors discarded (dimension 1, n=5): [6 0 2 4 8]\n", "Calculated threshold: 3.0\n", "Vectors discarded (dimension 1, n=3): [6 0 2]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 700x700 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": [">>> import matplotlib.pyplot as plt\n", ">>> rng = np.random.default_rng()\n", ">>> t, n = 100, 10\n", ">>> y = rng.random((t, n)) / 2\n", ">>> y +=  np.atleast_2d(2*np.sin(2*np.pi*np.linspace(0, 1, t))).T\n", ">>> for i in range(0, n, 2):\n", ">>>    j = rng.integers(t-20)\n", ">>>    p = rng.integers(20)\n", ">>>    y[j:j+p, i] = y[j:j+p, i] + rng.integers(10) - 5\n", ">>>    y[:, i] += rng.integers(4) - 2\n", ">>> ysr, ikeptr, inotkeptr, scoresr = similarity(y)\n", ">>> ysn, ikeptn, inotkeptn, scoresn = similarity(y, repeat=False)\n", ">>> fig, axs = plt.subplots(3, 1, sharex=True, figsize=(7, 7))\n", ">>> axs[0].plot(y, label=list(range(n)))\n", ">>> axs[0].legend(loc=(1.01, 0), ncol=2)\n", ">>> axs[0].set_title(f'Original vectors (n={n})')\n", ">>> axs[1].plot(ysr, label= ikeptr.tolist())\n", ">>> axs[1].set_title(f'Vectors maintained with repeat selection (n={len(ikeptr)})')\n", ">>> axs[1].legend(loc=(1.01, 0))\n", ">>> axs[2].plot(ysn, label= ikeptn.tolist())\n", ">>> axs[2].set_title(f'Vectors maintained without repeat selection (n={len(ikeptn)})')\n", ">>> axs[2].legend(loc=(1.01, 0))\n", ">>> plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculated threshold: 3.0\n", "Vectors discarded (dimension 1, n=5): [6 0 2 4 8]\n"]}], "source": ["ys, ikept, inotkept, scores = similarity(y, threshold=0, nmin=3, repeat=True, metric=mse,\n", "                                         msg=1, central=np.nanmedian, normalization=np.nanmedian)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(100, 5)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ys.shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 3, 5, 7, 9])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ikept"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([6, 0, 2, 4, 8])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["inotkept"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[  8.791,   0.162,   4.843,   0.159,   2.141,   0.172,  13.413,\n", "          0.153,   1.828,   0.13 ],\n", "       [109.377,   0.865,  64.015,   0.839,  27.5  ,   1.   ,     nan,\n", "          0.771,  24.609,   0.695],\n", "       [    nan,   0.955,  90.718,   0.905,  39.667,   1.045,     nan,\n", "          0.869,  36.199,   0.82 ],\n", "       [    nan,   1.   ,     nan,   0.962,  39.243,   1.245,     nan,\n", "          0.882,  35.509,   0.804],\n", "       [    nan,   1.003,     nan,   0.972,     nan,   1.165,     nan,\n", "          0.997,  47.104,   0.827],\n", "       [    nan,   0.998,     nan,   1.   ,     nan,   1.013,     nan,\n", "          0.967,     nan,   1.003]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculated threshold: 3.0\n", "Vectors discarded (dimension 1, n=5): [6 0 2 4 8]\n"]}], "source": ["ys, ikept, inotkept, scores = similarity(y, threshold=0, nmin=3, repeat=True, metric=mse,\n", "                                         drop=False, msg=True, central=np.nanmedian, normalization=np.nanmedian)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(100, 10)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ys.shape"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculated threshold: 3.0\n", "Vectors discarded (dimension 1, n=3): [6 0 2]\n"]}], "source": ["ys, ikept, inotkept, scores = similarity(y, threshold=0, nmin=3, repeat=False, metric=mse,\n", "                                         msg=True, central=np.nanmedian, normalization=np.nanmedian)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function profiling"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.3 ms ± 15.1 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%%timeit\n", "ys, ikept, inotkept, score_all = similarity(y, repeat=True, msg=False)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["882 µs ± 21.4 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n"]}], "source": ["%%timeit\n", "ys, ikept, inotkept, score_all = similarity(y, repeat=False, msg=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The computation of the metric score (in the example above, `mse`) takes 86% of the funtion time. Let's look on that."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["332 µs ± 3.12 µs per loop (mean ± std. dev. of 7 runs, 1,000 loops each)\n"]}], "source": ["%%timeit\n", "score = mse(y, central=np.nanmedian, normalization=np.nanmedian)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["77.6 µs ± 1.35 µs per loop (mean ± std. dev. of 7 runs, 10,000 loops each)\n"]}], "source": ["%%timeit\n", "score = mse(y, central=np.nanmean, normalization=np.nanmean)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Using `median` to calculate statistics slows down the code by about 4 times compared to using `mean`."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["Timer unit: 1e-09 s\n", "\n", "Total time: 0.00477567 s\n", "File: /home/<USER>/adrive/Python/BMC/notebooks/./../functions/simila.py\n", "Function: similarity at line 87\n", "\n", "Line #      Hits         Time  Per Hit   % Time  Line Contents\n", "==============================================================\n", "    87                                           def similarity(y: np.n<PERSON><PERSON>, axis1: int = 0, axis2: int = 1, threshold: float = 0,\n", "    88                                                          nmin: int = 3, repeat: bool = True, metric: Callable = mse,\n", "    89                                                          drop=True, msg: bool = True, **kwargs: Callable\n", "    90                                                          ) -> tuple[np.n<PERSON><PERSON>, np.n<PERSON><PERSON>, np.ndar<PERSON>, np.ndar<PERSON>]:\n", "    91                                           \n", "    92                                               \"\"\"Select vectors in array by their similarity using a metric score.\n", "    93                                           \n", "    94                                               For example, if `y` is a 2-D numpy.ndarray, with shape (n, m), `axis1`=0\n", "    95                                               (n is the number of rows) and `axis2`=1 (m is the number of columns), this\n", "    96                                               function will select the vectors along the columns, that are more similar\n", "    97                                               to a `central` statistics of `y` or to a `target`, using a `metric` score.\n", "    98                                               The metric score can be calculated repeatedly until all selected vectors\n", "    99                                               have a `metric` score not greater than a `threshold`, but the minimum\n", "   100                                               number of vectors to keep or the maximum number of vectors to discard\n", "   101                                               can be specified with parameter `nmin`.\n", "   102                                           \n", "   103                                               The default `metric` and target are the mean squared error (`mse`) of `y`\n", "   104                                               w.r.t. the median of `y` along `axis2`. The `mse` metric is equivalent\n", "   105                                               to the squared Euclidean distance and it is prefered because it\n", "   106                                               penalizes largest differences more than the Euclidian distance. But any\n", "   107                                               other `metric` that can be calculated with a function can be used.\n", "   108                                           \n", "   109                                               A possible use of this function is to discard the time-series data from bad\n", "   110                                               trials (treated as outliers) stored in a 2-D array (each trial as a column\n", "   111                                               and instants as rows) where the criterion is the similarity of the trial\n", "   112                                               w.r.t. the median trial (the median statistics is more robust to remove\n", "   113                                               outliers than the mean in case there are very bad trials). After the bad\n", "   114                                               trials are discarded, the mean of all trials could then be calculated more\n", "   115                                               reliablly.\n", "   116                                           \n", "   117                                               Parameters\n", "   118                                               ----------\n", "   119                                               y : numpy.n<PERSON><PERSON>\n", "   120                                                   Array for the calculation of similarity (defined by a `metric`) of its\n", "   121                                                   vectors w.r.t. a `target` or a `central` statistics of `y`.\n", "   122                                               axis1 : integer, optional, default = 0\n", "   123                                                   Axis of `y` for which the `metric` will be calculated at each value and\n", "   124                                                   possibly averaged in the `metric` calculation.\n", "   125                                               axis2 : integer, optional, default = 1\n", "   126                                                   Axis of `y` along which the different vectors are going to be compared\n", "   127                                                   with the `threshold` for their similarity (using their `metric` score).\n", "   128                                               threshold : float, optional, default = 0\n", "   129                                                   If greater than 0, vector with `metric` score above this value will be\n", "   130                                                   discarded. If 0, threshold will be automatically calculated as the\n", "   131                                                   minimum of [qs[2] + 1.5*(qs[2]-qs[0]), score[-2], 3], where qs are the\n", "   132                                                   quantiles and score[-2] is the before-last largest score of `metric`\n", "   133                                                   among vectors calculated at the first time, not updated by the `repeat`\n", "   134                                                   option.\n", "   135                                               nmin : integer, optional, default = 3\n", "   136                                                   If greater than 0, minumum number of vectors to keep.\n", "   137                                                   If lower than 0, maximum number of vectors to discard.\n", "   138                                               repeat : bool, optional, default = True\n", "   139                                                   Whether to calculate similarity `metric` repeatedly, updating the\n", "   140                                                   score calculation each time a vector is discarded.\n", "   141                                                   With `repeat` True, the output `scores` will contain at each row\n", "   142                                                   the updated score values for the used `metric` for each data vector.\n", "   143                                                   The first row will contain the calculated original scores before any\n", "   144                                                   vector was discarded. On the next equent rows, the vectors discarded\n", "   145                                                   are represented by NaN values and the kept vectors by their updated\n", "   146                                                   scores.\n", "   147                                                   The last row will contain the updated scores of the final vectors kept.\n", "   148                                                   With the `repeat` False, the comparison of score values with\n", "   149                                                   `threshold` are made only once and vectors are discarded accordingly at\n", "   150                                                   once. In this case, the output `scores` will contain only two rows, the\n", "   151                                                   first row will contain the calculated original scores before any\n", "   152                                                   vectors were discarded. At the second row, the vectors discarded are\n", "   153                                                   represented with NaN values and the kept vectors by their updated\n", "   154                                                   scores.\n", "   155                                               metric : optional, default=mse\n", "   156                                                   Function to use as metric to compute similarity.\n", "   157                                               drop : bool, optional, default = True\n", "   158                                                   Whether to drop (delete) the discarded vectors from `y` in the output.\n", "   159                                                   If False, the values of the vectors discarded will be replaced by nans\n", "   160                                                   and the returned array will have the same dimensions as the original\n", "   161                                                   array.\n", "   162                                               msg : bool, optional, default = True\n", "   163                                                   Whether to print some messages.\n", "   164                                               kwargs : optional\n", "   165                                                   Options for the metric function (e.g., see `mse` function).\n", "   166                                           \n", "   167                                               Returns\n", "   168                                               -------\n", "   169                                               y : numpy.n<PERSON><PERSON>\n", "   170                                                   Array similar to input `y` but with vectors discarded (deleted) if\n", "   171                                                   option `drop` is True or with all values of vectors discarded replaced\n", "   172                                                   by nans if option `drop` is False.\n", "   173                                               ikept : n<PERSON><PERSON>.n<PERSON><PERSON>\n", "   174                                                   Indexes of kept vectors.\n", "   175                                               inotkept : nump<PERSON>.n<PERSON><PERSON>\n", "   176                                                   Indexes of not kept (discarded or replaced by nan) vectors.\n", "   177                                               scores : 2-<PERSON> numpy.n<PERSON><PERSON>\n", "   178                                                   Metric score values of each vector (as columns) for each round of\n", "   179                                                   vector selection (one row per round plus the final values).\n", "   180                                           \n", "   181                                               References\n", "   182                                               ----------\n", "   183                                               .. [1] https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Similarity.ipynb\n", "   184                                           \n", "   185                                               Examples\n", "   186                                               --------\n", "   187                                               >>> import matplotlib.pyplot as plt\n", "   188                                               >>> rng = np.random.default_rng()\n", "   189                                               >>> t, n = 100, 10\n", "   190                                               >>> y = rng.random((t, n)) / 2\n", "   191                                               >>> y = y + np.atleast_2d(2*np.sin(2*np.pi*np.linspace(0, 1, t))).T\n", "   192                                               >>> for i in range(0, n, 2):\n", "   193                                               >>>    j = rng.integers(t-20)\n", "   194                                               >>>    p = rng.integers(20)\n", "   195                                               >>>    y[j:j+p, i] = y[j:j+p, i] + rng.integers(10) - 5\n", "   196                                               >>>    y[:, i] += rng.integers(4) - 2\n", "   197                                               >>> ysr, ikeptr, inotkeptr, scoresr = similarity(y)\n", "   198                                               >>> ysn, ikeptn, inotkeptn, scoresn = similarity(y, repeat=False)\n", "   199                                               >>> fig, axs = plt.subplots(3, 1, sharex=True, figsize=(8, 8))\n", "   200                                               >>> axs[0].plot(y, label=list(range(n)))\n", "   201                                               >>> axs[0].legend(loc=(1.01, 0), ncol=2)\n", "   202                                               >>> axs[0].set_title(f'Original vectors (n={n})')\n", "   203                                               >>> axs[1].plot(ysr, label= ikeptr.tolist())\n", "   204                                               >>> axs[1].set_title(f'Vectors maintained with repeat selection (n={len(ikeptr)})')\n", "   205                                               >>> axs[1].legend(loc=(1.01, 0))\n", "   206                                               >>> axs[2].plot(ysn, label= ikeptn.tolist())\n", "   207                                               >>> axs[2].set_title(f'Vectors maintained without repeat selection (n={len(ikeptn)})')\n", "   208                                               >>> axs[2].legend(loc=(1.01, 0))\n", "   209                                               >>> plt.show()\n", "   210                                           \n", "   211                                               Version history\n", "   212                                               ---------------\n", "   213                                               '1.0.0':\n", "   214                                                   First release version\n", "   215                                               \"\"\"\n", "   216                                           \n", "   217         1       9066.0   9066.0      0.2      logger.debug('Similarity...')\n", "   218                                           \n", "   219         1       1332.0   1332.0      0.0      if y.ndim < 2:\n", "   220                                                   raise ValueError('The input array must be at least a 2-D array.')\n", "   221         1      11963.0  11963.0      0.3      y = y.copy()\n", "   222         1    1185039.0 1185039.0     24.8      score: np.ndarray = metric(y, axis1=axis1, axis2=axis2, **kwargs)\n", "   223         1       8360.0   8360.0      0.2      scores: np.ndarray = np.atleast_2d(score)\n", "   224         1       3848.0   3848.0      0.1      ikept: np.ndarray = np.where(~np.isnan(score))[0]  # indexes of kept vectors\n", "   225         1       1669.0   1669.0      0.0      inotkept: np.ndarray = np.where(np.isnan(score))[0]  # indexes of discarded vectors\n", "   226         1       8119.0   8119.0      0.2      idx: np.ndarray = np.argsort(score)\n", "   227         1        779.0    779.0      0.0      score = score[idx]\n", "   228         1       2928.0   2928.0      0.1      nkept: int = np.count_nonzero(~np.isnan(score))  # number of kept vectors\n", "   229         1        295.0    295.0      0.0      if nkept < 3:\n", "   230                                                   logger.debug('nkept: %s', nkept)\n", "   231                                                   raise ValueError('The input array must have at least 3 valid vectors.')\n", "   232         1        240.0    240.0      0.0      if nmin < 0:\n", "   233                                                   nmin = np.max([3, nkept + nmin])\n", "   234         1        137.0    137.0      0.0      if threshold == 0:  # automatic threshold calculation\n", "   235         1     193447.0 193447.0      4.1          qs: np.n<PERSON>ray = np.nanquantile(a=score, q=[.25, .50, .75])\n", "   236         1      17995.0  17995.0      0.4          threshold = np.min([qs[2] + 1.5*(qs[2]-qs[0]), score[-2], 3.])\n", "   237         1        149.0    149.0      0.0          if msg:\n", "   238                                                       print(f'Calculated threshold: {threshold}')\n", "   239                                           \n", "   240         1        289.0    289.0      0.0      if not repeat:  # discard all vectors at once\n", "   241                                                   idx2: np.ndarray = np.nonzero(score > threshold)[0]  # vectors to discard\n", "   242                                                   if len(idx2) > 0:\n", "   243                                                       if nkept > nmin:  # keep at least nmin vectors\n", "   244                                                           inotkept = np.r_[inotkept, idx[idx2[-(y.shape[axis2] - nmin):]][::-1]]\n", "   245                                                           y.swapaxes(0, axis2)[inotkept, ...] = np.nan\n", "   246                                                           score = metric(y, axis1=axis1, axis2=axis2, **kwargs)\n", "   247                                                           scores = np.vstack((scores, score))\n", "   248                                                           logger.debug('not repeat - score: %s', score)\n", "   249                                               else:  # discard vector with largest updated score one by one\n", "   250         5       5133.0   1026.6      0.1          while nkept > nmin and score[nkept-1] > threshold:\n", "   251         5     136146.0  27229.2      2.9              inotkept = np.r_[inotkept, idx[nkept-1]]\n", "   252         5      14484.0   2896.8      0.3              y.swapaxes(0, axis2)[inotkept[-1], ...] = np.nan\n", "   253         5    2909616.0 581923.2     60.9              score = metric(y, axis1=axis1, axis2=axis2, **kwargs)\n", "   254         5      66628.0  13325.6      1.4              scores = np.vstack((scores, score))\n", "   255         5      27112.0   5422.4      0.6              idx = np.argsort(score)\n", "   256         5       3849.0    769.8      0.1              score = score[idx]\n", "   257         5       1292.0    258.4      0.0              nkept = nkept - 1\n", "   258         5       7149.0   1429.8      0.1              logger.debug('repeat - nkept: %s, score: %s', nkept, score)\n", "   259                                           \n", "   260         1        398.0    398.0      0.0      if len(inotkept):\n", "   261         1     151478.0 151478.0      3.2          ikept = np.setdiff1d(ikept, inotkept)\n", "   262         1        243.0    243.0      0.0          if drop:\n", "   263         1       6108.0   6108.0      0.1              y = y.swapaxes(0, axis2)[ikept, ...].swapaxes(0, axis2)\n", "   264         1        142.0    142.0      0.0          if msg:\n", "   265                                                       print(\n", "   266                                                             f'Vectors discarded (dimension {axis2}, n={len(inotkept)}): {inotkept}')\n", "   267                                           \n", "   268         1        232.0    232.0      0.0      return y, ikept, inotkept, scores"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%lprun -f similarity similarity(y, msg=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function `similarity`"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# %load ./../functions/simila.py\n", "\"\"\"Select vectors in array by their similarity using a metric score.\n", "\"\"\"\n", "\n", "import logging\n", "from typing import Callable\n", "import numpy as np\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = 'simila.py v.1.0.0 20123/07/31'\n", "__license__ = \"MIT\"\n", "\n", "# set logger and configure it not to confuse with matplotlib debug messages\n", "logger = logging.getLogger(__name__)\n", "logger.setLevel(20)  # 0: NOTS<PERSON>, 10: DEBUG, 20: INFO, 30: WARNING, 40: ERROR, 50: CRITIC<PERSON>\n", "ch = logging.StreamH<PERSON>ler()\n", "formatter = logging.Formatter('{name}: {levelname}: {message}', style='{')\n", "ch.<PERSON><PERSON><PERSON><PERSON><PERSON>(formatter)\n", "logger.add<PERSON><PERSON><PERSON>(ch)\n", "\n", "\n", "def mse(y: np.n<PERSON><PERSON>, target: np.ndarray | None = None, axis1: int = 0, axis2: int = 1,\n", "        central: Callable = np.nanmedian, normalization: Callable = np.nanmedian\n", "        ) -> np.ndarray:\n", "    \"\"\"Mean Squared Error of `y` w.r.t. `target` or `central` along `axis2` at `axis1`.\n", "\n", "    Parameters\n", "    ----------\n", "    y : numpy.n<PERSON><PERSON>\n", "        At least a 2-D numpy.ndarray of data for the calculation of mean squared\n", "        error w.r.t. a `target` or a `central` statistics of the data.\n", "    target : 1-D numpy.ndarray of length `axis1`, optional, default = None\n", "        Reference value to calculate the mean squared error of `y` w.r.t. this\n", "        vector. If it is None, the mse value will be calculated w.r.t. a `central`\n", "        calculated along `axis2` of `y`.\n", "    axis1 : integer, optional, default = 0\n", "        Axis of `y` for which the mse will be calculated at each value.\n", "    axis2 : integer, optional, default = 1\n", "        Axis of `y` along which the `central` statistics might be calculated or\n", "        along which the target will be subtracted.\n", "    central : Python function, optional, default = np.nanmedian\n", "        Function to calculate statistics on `y` w.r.t. mse is computed.\n", "    normalization : Python function, optional, default = np.nanmedian\n", "        Function to normalize the calculated mse values\n", "\n", "    Returns\n", "    -------\n", "    score : numpy.n<PERSON><PERSON>\n", "        Mean Squared Error values\n", "\n", "    References\n", "    ----------\n", "    .. [1] https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Similarity.ipynb\n", "\n", "    Examples\n", "    --------\n", "    >>> import numpy as np\n", "    >>> rng = np.random.default_rng()\n", "    >>> y = rng.random((100, 10))\n", "    >>> y = y + np.atleast_2d(np.sin(2*np.pi*np.linspace(0, 1, 100))).T\n", "    >>> mse(y, axis1=0, axis2=1, central=np.nanmedian, normalization=np.nanmedian)\n", "\n", "    Version history\n", "    ---------------\n", "    '1.0.0':\n", "        First release version\n", "    \"\"\"\n", "\n", "    logger.debug('mse...')\n", "\n", "    score: np.ndarray = np.empty((y.shape[axis2]), dtype=float)\n", "    score.fill(np.nan)\n", "    idx: np.ndarray = np.where(~np.all(np.isnan(y), axis=axis1))[0]\n", "    y = y.swapaxes(0, axis2)[idx, ...].swapaxes(0, axis2)  # faster than .take\n", "    if target is not None:\n", "        logger.debug('target shape: %s', target.shape)\n", "        score[idx] = np.nanmean((y - target)**2, axis=axis1)\n", "    else:\n", "        score[idx] = np.nanmean((y - central(y, axis=axis2, keepdims=True))**2, axis=axis1)\n", "\n", "    if normalization is not None:\n", "        score = score/normalization(score)\n", "    logger.debug('idx: %s, score: %s', idx, score)\n", "\n", "    return score\n", "\n", "\n", "def similarity(y: np.n<PERSON><PERSON>, axis1: int = 0, axis2: int = 1, threshold: float = 0,\n", "               nmin: int = 3, repeat: bool = True, metric: Callable = mse,\n", "               drop=True, msg: bool = True, **kwargs: Callable\n", "               ) -> tuple[np.n<PERSON><PERSON>, np.n<PERSON><PERSON>, np.ndarray, np.ndarray]:\n", "\n", "    \"\"\"Select vectors in array by their similarity using a metric score.\n", "\n", "    For example, if `y` is a 2-D numpy.ndarray, with shape (n, m), `axis1`=0\n", "    (n is the number of rows) and `axis2`=1 (m is the number of columns), this\n", "    function will select the vectors along the columns, that are more similar\n", "    to a `central` statistics of `y` or to a `target`, using a `metric` score.\n", "    The metric score can be calculated repeatedly until all selected vectors\n", "    have a `metric` score not greater than a `threshold`, but the minimum\n", "    number of vectors to keep or the maximum number of vectors to discard\n", "    can be specified with parameter `nmin`.\n", "\n", "    The default `metric` and target are the mean squared error (`mse`) of `y`\n", "    w.r.t. the median of `y` along `axis2`. The `mse` metric is equivalent\n", "    to the squared Euclidean distance and it is prefered because it\n", "    penalizes largest differences more than the Euclidian distance. But any\n", "    other `metric` that can be calculated with a function can be used.\n", "\n", "    A possible use of this function is to discard the time-series data from bad\n", "    trials (treated as outliers) stored in a 2-D array (each trial as a column\n", "    and instants as rows) where the criterion is the similarity of the trial\n", "    w.r.t. the median trial (the median statistics is more robust to remove\n", "    outliers than the mean in case there are very bad trials). After the bad\n", "    trials are discarded, the mean of all trials could then be calculated more\n", "    reliablly.\n", "\n", "    Parameters\n", "    ----------\n", "    y : numpy.n<PERSON><PERSON>\n", "        Array for the calculation of similarity (defined by a `metric`) of its\n", "        vectors w.r.t. a `target` or a `central` statistics of `y`.\n", "    axis1 : integer, optional, default = 0\n", "        Axis of `y` for which the `metric` will be calculated at each value and\n", "        possibly averaged in the `metric` calculation.\n", "    axis2 : integer, optional, default = 1\n", "        Axis of `y` along which the different vectors are going to be compared\n", "        with the `threshold` for their similarity (using their `metric` score).\n", "    threshold : float, optional, default = 0\n", "        If greater than 0, vector with `metric` score above this value will be\n", "        discarded. If 0, threshold will be automatically calculated as the\n", "        minimum of [qs[2] + 1.5*(qs[2]-qs[0]), score[-2], 3], where qs are the\n", "        quantiles and score[-2] is the before-last largest score of `metric`\n", "        among vectors calculated at the first time, not updated by the `repeat`\n", "        option.\n", "    nmin : integer, optional, default = 3\n", "        If greater than 0, minumum number of vectors to keep.\n", "        If lower than 0, maximum number of vectors to discard.\n", "    repeat : bool, optional, default = True\n", "        Whether to calculate similarity `metric` repeatedly, updating the\n", "        score calculation each time a vector is discarded.\n", "        With `repeat` True, the output `scores` will contain at each row\n", "        the updated score values for the used `metric` for each data vector.\n", "        The first row will contain the calculated original scores before any\n", "        vector was discarded. On the next equent rows, the vectors discarded\n", "        are represented by NaN values and the kept vectors by their updated\n", "        scores.\n", "        The last row will contain the updated scores of the final vectors kept.\n", "        With the `repeat` False, the comparison of score values with\n", "        `threshold` are made only once and vectors are discarded accordingly at\n", "        once. In this case, the output `scores` will contain only two rows, the\n", "        first row will contain the calculated original scores before any\n", "        vectors were discarded. At the second row, the vectors discarded are\n", "        represented with NaN values and the kept vectors by their updated\n", "        scores.\n", "    metric : optional, default=mse\n", "        Function to use as metric to compute similarity.\n", "    drop : bool, optional, default = True\n", "        Whether to drop (delete) the discarded vectors from `y` in the output.\n", "        If False, the values of the vectors discarded will be replaced by nans\n", "        and the returned array will have the same dimensions as the original\n", "        array.\n", "    msg : bool, optional, default = True\n", "        Whether to print some messages.\n", "    kwargs : optional\n", "        Options for the metric function (e.g., see `mse` function).\n", "\n", "    Returns\n", "    -------\n", "    y : numpy.n<PERSON><PERSON>\n", "        Array similar to input `y` but with vectors discarded (deleted) if\n", "        option `drop` is True or with all values of vectors discarded replaced\n", "        by nans if option `drop` is False.\n", "    ikept : numpy.n<PERSON><PERSON>\n", "        Indexes of kept vectors.\n", "    inotkept : numpy.n<PERSON><PERSON>\n", "        Indexes of not kept (discarded or replaced by nan) vectors.\n", "    scores : 2-<PERSON> numpy.ndarray\n", "        Metric score values of each vector (as columns) for each round of\n", "        vector selection (one row per round plus the final values).\n", "\n", "    References\n", "    ----------\n", "    .. [1] https://nbviewer.org/github/BMClab/BMC/blob/master/notebooks/Similarity.ipynb\n", "\n", "    Examples\n", "    --------\n", "    >>> import matplotlib.pyplot as plt\n", "    >>> rng = np.random.default_rng()\n", "    >>> t, n = 100, 10\n", "    >>> y = rng.random((t, n)) / 2\n", "    >>> y = y + np.atleast_2d(2*np.sin(2*np.pi*np.linspace(0, 1, t))).T\n", "    >>> for i in range(0, n, 2):\n", "    >>>    j = rng.integers(t-20)\n", "    >>>    p = rng.integers(20)\n", "    >>>    y[j:j+p, i] = y[j:j+p, i] + rng.integers(10) - 5\n", "    >>>    y[:, i] += rng.integers(4) - 2\n", "    >>> ysr, ikeptr, inotkeptr, scoresr = similarity(y)\n", "    >>> ysn, ikeptn, inotkeptn, scoresn = similarity(y, repeat=False)\n", "    >>> fig, axs = plt.subplots(3, 1, sharex=True, figsize=(8, 8))\n", "    >>> axs[0].plot(y, label=list(range(n)))\n", "    >>> axs[0].legend(loc=(1.01, 0), ncol=2)\n", "    >>> axs[0].set_title(f'Original vectors (n={n})')\n", "    >>> axs[1].plot(ysr, label= ikeptr.tolist())\n", "    >>> axs[1].set_title(f'Vectors maintained with repeat selection (n={len(ikeptr)})')\n", "    >>> axs[1].legend(loc=(1.01, 0))\n", "    >>> axs[2].plot(ysn, label= ikeptn.tolist())\n", "    >>> axs[2].set_title(f'Vectors maintained without repeat selection (n={len(ikeptn)})')\n", "    >>> axs[2].legend(loc=(1.01, 0))\n", "    >>> plt.show()\n", "\n", "    Version history\n", "    ---------------\n", "    '1.0.0':\n", "        First release version\n", "    \"\"\"\n", "\n", "    logger.debug('Similarity...')\n", "\n", "    if y.ndim < 2:\n", "        raise ValueError('The input array must be at least a 2-D array.')\n", "    y = y.copy()\n", "    score: np.n<PERSON><PERSON> = metric(y, axis1=axis1, axis2=axis2, **kwargs)\n", "    scores: np.ndarray = np.atleast_2d(score)\n", "    ikept: np.ndarray = np.where(~np.isnan(score))[0]  # indexes of kept vectors\n", "    inotkept: np.ndarray = np.where(np.isnan(score))[0]  # indexes of discarded vectors\n", "    idx: np.n<PERSON>ray = np.argsort(score)\n", "    score = score[idx]\n", "    nkept: int = np.count_nonzero(~np.isnan(score))  # number of kept vectors\n", "    if nkept < 3:\n", "        logger.debug('nkept: %s', nkept)\n", "        raise ValueError('The input array must have at least 3 valid vectors.')\n", "    if nmin < 0:\n", "        nmin = np.max([3, nkept + nmin])\n", "    if threshold == 0:  # automatic threshold calculation\n", "        qs: np.n<PERSON><PERSON> = np.nanquantile(a=score, q=[.25, .50, .75])\n", "        threshold = np.min([qs[2] + 1.5*(qs[2]-qs[0]), score[-2], 3.])\n", "        if msg:\n", "            print(f'Calculated threshold: {threshold}')\n", "\n", "    if not repeat:  # discard all vectors at once\n", "        idx2: np.ndarray = np.nonzero(score > threshold)[0]  # vectors to discard\n", "        if len(idx2) > 0:\n", "            if nkept > nmin:  # keep at least nmin vectors\n", "                inotkept = np.r_[inotkept, idx[idx2[-(y.shape[axis2] - nmin):]][::-1]]\n", "                y.swapaxes(0, axis2)[inotkept, ...] = np.nan\n", "                score = metric(y, axis1=axis1, axis2=axis2, **kwargs)\n", "                scores = np.vstack((scores, score))\n", "                logger.debug('not repeat - score: %s', score)\n", "    else:  # discard vector with largest updated score one by one\n", "        while nkept > nmin and score[nkept-1] > threshold:\n", "            inotkept = np.r_[inotkept, idx[nkept-1]]\n", "            y.swapaxes(0, axis2)[inotkept[-1], ...] = np.nan\n", "            score = metric(y, axis1=axis1, axis2=axis2, **kwargs)\n", "            scores = np.vstack((scores, score))\n", "            idx = np.argsort(score)\n", "            score = score[idx]\n", "            nkept = nkept - 1\n", "            logger.debug('repeat - nkept: %s, score: %s', nkept, score)\n", "\n", "    if len(inotkept):\n", "        ikept = np.setdiff1d(ikept, inotkept)\n", "        if drop:\n", "            y = y.swapaxes(0, axis2)[ikept, ...].swapaxes(0, axis2)\n", "        if msg:\n", "            print(\n", "                  f'Vectors discarded (dimension {axis2}, n={len(inotkept)}): {inotkept}')\n", "\n", "    return y, ikept, inotkept, scores\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}