{"cells": [{"cell_type": "markdown", "metadata": {"id": "PdleV478DnIu"}, "source": ["# Kinematics of particle\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"id": "3z2quwjuDnIy"}, "source": ["<div style=\"background-color:#F1F1F1;border:1px solid black;padding:10px;\">\n", "    <figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/usain_bolt_berlin2009.png?raw=1\" width=700 alt=\"Momentary velocity vs location for Usain Bolt\"/><figcaption><center><br><i><b>Figure. Momentary velocity vs location for <PERSON><PERSON> in the men’s 100m final at the IAAF World Championships in Athletics, Berlin 2009. This measurement represents the velocity of the body (considered as a particle) and was measured with a laser radar. From <a href=\"http://www.meathathletics.ie/devathletes/pdf/Biomechanics%20of%20Sprints.pdf\"><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011)</a>.</b></i></center></figcaption></figure>\n", " </div>"]}, {"cell_type": "markdown", "metadata": {"id": "P4qSUMLfDnIz", "toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Biomechanics-&amp;-Mechanics\" data-toc-modified-id=\"Biomechanics-&amp;-Mechanics-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Biomechanics &amp; Mechanics</a></span></li><li><span><a href=\"#Kinematics\" data-toc-modified-id=\"Kinematics-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Kinematics</a></span><ul class=\"toc-item\"><li><span><a href=\"#Vectors-in-Kinematics\" data-toc-modified-id=\"Vectors-in-Kinematics-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>Vectors in Kinematics</a></span></li></ul></li><li><span><a href=\"#Position\" data-toc-modified-id=\"Position-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Position</a></span></li><li><span><a href=\"#Basis\" data-toc-modified-id=\"Basis-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Basis</a></span></li><li><span><a href=\"#Displacement\" data-toc-modified-id=\"Displacement-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Displacement</a></span></li><li><span><a href=\"#Velocity\" data-toc-modified-id=\"Velocity-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Velocity</a></span></li><li><span><a href=\"#Acceleration\" data-toc-modified-id=\"Acceleration-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Acceleration</a></span></li><li><span><a href=\"#The-antiderivative\" data-toc-modified-id=\"The-antiderivative-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>The antiderivative</a></span></li><li><span><a href=\"#Some-cases-of-motion-of-a-particle\" data-toc-modified-id=\"Some-cases-of-motion-of-a-particle-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>Some cases of motion of a particle</a></span><ul class=\"toc-item\"><li><ul class=\"toc-item\"><li><span><a href=\"#Particle-at-rest\" data-toc-modified-id=\"Particle-at-rest-10.0.1\"><span class=\"toc-item-num\">10.0.1&nbsp;&nbsp;</span>Particle at rest</a></span></li><li><span><a href=\"#Particle-at-constant-speed\" data-toc-modified-id=\"Particle-at-constant-speed-10.0.2\"><span class=\"toc-item-num\">10.0.2&nbsp;&nbsp;</span>Particle at constant speed</a></span></li><li><span><a href=\"#Particle-at-constant-acceleration\" data-toc-modified-id=\"Particle-at-constant-acceleration-10.0.3\"><span class=\"toc-item-num\">10.0.3&nbsp;&nbsp;</span>Particle at constant acceleration</a></span></li></ul></li><li><span><a href=\"#Values-for-the-mechanical-quantities\" data-toc-modified-id=\"Values-for-the-mechanical-quantities-10.1\"><span class=\"toc-item-num\">10.1&nbsp;&nbsp;</span>Values for the mechanical quantities</a></span></li><li><span><a href=\"#Visual-representation-of-these-cases\" data-toc-modified-id=\"Visual-representation-of-these-cases-10.2\"><span class=\"toc-item-num\">10.2&nbsp;&nbsp;</span>Visual representation of these cases</a></span></li></ul></li><li><span><a href=\"#Symbolic-programming\" data-toc-modified-id=\"Symbolic-programming-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>Symbolic programming</a></span></li><li><span><a href=\"#Kinematics-of-human-movement\" data-toc-modified-id=\"Kinematics-of-human-movement-12\"><span class=\"toc-item-num\">12&nbsp;&nbsp;</span>Kinematics of human movement</a></span><ul class=\"toc-item\"><li><span><a href=\"#Kinematics-of-the-100-m-race\" data-toc-modified-id=\"Kinematics-of-the-100-m-race-12.1\"><span class=\"toc-item-num\">12.1&nbsp;&nbsp;</span>Kinematics of the 100-m race</a></span></li></ul></li><li><span><a href=\"#More-examples\" data-toc-modified-id=\"More-examples-13\"><span class=\"toc-item-num\">13&nbsp;&nbsp;</span>More examples</a></span></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-14\"><span class=\"toc-item-num\">14&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-15\"><span class=\"toc-item-num\">15&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-16\"><span class=\"toc-item-num\">16&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-17\"><span class=\"toc-item-num\">17&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "cFoLLOP2DnI1"}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:12:06.145171Z", "start_time": "2021-10-16T00:12:05.900880Z"}, "id": "vx5Gjo5rDnI1"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {"id": "RFjJVb2UDnI3"}, "source": ["## Biomechanics & Mechanics\n", "\n", "**A good knowledge of Mechanics is a necessary condition, although not sufficient!, to master Biomechanics**\n", "\n", "For this reason, we will review principles of Classical Mechanics in the context of Biomechanics.  \n", "\n", "The book [*Introduction to Statics and Dynamics*](http://ruina.tam.cornell.edu/Book/index.html), written by <PERSON> and <PERSON><PERSON><PERSON>, is an excellent reference (a rigorous and yet didactic presentation of Mechanics for undergraduate students) on Classical Mechanics. The preface and first chapter of the book are a good read on how someone should study Mechanics. You should read them!\n", "\n", "Most of the content in this notebook is covered in chapter 12 of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) and in chapter 1 of the <PERSON><PERSON>'s book.\n", "\n", "As we argued in the notebook [Biomechanics](https://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/Biomechanics.ipynb), we will start with a branch of Classical Mechanics that is simpler to measure its related quantities on biological systems, the Kinematics.  \n", "\n", "There are some relevant cases in the study of human movement where modeling the human body or one of its segments as a particle might be all we need to explore the phenomenon. The concept of kinematics of a particle, for instance, can be applied to study the performance in the 100-m race; to describe spatial and temporal characteristics of a movement pattern, and to conjecture about how voluntary movements are planned (the minimum jerk hypothesis).  \n", "\n", "Now, let's review the concept of kinematics of a particle and later apply to the study of human movement."]}, {"cell_type": "markdown", "metadata": {"id": "67L2d49pDnI4"}, "source": ["## Kinematics\n", "\n", "**Kinematics** is the branch of Classical Mechanics that describes the motion of objects without consideration of the causes of motion ([Wikipedia](http://en.wikipedia.org/wiki/Kinematics)).  \n", "\n", "Kinematics of a particle is the description of the motion when the object is considered a particle.  \n", "\n", "A particle as a physical object does not exist in nature; it is a simplification to understand the motion of a body or it is a conceptual definition such as the center of mass of a system of objects."]}, {"cell_type": "markdown", "metadata": {"id": "MR3kjyncDnI5"}, "source": ["### Vectors in Kinematics\n", "\n", "Some mechanical quantities in Kinematics (position and its derivatives) are represented as vectors and others, such as time and distance, are scalars.  \n", "A vector in Mechanics is a physical quantity with magnitude, direction, and satisfies some elementary vector arithmetic, whereas a scalar is a physical quantity that is fully expressed by a magnitude (a number) only.  \n", "\n", "For a review about scalars and vectors, see chapter 1 of [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html).  \n", "\n", " For how to use Python to work with scalars and vectors, see the notebook [Scalar and Vector](http://nbviewer.jupyter.org/github/BMCLab/BMC/blob/master/notebooks/ScalarVector.ipynb)."]}, {"cell_type": "markdown", "metadata": {"id": "yKb0RRJfDnI6"}, "source": ["## Position\n", "\n", "Consider a point in the three-dimensional Euclidean space described in a Cartesian coordinate system (see the notebook [Frame of reference](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/ReferenceFrame.ipynb) for an introduction on coordinate systems in Mechanics and Biomechanics):  \n", "<br>\n", "<figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/vector3Dijk.png?raw=1\" width=350/><figcaption><center><i>Figure. Representation of a point $\\mathbf{P}$ and its position vector $\\overrightarrow{\\mathbf{r}}$ in a Cartesian coordinate system. The versors <span class=\"notranslate\">$\\hat{\\mathbf{i}},\\, \\hat{\\mathbf{j}},\\, \\hat{\\mathbf{k}}\\,$ </span> form a basis for this coordinate system and are usually represented in the color sequence RGB (red, green, blue) for easier visualization.</i></center></figcaption></figure>  "]}, {"cell_type": "markdown", "metadata": {"id": "04mjNRf2DnI7"}, "source": ["The position of this point in space can be represented as a triple of values each representing the coordinate at each axis of the Cartesian coordinate system following the $ \\mathbf{X, Y, Z} $ convention order (which is omitted):\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "(x,\\, y,\\, z)\n", "\\label{eq_xyz}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "k-tE51YtDnI7"}, "source": ["The position of a particle in space can also be represented by a vector in the Cartesian coordinate system, with the origin of the vector at the origin of the coordinate system and the tip of the vector at the point position:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{r}}(t) = x\\,\\hat{\\mathbf{i}} + y\\,\\hat{\\mathbf{j}} + z\\,\\hat{\\mathbf{k}}\n", "\\label{eq_rxyz}\n", "\\end{equation}    \n", "</span>\n", "\n", "Where <span class=\"notranslate\"> $\\hat{\\mathbf{i}},\\, \\hat{\\mathbf{j}},\\, \\hat{\\mathbf{k}}\\,$ </span> are unit vectors in the directions of the axes $ \\mathbf{X, Y, Z} $.\n", "\n", "For a review on vectors, see the notebook [Scalar and vector](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/ScalarVector.ipynb)."]}, {"cell_type": "markdown", "metadata": {"id": "0yW1Hcy_DnI8"}, "source": ["With this new notation, the coordinates of a point representing the position of a particle that vary with time would be expressed by the following position vector $\\overrightarrow{\\mathbf{r}}(t)$:   \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{r}}(t) = x(t)\\,\\hat{\\mathbf{i}} + y(t)\\,\\hat{\\mathbf{j}} + z(t)\\,\\hat{\\mathbf{k}}\n", "\\label{eq_rxyzijk}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "J3fKhK-3DnI8"}, "source": ["A vector can also be represented in matrix form:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{r}}(t) = \\begin{bmatrix} x(t) \\\\y(t) \\\\z(t) \\end{bmatrix}\n", "\\label{eq_rxyzmatrix}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "MPBxiqMBDnI9"}, "source": ["And the unit vectors in each Cartesian coordinate in matrix form are given by:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\hat{\\mathbf{i}} = \\begin{bmatrix}1\\\\0\\\\0 \\end{bmatrix},\\; \\hat{\\mathbf{j}} = \\begin{bmatrix}0\\\\1\\\\0 \\end{bmatrix},\\; \\hat{\\mathbf{k}} = \\begin{bmatrix} 0 \\\\ 0 \\\\ 1 \\end{bmatrix}\n", "\\label{eq_ijk}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "mHDusQAFDnI9"}, "source": ["## Basis\n", "\n", "In [linear algebra](http://en.wikipedia.org/wiki/Linear_algebra), a set of unit linearly independent vectors as the three vectors above (orthogonal in the Euclidean space) that can represent any vector via [linear combination](http://en.wikipedia.org/wiki/Linear_combination) is called a basis. A basis is the foundation of creating a reference frame and we will study how to do that other time."]}, {"cell_type": "markdown", "metadata": {"id": "xbEqEJjsDnI9"}, "source": ["## Displacement\n", "\n", "The shortest distance between two positions of a particle.\n", "\n", "As the difference between two vectors, displacement is also a vector quantity.\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{\\overrightarrow{d}} = \\mathbf{\\overrightarrow{r}_2} - \\mathbf{\\overrightarrow{r}_1}\n", "\\label{eq_distance}\n", "\\end{equation}\n", "</span>\n", "\n", "<figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/displacement.png?raw=1\" width=450/><figcaption><center><i>Figure. Representation of the displacement vector $\\mathbf{\\overrightarrow{d}}$ between two positions $\\mathbf{\\overrightarrow{r}_1}$ and $\\mathbf{\\overrightarrow{r}_2}$.</i></center></figcaption></figure>  "]}, {"cell_type": "markdown", "metadata": {"id": "ouI8zbCmDnI-"}, "source": ["## Velocity\n", "\n", "Velocity is the rate (with respect to time) of change of the position of a particle.  \n", "\n", "The average velocity between two instants is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{v}}(t) = \\frac{\\overrightarrow{\\mathbf{r}}(t_2)-\\overrightarrow{\\mathbf{r}}(t_1)}{t_2-t_1} = \\frac{\\Delta \\overrightarrow{\\mathbf{r}}}{\\Delta t}\n", "\\label{eq_velocity}\n", "\\end{equation}\n", "</span>    \n", "\n", "The instantaneous velocity of the particle is obtained when $\\Delta t$ approaches to zero, which from calculus is the first-order [derivative](http://en.wikipedia.org/wiki/Derivative) of the position vector:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{v}}(t) = \\lim_{\\Delta t \\to 0} \\frac{\\Delta \\overrightarrow{\\mathbf{r}}}{\\Delta t} = \\lim_{\\Delta t \\to 0} \\frac{\\overrightarrow{\\mathbf{r}}(t+\\Delta t)-\\overrightarrow{\\mathbf{r}}(t)}{\\Delta t} = \\frac{\\mathrm{d}\\overrightarrow{\\mathbf{r}}}{dt}\n", "\\label{eq_velocityderiv}\n", "\\end{equation}\n", "</span>\n", "\n", "For the movement of a particle described with respect to an [inertial Frame of reference](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/ReferenceFrame.ipynb), the derivative of a vector is obtained by differentiating each vector component of the Cartesian coordinates (since the base versors <span class=\"notranslate\"> $\\hat{\\mathbf{i}}, \\hat{\\mathbf{j}}, \\hat{\\mathbf{k}}$ </span> are constant):   \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{v}}(t) = \\frac{\\mathrm{d}\\overrightarrow{\\mathbf{r}}(t)}{dt} = \\frac{\\mathrm{d}x(t)}{\\mathrm{d}t}\\hat{\\mathbf{i}} + \\frac{\\mathrm{d}y(t)}{\\mathrm{d}t}\\hat{\\mathbf{j}} + \\frac{\\mathrm{d}z(t)}{\\mathrm{d}t}\\hat{\\mathbf{k}}\n", "\\label{eq_velocityderiv2}\n", "\\end{equation}\n", "</span>\n", "\n", "Or in matrix form (and using the Newton's notation for differentiation):\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{v}}(t) = \\begin{bmatrix}\n", "\\dot x(t) \\\\\n", "\\dot y(t) \\\\\n", "\\dot z(t)\n", "\\end{bmatrix}\n", "\\label{eq_velocityderiv3}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "8T6OCR1lDnI-"}, "source": ["## Acceleration  \n", "\n", "Acceleration is the rate (with respect to time) of change of the velocity of a particle, which can also be given by the second-order rate of change of the position.\n", "\n", "The average acceleration between two instants is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{a}}(t) = \\frac{\\overrightarrow{\\mathbf{v}}(t_2)-\\overrightarrow{\\mathbf{v}}(t_1)}{t_2-t_1} = \\frac{\\Delta \\overrightarrow{\\mathbf{v}}}{\\Delta t}\n", "\\label{eq_acc}\n", "\\end{equation}\n", "</span>\n", "\n", "Likewise, instantaneous acceleration is the first-order derivative of the velocity or the second-order derivative of the position vector:   \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{a}}(t) = \\frac{\\mathrm{d}\\overrightarrow{\\mathbf{v}}(t)}{\\mathrm{d}t} = \\frac{\\mathrm{d}^2\\overrightarrow{\\mathbf{r}}(t)}{\\mathrm{d}t^2} = \\frac{\\mathrm{d}^2x(t)}{\\mathrm{d}t^2}\\hat{\\mathbf{i}} + \\frac{\\mathrm{d}^2y(t)}{\\mathrm{d}t^2}\\hat{\\mathbf{j}} + \\frac{\\mathrm{d}^2z(t)}{\\mathrm{d}t^2}\\hat{\\mathbf{k}}\n", "\\label{eq_accderiv}\n", "\\end{equation}\n", "</span>\n", "\n", "And in matrix form:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\overrightarrow{\\mathbf{a}}(t) = \\begin{bmatrix}\n", "\\ddot x(t) \\\\\n", "\\ddot y(t) \\\\\n", "\\ddot z(t)\n", "\\end{bmatrix}\n", "\\label{eq_accderiv2}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "5KQj-kJ3DnI-"}, "source": ["For curiosity, see [Notation for differentiation](https://en.wikipedia.org/wiki/Notation_for_differentiation) on the origin of the different notations for differentiation.\n", "\n", "When the base versors change in time, for instance when the basis is attached to a rotating frame or reference, the components of the vector’s derivative is not the derivatives of its components; we will also have to consider the derivative of the basis with respect to time."]}, {"cell_type": "markdown", "metadata": {"id": "ozi7lb-oDnI_"}, "source": ["## The antiderivative\n", "\n", "As the acceleration is the derivative of the velocity which is the derivative of position, the inverse mathematical operation is the [antiderivative](http://en.wikipedia.org/wiki/Antiderivative) (or integral):\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{r}(t) = \\mathbf{r}_0 + \\int \\mathbf{v}(t) \\:\\mathrm{d}t \\\\\n", "\\mathbf{v}(t) = \\mathbf{v}_0 + \\int \\mathbf{a}(t) \\:\\mathrm{d}t\n", "\\end{array}\n", "\\label{eq_antiderivative}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "vqEhK3toDnI_"}, "source": ["## Some cases of motion of a particle\n", "\n", "To deduce some trivial cases of motion of a particle (at rest, at constant speed, and at constant acceleration), we can start from the equation for its position and differentiate it to obtain expressions for the velocity and acceleration or the inverse approach, start with the equation for acceleration, and then integrate it to obtain the velocity and position of the particle. Both approachs are valid in Mechanics. For the present case, it probaly makes more sense to start with the expression for acceleration.\n", "\n", "#### Particle at rest\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\overrightarrow{\\mathbf{a}}(t) = 0 \\\\\n", "\\overrightarrow{\\mathbf{v}}(t) = 0 \\\\\n", "\\overrightarrow{\\mathbf{r}}(t) = \\overrightarrow{\\mathbf{r}}_0\n", "\\end{array}\n", "\\label{eq_rest}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "A2CDyS05DnI_"}, "source": ["#### Particle at constant speed\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\overrightarrow{\\mathbf{a}}(t) = 0 \\\\\n", "\\overrightarrow{\\mathbf{v}}(t) = \\overrightarrow{\\mathbf{v}}_0 \\\\\n", "\\overrightarrow{\\mathbf{r}}(t) = \\overrightarrow{\\mathbf{r}}_0 + \\overrightarrow{\\mathbf{v}}_0t\n", "\\end{array}\n", "\\label{eq_constantspeed}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "HklnapTXDnJA"}, "source": ["#### Particle at constant acceleration\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\overrightarrow{\\mathbf{a}}(t) = \\overrightarrow{\\mathbf{a}}_0 \\\\\n", "\\overrightarrow{\\mathbf{v}}(t) = \\overrightarrow{\\mathbf{v}}_0 + \\overrightarrow{\\mathbf{a}}_0t \\\\\n", "\\overrightarrow{\\mathbf{r}}(t) = \\overrightarrow{\\mathbf{r}}_0 + \\overrightarrow{\\mathbf{v}}_0t +\n", "\\frac{1}{2}\\overrightarrow{\\mathbf{a}}_0 t^2\n", "\\end{array}\n", "\\label{eq_constantacceleration}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"id": "qoJu_CDlDnJA"}, "source": ["### Values for the mechanical quantities"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:12:09.878569Z", "start_time": "2021-10-16T00:12:09.875239Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "id": "zg77xmo-DnJA", "outputId": "2191b553-7843-44eb-af6b-1a56d8e9d5b7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t: [0.   0.02 0.04 0.06 0.08 0.1  0.12 0.14 0.16 0.18 0.2  0.22 0.24 0.26\n", " 0.28 0.3  0.32 0.34 0.36 0.38 0.4  0.42 0.44 0.46 0.48 0.5  0.52 0.54\n", " 0.56 0.58 0.6  0.62 0.64 0.66 0.68 0.7  0.72 0.74 0.76 0.78 0.8  0.82\n", " 0.84 0.86 0.88 0.9  0.92 0.94 0.96 0.98 1.   1.02 1.04 1.06 1.08 1.1\n", " 1.12 1.14 1.16 1.18 1.2  1.22 1.24 1.26 1.28 1.3  1.32 1.34 1.36 1.38\n", " 1.4  1.42 1.44 1.46 1.48 1.5  1.52 1.54 1.56 1.58 1.6  1.62 1.64 1.66\n", " 1.68 1.7  1.72 1.74 1.76 1.78 1.8  1.82 1.84 1.86 1.88 1.9  1.92 1.94\n", " 1.96 1.98 2.  ]\n", "r: [ 1.      1.0408  1.0832  1.1272  1.1728  1.22    1.2688  1.3192  1.3712\n", "  1.4248  1.48    1.5368  1.5952  1.6552  1.7168  1.78    1.8448  1.9112\n", "  1.9792  2.0488  2.12    2.1928  2.2672  2.3432  2.4208  2.5     2.5808\n", "  2.6632  2.7472  2.8328  2.92    3.0088  3.0992  3.1912  3.2848  3.38\n", "  3.4768  3.5752  3.6752  3.7768  3.88    3.9848  4.0912  4.1992  4.3088\n", "  4.42    4.5328  4.6472  4.7632  4.8808  5.      5.1208  5.2432  5.3672\n", "  5.4928  5.62    5.7488  5.8792  6.0112  6.1448  6.28    6.4168  6.5552\n", "  6.6952  6.8368  6.98    7.1248  7.2712  7.4192  7.5688  7.72    7.8728\n", "  8.0272  8.1832  8.3408  8.5     8.6608  8.8232  8.9872  9.1528  9.32\n", "  9.4888  9.6592  9.8312 10.0048 10.18   10.3568 10.5352 10.7152 10.8968\n", " 11.08   11.2648 11.4512 11.6392 11.8288 12.02   12.2128 12.4072 12.6032\n", " 12.8008 13.    ]\n"]}], "source": ["t = np.linspace(0, 2, 101)\n", "print('t:', t)\n", "\n", "r0 = 1\n", "v0 = 2\n", "a0 = 4\n", "r = r0 + v0*t + 1/2*a0*t**2\n", "print('r:', r)"]}, {"cell_type": "markdown", "metadata": {"id": "7IhewV2PDnJB"}, "source": ["### Visual representation of these cases"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:13:05.314319Z", "start_time": "2021-10-16T00:13:05.227480Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 430}, "id": "swkO6jogDnJB", "outputId": "e20813ad-5b42-4a03-f9b1-9fce77755477"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# a simple plot\n", "plt.plot(t, r);"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:12:10.063562Z", "start_time": "2021-10-16T00:12:10.063554Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 694}, "id": "ftS4MfqRDnJC", "outputId": "8da438af-6ef4-451a-959e-604b7c3a9091"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1400x700 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# a more decorated plot\n", "plt.rc('axes',  labelsize=14,  titlesize=14)\n", "plt.rc('xtick', labelsize=10)\n", "plt.rc('ytick', labelsize=10)\n", "f, axarr = plt.subplots(3, 3, sharex = True, sharey = True, figsize=(14,7))\n", "plt.suptitle('Scalar kinematics of a particle', fontsize=20);\n", "\n", "tones = np.ones(np.size(t))\n", "\n", "axarr[0, 0].set_title('at rest', fontsize=14);\n", "axarr[0, 0].plot(t, r0*tones, 'g', linewidth=4, label='$r(t)=1$')\n", "axarr[1, 0].plot(t,  0*tones, 'b', linewidth=4, label='$v(t)=0$')\n", "axarr[2, 0].plot(t,  0*tones, 'r', linewidth=4, label='$a(t)=0$')\n", "axarr[0, 0].set_ylabel('r(t) [m]')\n", "axarr[1, 0].set_ylabel('v(t) [m/s]')\n", "axarr[2, 0].set_ylabel('a(t) [m/s$^2$]')\n", "\n", "axarr[0, 1].set_title('at constant speed');\n", "axarr[0, 1].plot(t, r0*tones+v0*t, 'g', linewidth=4, label='$r(t)=1+2t$')\n", "axarr[1, 1].plot(t, v0*tones,      'b', linewidth=4, label='$v(t)=2$')\n", "axarr[2, 1].plot(t,  0*tones,      'r', linewidth=4, label='$a(t)=0$')\n", "\n", "axarr[0, 2].set_title('at constant acceleration');\n", "axarr[0, 2].plot(t, r0*tones+v0*t+1/2.*a0*t**2,'g', linewidth=4,\n", "                 label='$r(t)=1+2t+\\\\frac{1}{2}4t^2$')\n", "axarr[1, 2].plot(t, v0*tones+a0*t,             'b', linewidth=4,\n", "                 label='$v(t)=2+4t$')\n", "axarr[2, 2].plot(t, a0*tones,                  'r', linewidth=4,\n", "                 label='$a(t)=4$')\n", "\n", "for i in range(3):\n", "    axarr[2, i].set_xlabel('Time [s]');\n", "    for j in range(3):\n", "        axarr[i,j].set_ylim((-.2, 10))\n", "        axarr[i,j].legend(loc = 'upper left', frameon=True, framealpha = 0.9, fontsize=14)\n", "\n", "plt.subplots_adjust(hspace=0.09, wspace=0.07)"]}, {"cell_type": "markdown", "metadata": {"id": "etd7IGRJDnJC"}, "source": ["## Symbolic programming\n", "\n", "We can use [Sympy](http://www.sympy.org/en/index.html), a Python library for symbolic mathematics, to deduce the expressions for the cases of motion of a particle we just visualized.  \n", "Let's show how to integrate with Sympy for the case of particle with constant acceleration:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:12:10.396031Z", "start_time": "2021-10-16T00:12:10.179336Z"}, "id": "Cda9l5jXDnJC"}, "outputs": [], "source": ["import sympy as sym\n", "sym.init_printing(use_latex='mathjax')  # print pretty symbols\n", "\n", "t = sym.symbols('t', real=True)\n", "r0, v0, a0 = sym.symbols('r0, v0, a0', real=True, constant=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:12:10.530627Z", "start_time": "2021-10-16T00:12:10.397043Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 39}, "id": "VNAyl0DoDnJD", "outputId": "9c0762ce-1645-42ae-be84-b604ae363417"}, "outputs": [{"data": {"text/latex": ["$\\displaystyle a_{0} t + v_{0}$"], "text/plain": ["a₀⋅t + v₀"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["v = sym.integrate(a0, t) + v0  # a constant has to be added\n", "v"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-10-16T00:12:10.540922Z", "start_time": "2021-10-16T00:12:10.534677Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "aVJrwFSODnJD", "outputId": "61a7eaad-044e-4201-de78-7f5f807ec8c7"}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{a_{0} t^{2}}{2} + r_{0} + t v_{0}$"], "text/plain": ["    2            \n", "a₀⋅t             \n", "───── + r₀ + t⋅v₀\n", "  2              "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["r = sym.integrate(v, t) + r0  # a constant has to be added\n", "r"]}, {"cell_type": "markdown", "metadata": {"id": "o7ey6LtjDnJD"}, "source": ["We can also plot a symbolic expression in a given range after we substitute the symbolic variables with numbers:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 307}, "id": "uyHPWpYDDnJD", "outputId": "b5584673-0002-4228-dfc0-0e04de3d9141"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sympy.plotting import plot\n", "\n", "r_n = r.subs({r0:1, v0:2, a0:4})  # r0=1 ... does not work!\n", "\n", "plot(r_n, (t, 0, 2), xlim=(0, 2), ylim=(0, 10), axis_center=(0, 0), line_color='g',\n", "     xlabel='Time [s]', ylabel='r(t) [m]', legend=True,\n", "     title='Scalar kinematics of a particle at constant acceleration',\n", "     backend='matplotlib', size=(5, 3));"]}, {"cell_type": "markdown", "metadata": {"id": "3-YPLaU_DnJD"}, "source": ["## Kinematics of human movement"]}, {"cell_type": "markdown", "metadata": {"id": "PjIoDB4zDnJE"}, "source": ["### Kinematics of the 100-m race\n", "\n", "An example where the analysis of some aspects of the human body movement can be reduced to the analysis of a particle is the study of the Biomechanics of the 100-m race.\n", "\n", "A technical report with the kinematic data for the 100-m world record by <PERSON><PERSON> can be downloaded from the [website for Research Projects](http://www.iaaf.org/development/research) from the International Association of Athletics Federations.  \n", "[Here is a direct link for that report](http://www.iaaf.org/download/download?filename=76ade5f9-75a0-4fda-b9bf-1b30be6f60d2.pdf&urlSlug=1-biomechanics-report-wc-berlin-2009-sprint). In particular, the following table shows the data for the three medalists in that race:  \n", "<br>\n", "<figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/Berlin2009_100m.png?raw=1\" width=700 alt=\"partial times of the 100m-race at Berlin 2009\"/><figcaption><center><i>Figure. Data from the three medalists of the 100-m dash in Berlin, 2009 (<a href=\"http://www.iaaf.org/download/download?filename=76ade5f9-75a0-4fda-b9bf-1b30be6f60d2.pdf&urlSlug=1-biomechanics-report-wc-berlin-2009-sprint)\">IAAF report</a>).</i></center></figcaption></figure>\n", "\n", "The column **RT** in the table above refers to the reaction time of each athlete. The IAAF has a very strict rule about reaction time: any athlete with a reaction time less than 100 ms is disqualified from the competition! See the website [Reaction Times and Sprint False Starts](http://condellpark.com/kd/reactiontime.htm) for a discussion about this rule.\n", "\n", "You can measure your own reaction time in a simple way visiting this website: [http://www.humanbenchmark.com/tests/reactiontime](http://www.humanbenchmark.com/tests/reactiontime).\n", "\n", "The article [A Kinematics Analysis Of Three Best 100 M Performances Ever](http://www.ncbi.nlm.nih.gov/pmc/articles/PMC3661886/) by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> presents a detailed kinematic analysis of 100-m races.  "]}, {"cell_type": "markdown", "metadata": {"id": "F2i-uf2-DnJE"}, "source": ["## More examples\n", "\n", " - From <PERSON><PERSON><PERSON>'s book, study the samples 12.2 and 12.3."]}, {"cell_type": "markdown", "metadata": {"id": "RxCzkwMmDnJE"}, "source": ["## Further reading\n", "\n", " - Read the preface and first chapter of the [<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about how someone should study Mechanics.  \n", " - See the notebook [Spatial and temporal characteristics](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/SpatialTemporalCharacteristcs.ipynb) about how the simple measurement of spatial and temporal kinematic variables can be very useful to describe the human gait.  \n", " - See the notebook [The minimum jerk hypothesis](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/MinimumJerkHypothesis.ipynb) about the conjecture that movements are performed (organized) with the smoothest trajectory possible.  "]}, {"cell_type": "markdown", "metadata": {"id": "kdSzIYg9DnJE"}, "source": ["## Video lectures on the Internet\n", "\n", " - Khan Academy: [One-dimensional motion](https://www.khanacademy.org/science/ap-physics-1/ap-one-dimensional-motion)  \n", " - [Powers of 10, Units, Dimensions, Uncertainties, Scaling Arguments](https://youtu.be/GtOGurrUPmQ)  \n", " - [1D Kinematics - Speed, Velocity, Acceleration](https://youtu.be/q9IWoQ199_o)  "]}, {"cell_type": "markdown", "metadata": {"id": "2W_A-TVQDnJF"}, "source": ["## Problems\n", "\n", "1. Answer the 12 questions of the [Khan Academy's test on one-dimensional motion](https://www.khanacademy.org/science/ap-physics-1/ap-one-dimensional-motion/test/ap-one-dimensional-motion-unit-test?modal=1).  \n", "\n", "2. Consider the data for the three medalists of the 100-m dash in Berlin, 2009, shown previously.  \n", "   a. Calculate the average velocity and acceleration.   \n", "   b. Plot the graphs for the displacement, velocity, and acceleration versus time.   \n", "   c. Plot the graphs velocity and acceleration versus partial distance (every 20m).   \n", "   d. Calculate the average velocity and average acceleration and the instants and values of the peak velocity and peak acceleration.  \n", "\n", "3. The article \"Biomechanical Analysis of the Sprint and Hurdles Events at the 2009 IAAF World Championships in Athletics\" by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> lists the 10-m split times for the three medalists of the 100-m dash in Berlin, 2009 and is shown below.  \n", "   a. Repeat the same calculations performed in problem 2 and compare the results.\n", "<br>\n", "<figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/Berlin2009_100m_10.png?raw=1\" width=600 alt=\"partial times of the 100m-race at Berlin 2009\"/></figure>  \n", "<br>   \n", "\n", "4. On an Olympic running track, runners A and <PERSON> start running on the first lane (a.k.a., the inside lane) from the same position on the track, but in opposite directions. If $\\lVert v_A \\rVert=4$ m/s and $\\lVert v_B \\rVert=6$ m/s, how far from the starting line will the runners meet?\n", "\n", "5. A body attached to a spring has its position (in cm) described by the equation $x(t) = 2\\sin(4\\pi t + \\pi/4)$.   \n", "   a. Calculate the equation for the body velocity and acceleration.   \n", "   b. Plot the position, velocity, and acceleration in the interval [0, 1] s.\n", "   \n", "6. The rectilinear motion of a particle is given by $x(t) = -12t^3 + 15t^2 + 5t + 2$ [s; m]. Calculate:  \n", "   a. Velocity and acceleration of the particle as a function of time. Solution: $v(t)=36t^2+30t+5$ [s; m/s], $a(t)=-72t+30$ [s; m/s2].  \n", "   b. Total distance traveled by the particle in the interval $0 \\leq t \\leq 4$ s. Solution: $\\Delta_{0-4}=524.02$m.  \n", "   c. Maximum value of the velocity module reached by the particle in the interval $0 \\leq t \\leq 4$ s. Solution: $\\lVert v_{max} \\rVert=451.0$m/s.  \n", "   d. Plots of the position, velocity and acceleration of the particle in the interval $0 \\leq t \\leq 4$ s.\n", "   \n", "7. A stone is released from the opening of a well, and the noise from its fall to the bottom is heard 4 seconds later. Knowing that the speed of sound in air is 340 m/s, determine the depth of the well. Solution: $h=70.55$m.  \n", "\n", "8. The position of a particle is given by $\\overrightarrow{\\mathbf{r}}(t)=(t^2\\hat{\\mathbf{i}}+e^{t}\\hat{\\mathbf{j}})$.    \n", "    a. Calculate the velocity and acceleration of the particle as functions of time.   \n", "    b. Draw the path of the particle and show the vectors $\\overrightarrow{\\mathbf{v}}(t)$ and $\\overrightarrow{\\mathbf{a}}(t)$ at $t=1$s.\n", "\n", "9. Sometimes all we have access to is the image with plotted data that interests us. For example, the first figure shown in this Notebook contains velocity versus position data for <PERSON><PERSON>'s 100m sprint at a much higher resolution than the numerical data shown here. If we want to access numerical data from an image, it is possible to extract this information using some software to automatically identify points of interest. If points cannot be extracted automatically, you may need to do it manually. The [WebPlotDigitizer](https://automeris.io/WebPlotDigitizer/) software is one such tool. Try using it to extract the data from the quoted figure. Note: it won't be easy; you may have to do this manually. Anyway, know that such a tool exists.\n", "\n", "10. There are some nice free software that can be used for the kinematic analysis of human motion. Some of them are: [<PERSON><PERSON><PERSON>](http://www.kinovea.org/), [Tracker](http://www.cabrillo.edu/~dbrown/tracker/), and [SkillSpector](http://video4coach.com/index.php?option=com_content&task=view&id=13&Itemid=45). Visit their websites and explore these software to understand in which biomechanical applications they could be used.  "]}, {"cell_type": "markdown", "metadata": {"id": "hnjwWLMjDnJF"}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2011) [Biomechanical Analysis of the Sprint and Hurdles Events at the 2009 IAAF World Championships in Athletics ](http://www.meathathletics.ie/devathletes/pdf/Biomechanics%20of%20Sprints.pdf). [New Studies in Athletics](http://www.iaaf.org/development/new-studies-in-athletics), 1/2, 19-53.\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013) [A Kinematics Analysis Of Three Best 100 M Performances Ever](http://www.ncbi.nlm.nih.gov/pmc/articles/PMC3661886/). Journal of Human Kinetics, 36, 149–160.\n", "- [Research Projects](http://www.iaaf.org/development/research) from the International Association of Athletics Federations.  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  "]}], "metadata": {"anaconda-cloud": {}, "colab": {"provenance": []}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}