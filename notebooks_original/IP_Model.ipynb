{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# The inverted pendulum model of the human standing\n", "\n", "<PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Despite the enormous complexity of the human body, part of the mechanical behavior of the human body during the standing still posture, namely the displacements of the center of gravity ($COG$) and center of pressure ($COP$) in the anterior-posterior direction, can be elegantly portraied by a physical-mathematical model of an inverted pendulum with rigid segments articulated by joints.\n", "\n", "Using such a model, it's possible to estimate the COG vertical projection (COGv) from the COP displacement. The Python function `cogve.py` (code at the end of this text) performs this estimation. The function signature is:\n", "```python\n", "cogv = cogve(COP, freq, mass, height, show=False, ax=None)\n", "```\n", "Let's now derive the inverted pendulum model of the human standing posture implemented in this function."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Derivation of the inverted pendulum model\n", "\n", "In the most simple version of the model, the human body in the sagittal plane is reduced to a two-link body with a single inverted pendulum articulated by only one joint (representing the feet, with the rest of the body articulated by the ankle joint). Let's deduce the equations for such inverted pendulum model as the representation at the sagital plane of the human standing still posture. The inverted pendulum model and the correspondent free-body diagrams (FBDs) are shown in Figure 1.\n", "\n", "<div><figure><img src=\"./../images/invpendulum.png\" width=400 alt=\"onelink\"/><figcaption><b>Figure 1.</b> <i>Model of a two-link inverted pendulum and the external forces acting on it for the representation at the sagital plane of the human standing still posture and the corresponding free-body diagrams. $COG$: body center of gravity; $COG_v$: $COG$ vertical projection (at the horizontal plane) in relation to the ankle joint; $COP$: body center of pressure in relation to the ankle joint; $GRF$: ground reaction force (typically measured by a force plate); $\\alpha$: angle of the body in relation to the vertical direction; $m$: mass of the body minus feet; $g$: acceleration of gravity; $F_a$ and $T_a$: resultant force and torque at the ankle joint; $h$: height of the $COG$ in relation to the ankle joint; $m_f$ and $h_f$: mass and height of the feet.</i></figcaption></figure></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The equations of motion for each FBD of the feet and rest-of-body segments at the sagittal plane ($xy$ plane) can be expressed in the form of the Newton-<PERSON><PERSON><PERSON> equations.  \n", "<br>\n", "<div style=\"background-color:#FBFBEF;border:1px solid black;padding:10px;\">\n", "<b>The Newton-Euler equations</b>\n", "<br />\n", "The <a href=\"http://en.wikipedia.org/wiki/Newton%E2%80%93Euler_equations\">Newton-Euler equations</a> are a formalism to describe the combined translational and rotational dynamics of a rigid body.  \n", "For a two-dimensional (at the $xy$ plane) movement, their general form are given by:  \n", "<br />\n", "$$ \\sum \\mathbf{F} = m \\mathbf{\\ddot{r}}_{cm} $$  \n", "\n", "$$ \\sum \\mathbf{T}_z = I_{cm} \\mathbf{\\ddot{\\alpha}}_z $$  \n", "\n", "Where the movement is considered around the center of mass ($cm$) of the body, $\\mathbf{F}$ and $\\mathbf{T}$ are, respectively, the forces and torques acting on the body, $\\mathbf{\\ddot{r}}$ and $\\mathbf{\\ddot{\\alpha}}$ are, respectively, the linear and angular accelerations, and $I$ is the body moment of inertia around the $z$ axis passing through the body center of mass.   \n", "It can be convenient to describe the rotation of the body around other point than the center of mass. In such cases, we express the moment of inertia around a reference point $o$ instead of around the body center of mass and we will have an additional term to the equation for the torque:  \n", "<br />\n", "$$ \\sum \\mathbf{T}_{z,O} = I_{o} \\mathbf{\\ddot{\\alpha}}_z + \\mathbf{r}_{cm,o}\\times m \\mathbf{\\ddot{r}}_o $$  \n", "\n", "Where $\\mathbf{r}_{cm,o}$ is the position vector of the center of mass in relation to the reference point $o$ and $\\mathbf{\\ddot{r}}_o$ is the linear acceleration of this reference point.  \n", "<a href=\"http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/FreeBodyDiagram.ipynb\">See this notebook about free-body diagram</a>.  \n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the case of an inverted pendulum representing at the sagittal plane the body at the standing still posture, let's solve the Newton-Euler equations considering the rotation around the ankle joint (because it will simplify the problem) and let's adopt the simplifications that the feet don't move (including the ankle, so $\\mathbf{\\ddot{r}}_o=0$ in the Newton-Euler equation) and their mass are neglegible in relation to the mass of the rest of the body.   \n", "\n", "For the feet, we have:\n", "\n", "$$ \\begin{array}{l l}\n", "-F_{ax} + GRF_x = 0 \\\\\n", "\\\\\n", "-F_{ay} + GRF_y = 0 \\\\\n", "\\\\\n", "-T_a + COP \\cdot GRF_y + h_f \\cdot GRF_x = 0 \n", "\\end{array} $$  \n", "\n", "And for the rest of the body:  \n", "\n", "$$ \\begin{array}{l l}\n", "F_{ax} = m\\ddot{x}_{cm} \\\\\n", "\\\\\n", "F_{ay} - mg = m\\ddot{y}_{cm} \\\\\n", "\\\\\n", "T_a - COG_v \\cdot mg = I_a \\ddot{\\alpha} \n", "\\end{array} $$  \n", "\n", "Where $I_a$ is the moment of inertia of the whole body around the ankle joint.  \n", "During the standing still posture, the $GRF$ horizontal component is typically much smaller than the $GRF$ vertical component and the torque of the former can be neglected. In addition, the magnitude of the $GRF$ vertical component is approximately constant and equal to the body weight.   \n", "Considering these approximations, the ankle joint torque using the equation for the feet is given by:  \n", "\n", "$$ T_a \\approx COP \\cdot mg $$  \n", "\n", "If now we substitute the ankle joint torque term in the equation for the torques calculated for the rest-of-body segment, we have:  \n", "\n", "$$ COP - COG_v \\approx \\frac{I_a}{mg} \\ddot{\\alpha} $$  \n", "\n", "That is, the angular acceleration of the body is proportional to the difference between $COP$ and $COG_v$ displacements (with respect to the ankle joint position).  \n", "\n", "We can continue with the deduction and now substitute the angular displacement by a term proportional to $COG_v$ if we use the following trignometric relation (see figure above): $sin \\alpha=COG_v/h$. But, during the standing still posture $\\alpha$ is typicall very small and we can approximate $ sin\\alpha \\approx \\alpha $ and $\\alpha \\approx COG_v/h$. However, bear in mind that $\\alpha$ is defined as counterclockwise positive while $COG_v$ is positive when pointing to the right direction. This means that in fact $\\alpha \\approx -COG_v/h$. As $h$ is constant, the second derivative of $\\alpha$ with respect to time is simply the second derivative of $COG_v$ divided by $h$. \n", "\n", "Finally, the last equation can be expressed in the following form:\n", "\n", "$$ COG_v - COP \\approx \\frac{I_a}{mgh} \\ddot{COG}_v $$  \n", "\n", "Or simply:  \n", "\n", "$$ COG_v - COP \\approx k \\, \\ddot{COG}_v $$  \n", "\n", "Where $k = I_a/(mgh)$.  \n", "If the human body is represented as a rigid bar, its moment of inertia will be approximately equal to $1.33mh^2$, so $k \\approx 1.33h/g$.  \n", "In turn, from the Newton-<PERSON><PERSON> equations, the horizontal acceleration of $COG$ is equal to the horizontal component of $GRF$ divided by the body mass and the equation above can be expressed as:  \n", "\n", "$$ COG_v - COP \\approx \\frac{k}{m} GRF_x $$  \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implications of the inverted pendulum model\n", "\n", "These two last equations express a very simple relation between body segment parameters, $COG_v$, $COP$, body acceleration, and horizontal force.   \n", "Solely based on these equations it is possible to predict some interesting relations among the variables in these equations, which have been experimentally observed:  \n", " -\t$COG_v-COP$ is positively correlated with the horizontal ground reaction force in the anterior-posterior direction (<PERSON> et al. 1998; <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> 1999; <PERSON><PERSON><PERSON> and <PERSON><PERSON> 2000);  \n", " -\t$COG_v$ behaves as a low-pass filtered version of the $COP$ signal and this fact has been used in a method to derive $COG_v$ from the $COP$ signal (Winter 1995; <PERSON><PERSON> et al. 1997; <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> 1999). This method produces similar results as other methods (<PERSON><PERSON><PERSON> et al. 2004);   \n", " -\tFor a continuously regulated inverted pendulum (like the standing still posture), the common frequencies of $COG_v$ and $COP$ signals are in phase (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> 1999);  \n", " -\tWhen the horizontal force is zero, $COG_v$ and $COP$ coincide and this fact has been used as a method to derive $COG_v$ from the $COP$ displacement and the horizontal $GRF$ (<PERSON> and <PERSON> 1997; <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> 2000).  \n", " \n", "Note that the four predictions made above are based entirely on the mechanical derivation of the inverted pendulum model. Nothing has been said about what type of neural control is being used for the regulation of the standing posture. This means that the statements above are consequence of the mechanical nature of the modeled phenomenon.\n", "\n", "Obviously, the most straightforward prediction of the single inverted pendulum model concerning the **kinematics of the segments** of the human body would be that we should observe merely the motion at the ankle joint and nothing in the other joints. This prediction has not been observed (see for example, <PERSON><PERSON> et al. 2008 and <PERSON><PERSON> et al. 2009). During standing still, we seem to use all our joints and this is task dependent.   \n", "However, one important point to consider is that even if the inverted pendulum fails as a suitable model of the **kinematics of the segments**, the inverted pendulum succeds as a model of the **kinematics of global body variables**, such as $COG_v$ and $COP$, and their relation to kinetic variables, the external forces acting on the body.   \n", "\n", "Certainly everyone agrees that the inverted pendulum model is insufficient to capture all the essential characteristics of the posture during standing. Nevertheless, the greatest power of the single inverted pendulum model is its simplicity, and it is somewhat surprising to note how much this simple model can capture the of the investigated phenomenon."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Estimation of COGv from the COP signal\n", "\n", "Based on the inverted pendulum model, it's possible to estimate the $COG_v$ displacement from the $COP$ displacement after some mathematical manipulation we show next.  \n", "Back to the relation between $COG_v$ and $COP$ displacements, it has the form:  \n", "\n", "$$ y(t) - x(t) = k\\,\\ddot{y}(t) $$  \n", "\n", "Where $y(t)$ stands for the $COG_v$ signal and $x(t)$ for the $COP$ signal, which are functions of time, and $k = I_a/(mgh)$.  \n", "The equation above is a linear ordinary differential equation of second order. This equation is solvable in the time domain, but if we transform it to the frequency domain using the Fourier transform, we will find a simpler relation between $COG_v$ and $COP$. \n", "\n", "<br>\n", "<div style=\"background-color:#FBFBEF;border:1px solid black;padding:10px;\">\n", "<b>The Fourier transform</b>  \n", "The <a href=\"http://en.wikipedia.org/wiki/Fourier_transform\">Fourier transform</a> is a mathematical operation to transform a signal which is function of time, $g(t)$, into a signal which is function of frequency, $G(f)$, and it is defined by:  \n", "<br />\n", "$$ \\mathcal{F}[g(t)] = G(f) = \\int_{-\\infty}^{\\infty} g(t) e^{-i2\\pi ft} dt $$  \n", "\n", "Its inverse operation is:  \n", "<br />\n", "$$ \\mathcal{F}^{-1}[G(f)] = g(t) = \\int_{-\\infty}^{\\infty} G(f) e^{i2\\pi ft} df $$  \n", "\n", "The function $G(f)$ is the representation in the frequency domain of the time-domain signal, $g(t)$, and vice-versa. The functions $g(t)$ and $G(f)$ are referred to as a Fourier integral pair, or Fourier transform pair, or simply the Fourier pair.  \n", "<a href=\"http://www.thefouriertransform.com/transform/fourier.php\">See here for an introduction to Fourier transform</a> and <a href=\"http://www.thefouriertransform.com/applications/differentialequations.php\">see here for the use of Fourier transform to solve differential equations</a>.\n", "</div>\n", "\n", "<br>\n", "Let's apply the Fourier transform to the differential equation with $COG_v$ and $COP$:  \n", "\n", "$$ Y(j\\omega) - X(j\\omega) = -k\\,\\omega^2Y(j\\omega) $$  \n", "\n", "Where we defined $y(t) \\Leftrightarrow Y(j\\omega)$ and $x(t) \\Leftrightarrow X(j\\omega)$ as the Fourier pairs, $j$ is the imaginary unit, and $\\omega$ is the angular frequency, $2\\pi f$.   \n", "The reason why we use the Fourier transform is because we started with a second order differential equation and ended with the simple algebraic equation above. Rearranging the equation above:  \n", "\n", "$$ \\frac{Y(j\\omega)}{X(j\\omega)} = \\frac{\\omega_0^2}{\\omega_0^2 + \\omega^2} $$  \n", "\n", "Where $ \\omega_0 = 1/\\sqrt{k}$.  \n", "If we imagine a system where the $COP$ is the input and the $COG_v$ the output, the right side of the equation above is known as the <a href=\"http://en.wikipedia.org/wiki/Transfer_function\">transfer function</a> of such system, the ratio between the output and the input.   \n", "Analysing the transfer function given in the equation above, we see that it is of the type of a low-pass filter (and $\\omega_0$ is the cutoff frequency); because of that, we can say that the $COGv$ signal is a low-pass filtered version of the $COP$ signal.\n", "\n", "We can implement such low-pass filter in order to determine the $COG_v$ using the $COP$ signal. For that, we simply have to estimate the Fourier transform of the $COP$ signal, multiply by the transfer function (the right side of the equation above), and calculate the inverse Fourier transform of this result. The Python function `cogve.py` (code at the end of this text) estimates the $COG_v$ using the $COP$ data based on this algorithm. Let's test this function, first we have to import the necessary Python libraries and configure the emvironment:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Import the necessary libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "import sys\n", "sys.path.insert(1, r'./../functions')\n", "from cogve import cogve"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use stabilographic data found in the internet:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COP shape:  (4095, 2)\n"]}], "source": ["import pandas as pd  # use Pandas to read data from a website\n", "fileUrl = 'http://www.udel.edu/biology/rosewc/kaap686/reserve/cop/copdata.txt'\n", "COP = pd.read_table(fileUrl, skipinitialspace=True, sep=None, engine='python')\n", "COP = COP.values / 10  # mm to cm\n", "freq = 100\n", "print('COP shape: ', COP.shape)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfwAAAFXCAYAAABHifw+AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x2a18093c0b8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1, figsize=(8, 5))\n", "cogv = cogve(COP[:, 0], freq=100, mass=70, height=175, ax=ax, show=True)  # guess mass, height"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. (1997) [Estimating the centre of gravity of the body on the basis of the centre of pressure in standing posture](http://www.ncbi.nlm.nih.gov/pubmed/9456386). J. Biomech. 30, 1169-1171.   \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. (2004) [Comparison of three methods to estimate the center of mass during balance assessment](http://ebm.ufabc.edu.br/publications/md/JB03.pdf). J. Biomech. 37, 1421-1426.\n", "- <PERSON>, <PERSON><PERSON><PERSON><PERSON> V<PERSON> (1997) [Extracting gravity line displacement from stabilographic recordings](http://www.sciencedirect.com/science/article/pii/S0966636296011010). Gait & Posture 6, 27-38.   \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. (1999) [Computing the COM from the COP in postural sway movements](http://www.sciencedirect.com/science/article/pii/S0167945799000391). Human Movement Science 18, 759-767.   \n", "- Winter DA (1995) [A.B.C. (Anatomy, Biomechanics and Control) of Balance during Standing and Walking](https://books.google.com.br/books?id=0lSqQgAACAAJ&). Waterloo, Waterloo Biomechanics.   \n", "- Winter DA, <PERSON><PERSON>, et al. (1998) [Stiffness control of balance in quiet standing](http://www.ncbi.nlm.nih.gov/pubmed/9744933). J. Neurophysiol. 80, 1211-1221.  \n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2000) [Rambling and trembling in quiet standing](http://ebm.ufabc.edu.br/publications/md/MC00.pdf). Motor Control 4, 185-200."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function cogve.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["# %load ./../functions/cogve.py\n", "\"\"\"COGv estimation using COP data based on the inverted pendulum model.\"\"\"\n", "\n", "from __future__ import division, print_function\n", "import numpy as np\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = \"1.0.2\"\n", "__license__ = \"MIT\"\n", "\n", "\n", "def cogve(COP, freq, mass, height, show=False, ax=None):\n", "    \"\"\"COGv estimation using COP data based on the inverted pendulum model.\n", "\n", "    This function estimates the center of gravity vertical projection (COGv)\n", "    displacement from the center of pressure (COP) displacement at the\n", "    anterior-posterior direction during quiet upright standing. COP and COGv\n", "    displacements are measurements useful to quantify the postural sway of a\n", "    person while standing.\n", "\n", "    The COGv displacement is estimated by low-pass filtering the COP\n", "    displacement in the frequency domain according to the person's moment\n", "    of rotational inertia as a single inverted pendulum [1]_.\n", "\n", "    Parameters\n", "    ----------\n", "    COP    : 1D array_like\n", "             center of pressure data [cm]\n", "    freq   : float\n", "             sampling frequency of the COP data\n", "    mass   : float\n", "             body mass of the subject [kg]\n", "    height : float\n", "             height of the subject [cm]\n", "    show   : bool, optional (default = False)\n", "             True (1) plots data and results in a matplotlib figure\n", "             False (0) to not plot\n", "    ax     : matplotlib.axes.Axes instance, optional (default = None)\n", "\n", "    Returns\n", "    -------\n", "    COGv   : 1D array\n", "             center of gravity vertical projection data [cm]\n", "\n", "    References\n", "    ----------\n", "    .. [1] http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/IP_Model.ipynb\n", "\n", "    Examples\n", "    --------\n", "    >>> from cogve import cogve\n", "    >>> y = np.cumsum(np.random.randn(3000))/50\n", "    >>> cogv = cogve(y, freq=100, mass=70, height=170, show=True)\n", "    \"\"\"\n", "\n", "    from scipy.signal._arraytools import odd_ext\n", "    import scipy.fftpack\n", "\n", "    COP = np.asarray(COP)\n", "    height = height / 100  # cm to m\n", "    g = 9.8  # gravity acceleration in m/s2\n", "    # height of the COG w.r.t. ankle (<PERSON><PERSON><PERSON><PERSON><PERSON>, 2005; Winter, 2005)\n", "    hcog = 0.56 * height - 0.039 * height\n", "    # body moment of inertia around the ankle\n", "    # (<PERSON><PERSON><PERSON><PERSON>, 1996), (0.0572 for the ml direction)\n", "    I = mass * 0.0533 * height ** 2 + mass * hcog ** 2\n", "    # Newton-Euler equation of motion for the inverted pendulum\n", "    # COGv'' = w02*(COGv - COP)\n", "    # where w02 is the squared pendulum natural frequency\n", "    w02 = mass * g * hcog / I\n", "    # add (pad) data and remove mean to avoid problems at the extremities\n", "    COP = odd_ext(COP, n=freq)\n", "    COPm = np.mean(COP)\n", "    COP = COP - COPm\n", "    # COGv is estimated by filtering the COP data in the frequency domain\n", "    # using the transfer function for the inverted pendulum equation of motion\n", "    N = COP.size\n", "    COPfft = scipy.fftpack.fft(COP, n=N) / N  # COP fft\n", "    w = 2 * np.pi * scipy.fftpack.fftfreq(n=N, d=1 / freq)  # angular frequency\n", "    # transfer function\n", "    TF = w02 / (w02 + w ** 2)\n", "    COGv = np.real(scipy.fftpack.ifft(TF * COPfft) * N)\n", "    COGv = COGv[0: N]\n", "    # get back the mean and pad off data\n", "    COP, COGv = COP + COPm, COGv + COPm\n", "    COP, COGv = COP[freq: -freq], COGv[freq: -freq]\n", "\n", "    if show:\n", "        _plot(COP, COGv, freq, ax)\n", "\n", "    return COGv\n", "\n", "\n", "def _plot(COP, COGv, freq, ax):\n", "    \"\"\"Plot results of the cogve function, see its help.\"\"\"\n", "    try:\n", "        import matplotlib.pyplot as plt\n", "    except ImportError:\n", "        print('matplotlib is not available.')\n", "    else:\n", "        time = np.linspace(0, COP.size / freq, COP.size)\n", "        if ax is None:\n", "            _, ax = plt.subplots(1, 1)\n", "        ax.plot(time, COP, color=[0, 0, 1, .8], lw=2, label='COP')\n", "        ax.plot(time, COGv, color=[1, 0, 0, .8], lw=2, label='COGv')\n", "        ax.legend(fontsize=14, loc='best', framealpha=.5, numpoints=1)\n", "        ax.set_xlabel('Time [s]', fontsize=14)\n", "        ax.set_ylabel('Amplitude [cm]', fontsize=14)\n", "        ax.set_title('COGv estimation using the COP data', fontsize=16)\n", "        ax.set_xlim(time[0], time[-1])\n", "        plt.grid()\n", "        plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.1"}, "widgets": {"state": {}, "version": "1.1.2"}}, "nbformat": 4, "nbformat_minor": 0}