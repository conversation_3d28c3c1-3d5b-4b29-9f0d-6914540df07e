{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Free-Body Diagram for particles\n", "\n", "> <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1><br>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Free-Body-Diagram\" data-toc-modified-id=\"Free-Body-Diagram-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Free-Body Diagram</a></span></li><li><span><a href=\"#Steps-to-draw-a-free-body-diagram-(FBD)\" data-toc-modified-id=\"Steps-to-draw-a-free-body-diagram-(FBD)-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Steps to draw a free-body diagram (FBD)</a></span></li><li><span><a href=\"#Basic-element-and-forces\" data-toc-modified-id=\"Basic-element-and-forces-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Basic element and forces</a></span><ul class=\"toc-item\"><li><span><a href=\"#Gravity\" data-toc-modified-id=\"Gravity-4.1\"><span class=\"toc-item-num\">4.1&nbsp;&nbsp;</span>Gravity</a></span><li><span><a href=\"#Spring\" data-toc-modified-id=\"Spring-4.2\"><span class=\"toc-item-num\">4.2&nbsp;&nbsp;</span>Spring</a></span><li><span><a href=\"#Damping\" data-toc-modified-id=\"Damping-4.3\"><span class=\"toc-item-num\">4.3&nbsp;&nbsp;</span>Damping</a></span></li></ul><li><span><a href=\"#Examples-of-free-body-diagram\" data-toc-modified-id=\"Examples-of-free-body-diagram-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Examples of free-body diagram</a></span><ul class=\"toc-item\"><li><span><a href=\"#No-force-acting-on-the-particle\" data-toc-modified-id=\"No-force-acting-on-the-particle-5.1\"><span class=\"toc-item-num\">5.1&nbsp;&nbsp;</span>No force acting on the particle</a></span><li><span><a href=\"#Gravity-force-acting-on-the-particle\" data-toc-modified-id=\"Gravity-force-acting-on-the-particle-5.2\"><span class=\"toc-item-num\">5.2&nbsp;&nbsp;</span>Gravity force acting on the particle</a><li><span><a href=\"#Ground-reaction-force\" data-toc-modified-id=\"Ground-reaction-force-5.3\"><span class=\"toc-item-num\">5.3&nbsp;&nbsp;</span>Ground reaction force</a><li><span><a href=\"#Mass-spring-system-with-horizontal-movement\" data-toc-modified-id=\"Mass-spring-system-with-horizontal-movement-5.4\"><span class=\"toc-item-num\">5.4&nbsp;&nbsp;</span>Mass-spring system with horizontal movement</a></span><li><span><a href=\"#Linear-spring-in-bidimensional-movement-at-horizontal-plane\" data-toc-modified-id=\"Linear-spring-in-bidimensional-movement-at-horizontal-plane-5.5\"><span class=\"toc-item-num\">5.5&nbsp;&nbsp;</span>Linear spring in bidimensional movement at horizontal plane</a></span><li><span><a href=\"#Particle-under-action-of-gravity-and-linear-air-resistance\" data-toc-modified-id=\"Particle-under-action-of-gravity-and-linear-air-resistance-5.6\"><span class=\"toc-item-num\">5.6&nbsp;&nbsp;</span>Particle under action of gravity and linear air resistance</a></span><li><span><a href=\"#Particle-under-action-of-gravity-and-nonlinear-air-resistance\" data-toc-modified-id=\"Particle-under-action-of-gravity-and-nonlinear-air-resistance-5.7\"><span class=\"toc-item-num\">5.7&nbsp;&nbsp;</span>Particle under action of gravity and nonlinear air resistance</a></span><li><span><a href=\"#Linear-spring-and-damping-on-bidimensional-horizontal-movement\" data-toc-modified-id=\"Linear-spring-and-damping-on-bidimensional-horizontal-movement-5.8\"><span class=\"toc-item-num\">5.8&nbsp;&nbsp;</span>Linear spring and damping on bidimensional horizontal movement</a></span><li><span><a href=\"#Simple-muscle-model\" data-toc-modified-id=\"Simple-muscle-model-5.9\"><span class=\"toc-item-num\">5.9&nbsp;&nbsp;</span>Simple muscle model</a></span></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-internet\" data-toc-modified-id=\"Video-lectures-on-the-internet-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Video lectures on the internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-02-07T19:10:11.910265Z", "start_time": "2021-02-07T19:10:06.092381Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "%matplotlib inline\n", "sns.set_context('notebook', font_scale=1.2)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Free-Body Diagram\n", "\n", "In the mechanical modeling of an inanimate or living system, composed by one or more bodies (bodies as units that are mechanically isolated according to the question one is trying to answer), it is convenient to isolate each body (be they originally interconnected or not) and identify each force and moment of force (torque) that act on this body in order to apply the laws of mechanics.\n", "\n", "**The free body diagram (FBD) of a mechanical system or model is the representation in a diagram of all forces and moments of force acting on each body, isolated from the rest of the system.**  \n", "\n", "The term free means that each body, which maybe was part of a connected system, is represented as isolated (free) and any existent contact force is represented in the diagram as forces (action and reaction) acting on the formerly connected bodies. Then, the laws of mechanics are applied on each body, and the unknown movement, force or moment of force can be found if the system of equations is determined (the number of unknown variables can not be greater than the number of equations for each body).\n", "\n", "How exactly a FBD is drawn for a mechanical model of something is dependent on what one is trying to find. For example, the air resistance might be neglected or not when modeling the movement of an object and the number of parts the system is divided is dependent on what is needed to know about the model.  \n", "\n", "The use of FBD is very common in biomechanics; a typical use is to use the FBD in order to determine the forces and torques on the ankle, knee, and hip joints of the lower limb (foot, leg, and thigh) during locomotion, and the FBD can be applied to any problem where the laws of mechanics are needed to solve a problem.\n", "\n", "For now, let's study how to draw free-body diagrams for systems that can be modeled as particles."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Steps to draw a free-body diagram (FBD)\n", "\n", "1. Draw separately each object considered in the problem. How you separate depends on what questions you want to answer.  \n", "2. Identify the forces acting on each object. If you are analyzing more than one object, remember the Newton's third Law (action and reaction), and identify where the reaction of a force is being applied.  \n", "3. Draw all the identified forces, representing them as vectors. The vectors should be represented with the origin in the object. In the case of particles, the origin should be in the center of the particle.  \n", "4. If necessary, you should represent the reference frame in the free-body diagram.  \n", "5. After this, you can solve the problem using the Newton's second Law (see, e.g, [Newton's Laws](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/Notebooks/newtonLawForParticles.ipynb)) to find the motion of the particle."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Basic element and forces"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Gravity\n", "\n", "The gravity force acts on two masses, each one atracting each other:\n", "\n", "\\begin{equation}\n", "\\vec{{\\bf{F}}} = - G\\frac{m_1m_2}{||\\vec{\\bf{r}}||^2}\\frac{\\vec{\\bf{r}}}{||\\vec{\\bf{r}}||}\n", "\\end{equation}\n", "\n", "where $G = 6.67.10^{−11} Nm^2/kg^2$ and $\\vec{\\bf{r}}$ is a vector with length equal to the distance between the masses and directing towards the other mass. Note the forces acting on each mass have the same absolute value.\n", "\n", "Since the mass of the Earth is $m_1=5.9736×10^{24}kg$ and its radius is 6.371×10$^6$ m, the gravity force near the surface of the Earth is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{{\\bf{F}}} = m\\vec{\\bf{g}}\n", "\\end{equation}\n", "</span>\n", "\n", "with the absolute value of $\\vec{\\bf{g}}$ approximately equal to 9.81 $m/s^2$, pointing towards the center of Earth."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Spring\n", "\n", "Spring is an element used to represent a force proportional to some length or displacement. It produces a force in the same direction of the vector linking the spring extremities and opposite to its length or displacement from an equilibrium length. Frequently it has a linear relation, but it could be nonlinear as well. The force exerted by the spring in one of the extremities is: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = - k(||\\vec{\\bf{r}}||-l_0)\\frac{\\vec{\\bf{r}}}{||\\vec{\\bf{r}}||} = -k\\vec{\\bf{r}} +kl_0\\frac{\\vec{\\bf{r}}}{||\\vec{\\bf{r}}||} = -k\\left(1-\\frac{l_0}{||\\vec{\\bf{r}}||}\\right)\\vec{\\bf{r}}\n", "\\end{equation}\n", "</span>\n", "\n", "where $\\vec{\\bf{r}}$ is the vector linking the extremity applying the force to the other extremity and $l_0$ is the equilibrium length of the spring.\n", "\n", "Since the spring element is a massless element, the force in  both extremities have the same absolute value and opposite directions. "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Damping \n", "\n", "Damper is an element used to represent a force proportional to the velocity of displacement. It produces a force in the opposite direction of its velocity.\n", "\n", "Frequently it has a linear relation, but it could be nonlinear as well. The force exerted by the damper element in one of its extremities is: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = - b||\\vec{\\bf{v}}||\\frac{\\vec{\\bf{v}}}{||\\vec{\\bf{v}}||} = -b\\vec{\\bf{v}} = -b\\frac{d\\vec{\\bf{r}}}{dt}\n", "\\end{equation}\n", "</span>\n", "\n", "where $\\vec{\\bf{r}}$ is the vector linking the extremity applying the force to the other extremity.\n", "\n", "Since the damper element is a massless element, , the force in  both extremities have the same absolute value and opposite directions. "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Examples of free-body diagram\n", "\n", "Let's see some examples on how to draw the free-body diagram and obtain the motion equations to solve the problems."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 1. No force acting on the particle\n", "\n", "The most trivial situation is a particle with no force acting on it.   \n", "\n", "The free-body diagram is below, with no force vectors acting on the particle.\n", "\n", "<figure><center><img src=\"../images/ballNoGrav.png\" alt=\"free-body diagram of a ball\" width=\"500\"/><figcaption><i>Figure. Free-body diagram of a ball with no force acting on it.</i></figcaption></center></figure>\n", "    \n", "In this situation, the resultant force is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = 0\n", "\\end{equation}\n", "</span>\n", "    \n", "And the second Newton law for this particle is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "m\\frac{d^2\\vec{\\bf{r}}}{dt^2} = 0 \\quad \\rightarrow \\quad \\frac{d^2\\vec{\\bf{r}}}{dt^2} = 0\n", "\\end{equation}\n", "</span>\n", "    \n", "The motion of of the particle can be found by integrating twice both times, getting the following:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{r}} = \\vec{\\bf{v}}_0t + \\vec{\\bf{r}}_0\n", "\\end{equation}\n", "</span>\n", "    \n", "The particle continues to change its position with the same velocity it was at the beginning of the analysis. This could be predicted by <PERSON>'s first law."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 2. Gravity force acting on the particle\n", "\n", "Now, let's consider a ball with the gravity force acting on it. The free-body diagram is depicted below.\n", "\n", "<figure><center><img src=\"../images/ballGrav.png\" alt=\"free-body diagram of a ball\" width=\"500\"/><figcaption><i>Figure. Free-body diagram of a ball under the influence of gravity.</i></figcaption></center></figure>\n", "\n", "The only force acting on the ball is the gravitational force:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}}_g = - mg \\; \\hat{\\bf{j}}\n", "\\end{equation}\n", "</span>\n", "\n", "Applying <PERSON>'s second Law:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}}_g = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\rightarrow - mg \\; \\hat{\\bf{j}} = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\rightarrow - g \\; \\hat{\\bf{j}} = \\frac{d^2\\vec{\\bf{r}}}{dt^2}\n", "\\end{equation}\n", "</span>\n", "\n", "Now, we can separate the equation in two components (x and y):\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "0 = \\frac{d^2x}{dt^2}\n", "\\end{equation}\n", "</span>\n", "\n", "and\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "- g = \\frac{d^2y}{dt^2}\n", "\\end{equation}\n", "</span>\n", "\n", "These equations were solved in [this Notebook about the Newton's laws](https://nbviewer.jupyter.org/github/BMClab/BMC/blob/master/notebooks/newtonLawForParticles.ipynb). "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 3. Ground reaction force\n", "\n", "Now, we will analyze the situation of a particle at rest in contact with the ground. To simplify the analysis, only the vertical movement will be considered.\n", "\n", "<figure><center><img src=\"../images/ballGravGRF.png\" alt=\"free-body diagram of a ball\" width=\"400\"/><figcaption><i>Figure. Free-body diagram of a ball at rest in contact with the ground.</i></figcaption></center></figure>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The forces acting on the particle are the ground reaction force (often called as normal force) and the gravity force. The free-body diagram of the particle is below:\n", "\n", "<figure><center><img src=\"../images/ballGravGRFFBD.png\" alt=\"free-body diagram of a ball\" width=\"200\"/><figcaption><i>Figure. Free-body diagram of a ball under the influence of gravity.</i></figcaption></center></figure>    \n", "\n", "So, the resultant force in the particle is:\n", "    \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{F}} = \\overrightarrow{\\bf{GRF}} + m\\vec{\\bf{g}} = \\overrightarrow{\\bf{GRF}} - mg \\; \\hat{\\bf{j}}\n", "\\end{equation}\n", "</span>\n", "\n", "Considering only the y direction:\n", "    \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    F = GRF - mg\n", "\\end{equation}\n", "</span>\n", "\n", "Applying <PERSON>'s second law to the particle:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    m \\frac{d^2y}{dt^2} = GRF - mg\n", "\\end{equation}\n", "</span>\n", "\n", "Note that since we have no information about how the force GRF varies along time, we cannot solve this equation. To find the position of the particle along time, one would have to measure the ground reaction force. See [the notebook on Vertical jump](http://nbviewer.jupyter.org/github/BMClab/BMC/blob/master/notebooks/VerticalJump.ipynb) for an application of this model."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 4. Mass-spring system with horizontal movement\n", "\n", "The example below represents a mass attached to a spring and the other extremity of the spring is fixed. \n", "\n", "<figure><center><img src=\"../images/ballspring.png\" alt=\"free-body diagram of a ball\" width=\"500\"/><figcaption><i>Figure. Mass-spring system with horizontal movement.</i></figcaption></center></figure>      "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The only force force acting on the mass is from the spring. Below is the free-body diagram from the mass.\n", "\n", "<figure><center><img src=\"../images/ballspringFBD.png\" alt=\"free-body diagram of a ball\" width=\"200\"/><figcaption><i>Figure. Free-body diagram of a mass-spring system.</i></figcaption></center></figure>  \n", "\n", "Since the movement is horizontal, we can neglect the gravity force.  \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{F}} = -k\\left(1-\\frac{l_0}{||\\vec{\\bf{r}}||}\\right)\\vec{\\bf{r}}\n", "\\end{equation}\n", "</span>\n", "    \n", "Applying <PERSON>'s second law to the mass:\n", "    \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    m\\frac{d^2\\vec{\\bf{r}}}{dt^2} = -k\\left(1-\\frac{l_0}{||\\vec{\\bf{r}}||}\\right)\\vec{\\bf{r}} \\rightarrow \\frac{d^2\\vec{\\bf{r}}}{dt^2} = -\\frac{k}{m}\\left(1-\\frac{l_0}{||\\vec{\\bf{r}}||}\\right)\\vec{\\bf{r}}\n", "\\end{equation}\n", "</span>\n", "    \n", "Since the movement is unidimensional, we can deal with it scalarly:\n", "    \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2x}{dt^2} = -\\frac{k}{m}\\left(1-\\frac{l_0}{x}\\right)x = -\\frac{k}{m}(x-l_0) \n", "\\end{equation}\n", "</span>\n", "\n", "To solve this equation numerically, we must break the equations into two first-order differential equation:\n", "    \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_x}{dt} =  -\\frac{k}{m}(x-l_0) \n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dx}{dt} =  v_x\n", "\\end{equation}\n", "</span>\n", "\n", "In the numerical solution below, we will use $k = 40 N/m$, $m = 2 kg$, $l_0 = 0.5 m$ and the mass starts from the position $x = 0.8m$ and at rest."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-02-07T19:10:12.504470Z", "start_time": "2021-02-07T19:10:11.912384Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["k = 40\n", "m = 2\n", "l0 = 0.5\n", "x0 = 0.8\n", "v0 = 0\n", "\n", "x = x0\n", "v = v0\n", "\n", "dt = 0.001\n", "t = np.arange(0, 3, dt)\n", "\n", "r = np.array([x])\n", "\n", "for i in t[1:]:\n", "    dxdt = v\n", "    dvxdt = -k/m*(x-l0)\n", "    x = x + dt*dxdt\n", "    v = v + dt*dvxdt\n", "    r = np.vstack((r,np.array([x])))\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(t, r, lw=4)\n", "plt.xlabel('t(s)')\n", "plt.ylabel('x(m)')\n", "plt.title('Spring displacement')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 5. Linear spring in bidimensional movement at horizontal plane\n", "\n", "This example below represents a system with two masses attached to a spring.  \n", "To solve the motion of both masses, we have to draw a free-body diagram for each one of the masses. \n", "\n", "<figure><center><img src=\"../images/twoballspring.png\" alt=\"Linear spring\" width=\"500\"/><figcaption><i>Figure. Linear spring in bidimensional movement at horizontal plane.</i></figcaption></center></figure>  "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The only force acting on each mass is the force due to the spring. Since the movement is happening at the horizontal plane, the gravity force can be neglected.\n", "\n", "<figure><center><img src=\"../images/twoballspringFBD.png\" alt=\"Linear spring\" width=\"200\"/><figcaption><i>Figure. FBD of linear spring in bidimensional movement at horizontal plane.</i></figcaption></center></figure>  \n", "\n", "So, the forces acting on mass 1 is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{F_1}} = k\\left(||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||-l_0\\right)\\frac{(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\n", "\\end{equation}\n", "</span>\n", "    \n", "and the forces acting on mass 2 is:\n", "    \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{F_2}} =k\\left(||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||-l_0\\right)\\frac{(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Applying <PERSON>'s second law for the masses:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "     m_1\\frac{d^2\\vec{\\bf{r_1}}}{dt^2} = k\\left(||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||-l_0\\right)\\frac{(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||} \n", "    \\\\\n", "    \\frac{d^2\\vec{\\bf{r_1}}}{dt^2} = -\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_1}}+\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_2}} \n", "    \\\\\n", "    \\frac{d^2x_1\\hat{\\bf{i}}}{dt^2}+\\frac{d^2y_1\\hat{\\bf{j}}}{dt^2} = -\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_1\\hat{\\bf{i}}+y_1\\hat{\\bf{j}})+\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_2\\hat{\\bf{i}}+y_2\\hat{\\bf{j}})\n", "\\end{equation}\n", "</span>\n", "\n", "<br/>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "     m_2\\frac{d^2\\vec{\\bf{r_2}}}{dt^2} = k\\left(||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||-l_0\\right)\\frac{(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\n", "    \\\\\n", "    \\frac{d^2\\vec{\\bf{r_2}}}{dt^2} = -\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_2}}+\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_1}} \n", "    \\\\\n", "    \\frac{d^2x_2\\hat{\\bf{i}}}{dt^2}+\\frac{d^2y_2\\hat{\\bf{j}}}{dt^2} = -\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_2\\hat{\\bf{i}}+y_2\\hat{\\bf{j}})+\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_1\\hat{\\bf{i}}+y_1\\hat{\\bf{j}})\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Now, we can separate the equations for each of the coordinates:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2x_1}{dt^2} = -\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)x_1+\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)x_2=-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_1-x_2)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2y_1}{dt^2} = -\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)y_1+\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)y_2=-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_1-y_2)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2x_2}{dt^2} = -\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)x_2+\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)x_1=-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_2-x_1)\n", "\\end{equation}\n", "</span>\n", "        \n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2y_2}{dt^2} = -\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)y_2+\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)y_1=-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_2-y_1)\n", "\\end{equation}\n", "</span>    "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["To solve these equations numerically, you must break these equations into first-order equations:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{x_1}}{dt} = -\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_1-x_2)\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{y_1}}{dt} = -\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_1-y_2)\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{x_2}}{dt} = -\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_2-x_1)\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{y_2}}{dt} = -\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_2-y_1)\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dx_1}{dt} = v_{x_1}\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dy_1}{dt} = v_{y_1}\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dx_2}{dt} = v_{x_2}\n", "\\end{equation}\n", "</span>    \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dy_2}{dt} = v_{y_2}\n", "\\end{equation}\n", "</span>    "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Note that if you did not want to know the details about the motion of each mass, but only the motion of the center of mass of the masses-spring system, you could have modeled the whole system as a single particle.\n", "\n", "To solve the equations numerically, we will use the $m_1=1 kg$, $m_2 = 2 kg$, $l_0 = 0.5 m$, $k = 90 N/m$ and $x_{1_0} = 0 m$, $x_{2_0} = 0 m$, $y_{1_0} = 1 m$, $y_{2_0} = -1 m$, $v_{x1_0} = -2 m/s$, $v_{x2_0} = 0 m/s$, $v_{y1_0} = 0 m/s$, $v_{y2_0} = 0 m/s$. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-02-07T19:11:41.996766Z", "start_time": "2021-02-07T19:11:37.785938Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x01 = 0\n", "y01= 0.5\n", "x02 = 0\n", "y02 = -0.5\n", "vx01 = 0.1\n", "vy01 = 0\n", "vx02 = -0.1\n", "vy02 = 0\n", "\n", "x1= x01\n", "y1 = y01\n", "x2= x02\n", "y2 = y02\n", "vx1= vx01\n", "vy1 = vy01\n", "vx2= vx02\n", "vy2 = vy02\n", "r1 = np.array([x1,y1])\n", "r2 = np.array([x2,y2])\n", "\n", "k = 30\n", "m1 = 1\n", "m2 = 1\n", "l0 = 0.5\n", "\n", "dt = 0.0001\n", "t = np.arange(0,5,dt)\n", "\n", "for i in t[1:]:\n", "    \n", "    dvx1dt = -k/m1*(x1-x2)*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))\n", "    dvx2dt = -k/m2*(x2-x1)*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))\n", "    dvy1dt = -k/m1*(y1-y2)*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))\n", "    dvy2dt = -k/m2*(y2-y1)*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))\n", "    \n", "    dx1dt = vx1\n", "    dx2dt = vx2\n", "    dy1dt = vy1\n", "    dy2dt = vy2\n", "    \n", "    x1 = x1 + dt*dx1dt\n", "    x2 = x2 + dt*dx2dt\n", "    y1 = y1 + dt*dy1dt\n", "    y2 = y2 + dt*dy2dt\n", "    \n", "    vx1 = vx1 + dt*dvx1dt\n", "    vx2 = vx2 + dt*dvx2dt\n", "    vy1 = vy1 + dt*dvy1dt\n", "    vy2 = vy2 + dt*dvy2dt\n", "    \n", "    r1 = np.vstack((r1,np.array([x1,y1])))\n", "    r2 = np.vstack((r2,np.array([x2,y2])))\n", "\n", "springLength = np.sqrt((r1[:,0]-r2[:,0])**2+(r1[:,1]-r2[:,1])**2)    \n", "plt.figure(figsize=(8, 4))\n", "plt.plot(t, springLength, lw=4)\n", "plt.xlabel('t(s)')\n", "plt.ylabel('Spring length (m)')\n", "plt.show()\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(r1[:,0], r1[:,1], 'r.', lw=4)\n", "plt.plot(r2[:,0], r2[:,1], 'b.', lw=4)\n", "plt.plot((m1*r1[:,0]+m2*r2[:,0])/(m1+m2), (m1*r1[:,1]+m2*r2[:,1])/(m1+m2),'g.')\n", "plt.xlim(-0.7,0.7)\n", "plt.ylim(-0.7,0.7)\n", "plt.xlabel('x(m)')\n", "plt.ylabel('y(m)')\n", "plt.title('Masses position')\n", "plt.legend(('Mass1','Mass 2','Masses center of mass'))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 6. Particle under action of gravity and linear air resistance\n", "\n", "Below is the free-body diagram of a particle with the gravity force and a linear drag force due to the air resistance. \n", "\n", "<figure><center><img src=\"../images/ballGravLinearRes.png\" alt=\"Linear spring\" width=\"700\"/><figcaption><i>Figure. Particle under action of gravity and linear air resistance.</i></figcaption></center></figure>  \n", "\n", "the forces being applied in the ball are:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = -mg \\hat{\\bf{j}} - b\\vec{\\bf{v}} = -mg \\hat{\\bf{j}} - b\\frac{d\\vec{\\bf{r}}}{dt} = -mg \\hat{\\bf{j}} - b\\left(\\frac{dx}{dt}\\hat{\\bf{i}}+\\frac{dy}{dt}\\hat{\\bf{j}}\\right) = - b\\frac{dx}{dt}\\hat{\\bf{i}} - \\left(mg + b\\frac{dy}{dt}\\right)\\hat{\\bf{j}}\n", "\\end{equation}\n", "</span>\n", "\n", "Writing down <PERSON>'s second law:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\rightarrow - b\\frac{dx}{dt}\\hat{\\bf{i}} - \\left(mg + b\\frac{dy}{dt}\\right)\\hat{\\bf{j}} = m\\left(\\frac{d^2x}{dt^2}\\hat{\\bf{i}}+\\frac{d^2y}{dt^2}\\hat{\\bf{j}}\\right)\n", "\\end{equation}\n", "</span>\n", "\n", "Now, we can separate into one equation for each coordinate:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "- b\\frac{dx}{dt} = m\\frac{d^2x}{dt^2} -\\rightarrow \\frac{d^2x}{dt^2} = -\\frac{b}{m} \\frac{dx}{dt}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "-mg - b\\frac{dy}{dt} = m\\frac{d^2y}{dt^2} \\rightarrow \\frac{d^2y}{dt^2} = -\\frac{b}{m}\\frac{dy}{dt} - g\n", "\\end{equation}\n", "</span>\n", "\n", "These equations were solved in [this notebook](https://nbviewer.jupyter.org/github/BMClab/BMC/blob/master/notebooks/newtonLawForParticles.ipynb)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 7. Particle under action of gravity and nonlinear air resistance\n", "\n", "Below, is the free-body diagram of a particle with the gravity force and a drag force due to the air resistance proportional to the square of the particle velocity. \n", "\n", "<figure><center><img src=\"../images/ballGravSquareRes.png\" alt=\"Linear spring\" width=\"700\"/><figcaption><i>Figure. Particle under action of gravity and nonlinear air resistance.</i></figcaption></center></figure> \n", "    \n", "The forces being applied in the ball are:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = -mg \\hat{\\bf{j}} - bv^2\\hat{\\bf{e_t}} = -mg \\hat{\\bf{j}} - b (v_x^2+v_y^2) \\frac{v_x\\hat{\\bf{i}}+v_y\\hat{\\bf{j}}}{\\sqrt{v_x^2+v_y^2}} =   -mg \\hat{\\bf{j}} - b \\sqrt{v_x^2+v_y^2} \\,(v_x\\hat{\\bf{i}}+v_y\\hat{\\bf{j}}) =  -mg \\hat{\\bf{j}} - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\left(\\frac{dx}{dt} \\hat{\\bf{i}}+\\frac{dy}{dt}\\hat{\\bf{j}}\\right)\n", "\\end{equation}\n", "</span>\n", "\n", "Writing down <PERSON>'s second law:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\vec{\\bf{F}} = m \\frac{d^2\\vec{\\bf{r}}}{dt^2} \\rightarrow -mg \\hat{\\bf{j}} - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\left(\\frac{dx}{dt} \\hat{\\bf{i}}+\\frac{dy}{dt}\\hat{\\bf{j}}\\right) = m\\left(\\frac{d^2x}{dt^2}\\hat{\\bf{i}}+\\frac{d^2y}{dt^2}\\hat{\\bf{j}}\\right)\n", "\\end{equation}\n", "</span>\n", "\n", "Now, we can separate into one equation for each coordinate:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "- b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dx}{dt} = m\\frac{d^2x}{dt^2} \\rightarrow \\frac{d^2x}{dt^2} = - \\frac{b}{m} \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dx}{dt} \n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "-mg - b \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dy}{dt} = m\\frac{d^2y}{dt^2} \\rightarrow \\frac{d^2y}{dt^2} = - \\frac{b}{m} \\sqrt{\\left(\\frac{dx}{dt} \\right)^2+\\left(\\frac{dy}{dt} \\right)^2} \\,\\frac{dy}{dt} -g \n", "\\end{equation}\n", "</span>\n", "\n", "These equations were solved numerically in [this notebook](https://nbviewer.jupyter.org/github/BMClab/BMC/blob/master/notebooks/newtonLawForParticles.ipynb)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 8. Linear spring and damping on bidimensional horizontal movement\n", "\n", "This situation is very similar to the example of horizontal movement with one spring and two masses, with a damper added in parallel to the spring.\n", "\n", "<figure><center><img src=\"../images/twomassDamp.png\" alt=\"Linear spring\"/><figcaption><i>Figure. Linear spring and damping on bidimensional horizontal movement.</i></figcaption></center></figure>    "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Now, the forces acting on each mass are the force due to the spring and the force due to the damper. \n", "\n", "<figure><center><img src=\"../images/twomassDampFBD.png\" alt=\"Linear spring\" width=\"200\"/><figcaption><i>Figure. FBD of linear spring and damping on bidimensional horizontal movement.</i></figcaption></center></figure>    \n", "\n", "So, the forces acting on mass 1 is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{F_1}} = b\\frac{d(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})}{dt} +  k\\left(||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||-l_0\\right)\\frac{(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||} =  b\\frac{d(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})}{dt} +  k\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})\n", "\\end{equation}\n", "</span>\n", "\n", "and the forces acting on mass 2 is:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\bf{F_2}} = b\\frac{d(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})}{dt} +  k\\left(||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||-l_0\\right)\\frac{(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})}{||\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}}||}= b\\frac{d(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})}{dt} +  k\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Applying the Newton's second law for the masses:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "     m_1\\frac{d^2\\vec{\\bf{r_1}}}{dt^2} = b\\frac{d(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})}{dt}+k\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}})\n", "    \\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "     \\frac{d^2\\vec{\\bf{r_1}}}{dt^2} = -\\frac{b}{m_1}\\frac{d\\vec{\\bf{r_1}}}{dt} -\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_1}} + \\frac{b}{m_1}\\frac{d\\vec{\\bf{r_2}}}{dt}+\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_2}}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "     \\frac{d^2x_1\\hat{\\bf{i}}}{dt^2}+\\frac{d^2y_1\\hat{\\bf{j}}}{dt^2} = -\\frac{b}{m_1}\\left(\\frac{dx_1\\hat{\\bf{i}}}{dt}+\\frac{dy_1\\hat{\\bf{j}}}{dt}\\right)-\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_1\\hat{\\bf{i}}+y_1\\hat{\\bf{j}})+\\frac{b}{m_1}\\left(\\frac{dx_2\\hat{\\bf{i}}}{dt}+\\frac{dy_2\\hat{\\bf{j}}}{dt}\\right)+\\frac{k}{m_1}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_2\\hat{\\bf{i}}+y_2\\hat{\\bf{j}}) = -\\frac{b}{m_1}\\left(\\frac{dx_1\\hat{\\bf{i}}}{dt}+\\frac{dy_1\\hat{\\bf{j}}}{dt}-\\frac{dx_2\\hat{\\bf{i}}}{dt}-\\frac{dy_2\\hat{\\bf{j}}}{dt}\\right)-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_1\\hat{\\bf{i}}+y_1\\hat{\\bf{j}}-x_2\\hat{\\bf{i}}-y_2\\hat{\\bf{j}})\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["\\begin{equation}\n", "     m_2\\frac{d^2\\vec{\\bf{r_2}}}{dt^2} = b\\frac{d(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})}{dt}+k\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(\\vec{\\bf{r_1}}-\\vec{\\bf{r_2}})\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "     \\frac{d^2\\vec{\\bf{r_2}}}{dt^2} = -\\frac{b}{m_2}\\frac{d\\vec{\\bf{r_2}}}{dt} -\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_2}} + \\frac{b}{m_2}\\frac{d\\vec{\\bf{r_1}}}{dt}+\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)\\vec{\\bf{r_1}}\n", "\\end{equation}\n", "\n", "\\begin{equation}\n", "    \\frac{d^2x_2\\hat{\\bf{i}}}{dt^2}+\\frac{d^2y_2\\hat{\\bf{j}}}{dt^2} = -\\frac{b}{m_2}\\left(\\frac{dx_2\\hat{\\bf{i}}}{dt}+\\frac{dy_2\\hat{\\bf{j}}}{dt}\\right)-\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_2\\hat{\\bf{i}}+y_2\\hat{\\bf{j}})+\\frac{b}{m_2}\\left(\\frac{dx_1\\hat{\\bf{i}}}{dt}+\\frac{dy_1\\hat{\\bf{j}}}{dt}\\right)+\\frac{k}{m_2}\\left(1-\\frac{l_0}{||\\vec{\\bf{r_2}}-\\vec{\\bf{r_1}}||}\\right)(x_1\\hat{\\bf{i}}+y_1\\hat{\\bf{j}})=-\\frac{b}{m_2}\\left(\\frac{dx_2\\hat{\\bf{i}}}{dt}+\\frac{dy_2\\hat{\\bf{j}}}{dt}-\\frac{dx_1\\hat{\\bf{i}}}{dt}-\\frac{dy_1\\hat{\\bf{j}}}{dt}\\right)-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_2\\hat{\\bf{i}}+y_2\\hat{\\bf{j}}-x_1\\hat{\\bf{i}}-y_1\\hat{\\bf{j}})\n", "\\end{equation}"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Now, we can separate the equations for each of the coordinates:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2x_1}{dt^2} = -\\frac{b}{m_1}\\left(\\frac{dx_1}{dt}-\\frac{dx_2}{dt}\\right)-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_1-x_2)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2y_1}{dt^2} = -\\frac{b}{m_1}\\left(\\frac{dy_1}{dt}-\\frac{dy_2}{dt}\\right)-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_1-y_2)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2x_2}{dt^2} = -\\frac{b}{m_2}\\left(\\frac{dx_2}{dt}-\\frac{dx_1}{dt}\\right)-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_2-x_1)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{d^2y_2}{dt^2} = -\\frac{b}{m_2}\\left(\\frac{dy_2}{dt}-\\frac{dy_1}{dt}\\right)-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_2-y_1)\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["If you want to solve these equations numerically, you must break these equations into first-order  equations:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{x_1}}{dt} = -\\frac{b}{m_1}\\left(v_{x_1}-v_{x_2}\\right)-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_1-x_2)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{y_1}}{dt} =  -\\frac{b}{m_1}\\left(v_{y_1}-v_{y_2}\\right)-\\frac{k}{m_1}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_1-y_2)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{x_2}}{dt} = -\\frac{b}{m_2}\\left(v_{x_2}-v_{x_1}\\right)-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(x_2-x_1)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dv_{y_2}}{dt} = -\\frac{b}{m_2}\\left(v_{y_2}-v_{y_1}\\right)-\\frac{k}{m_2}\\left(1-\\frac{l_0}{\\sqrt{(x_2-x_1)^2+(y_2-y_1)^2}}\\right)(y_2-y_1)\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dx_1}{dt} = v_{x_1}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dy_1}{dt} = v_{y_1}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dx_2}{dt} = v_{x_2}\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\frac{dy_2}{dt} = v_{y_2}\n", "\\end{equation}\n", "</span>\n", "\n", "To solve the equations numerically, we will use the $m_1=1 kg$, $m_2 = 2 kg$, $l_0 = 0.5 m$, $k = 10 N/m$, $b = 0.6 Ns/m$ and $x_{1_0} = 0 m$, $x_{2_0} = 0 m$, $y_{1_0} = 1 m$, $y_{2_0} = -1 m$, $v_{x1_0} = -2 m/s$, $v_{x2_0} = 1 m/s$, $v_{y1_0} = 0 m/s$, $v_{y2_0} = 0 m/s$. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-02-07T19:10:18.144952Z", "start_time": "2021-02-07T19:10:17.150046Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x01 = 0\n", "y01= 1\n", "x02 = 0\n", "y02 = -1\n", "\n", "vx01 = -2\n", "vy01 = 0\n", "vx02 = 1\n", "vy02 = 0\n", "\n", "x1= x01\n", "y1 = y01\n", "x2= x02\n", "y2 = y02\n", "vx1= vx01\n", "vy1 = vy01\n", "vx2= vx02\n", "vy2 = vy02\n", "r1 = np.array([x1,y1])\n", "r2 = np.array([x2,y2])\n", "\n", "k = 10\n", "m1 = 1\n", "m2 = 2\n", "b = 0.6\n", "l0 = 0.5\n", "\n", "dt = 0.001\n", "t = np.arange(0,5,dt)\n", "\n", "for i in t[1:]:\n", "    dvx1dt = -b/m1*(vx1-vx2) -k/m1*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))*(x1-x2)\n", "    dvx2dt = -b/m2*(vx2-vx1) -k/m2*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))*(x2-x1)\n", "    dvy1dt = -b/m1*(vy1-vy2) -k/m1*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))*(y1-y2)\n", "    dvy2dt = -b/m2*(vy2-vy1) -k/m2*(1-l0/np.sqrt((x2-x1)**2+(y2-y1)**2))*(y2-y1)\n", "    dx1dt = vx1\n", "    dx2dt = vx2\n", "    dy1dt = vy1\n", "    dy2dt = vy2\n", "    x1 = x1 + dt*dx1dt\n", "    x2 = x2 + dt*dx2dt\n", "    y1 = y1 + dt*dy1dt\n", "    y2 = y2 + dt*dy2dt\n", "    vx1 = vx1 + dt*dvx1dt\n", "    vx2 = vx2 + dt*dvx2dt\n", "    vy1 = vy1 + dt*dvy1dt\n", "    vy2 = vy2 + dt*dvy2dt\n", "    r1 = np.vstack((r1,np.array([x1,y1])))\n", "    r2 = np.vstack((r2,np.array([x2,y2])))\n", "\n", "springDampLength = np.sqrt((r1[:,0]-r2[:,0])**2+(r1[:,1]-r2[:,1])**2)\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(t, springDampLength, lw=4)\n", "plt.xlabel('t(s)')\n", "plt.ylabel('Spring length (m)')\n", "plt.show()\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(r1[:,0], r1[:,1], 'r.', lw=4)\n", "plt.plot(r2[:,0], r2[:,1], 'b.', lw=4)\n", "plt.plot((m1*r1[:,0]+m2*r2[:,0])/(m1+m2), (m1*r1[:,1]+m2*r2[:,1])/(m1+m2),'g.')\n", "plt.xlim(-2,2)\n", "plt.ylim(-2,2)\n", "plt.xlabel('x(m)')\n", "plt.ylabel('y(m)')\n", "plt.title('Masses position')\n", "plt.legend(('Mass1','Mass 2','Masses center of mass'))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 9. Simple muscle model\n", "\n", "The diagram below shows a simple muscle model. The spring in the left represents the tendinous tissues and the spring in the right represents the elastic properties of the muscle fibers. The damping is present to model the viscous properties of the muscle fibers, the element CE is the contractile element (force production) and the mass $m$ is the muscle mass. \n", "\n", "The length $L_{MT}$ is the length of the muscle plus the tendon. In our model $L_{MT}$ is constant, but it could be a function of the joint angle. \n", "\n", "<figure><center><img src=\"../images/simpleMuscle.png\" alt=\"Linear spring\" width=\"500\"/><figcaption><i>Figure. Simple muscle model.</i></figcaption></center></figure>    \n", "\n", "The length of the tendon will be denoted by $l_T(t)$ and the muscle length, by $l_{m}(t)$. Both lengths are related by each other  by the following expression:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "l_t(t) + l_m(t) = L_{MT}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The free-body diagram of the muscle mass is depicted below.\n", "\n", "<figure><center><img src=\"../images/simpleMuscleFBD.png\" alt=\"Linear spring\" width=\"200\"/><figcaption><i>Figure. FBD of simple muscle model.</i></figcaption></center></figure>    \n", "    \n", "The resultant force being applied in the muscle mass is:\n", "\n", "<span class=\"notranslate\">\n", "$$\\vec{\\bf{F}} = -k_T(||\\vec{\\bf{r_m}}||-l_{t_0})\\frac{\\vec{\\bf{r_m}}}{||\\vec{\\bf{r_m}}||} + b\\frac{d(L_{MT}\\hat{\\bf{i}} - \\vec{\\bf{r_{m}}})}{dt} + k_m (||L_{MT}\\hat{\\bf{i}} - \\vec{\\bf{r_{m}}}||-l_{m_0})\\frac{L_{MT}\\hat{\\bf{i}} - \\vec{\\bf{r_{m}}}}{||L_{MT}\\hat{\\bf{i}} - \\vec{\\bf{r_{m}}}||} +\\vec{\\bf{F}}{\\bf{_{CE}}}(t)$$\n", "</span>\n", "\n", "where $\\vec{\\bf{r_m}}$ is the muscle mass position."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Since the model is unidimensional, we can assume that the force $\\vec{\\bf{F}}\\bf{_{CE}}(t)$ is in the x direction, so the analysis will be done only in this direction. \n", "\n", "<span class=\"notranslate\">\n", "$$F = -k_T(l_t-l_{t_0}) + b\\frac{d(L_{MT} - l_t)}{dt} + k_m (l_m-l_{m_0}) + F_{CE}(t) \\\\\n", "F = -k_T(l_t-l_{t_0}) -b\\frac{dl_t}{dt} + k_m (L_{MT}-l_t-l_{m_0}) + F_{CE}(t) \\\\\n", "F = -b\\frac{dl_t}{dt}-(k_T+k_m)l_t+F_{CE}(t)+k_Tl_{t_0}+k_m(L_{MT}-l_{m_0})$$\n", "</span>\n", "\n", "Applying the Newton's second law:\n", "\n", "<span class=\"notranslate\">\n", "$$m\\frac{d^2l_t}{dt^2} = -b\\frac{dl_t}{dt}-(k_T+k_m)l_t+F_{CE}(t)+k_Tl_{t_0}+k_m(L_{MT}-l_{m_0})$$\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["To solve this equation, we must break the equation into two first-order differential equations:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{dvt}{dt} = - \\frac{b}{m}v_t - \\frac{k_T+k_m}{m}l_t +\\frac{F_{CE}(t)}{m} + \\frac{k_T}{m}l_{t_0}+\\frac{k_m}{m}(L_{MT}-l_{m_0})\n", "\\end{equation}\n", "</span>\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\frac{d l_t}{dt} = v_t\n", "\\end{equation}\n", "</span>\n", "\n", "Now, we can solve these equations by using some numerical method. To obtain the solution, we will use the damping factor of the muscle as $b = 10\\,Ns/m$, the muscle mass is $m = 2 kg$, the stiffness of the tendon as $k_t=1000\\,N/m$ and the elastic element of the muscle as $k_m=1500\\,N/m$. The tendon-length is $L_{MT} = 0.35\\,m$, and the tendon equilibrium length is $l_{t0} = 0.28\\,m$ and the muscle fiber equilibrium length is $l_{m0} = 0.07\\,m$. Both the tendon and the muscle fiber are at their equilibrium lengths and at rest.\n", "\n", "Also, we will model the force of the contractile element as a Heaviside step of $90\\,N$ (90 N beginning at $t=0$), but normally it is modeled as a function of $l_m$ and $v_m$ having a neural activation signal as input. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-02-07T19:10:28.346663Z", "start_time": "2021-02-07T19:10:18.148266Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["m = 2\n", "b = 10\n", "km = 1500\n", "kt = 1000\n", "lt0 = 0.28\n", "lm0 = 0.07\n", "\n", "Lmt = 0.35\n", "vt0 = 0\n", "\n", "dt = 0.0001\n", "t = np.arange(0, 10, dt)\n", "\n", "Fce = 90\n", "\n", "lt = lt0\n", "vt = vt0\n", "ltp = np.array([lt0])\n", "lmp = np.array([lm0])\n", "Ft = np.array([0])\n", "for i in range(1,len(t)):\n", "    dvtdt = -b/m*vt-(kt+km)/m*lt  + Fce/m + kt/m*lt0 +km/m*(Lmt-lm0)\n", "    dltdt = vt\n", "    vt = vt + dt*dvtdt\n", "    lt = lt + dt*dltdt\n", "    Ft = np.vstack((Ft,np.array(kt*(lt-lt0))))\n", "    ltp = np.vstack((ltp,np.array(lt)))\n", "    lmp = np.vstack((lmp,np.array(Lmt - lt)))\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(t, Ft, lw=4)\n", "plt.xlabel('t(s)')\n", "plt.ylabel('Tendon force (N)')\n", "plt.show()\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(t, ltp, lw=4)\n", "plt.plot(t, lmp, lw=4)\n", "plt.xlabel('t(s)')\n", "plt.ylabel('Length (m)')\n", "plt.legend(('Tendon length', 'Muscle fiber length'))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "- Read the 2nd chapter of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about free-body diagrams;\n", "- Read the 13th of the [<PERSON><PERSON><PERSON>'s book](https://drive.google.com/file/d/1sDLluWCiBCog2C11_Iu1fjv-BtfVUxBU/view) (available in the Classroom)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Problems\n", "\n", "1. Draw free-body diagram for (see answers at https://youtu.be/3rZR7FSSidc):\n", "   1. A book is at rest on a table.  \n", "   2. A book is attached to a string and hanging from the ceiling.  \n", "   3. A person pushes a crate to the right across a floor at a constant speed.  \n", "   4. A skydiver is falling downward a speeding up.  \n", "   5. A rightward-moving car has locked wheels and is skidding to a stop.  \n", "   6. A freight elevator is attached by a cable, being pulled upward, and slowing down. It is not touching the sides of the elevator shaft.\n", "   \n", "2. A block (2) is stacked on top of block (1) which is at rest on a surface (all contacts have friction). Which is the maximum horizontal force that can be applied to the block (1) such that block (2) does not slip? Answer at https://youtu.be/63U4_OxohOw\n", "\n", "3. Consider two masses m1 = 5 kg and m2 = 3 kg attached to a pulley which is attached to a wall with a rope and m1 is pulled in the opposite direction with force F = 100 N as shown in the figure below. Calculate (Answer at https://youtu.be/Dgg76vNChEU):\n", "   1. Friction force on m1\n", "   2. Friction force on m2\n", "   3. Tension on the rope attached to masses m1 and m2\n", "   4. Acceleration of m1  \n", "<figure><img src=\"./../images/friction_block.png\" width=\"300\"/></figure>\n", "\n", "\n", "4. (Example 13.4 of <PERSON><PERSON><PERSON>'s book) A smooth 2-kg collar C, shown in the figure below, is attached to a spring having a stiffness k = 3 N/m and an unstretched length of 0.75 m. If the collar is released from rest at A, determine its acceleration and the normal force of the rod on the collar at the instant y = 1 m.\n", "<figure><img src=\"./../images/spring_collar.png\" width=\"200\"/></figure>\n", "\n", "5. (Example 13.5 of <PERSON><PERSON><PERSON>'s book) The 100-kg block A shown in the figure below is released from rest. If the masses of the pulleys and the cord are neglected, determine the speed of the 20-kg block B in 2 s.\n", "<figure><img src=\"./../images/pulley_block.png\" width=\"200\"/></figure>\n", "\n", "6. (Example 13.9 of <PERSON><PERSON><PERSON>'s book) The 60-kg skateboarder in the figure below coasts down the circular track. If he starts from rest when e = 0$^o$, determine the magnitude of the normal reaction the track exerts on him when e = 60$^o$. Neglect his size for the calculation.\n", "<figure><img src=\"./../images/skateboarder.png\" width=\"300\"/></figure>\n", "\n", "7. Solve the problems 2.3.9, 2.3.20, 11.1.6, 13.1.6 (a, b, c, d, f), 13.1.7, 13.1.10 (a, b) from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2019) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press. \n", "- <PERSON><PERSON> <PERSON><PERSON> (2010) [Engineering Mechanics Dynamics](https://drive.google.com/file/d/1sDLluWCiBCog2C11_Iu1fjv-BtfVUxBU/view). 12th Edition. Pearson Prentice Hall.\n", "- <PERSON><PERSON> & <PERSON> (2006) [Biomechanics of the Musculo-skeletal System](https://books.google.com.br/books?id=hOIeAQAAIAAJ&dq=editions:ISBN0470017678). 3rd Edition. Wiley."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "pt", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}