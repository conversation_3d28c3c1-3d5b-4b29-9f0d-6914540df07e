{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Rigid-body transformations in three-dimensions\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Translation\" data-toc-modified-id=\"Translation-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Translation</a></span></li><li><span><a href=\"#Rotation\" data-toc-modified-id=\"Rotation-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Rotation</a></span><ul class=\"toc-item\"><li><span><a href=\"#Determination-of-the-rotation-matrix-using-direction-cosines\" data-toc-modified-id=\"Determination-of-the-rotation-matrix-using-direction-cosines-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>Determination of the rotation matrix using direction cosines</a></span></li><li><span><a href=\"#Determination-of-the-rotation-matrix-using-the-basis-method\" data-toc-modified-id=\"Determination-of-the-rotation-matrix-using-the-basis-method-3.2\"><span class=\"toc-item-num\">3.2&nbsp;&nbsp;</span>Determination of the rotation matrix using the basis method</a></span></li><li><span><a href=\"#Euler-angles\" data-toc-modified-id=\"Euler-angles-3.3\"><span class=\"toc-item-num\">3.3&nbsp;&nbsp;</span>Euler angles</a></span></li><li><span><a href=\"#Elemental-rotations\" data-toc-modified-id=\"Elemental-rotations-3.4\"><span class=\"toc-item-num\">3.4&nbsp;&nbsp;</span>Elemental rotations</a></span></li><li><span><a href=\"#Rotations-around-the-fixed-coordinate-system\" data-toc-modified-id=\"Rotations-around-the-fixed-coordinate-system-3.5\"><span class=\"toc-item-num\">3.5&nbsp;&nbsp;</span>Rotations around the fixed coordinate system</a></span></li><li><span><a href=\"#Rotations-around-the-local-coordinate-system\" data-toc-modified-id=\"Rotations-around-the-local-coordinate-system-3.6\"><span class=\"toc-item-num\">3.6&nbsp;&nbsp;</span>Rotations around the local coordinate system</a></span></li><li><span><a href=\"#Sequence-of-elemental-rotations\" data-toc-modified-id=\"Sequence-of-elemental-rotations-3.7\"><span class=\"toc-item-num\">3.7&nbsp;&nbsp;</span>Sequence of elemental rotations</a></span></li><li><span><a href=\"#Rotations-in-a-coordinate-system-is-equivalent-to-minus-rotations-in-the-other-coordinate-system\" data-toc-modified-id=\"Rotations-in-a-coordinate-system-is-equivalent-to-minus-rotations-in-the-other-coordinate-system-3.8\"><span class=\"toc-item-num\">3.8&nbsp;&nbsp;</span>Rotations in a coordinate system is equivalent to minus rotations in the other coordinate system</a></span></li><li><span><a href=\"#Rotations-in-a-coordinate-system-is-the-transpose-of-inverse-order-of-rotations-in-the-other-coordinate-system\" data-toc-modified-id=\"Rotations-in-a-coordinate-system-is-the-transpose-of-inverse-order-of-rotations-in-the-other-coordinate-system-3.9\"><span class=\"toc-item-num\">3.9&nbsp;&nbsp;</span>Rotations in a coordinate system is the transpose of inverse order of rotations in the other coordinate system</a></span></li><li><span><a href=\"#Sequence-of-rotations-of-a-Vector\" data-toc-modified-id=\"Sequence-of-rotations-of-a-Vector-3.10\"><span class=\"toc-item-num\">3.10&nbsp;&nbsp;</span>Sequence of rotations of a Vector</a></span></li><li><span><a href=\"#The-12-different-sequences-of-Euler-angles\" data-toc-modified-id=\"The-12-different-sequences-of-Euler-angles-3.11\"><span class=\"toc-item-num\">3.11&nbsp;&nbsp;</span>The 12 different sequences of Euler angles</a></span></li><li><span><a href=\"#Line-of-nodes\" data-toc-modified-id=\"Line-of-nodes-3.12\"><span class=\"toc-item-num\">3.12&nbsp;&nbsp;</span>Line of nodes</a></span></li><li><span><a href=\"#Determination-of-the-Euler-angles\" data-toc-modified-id=\"Determination-of-the-Euler-angles-3.13\"><span class=\"toc-item-num\">3.13&nbsp;&nbsp;</span>Determination of the Euler angles</a></span></li><li><span><a href=\"#Gimbal-lock\" data-toc-modified-id=\"Gimbal-lock-3.14\"><span class=\"toc-item-num\">3.14&nbsp;&nbsp;</span>Gimbal lock</a></span></li></ul></li><li><span><a href=\"#Determination-of-the-rotation-matrix\" data-toc-modified-id=\"Determination-of-the-rotation-matrix-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Determination of the rotation matrix</a></span></li><li><span><a href=\"#Determination-of-the-rotation-matrix-between-two-local-coordinate-systems\" data-toc-modified-id=\"Determination-of-the-rotation-matrix-between-two-local-coordinate-systems-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Determination of the rotation matrix between two local coordinate systems</a></span></li><li><span><a href=\"#Translation-and-Rotation\" data-toc-modified-id=\"Translation-and-Rotation-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Translation and Rotation</a></span><ul class=\"toc-item\"><li><span><a href=\"#Transformation-matrix\" data-toc-modified-id=\"Transformation-matrix-6.1\"><span class=\"toc-item-num\">6.1&nbsp;&nbsp;</span>Transformation matrix</a></span></li><li><span><a href=\"#Example-with-actual-motion-analysis-data\" data-toc-modified-id=\"Example-with-actual-motion-analysis-data-6.2\"><span class=\"toc-item-num\">6.2&nbsp;&nbsp;</span>Example with actual motion analysis data</a></span></li></ul></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-Internet\" data-toc-modified-id=\"Video-lectures-on-the-Internet-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Video lectures on the Internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>References</a></span></li><li><span><a href=\"#Function-euler_rotmatrix.py\" data-toc-modified-id=\"Function-euler_rotmatrix.py-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>Function <code>euler_rotmatrix.py</code></a></span></li><li><span><a href=\"#Appendix\" data-toc-modified-id=\"Appendix-12\"><span class=\"toc-item-num\">12&nbsp;&nbsp;</span>Appendix</a></span><ul class=\"toc-item\"><li><span><a href=\"#How-to-load-.trc-files\" data-toc-modified-id=\"How-to-load-.trc-files-12.1\"><span class=\"toc-item-num\">12.1&nbsp;&nbsp;</span>How to load .trc files</a></span></li></ul></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "skip"}}, "source": ["The kinematics of a rigid body is completely described by its pose, i.e., its position and orientation in space (and the corresponding changes, translation and rotation). In a three-dimensional space, at least three coordinates and three angles are necessary to describe the pose of the rigid body, totalizing six degrees of freedom for a rigid body.\n", "\n", "In motion analysis, to describe a translation and rotation of a rigid body with respect to a coordinate system, typically we attach another coordinate system to the rigid body and determine a transformation between these two coordinate systems.\n", "\n", "A transformation is any function mapping a set to another set. For the description of the kinematics of rigid bodies, we are interested only in what is called rigid or Euclidean transformations (denoted as SE(3) for the three-dimensional space) because they preserve the distance between every pair of points of the body (which is considered rigid by definition). Translations and rotations are examples of rigid transformations (a reflection is also an example of rigid transformation but this changes the right-hand axis convention to a left hand, which usually is not of interest). In turn, rigid transformations are examples of [affine transformations](https://en.wikipedia.org/wiki/Affine_transformation). Examples of other affine transformations are shear and scaling transformations (which preserves angles but not lengths). \n", "\n", "We will follow the same rationale as in the notebook [Rigid-body transformations in a plane (2D)](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/Transformation2D.ipynb) and we will skip the fundamental concepts already covered there. So, you if haven't done yet, you should read that notebook before continuing here."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:04.441332Z", "start_time": "2021-11-18T00:05:04.341026Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["# Import the necessary libraries\n", "import numpy as np\n", "# suppress scientific notation for small numbers:\n", "np.set_printoptions(precision=4, suppress=True)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Translation\n", "\n", "A pure three-dimensional translation of a rigid body (or a coordinate system attached to it) in relation to other rigid body (with other coordinate system) is illustrated in the figure below.  \n", "<br>\n", "<figure><img src='./../images/translation3D.png' alt='translation 3D'/> <figcaption><center><i>Figure. A point in three-dimensional space represented in two coordinate systems, with one coordinate system translated.</i></center></figcaption> </figure>\n", "\n", "The position of point $\\mathbf{P}$ originally described in the $xyz$ (local) coordinate system but now described in the $\\mathbf{XYZ}$ (Global) coordinate system in vector form is:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{L_G} + \\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "Or in terms of its components:  \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{P_X} =& \\mathbf{L_X} + \\mathbf{P}_x \\\\\n", "\\mathbf{P_Y} =& \\mathbf{L_Y} + \\mathbf{P}_y \\\\\n", "\\mathbf{P_Z} =& \\mathbf{L_Z} + \\mathbf{P}_z \n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "And in matrix form:  \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y} \\\\\n", "\\mathbf{P_Z} \n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\mathbf{L_X} \\\\\n", "\\mathbf{L_Y} \\\\\n", "\\mathbf{L_Z} \n", "\\end{bmatrix} +\n", "\\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y \\\\\n", "\\mathbf{P}_z \n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "From classical mechanics, this is an example of [Galilean transformation](http://en.wikipedia.org/wiki/Galilean_transformation).   \n", "\n", "Let's use Python to compute some numeric examples:"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["For example, if the local coordinate system is translated by $\\mathbf{L_G}=[1, 2, 3]$ in relation to the Global coordinate system, a point with coordinates $\\mathbf{P_l}=[4, 5, 6]$ at the local coordinate system will have the position $\\mathbf{P_G}=[5, 7, 9]$ at the Global coordinate system:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:05.241043Z", "start_time": "2021-11-18T00:05:05.234528Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["array([5, 7, 9])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["LG = np.array([1, 2, 3])  # Numpy array\n", "Pl = np.array([4, 5, 6])\n", "PG = LG + Pl\n", "PG"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["This operation also works if we have more than one point (<PERSON><PERSON><PERSON><PERSON> try to guess how to handle vectors with different dimensions):"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:05.627167Z", "start_time": "2021-11-18T00:05:05.623887Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["array([[ 2,  4,  6],\n", "       [ 5,  7,  9],\n", "       [ 8, 10, 12]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["Pl = np.array([[1, 2, 3],\n", "               [4, 5, 6],\n", "               [7, 8, 9]])  # 2D array with 3 rows and 3 columns\n", "PG = LG + Pl\n", "PG"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Rotation\n", "\n", "A pure three-dimensional rotation of a $xyz$ (local) coordinate system in relation to other $\\mathbf{XYZ}$ (Global) coordinate system and the position of a point in these two coordinate systems are illustrated in the next figure (remember that this is equivalent to describing a rotation between two rigid bodies).  \n", "<br>\n", "<figure><img src='./../images/rotation3D.png' alt='rotation 3D'/> <figcaption><center><i>A point in three-dimensional space represented in two coordinate systems, with one system rotated.</i></center></figcaption> </figure>\n", "\n", "An important characteristic of angles in the three-dimensional space is that angles cannot be treated as vectors: the result of a sequence of rotations of a rigid body around different axes depends on the order of the rotations, as illustrated in the next figure.  \n", "<br>\n", "<figure>\n", "<img src='./../images/rotationsseqs2.png' alt='rotations'/><figcaption><i>Figure. The result of a sequence of rotations around different axes of a coordinate system depends on the order of the rotations. In the first example (first row), the rotations are around a Global (fixed) coordinate system. In the second example (second row), the rotations are around a local (rotating) coordinate system.</i></figcaption>\n", "</figure>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Determination of the rotation matrix using direction cosines\n", "\n", "In analogy to the rotation in two dimensions, we can calculate the rotation matrix that describes the rotation of the $xyz$ (local) coordinate system in relation to the $\\mathbf{XYZ}$ (Global) coordinate system using the direction cosines between the axes of the two coordinate systems:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl}} = \\begin{bmatrix}\n", "\\cos\\mathbf{X}x & \\cos\\mathbf{X}y & \\cos\\mathbf{X}z \\\\\n", "\\cos\\mathbf{Y}x & \\cos\\mathbf{Y}y & \\cos\\mathbf{Y}z \\\\\n", "\\cos\\mathbf{Z}x & \\cos\\mathbf{Z}y & \\cos\\mathbf{Z}z\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Note however that for rotations around more than one axis, these angles will not lie in the main planes ($\\mathbf{XY, YZ, ZX}$) of the $\\mathbf{XYZ}$ coordinate system, as illustrated in the figure below for the direction angles of the $y$ axis only. Thus, the determination of these angles by simple inspection, as we have done for the two-dimensional case, would not be simple.  \n", "<br>\n", "<figure>\n", "<img src='./../images/directioncosine3D.png' width=260 alt='direction angles 3D'/> <figcaption><center><i>Figure. Definition of direction angles for the $y$ axis of the local coordinate system in relation to the $\\mathbf{XYZ}$ Global coordinate system.</i></center></figcaption>\n", "</figure>\n", "\n", "Note that the nine angles shown in the matrix above for the direction cosines are obviously redundant since only three angles are necessary to describe the orientation of a rigid body in the three-dimensional space. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Determination of the rotation matrix using the basis method\n", "\n", "Analogue to what we have described in the notebook [Rigid-body transformations in a plane (2D)](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/Transformation2D.ipynb), the columns (or rows) of the 3D rotation matrix $\\mathbf{R_{Gl}}$ (or $\\mathbf{R_{lG}}$) between two coordinate systems are the versors of the basis of the rotated coordinate system.\n", "\n", "For example, consider that we have measured the position of at least three non-collinear markers placed on the rigid body, let's calculate a basis with these positions and then the rotation matrix. \n", "\n", "If we have the position of three markers: **m1**, **m2**, **m3**, a basis (formed by three orthogonal versors) can be found as:   \n", "\n", " - First axis, **v1**, the vector **m2-m1**;   \n", " - Second axis, **v2**, the cross product between the vectors **v1** and **m3-m1**;   \n", " - Third axis, **v3**, the cross product between the vectors **v1** and **v2**.  \n", " \n", "Then, each of these vectors are normalized resulting in three orthogonal versors.   \n", "\n", "For example, given the positions m1 = [1,0,0], m2 = [0,1,0], m3 = [0,0,1], a basis can be found:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors:\n", "v1 = [-0.7071  0.7071  0.    ]\n", "v2 = [0.5774 0.5774 0.5774]\n", "v3 = [ 0.4082  0.4082 -0.8165]\n", "\n", "Norm of each versor:\n", " 1.0 1.0 1.0000000000000002\n", "\n", "Rotation matrix RGl:\n", " [[-0.7071  0.5774  0.4082]\n", " [ 0.7071  0.5774  0.4082]\n", " [ 0.      0.5774 -0.8165]]\n"]}], "source": ["m1 = np.array([1, 0, 0])\n", "m2 = np.array([0, 1, 0])\n", "m3 = np.array([0, 0, 1])\n", "\n", "v1 = m2 - m1\n", "v2 = np.cross(v1, m3 - m1)\n", "v3 = np.cross(v1, v2)\n", "\n", "print('Versors:')\n", "v1 = v1/np.linalg.norm(v1)\n", "print('v1 =', v1)\n", "\n", "v2 = v2/np.linalg.norm(v2)\n", "print('v2 =', v2)\n", "\n", "v3 = v3/np.linalg.norm(v3)\n", "print('v3 =', v3)\n", "\n", "print('\\nNorm of each versor:\\n',\n", "      np.linalg.norm(np.cross(v1, v2)),\n", "      np.linalg.norm(np.cross(v1, v3)),\n", "      np.linalg.norm(np.cross(v2, v3)))\n", "\n", "RGl = np.array([v1, v2, v3]).T  # or np.vstack((v1, v2, v3)).T\n", "print('\\nRotation matrix RGl:\\n', RGl)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["However, we have the same problem as with the rotation matrix deduced from the direction cosines; there is no simple way to determine/interpret the angles of rotation from this matrix!"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Euler angles\n", "\n", "There are different ways to describe a three-dimensional rotation of a rigid body (or of a coordinate system). The most straightforward solution would probably be to use a spherical coordinate system, but spherical coordinates would be difficult to give an anatomical or clinical interpretation. A solution that has been often employed in biomechanics to handle rotations in the three-dimensional space is to use Euler angles. Under certain conditions, Euler angles can have an anatomical interpretation, but this representation also has some caveats. Let's see the Euler angles now.\n", "\n", "[<PERSON><PERSON>](https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>) in the XVIII century showed that two three-dimensional coordinate systems with a common origin can be related by a sequence of up to three elemental rotations about the axes of the local coordinate system, where no two successive rotations may be about the same axis, which now are known as [Euler (or Eulerian) angles](http://en.wikipedia.org/wiki/Euler_angles). \n", "\n", "<br>\n", "<figure><img src='https://upload.wikimedia.org/wikipedia/commons/8/85/Euler2a.gif' width=300 alt='translation and rotation 3D'/> <figcaption><center><i>Figure. Euler angles: a way to reach any orientation using a specific sequence of elemental rotations (<a href=\"https://en.wikipedia.org/wiki/Euler_angles\">image from Wikipedia</a>).</i></center></figcaption> </figure>\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Elemental rotations\n", "\n", "First, let's see rotations around a fixed Global coordinate system as we did for the two-dimensional case. The next figure illustrates elemental rotations of the local coordinate system around each axis of the fixed Global coordinate system.  \n", "<br>\n", "<figure>\n", "<img src='./../images/rotations.png' alt='rotations'/> <figcaption><center><i>Figure. Elemental rotations of the $xyz$ coordinate system around each axis, $\\mathbf{X}$, $\\mathbf{Y}$, and $\\mathbf{Z}$, of the fixed $\\mathbf{XYZ}$ coordinate system. Note that for better clarity, the axis around where the rotation occurs is shown perpendicular to this page for each elemental rotation.</i></center></figcaption>\n", "</figure>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Rotations around the fixed coordinate system\n", "\n", "The rotation matrices for the elemental rotations around each axis of the fixed $\\mathbf{XYZ}$ coordinate system (rotations of the local coordinate system in relation to the Global coordinate system) are shown next.\n", "\n", "Around $\\mathbf{X}$ axis: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl,\\,X}} = \n", "\\begin{bmatrix}\n", "1 & 0 & 0 \\\\\n", "0 & \\cos\\alpha & -\\sin\\alpha \\\\\n", "0 & \\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Around $\\mathbf{Y}$ axis: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl,\\,Y}} = \n", "\\begin{bmatrix}\n", "\\cos\\beta & 0 & \\sin\\beta \\\\\n", "0 & 1 & 0 \\\\\n", "-\\sin\\beta & 0 & \\cos\\beta\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Around $\\mathbf{Z}$ axis: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R_{Gl,\\,Z}} = \n", "\\begin{bmatrix}\n", "\\cos\\gamma & -\\sin\\gamma & 0\\\\\n", "\\sin\\gamma & \\cos\\gamma & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "These matrices are the rotation matrices for the case of two-dimensional coordinate systems plus the corresponding terms for the third axes of the local and Global coordinate systems, which are parallel.   \n", "To understand why the terms for the third axes are 1's or 0's, for instance, remember they represent the cosine directors. The cosines between $\\mathbf{X}x$, $\\mathbf{Y}y$, and $\\mathbf{Z}z$ for the elemental rotations around respectively the $\\mathbf{X}$, $\\mathbf{Y}$, and $\\mathbf{Z}$ axes are all 1 because $\\mathbf{X}x$, $\\mathbf{Y}y$, and $\\mathbf{Z}z$ are parallel <span class=\"notranslate\">($\\cos 0^o$)</span>. The cosines of the other elements are zero because the axis around where each rotation occurs is perpendicular to the other axes of the coordinate systems <span class=\"notranslate\">($\\cos 90^o$)</span>."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Rotations around the local coordinate system\n", "\n", "The rotation matrices for the elemental rotations this time around each axis of the $xyz$ coordinate system (rotations of the Global coordinate system in relation to the local coordinate system), similarly to the two-dimensional case, are simply the transpose of the above matrices as shown next.\n", "\n", "Around $x$ axis: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R}_{\\mathbf{lG},\\,x} = \n", "\\begin{bmatrix}\n", "1 & 0 & 0 \\\\\n", "0 & \\cos\\alpha & \\sin\\alpha \\\\\n", "0 & -\\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Around $y$ axis: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R}_{\\mathbf{lG},\\,y} = \n", "\\begin{bmatrix}\n", "\\cos\\beta & 0 & -\\sin\\beta \\\\\n", "0 & 1 & 0 \\\\\n", "\\sin\\beta & 0 & \\cos\\beta\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Around $z$ axis: \n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{R}_{\\mathbf{lG},\\,z} = \n", "\\begin{bmatrix}\n", "\\cos\\gamma & \\sin\\gamma & 0\\\\\n", "-\\sin\\gamma & \\cos\\gamma & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Notice this is equivalent to instead of rotating the local coordinate system by $\\alpha, \\beta, \\gamma$ in relation to axes of the Global coordinate system, to rotate the Global coordinate system by $-\\alpha, -\\beta, -\\gamma$ in relation to the axes of the local coordinate system; remember that <span class=\"notranslate\">\n", "$\\cos(-\\:\\cdot)=\\cos(\\cdot)$</span> and <span class=\"notranslate\">$\\sin(-\\:\\cdot)=-\\sin(\\cdot)$</span>.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Sequence of elemental rotations\n", "\n", "Consider now a sequence of elemental rotations around the $\\mathbf{X}$, $\\mathbf{Y}$, and $\\mathbf{Z}$ axes of the fixed $\\mathbf{XYZ}$ coordinate system illustrated in the next figure.  \n", "<br>\n", "<figure><img src='./../images/rotations_XYZ.png' alt='rotations'/> <figcaption><center><i>Figure. Sequence of elemental rotations of the $xyz$ coordinate system around each axis, $\\mathbf{X}$, $\\mathbf{Y}$, $\\mathbf{Z}$, of the fixed $\\mathbf{XYZ}$ coordinate system.</i></center></figcaption> </figure>\n", "\n", "This sequence of elemental rotations (each one of the local coordinate system with respect to the fixed Global coordinate system) is mathematically represented by a multiplication between the rotation matrices:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{R_{Gl,\\;XYZ}} & = \\mathbf{R_{Z}} \\mathbf{R_{Y}} \\mathbf{R_{X}} \\\\\n", "\\\\ \n", "& = \\begin{bmatrix}\n", "\\cos\\gamma & -\\sin\\gamma & 0\\\\\n", "\\sin\\gamma & \\cos\\gamma & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\cos\\beta & 0 & \\sin\\beta \\\\\n", "0 & 1 & 0 \\\\\n", "-\\sin\\beta & 0 & \\cos\\beta\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "1 & 0 & 0 \\\\\n", "0 & \\cos\\alpha & -\\sin\\alpha \\\\\n", "0 & \\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix} \\\\\n", "\\\\ \n", "& =\n", "\\begin{bmatrix}\n", "\\cos\\beta\\:\\cos\\gamma \\;&\\;\n", "\\sin\\alpha\\:\\sin\\beta\\:\\cos\\gamma-\\cos\\alpha\\:\\sin\\gamma \\;&\\;\n", "\\cos\\alpha\\:\\sin\\beta\\:cos\\gamma+\\sin\\alpha\\:\\sin\\gamma \\;\\;\\; \\\\\n", "\\cos\\beta\\:\\sin\\gamma \\;&\\;\n", "\\sin\\alpha\\:\\sin\\beta\\:\\sin\\gamma+\\cos\\alpha\\:\\cos\\gamma \\;&\\;\n", "\\cos\\alpha\\:\\sin\\beta\\:\\sin\\gamma-\\sin\\alpha\\:\\cos\\gamma \\;\\;\\;  \\\\\n", "-\\sin\\beta \\;&\\; \\sin\\alpha\\:\\cos\\beta \\;&\\; \\cos\\alpha\\:\\cos\\beta \\;\\;\\;\n", "\\end{bmatrix} \n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Note the order of the matrices.   \n", "\n", "We can check this matrix multiplication using [Sympy](http://sympy.org/en/index.html):"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:08.150746Z", "start_time": "2021-11-18T00:05:07.826045Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R_{Gl,\\,XYZ}}=\\left[\\begin{matrix}\\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} - \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)}\\\\\\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & - \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)}\\\\- \\sin{\\left(\\beta \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#import the necessary libraries\n", "from IPython.core.display import Math, display\n", "import sympy as sym\n", "cos, sin = sym.cos, sym.sin\n", "\n", "a, b, g = sym.symbols('alpha, beta, gamma')\n", "\n", "# Elemental rotation matrices of xyz in relation to XYZ:\n", "RX = sym.Matrix([[1,      0,       0],\n", "                 [0, cos(a), -sin(a)],\n", "                 [0, sin(a), cos(a)]])\n", "\n", "RY = sym.Matrix([[cos(b), 0,  sin(b)],\n", "                 [0,       1,       0],\n", "                 [-sin(b), 0,  cos(b)]])\n", "\n", "RZ = sym.Matrix([[cos(g), -sin(g), 0],\n", "                 [sin(g),  cos(g), 0],\n", "                 [     0,       0, 1]])\n", "\n", "# Rotation matrix of xyz in relation to XYZ:\n", "RXYZ = RZ @ RY @ RX\n", "\n", "display(Math(r'\\mathbf{R_{Gl,\\,XYZ}}=' + sym.latex(RXYZ,\n", "                                                              mat_str='matrix')))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["For instance, we can calculate the numerical rotation matrix for these sequential elemental rotations by $90^o$ around $\\mathbf{X,Y,Z}$:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:08.194913Z", "start_time": "2021-11-18T00:05:08.182472Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R_{Gl,\\,XYZ\\,}}(90^o, 90^o, 90^o) =\\left[\\begin{matrix}0 & 0 & 1.0\\\\0 & 1.0 & 0\\\\-1.0 & 0 & 0\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["R = sym.lambdify((a, b, g), RXYZ, 'numpy')\n", "R = R(np.pi/2, np.pi/2, np.pi/2)\n", "display(Math(r'\\mathbf{R_{Gl,\\,XYZ\\,}}(90^o, 90^o, 90^o) =' + \\\n", "             sym.latex(sym.Matrix(R).n(3, chop=True))))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Below you can test any sequence of rotation around the global coordinates. Just change the matrix R, and the angles of the variables $\\alpha$, $\\beta$ and $\\gamma$. In the example below is the rotation around the global basis, in the sequence x,y,z, with the angles $\\alpha=\\pi/3$ rad, $\\beta=\\pi/4$ rad and $\\gamma=\\pi/6$ rad."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:08.724190Z", "start_time": "2021-11-18T00:05:08.524967Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (fig.ws.readyState == 1 && width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '\" + msg_type + \"' message type: \",\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"499.9999891627921\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath\n", "%matplotlib notebook\n", "import matplotlib.pyplot as plt\n", "from CCS import CCS\n", "\n", "R = RZ*RY*RX\n", "R = sym.lambdify((a, b, g), R, 'numpy')\n", "\n", "alpha = np.pi/2\n", "beta = np.pi/2\n", "gamma = np.pi/2\n", "\n", "R = R(alpha, beta, gamma)\n", "\n", "e1 = np.array([[1, 0, 0]])\n", "e2 = np.array([[0, 1, 0]])\n", "e3 = np.array([[0, 0, 1]])\n", "\n", "basis = np.vstack((e1, e2, e3))\n", "basisRot = R @ basis\n", "CCS(Oijk=np.array([0, 0, 0]), Oxyz=np.array([0, 0, 0]),\n", "    ijk=basis.T, xyz=basisRot.T, vector=False)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Examining the matrix above and the correspondent previous figure, one can see they agree: the rotated $x$ axis (first column of the above matrix) has value -1 in the $\\mathbf{Z}$ direction $[0,0,-1]$, the rotated $y$ axis (second column) is at the $\\mathbf{Y}$ direction $[0,1,0]$, and the rotated $z$ axis (third column) is at the $\\mathbf{X}$ direction $[1,0,0]$.\n", "\n", "We also can calculate the sequence of elemental rotations around the $x$, $y$, $z$ axes of the rotating $xyz$ coordinate system illustrated in the next figure.  \n", "<br>\n", "<figure>\n", "<img src='./../images/rotations_xyz2.png' alt='rotations'/> <figcaption><center><i>Figure. Sequence of elemental rotations of a second $xyz$ local coordinate system around each axis, $x$, $y$, $z$, of the rotating $xyz$ coordinate system.</i></center></figcaption>\n", "</figure>\n", "\n", "Likewise, this sequence of elemental rotations (each one of the local coordinate system with respect to the rotating local coordinate system) is mathematically represented by a multiplication between the rotation matrices (which are the inverse of the matrices for the rotations around $\\mathbf{X,Y,Z}$ as we saw earlier):  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{R}_{\\mathbf{lG},\\,xyz} & = \\mathbf{R_{z}} \\mathbf{R_{y}} \\mathbf{R_{x}} \\\\\n", "\\\\\n", "& = \\begin{bmatrix}\n", "\\cos\\gamma & \\sin\\gamma & 0\\\\\n", "-\\sin\\gamma & \\cos\\gamma & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\cos\\beta & 0 & -\\sin\\beta \\\\\n", "0 & 1 & 0 \\\\\n", "\\sin\\beta & 0 & \\cos\\beta\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "1 & 0 & 0 \\\\\n", "0 & \\cos\\alpha & \\sin\\alpha \\\\\n", "0 & -\\sin\\alpha & \\cos\\alpha\n", "\\end{bmatrix} \\\\\n", "\\\\\n", "& =\n", "\\begin{bmatrix}\n", "\\cos\\beta\\:\\cos\\gamma \\;&\\;\n", "\\sin\\alpha\\:\\sin\\beta\\:\\cos\\gamma+\\cos\\alpha\\:\\sin\\gamma \\;&\\;\n", "\\cos\\alpha\\:\\sin\\beta\\:\\cos\\gamma-\\sin\\alpha\\:\\sin\\gamma \\;\\;\\; \\\\\n", "-\\cos\\beta\\:\\sin\\gamma \\;&\\;\n", "-\\sin\\alpha\\:\\sin\\beta\\:\\sin\\gamma+\\cos\\alpha\\:\\cos\\gamma \\;&\\;\n", "\\cos\\alpha\\:\\sin\\beta\\:\\sin\\gamma+\\sin\\alpha\\:\\cos\\gamma \\;\\;\\;  \\\\\n", "\\sin\\beta \\;&\\; -\\sin\\alpha\\:\\cos\\beta \\;&\\; \\cos\\alpha\\:\\cos\\beta \\;\\;\\;\n", "\\end{bmatrix} \n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "As before, the order of the matrices is from right to left.   \n", "\n", "Once again, we can check this matrix multiplication using [Sympy](http://sympy.org/en/index.html):"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:08.869048Z", "start_time": "2021-11-18T00:05:08.860266Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{\\mathbf{lG},\\,xyz}=\\left[\\begin{matrix}\\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} - \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)}\\\\- \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} & - \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)}\\\\\\sin{\\left(\\beta \\right)} & - \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a, b, g = sym.symbols('alpha, beta, gamma')\n", "# Elemental rotation matrices of xyz (local):\n", "Rx = sym.Matrix([[1, 0, 0],\n", "                 [0, cos(a), sin(a)],\n", "                 [0, -sin(a), cos(a)]])\n", "Ry = sym.Matrix([[cos(b), 0, -sin(b)],\n", "                 [0, 1, 0], [sin(b), 0, cos(b)]])\n", "Rz = sym.Matrix([[cos(g), sin(g), 0],\n", "                 [-sin(g), cos(g), 0],\n", "                 [0, 0, 1]])\n", "# Rotation matrix of xyz' in relation to xyz:\n", "Rxyz = Rz @ Ry @ Rx\n", "Math(r'\\mathbf{R}_{\\mathbf{lG},\\,xyz}=' + sym.latex(Rxyz, mat_str='matrix'))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["For instance, let's calculate the numerical rotation matrix for these sequential elemental rotations by $90^o$ around $x,y,z$:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:09.183585Z", "start_time": "2021-11-18T00:05:09.175449Z"}, "scrolled": true, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(90^o, 90^o, 90^o) =\\left[\\begin{matrix}0 & 0 & 1.0\\\\0 & -1.0 & 0\\\\1.0 & 0 & 0\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["R = sym.lambdify((a, b, g), Rxyz, 'numpy')\n", "R = R(np.pi/2, np.pi/2, np.pi/2)\n", "display(Math(r'\\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(90^o, 90^o, 90^o) =' + \\\n", "             sym.latex(sym.Matrix(R).n(3, chop=True))))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Once again, let's compare the above matrix and the correspondent previous figure to see if it makes sense. But remember that this matrix is the Global-to-local rotation matrix, $\\mathbf{R}_{\\mathbf{lG},\\,xyz}$, where the coordinates of the local basis' versors are rows, not columns, in this matrix. With this detail in mind, one can see that the previous figure and matrix also agree: the rotated $x$ axis (first row of the above matrix) is at the $\\mathbf{Z}$ direction $[0,0,1]$, the rotated $y$ axis (second row) is at the $\\mathbf{-Y}$ direction $[0,-1,0]$, and the rotated $z$ axis (third row) is at the $\\mathbf{X}$ direction $[1,0,0]$.\n", "\n", "In fact, this example didn't serve to distinguish versors as rows or columns because the $\\mathbf{R}_{\\mathbf{lG},\\,xyz}$ matrix above is symmetric!  \n", "Let's look on the resultant matrix for the example above after only the first two rotations, $\\mathbf{R}_{\\mathbf{lG},\\,xy}$ to understand this difference: "]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:09.491586Z", "start_time": "2021-11-18T00:05:09.484991Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{\\mathbf{lG},\\,xy\\,}(90^o, 90^o) =\\left[\\begin{matrix}0 & 1.0 & 0\\\\0 & 0 & 1.0\\\\1.0 & 0 & 0\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Rxy = Ry*Rx\n", "R = sym.lambdify((a, b), Rxy, 'numpy')\n", "R = R(np.pi/2, np.pi/2)\n", "display(Math(r'\\mathbf{R}_{\\mathbf{lG},\\,xy\\,}(90^o, 90^o) =' + \\\n", "             sym.latex(sym.Matrix(R).n(3, chop=True))))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Comparing this matrix with the third plot in the figure, we see that the coordinates of versor $x$ in the Global coordinate system are $[0,1,0]$, i.e., local axis $x$ is aligned with Global axis $Y$, and this versor is indeed the first row, not first column, of the matrix above. Confer the other two rows.  \n", "\n", "What are then in the columns of the local-to-Global rotation matrix?  \n", "The columns are the coordinates of Global basis' versors in the local coordinate system! For example, the first column of the matrix above is the coordinates of $X$, which is aligned with $z$: $[0,0,1]$."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Below you can test any sequence of rotation, around the local coordinates. Just change the matrix R, and the angles of the variables $\\alpha$, $\\beta$ and $\\gamma$. In the example below is the rotation around the local basis, in the sequence x,y,z, with the angles $\\alpha=\\pi/3$ rad, $\\beta=\\pi/4$ rad and $\\gamma=\\pi/2$ rad."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:10.145737Z", "start_time": "2021-11-18T00:05:10.084308Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (fig.ws.readyState == 1 && width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '\" + msg_type + \"' message type: \",\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"499.9999891627921\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["R = Rz*Ry*Rx\n", "R = sym.lambdify((a, b, g), R, 'numpy')\n", "\n", "alpha = np.pi/3\n", "beta = np.pi/4\n", "gamma = np.pi/6\n", "\n", "R = R(alpha, beta, gamma)\n", "\n", "e1 = np.array([[1, 0, 0]])\n", "e2 = np.array([[0, 1, 0]])\n", "e3 = np.array([[0, 0, 1]])\n", "\n", "basis = np.vstack((e1, e2, e3))\n", "basisRot = R @ basis\n", "CCS(Oijk=np.array([0, 0, 0]), Oxyz=np.array([0, 0, 0]),\n", "    ijk=basisRot.T, xyz=basis.T, vector=False)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Rotations in a coordinate system is equivalent to minus rotations in the other coordinate system\n", "\n", "Remember that we saw for the elemental rotations that it's equivalent to instead of rotating the local coordinate system, $xyz$, by $\\alpha, \\beta, \\gamma$ in relation to axes of the Global coordinate system, to rotate the Global coordinate system, $\\mathbf{XYZ}$, by $-\\alpha, -\\beta, -\\gamma$ in relation to the axes of the local coordinate system. The same property applies to a sequence of rotations: rotations of $xyz$ in relation to $\\mathbf{XYZ}$ by $\\alpha, \\beta, \\gamma$ result in the same matrix as rotations of $\\mathbf{XYZ}$ in relation to $xyz$ by $-\\alpha, -\\beta, -\\gamma$:   \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{R_{Gl,\\,XYZ\\,}}(\\alpha,\\beta,\\gamma) & = \\mathbf{R_{Gl,\\,Z}}(\\gamma)\\, \\mathbf{R_{Gl,\\,Y}}(\\beta)\\, \\mathbf{R_{Gl,\\,X}}(\\alpha) \\\\\n", "& = \\mathbf{R}_{\\mathbf{lG},\\,z\\,}(-\\gamma)\\, \\mathbf{R}_{\\mathbf{lG},\\,y\\,}(-\\beta)\\, \\mathbf{R}_{\\mathbf{lG},\\,x\\,}(-\\alpha) \\\\\n", "& = \\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(-\\alpha,-\\beta,-\\gamma)\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Confer that by examining the $\\mathbf{R_{Gl,\\,XYZ}}$ and $\\mathbf{R}_{\\mathbf{lG},\\,xyz}$ matrices above.\n", "\n", "Let's verify this property with <PERSON><PERSON><PERSON>:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:10.406315Z", "start_time": "2021-11-18T00:05:10.390102Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R_{Gl,\\,XYZ\\,}}(\\alpha,\\beta,\\gamma) =$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}\\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} - \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)}\\\\\\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & - \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)}\\\\- \\sin{\\left(\\beta \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(-\\alpha,-\\beta,-\\gamma) =$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}\\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} - \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)}\\\\\\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & - \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)}\\\\- \\sin{\\left(\\beta \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\mathbf{R_{Gl,\\,XYZ\\,}}(\\alpha,\\beta,\\gamma) \\;==\\;\\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(-\\alpha,-\\beta,-\\gamma)$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["True"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["RXYZ = RZ*RY*RX\n", "# Rotation matrix of xyz in relation to XYZ:\n", "display(Math(r'\\mathbf{R_{Gl,\\,XYZ\\,}}(\\alpha,\\beta,\\gamma) ='))\n", "display(Math(sym.latex(RXYZ, mat_str='matrix')))\n", "\n", "# Elemental rotation matrices of XYZ in relation to xyz and negate all angles:\n", "Rx_neg = sym.Matrix([[1, 0, 0], [0, cos(-a), -sin(-a)], [0, sin(-a), cos(-a)]]).T\n", "Ry_neg = sym.Matrix([[cos(-b), 0, sin(-b)], [0, 1, 0], [-sin(-b), 0, cos(-b)]]).T\n", "Rz_neg = sym.Matrix([[cos(-g), -sin(-g), 0], [sin(-g), cos(-g), 0], [0, 0, 1]]).T\n", "\n", "# Rotation matrix of XYZ in relation to xyz:\n", "Rxyz_neg = Rz_neg * Ry_neg * Rx_neg\n", "display(Math(r'\\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(-\\alpha,-\\beta,-\\gamma) ='))\n", "display(Math(sym.latex(Rxyz_neg, mat_str='matrix')))\n", "\n", "# Check that the two matrices are equal:\n", "display(Math(r'\\mathbf{R_{Gl,\\,XYZ\\,}}(\\alpha,\\beta,\\gamma) \\;==\\;' + \\\n", "                       r'\\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(-\\alpha,-\\beta,-\\gamma)'))\n", "RXYZ == Rxyz_neg"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Rotations in a coordinate system is the transpose of inverse order of rotations in the other coordinate system\n", "\n", "There is another property of the rotation matrices for the different coordinate systems: the rotation matrix, for example from the Global to the local coordinate system for the $xyz$ sequence, is just the transpose of the rotation matrix for the inverse operation (from the local to the Global coordinate system) of the inverse sequence ($\\mathbf{ZYX}$) and vice-versa:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{R}_{\\mathbf{lG},\\,xyz}(\\alpha,\\beta,\\gamma) & = \\mathbf{R}_{\\mathbf{lG},\\,z\\,} \\mathbf{R}_{\\mathbf{lG},\\,y\\,} \\mathbf{R}_{\\mathbf{lG},\\,x} \\\\\n", "& = \\mathbf{R_{Gl,\\,Z\\,}^{-1}} \\mathbf{R_{Gl,\\,Y\\,}^{-1}} \\mathbf{R_{Gl,\\,X\\,}^{-1}} \\\\\n", "& = \\mathbf{R_{Gl,\\,Z\\,}^{T}} \\mathbf{R_{Gl,\\,Y\\,}^{T}} \\mathbf{R_{Gl,\\,X\\,}^{T}} \\\\\n", "& = (\\mathbf{R_{Gl,\\,X\\,}} \\mathbf{R_{Gl,\\,Y\\,}} \\mathbf{R_{Gl,\\,Z}})^\\mathbf{T} \\\\\n", "& = \\mathbf{R_{Gl,\\,ZYX\\,}^{T}}(\\gamma,\\beta,\\alpha)\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Where we used the properties that the inverse of the rotation matrix (which is orthonormal) is its transpose and that the transpose of a product of matrices is equal to the product of their transposes in reverse order.\n", "\n", "Let's verify this property with <PERSON><PERSON><PERSON>:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:10.674865Z", "start_time": "2021-11-18T00:05:10.664851Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R_{Gl,\\,ZYX\\,}^T}=\\left[\\begin{matrix}\\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} - \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)}\\\\- \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} & - \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)}\\\\\\sin{\\left(\\beta \\right)} & - \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(\\alpha,\\beta,\\gamma) \\,==\\,\\mathbf{R_{Gl,\\,ZYX\\,}^T}(\\gamma,\\beta,\\alpha)$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["RZYX = RX * RY * RZ\n", "Rxyz = Rz * Ry * Rx\n", "display(Math(r'\\mathbf{R_{Gl,\\,ZYX\\,}^T}=' + sym.latex(RZYX.T, mat_str='matrix')))\n", "display(Math(r'\\mathbf{R}_{\\mathbf{lG},\\,xyz\\,}(\\alpha,\\beta,\\gamma) \\,==\\,' + \\\n", "                       r'\\mathbf{R_{Gl,\\,ZYX\\,}^T}(\\gamma,\\beta,\\alpha)'))\n", "Rxyz == RZYX.T"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### Sequence of rotations of a Vector\n", "\n", "We saw in the notebook [Rigid-body transformations in a plane (2D)](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/Transformation2D.ipynb#Rotation-of-a-Vector) that the rotation matrix can also be used to rotate a vector (in fact, a point, image, solid, etc.) by a given angle around an axis of the coordinate system. Let's investigate that for the 3D case using the example earlier where a book was rotated in different orders and around the Global and local coordinate systems.  \n", "\n", "Before any rotation, the point shown in that figure as a round black dot on the spine of the book has coordinates $\\mathbf{P}=[0, 1, 2]$ (the book has thickness 0, width 1, and height 2). \n", "\n", "After the first sequence of rotations shown in the figure (rotated around $X$ and $Y$ by $90^0$ each time), $\\mathbf{P}$ has coordinates $\\mathbf{P}=[1, -2, 0]$ in the global coordinate system. Let's verify that:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:10.935562Z", "start_time": "2021-11-18T00:05:10.929856Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P1 = [[ 1. -2.  0.]]\n"]}], "source": ["P = np.array([[0, 1, 2]]).T\n", "\n", "RXY = RY*RX\n", "R = sym.lambdify((a, b), RXY, 'numpy')\n", "R = R(np.pi/2, np.pi/2)\n", "P1 = np.dot(R, P)\n", "print('P1 =', P1.T)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["As expected.  \n", "The reader is invited to deduce the position of point $\\mathbf{P}$ after the inverse order of rotations, but still around the Global coordinate system.\n", "\n", "Although we are performing vector rotation, where we don't need the concept of transformation between coordinate systems, in the example above we used the local-to-Global rotation matrix, $\\mathbf{R_{Gl}}$. As we saw in the notebook for the 2D transformation, when we use this matrix, it performs a counter-clockwise (positive) rotation.  \n", "If we want to rotate the vector in the clockwise (negative) direction, we can use the very same rotation matrix entering a negative angle or we can use the inverse rotation matrix, the Global-to-local rotation matrix, $\\mathbf{R_{lG}}$ and a positive (negative of negative) angle, because $\\mathbf{R_{Gl}}(\\alpha) = \\mathbf{R_{lG}}(-\\alpha)$, but bear in mind that even in this latter case we are rotating around the Global coordinate system!  \n", "\n", "Consider now that we want to deduce algebraically the position of the point $\\mathbf{P}$ after the rotations around the local coordinate system as shown in the second set of examples in the figure with the sequence of book rotations. The point has the same initial position, $\\mathbf{P}=[0, 1, 2]$, and after the rotations around $x$ and $y$ by $90^0$ each time, what is the position of this point?  \n", "It's implicit in this question that the new desired position is in the Global coordinate system because the local coordinate system rotates with the book and the point never changes its position in the local coordinate system. So, by inspection of the figure, the new position of the point is $\\mathbf{P1}=[2, 0, 1]$.  \n", "Let's naively try to deduce this position by repeating the steps as before:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:11.197824Z", "start_time": "2021-11-18T00:05:11.192818Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P1 = [[ 1.  2. -0.]]\n"]}], "source": ["Rxy = Ry*Rx\n", "R = sym.lambdify((a, b), Rxy, 'numpy')\n", "R = R(np.pi/2, np.pi/2)\n", "P1 = np.dot(R, P)\n", "print('P1 =', P1.T)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The wrong answer.  \n", "The problem is that we defined the rotation of a vector using the local-to-Global rotation matrix. One correction solution for this problem is to continuing using the multiplication of the Global-to-local rotation matrices, $\\mathbf{R}_{xy} = \\mathbf{R}_y\\,\\mathbf{R}_x$, transpose $\\mathbf{R}_{xy}$ to get the Global-to-local coordinate system, $\\mathbf{R_{XY}}=\\mathbf{R^T}_{xy}$, and then rotate the vector using this matrix:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:11.456929Z", "start_time": "2021-11-18T00:05:11.451945Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P1 = [[ 2. -0.  1.]]\n"]}], "source": ["Rxy = Ry*Rx\n", "RXY = Rxy.T\n", "R = sym.lambdify((a, b), RXY, 'numpy')\n", "R = R(np.pi/2, np.pi/2)\n", "P1 = np.dot(R, P)\n", "print('P1 =', P1.T)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["The correct answer.\n", "\n", "Another solution is to understand that when using the Global-to-local rotation matrix, counter-clockwise rotations (as performed with the book the figure) are negative, not positive, and that when dealing with rotations with the Global-to-local rotation matrix the order of matrix multiplication is inverted, for example, it should be $\\mathbf{R\\_}_{xyz} = \\mathbf{R}_x\\,\\mathbf{R}_y\\,\\mathbf{R}_z$ (an added underscore to remind us this is not the convention adopted here)."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:11.715694Z", "start_time": "2021-11-18T00:05:11.710480Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P1 = [[ 2. -0.  1.]]\n"]}], "source": ["R_xy = Rx*Ry\n", "R = sym.lambdify((a, b), R_xy, 'numpy')\n", "R = R(-np.pi/2, -np.pi/2)\n", "P1 = np.dot(R, P)\n", "print('P1 =', P1.T)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["The correct answer.  \n", "The reader is invited to deduce the position of point $\\mathbf{P}$ after the inverse order of rotations, around the local coordinate system.\n", "\n", "In fact, you will find elsewhere texts about rotations in 3D adopting this latter convention as the standard, i.e., they introduce the Global-to-local rotation matrix and describe sequence of rotations algebraically as matrix multiplication in the direct order, $\\mathbf{R\\_}_{xyz} = \\mathbf{R}_x\\,\\mathbf{R}_y\\,\\mathbf{R}_z$, the inverse we have done in this text. It's all a matter of convention, just that."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### The 12 different sequences of Euler angles\n", "\n", "The Euler angles are defined in terms of rotations around a rotating local coordinate system. As we saw for the sequence of rotations around $x, y, z$, the axes of the local rotated coordinate system are not fixed in space because after the first elemental rotation, the other two axes rotate. \n", "\n", "Other sequences of rotations could be produced without combining axes of the two different coordinate systems (Global and local) for the definition of the rotation axes. There is a total of 12 different sequences of three elemental rotations that are valid and may be used for describing the rotation of a coordinate system with respect to another coordinate system:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "xyz \\quad xzy \\quad yzx \\quad yxz \\quad zxy \\quad zyx  \\\\\n", "xyx \\quad xzx \\quad yzy \\quad yxy \\quad zxz \\quad zyz\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "The first six sequences (first row) are all around different axes, they are usually referred as <PERSON><PERSON> or <PERSON><PERSON><PERSON> angles. The other six sequences (second row) have the first and third rotations around the same axis, but keep in mind that the axis for the third rotation is not at the same place anymore because it changed its orientation after the second rotation. The sequences with repeated axes are known as proper or classic Euler angles.\n", "\n", "Which order to use it is a matter of convention, but because the order affects the results, it's fundamental to follow a convention and report it. In Engineering Mechanics (including Biomechanics), the $xyz$ order is more common; in Physics the $zxz$ order is more common (but the letters chosen to refer to the axes are arbitrary, what matters is the directions they represent). In Biomechanics, the order for the Cardan angles is most often based on the angle of most interest or of most reliable measurement. Accordingly, the axis of flexion/extension is typically selected as the first axis, the axis for abduction/adduction is the second, and the axis for internal/external rotation is the last one. We will see about this order later. The $zyx$ order is commonly used to describe the orientation of a ship or aircraft and the rotations are known as the nautical angles: yaw, pitch and roll, respectively (see next figure).  \n", "<br>\n", "<figure><img src='https://upload.wikimedia.org/wikipedia/commons/thumb/1/16/Yaw_Axis.svg/319px-Yaw_Axis.svg.png' alt='translation and rotation 3D'/> <figcaption><center><i>Figure. The principal axes of an aircraft and the names for the rotations around these axes (<a href=\"https://en.wikipedia.org/wiki/Euler_angles\">image from Wikipedia</a>).</i></center></figcaption> </figure>\n", "\n", "If instead of rotations around the rotating local coordinate system we perform rotations around the fixed Global coordinate system, we will have other 12 different sequences of three elemental rotations, these are called simply rotation angles. So, in total there are 24 possible different sequences of three elemental rotations, but the 24 orders are not independent; with the 12 different sequences of Euler angles at the local coordinate system we can obtain the other 12 sequences at the Global coordinate system.\n", "\n", "The Python function `euler_rotmat.py` (code at the end of this text) determines the rotation matrix in algebraic form for any of the 24 different sequences (and sequences with only one or two axes can be inputed). This function also determines the rotation matrix in numeric form if a list of up to three angles are inputed.\n", "\n", "For instance, the rotation matrix in algebraic form for the $zxz$ order of Euler angles at the local coordinate system and the correspondent rotation matrix in numeric form after three elemental rotations by $90^o$ each are:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:12.226882Z", "start_time": "2021-11-18T00:05:12.224139Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["from euler_rotmat import euler_rotmat"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:12.246987Z", "start_time": "2021-11-18T00:05:12.228927Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{local}( z:\\alpha,x:\\beta,z:\\gamma) =\\left[\\begin{matrix}- \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)}\\\\- \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} - \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & - \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)}\\\\\\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} & - \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} & \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{local}(z:\\alpha=90^o,\\;x:\\beta=90^o,\\;z:\\gamma=90^o)=\\left[\\begin{matrix}0 & 0 & 1.0\\\\0 & -1.0 & 0\\\\1.0 & 0 & 0\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Ra, Rn = euler_rotmat(order='zxz', frame='local', angles=[90, 90, 90])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Line of nodes\n", "\n", "The second axis of rotation in the rotating coordinate system is also referred as the nodal axis or line of nodes; this axis coincides with the intersection of two perpendicular planes, one from each Global (fixed) and local (rotating) coordinate systems. The figure below shows an example of rotations and the nodal axis for the $xyz$ sequence of the Cardan angles.\n", "\n", "<div class='center-align'><figure><img src='./../images/Node.png' alt='rotations'/> <figcaption><center><i>Figure. First row: example of rotations for the $xyz$ sequence of the Cardan angles. The Global (fixed) $XYZ$ coordinate system is shown in green, the local (rotating) $xyz$ coordinate system is shown in blue. The nodal axis (<b>N</b>, shown in red) is defined by the intersection of the $YZ$ and $xy$ planes and all rotations can be described in relation to this nodal axis or to a perpendicular axis to it. Second row: starting from no rotation, the local coordinate system is rotated by $\\alpha$ around the $x$ axis, then by $\\beta$ around the rotated $y$ axis, and finally by $\\gamma$ around the twice rotated $z$ axis. Note that the line of nodes coincides with the $y$ axis for the second rotation. </i></center></figcaption> </figure></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Determination of the <PERSON>uler angles\n", "\n", "Once a convention is adopted, the corresponding three Euler angles of rotation can be found.   \n", "For example, for the $\\mathbf{R}_{xyz}$ rotation matrix:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:13.019968Z", "start_time": "2021-11-18T00:05:12.719219Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{local}( x:\\alpha,y:\\beta,z:\\gamma) =\\left[\\begin{matrix}\\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} - \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)}\\\\- \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} & - \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)}\\\\\\sin{\\left(\\beta \\right)} & - \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["R = euler_rotmat(order='xyz', frame='local')"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["The corresponding Cardan angles for the `xyz` sequence can be given by:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\alpha = \\arctan\\left(\\dfrac{\\sin(\\alpha)}{\\cos(\\alpha)}\\right) = \\arctan\\left(\\dfrac{-\\mathbf{R}_{21}}{\\;\\;\\;\\mathbf{R}_{22}}\\right) \\\\\n", "\\\\\n", "\\beta = \\arctan\\left(\\dfrac{\\sin(\\beta)}{\\cos(\\beta)}\\right) = \\arctan\\left(\\dfrac{\\mathbf{R}_{20}}{\\sqrt{\\mathbf{R}_{00}^2+\\mathbf{R}_{10}^2}}\\right) \\\\  \n", "\\\\\n", "\\gamma = \\arctan\\left(\\dfrac{\\sin(\\gamma)}{\\cos(\\gamma)}\\right) = \\arctan\\left(\\dfrac{-\\mathbf{R}_{10}}{\\;\\;\\;\\mathbf{R}_{00}}\\right)\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Note that we prefer to use the mathematical function `arctan2` rather than simply `arcsin`, `arccos` or `arctan` because the latter cannot for example distinguish $45^o$ from $135^o$ and also for better numerical accuracy. See the text [Angular kinematics in a plane (2D)](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/KinematicsAngular2D.ipynb) for more on these issues.\n", "\n", "And here is a Python function to compute the Euler angles of rotations from the Global to the local coordinate system for the $xyz$ Cardan sequence: "]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:13.024354Z", "start_time": "2021-11-18T00:05:13.021004Z"}}, "outputs": [], "source": ["def euler_angles_from_rot_xyz(rot_matrix, unit='deg'):\n", "    \"\"\" Compute Euler angles from rotation matrix in the xyz sequence.\"\"\"\n", "    \n", "    import numpy as np\n", "\n", "    R = np.array(rot_matrix, copy=False).astype(np.float64)[:3, :3]\n", "    angles = np.zeros(3)\n", "    \n", "    angles[0] = np.arctan2(-R[2, 1], R[2, 2])\n", "    angles[1] = np.arctan2( R[2, 0], np.sqrt(R[0, 0]**2 + R[1, 0]**2))\n", "    angles[2] = np.arctan2(-R[1, 0], R[0, 0])\n", "\n", "    if unit[:3].lower() == 'deg': # convert from rad to degree\n", "        angles = np.rad2deg(angles)\n", "\n", "    return angles"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For instance, consider sequential rotations of 45$^o$ around $x,y,z$. The resultant rotation matrix is:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:13.212834Z", "start_time": "2021-11-18T00:05:13.197928Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{local}(x:\\alpha=45^o,\\;y:\\beta=45^o,\\;z:\\gamma=45^o)=\\left[\\begin{matrix}0.5 & 0.854 & 0.146\\\\-0.5 & 0.146 & 0.854\\\\0.707 & -0.5 & 0.5\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Ra, Rn = euler_rotmat(order='xyz', frame='local',\n", "                      angles=[45, 45, 45], showA=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's check that calculating back the Cardan angles from this rotation matrix using the `euler_angles_from_rot_xyz()` function:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:13.430975Z", "start_time": "2021-11-18T00:05:13.427804Z"}}, "outputs": [{"data": {"text/plain": ["array([45., 45., 45.])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["euler_angles_from_rot_xyz(Rn, unit='deg')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We could implement a function to calculate the Euler angles for any of the 12 sequences (in fact, plus another 12 sequences if we consider all the rotations from and to the two coordinate systems), but this is tedious. There is a smarter solution using the concept of [quaternion](http://en.wikipedia.org/wiki/Quaternion), but we will not see that now.  \n", "\n", "Let's see a problem with using Euler angles known as gimbal lock."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gimbal lock\n", "\n", "[Gimbal lock](http://en.wikipedia.org/wiki/Gimbal_lock) is the loss of one degree of freedom in a three-dimensional coordinate system that occurs when an axis of rotation is placed parallel with another previous axis of rotation and two of the three rotations will be around the same direction given a certain convention of the Euler angles. This \"locks\" the system into rotations in a degenerate two-dimensional space. The system is not really locked in the sense it can't be moved or reach the other degree of freedom, but it will need an extra rotation for that.  \n", "For instance, let's look at the $zxz$ sequence of rotations by the angles $\\alpha, \\beta, \\gamma$:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{R}_{zxz} & = \\mathbf{R_{z}} \\mathbf{R_{x}} \\mathbf{R_{z}} \\\\ \n", "\\\\\n", "& = \n", "\\begin{bmatrix}\n", "\\cos\\gamma & \\sin\\gamma & 0\\\\\n", "-\\sin\\gamma & \\cos\\gamma & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "1 & 0 & 0 \\\\\n", "0 & \\cos\\beta & \\sin\\beta \\\\\n", "0 & -\\sin\\beta & \\cos\\beta\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\cos\\alpha & \\sin\\alpha & 0\\\\\n", "-\\sin\\alpha & \\cos\\alpha & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "Which results in:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:13.890575Z", "start_time": "2021-11-18T00:05:13.881743Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{zxz}=\\left[\\begin{matrix}- \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\beta \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} & \\sin{\\left(\\beta \\right)} \\sin{\\left(\\gamma \\right)}\\\\- \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} - \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & - \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\beta \\right)} \\cos{\\left(\\gamma \\right)}\\\\\\sin{\\left(\\alpha \\right)} \\sin{\\left(\\beta \\right)} & - \\sin{\\left(\\beta \\right)} \\cos{\\left(\\alpha \\right)} & \\cos{\\left(\\beta \\right)}\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["a, b, g = sym.symbols('alpha, beta, gamma')\n", "# Elemental rotation matrices of xyz (local):\n", "Rz = sym.Matrix([[cos(a), sin(a), 0],\n", "                 [-sin(a), cos(a), 0],\n", "                 [0, 0, 1]])\n", "Rx = sym.Matrix([[1, 0, 0],\n", "                 [0, cos(b), sin(b)],\n", "                 [0, -sin(b), cos(b)]])\n", "Rz2 = sym.Matrix([[cos(g), sin(g), 0],\n", "                  [-sin(g), cos(g), 0],\n", "                  [0, 0, 1]])\n", "# Rotation matrix for the zxz sequence:\n", "Rzxz = Rz2*Rx*Rz\n", "Math(r'\\mathbf{R}_{zxz}=' + sym.latex(Rzxz, mat_str='matrix'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Let's examine what happens with this rotation matrix when the rotation around the second axis ($x$) by $\\beta$ is zero:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "\\mathbf{R}_{zxz}(\\alpha, \\beta=0, \\gamma) = \n", "\\begin{bmatrix}\n", "\\cos\\gamma & \\sin\\gamma & 0\\\\\n", "-\\sin\\gamma & \\cos\\gamma & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "1 & 0 & 0 \\\\\n", "0 & 1 & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\cos\\alpha & \\sin\\alpha & 0\\\\\n", "-\\sin\\alpha & \\cos\\alpha & 0 \\\\\n", "0 & 0 & 1\n", "\\end{bmatrix}\n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "The second matrix is the identity matrix and has no effect on the product of the matrices, which will be:"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:14.132888Z", "start_time": "2021-11-18T00:05:14.127780Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{xyz}(\\alpha, \\beta=0, \\gamma)=\\left[\\begin{matrix}- \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} + \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & 0\\\\- \\sin{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} - \\sin{\\left(\\gamma \\right)} \\cos{\\left(\\alpha \\right)} & - \\sin{\\left(\\alpha \\right)} \\sin{\\left(\\gamma \\right)} + \\cos{\\left(\\alpha \\right)} \\cos{\\left(\\gamma \\right)} & 0\\\\0 & 0 & 1\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["Rzxz = Rz2*Rz\n", "Math(r'\\mathbf{R}_{xyz}(\\alpha, \\beta=0, \\gamma)=' + \\\n", "     sym.latex(Rzxz, mat_str='matrix'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Which simplifies to:"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:14.448758Z", "start_time": "2021-11-18T00:05:14.336564Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{xyz}(\\alpha, \\beta=0, \\gamma)=\\left[\\begin{matrix}\\cos{\\left(\\alpha + \\gamma \\right)} & \\sin{\\left(\\alpha + \\gamma \\right)} & 0\\\\- \\sin{\\left(\\alpha + \\gamma \\right)} & \\cos{\\left(\\alpha + \\gamma \\right)} & 0\\\\0 & 0 & 1\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["Rzxz = sym.simplify(Rzxz)\n", "Math(r'\\mathbf{R}_{xyz}(\\alpha, \\beta=0, \\gamma)=' + \\\n", "     sym.latex(Rzxz, mat_str='matrix'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Despite different values of $\\alpha$ and $\\gamma$ the result is a single rotation around the $z$ axis given by the sum $\\alpha+\\gamma$. In this case, of the three degrees of freedom one was lost (the other degree of freedom was set by $\\beta=0$). For movement analysis, this means for example that one angle will be undetermined because everything we know is the sum of the two angles obtained from the rotation matrix. We can set the unknown angle to zero but this is arbitrary.\n", "\n", "In fact, we already dealt with another example of gimbal lock when we looked at the $xyz$ sequence with rotations by $90^o$. See the figure representing these rotations again and perceive that the first and third rotations were around the same axis because the second rotation was by $90^o$. Let's do the matrix multiplication replacing only the second angle by $90^o$ (and let's use the `euler_rotmat.py`:"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:14.741628Z", "start_time": "2021-11-18T00:05:14.543153Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\mathbf{R}_{local}(x:\\alpha,\\;y:\\beta=90^o,\\;z:\\gamma)=\\left[\\begin{matrix}0 & \\sin{\\left(\\alpha + \\gamma \\right)} & - \\cos{\\left(\\alpha + \\gamma \\right)}\\\\0 & \\cos{\\left(\\alpha + \\gamma \\right)} & \\sin{\\left(\\alpha + \\gamma \\right)}\\\\1 & 0 & 0\\end{matrix}\\right]$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Ra, Rn = euler_rotmat(order='xyz', frame='local',\n", "                      angles=[None, 90., None], showA=False)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Once again, one degree of freedom was lost and we will not be able to uniquely determine the three angles for the given rotation matrix and sequence.\n", "\n", "Possible solutions to avoid the gimbal lock are: choose a different sequence; do not rotate the system by the angle that puts the system in gimbal lock (in the examples above, avoid $\\beta=90^o$); or add an extra fourth parameter in the description of the rotation angles.   \n", "\n", "But if we have a physical system where we measure or specify exactly three Euler angles in a fixed sequence to describe or control it, and we can't avoid the system to assume certain angles, then we might have to say \"Houston, we have a problem\".   \n", "A famous situation where such a problem occurred was during the Apollo 13 mission. This is an actual conversation between crew and mission control during the Apollo 13 mission (<PERSON><PERSON>, 2011):\n", "\n", ">`Mission clock: 02 08 12 47`      \n", "**Flight**: *Go, Guidance.*    \n", "**Guido**: *He’s getting close to gimbal lock there.*    \n", "**Flight**: *<PERSON><PERSON>, recommend he bring up C3, C4, B3, B4, C1 and C2 thrusters, and advise he’s getting close to gimbal lock.*   \n", "**CapCom**: *<PERSON>.*    \n", "\n", "*Of note, it was not a gimbal lock that caused the accident with the the Apollo 13 mission, the problem was an oxygen tank explosion.*"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Determination of the rotation matrix\n", "\n", "Let's revise the example where we determined the rotation matrix by building a basis given at least three non-collinear points, but now we will find the Euler angles of rotation.\n", "\n", "Given the positions m1 = [1,0,0], m2 = [0,1,0], m3 = [0,0,1], a basis can be found:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:15.040999Z", "start_time": "2021-11-18T00:05:15.035725Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors:\n", "v1 = [-0.7071  0.7071  0.    ]\n", "v2 = [0.5774 0.5774 0.5774]\n", "v3 = [ 0.4082  0.4082 -0.8165]\n"]}], "source": ["m1 = np.array([1, 0, 0])\n", "m2 = np.array([0, 1, 0])\n", "m3 = np.array([0, 0, 1])\n", "\n", "v1 = m2 - m1\n", "v2 = np.cross(v1, m3 - m1)\n", "v3 = np.cross(v1, v2)\n", "\n", "print('Versors:')\n", "v1 = v1/np.linalg.norm(v1)\n", "print('v1 =', v1)\n", "v2 = v2/np.linalg.norm(v2)\n", "print('v2 =', v2)\n", "v3 = v3/np.linalg.norm(v3)\n", "print('v3 =', v3)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:15.233530Z", "start_time": "2021-11-18T00:05:15.230491Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rotation matrix from Global to local coordinate system:\n", " [[-0.7071  0.7071  0.    ]\n", " [ 0.5774  0.5774  0.5774]\n", " [ 0.4082  0.4082 -0.8165]]\n"]}], "source": ["RlG = np.array([v1, v2, v3])\n", "print('Rotation matrix from Global to local coordinate system:\\n', RlG)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["And the corresponding angles of rotation using the $xyz$ sequence are:"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:15.429399Z", "start_time": "2021-11-18T00:05:15.426423Z"}, "slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/plain": ["array([-153.4349,   24.0948, -140.7685])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["euler_angles_from_rot_xyz(RlG)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["These angles don't mean anything now because they are angles of the axes of the arbitrary basis we computed. In biomechanics, if we want an anatomical interpretation of the coordinate system orientation, we define the versors of the basis oriented with anatomical axes (e.g., for the shoulder, one versor would be aligned with the long axis of the upper arm) as seen [in this notebook about reference frames](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/ReferenceFrame.ipynb).  "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Determination of the rotation matrix between two local coordinate systems\n", "\n", "Similarly to the [bidimensional case](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/Transformation2D.ipynb), to compute the rotation matrix between two local coordinate systems we can use the rotation matrices of both coordinate systems:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "R_{l_1l_2} = R_{Gl_1}^TR_{Gl_2}\n", "\\end{equation}\n", "</span>\n", "\n", "After this, the Euler angles between both coordinate systems can be found using the `arctan2` function as shown previously. "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Translation and Rotation\n", "\n", "Consider the case where the local coordinate system is translated and rotated in relation to the Global coordinate system as illustrated in the next figure.  \n", "<br>\n", "<figure><img src='./../images/transrot3D.png' alt='translation and rotation 3D'/> <figcaption><center><i>Figure. A point in three-dimensional space represented in two coordinate systems, with one system translated and rotated.</i></center></figcaption> </figure>\n", "\n", "The position of point $\\mathbf{P}$ originally described in the local coordinate system, but now described in the Global coordinate system in vector form is:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{L_G} + \\mathbf{R_{Gl}}\\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "This means that we first *disrotate* the local coordinate system and then correct for the translation between the two coordinate systems. Note that we can't invert this order: the point position is expressed in the local coordinate system and we can't add this vector to another vector expressed in the Global coordinate system, first we have to convert the vectors to the same coordinate system.\n", "\n", "If now we want to find the position of a point at the local coordinate system given its position in the Global coordinate system, the rotation matrix and the translation vector, we have to invert the expression above:   \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{array}{ll}\n", "\\mathbf{P_G} = \\mathbf{L_G} + \\mathbf{R_{Gl}}\\mathbf{P_l} \\implies \\\\\n", "\\\\\n", "\\mathbf{R_{Gl}^{-1}}\\cdot\\mathbf{P_G} = \\mathbf{R_{Gl}^{-1}}\\left(\\mathbf{L_G} + \\mathbf{R_{Gl}}\\mathbf{P_l}\\right) \\implies \\\\\n", "\\\\\n", "\\mathbf{R_{Gl}^{-1}}\\mathbf{P_G} = \\mathbf{R_{Gl}^{-1}}\\mathbf{L_G} + \\mathbf{R_{Gl}^{-1}}\\mathbf{R_{Gl}}\\mathbf{P_l} \\implies \\\\\n", "\\\\\n", "\\mathbf{P_l} = \\mathbf{R_{Gl}^{-1}}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right) =  \\mathbf{R_{Gl}^T}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right) \\;\\;\\;\\;\\; \\text{or} \\;\\;\\;\\;\\; \\mathbf{P_l} = \\mathbf{R_{lG}}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right) \n", "\\end{array}\n", "\\end{equation}\n", "</span>\n", "\n", "The expression above indicates that to perform the inverse operation, to go from the Global to the local coordinate system, we first translate and then rotate the coordinate system."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transformation matrix\n", "\n", "It is possible to combine the translation and rotation operations in only one matrix, called the transformation matrix:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y} \\\\\n", "\\mathbf{P_Z} \\\\\n", "1\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", ". & . & . & \\mathbf{L_{X}} \\\\\n", ". & \\mathbf{R_{Gl}} & .  & \\mathbf{L_{Y}} \\\\\n", ". & . & .  & \\mathbf{L_{Z}} \\\\\n", "0 & 0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\mathbf{P}_x \\\\\n", "\\mathbf{P}_y \\\\\n", "\\mathbf{P}_z \\\\\n", "1\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>\n", "\n", "Or simply:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_G} = \\mathbf{T_{Gl}}\\mathbf{P_l}\n", "\\end{equation}\n", "</span>\n", "\n", "Remember that in general the transformation matrix is not orthonormal, i.e., its inverse is not equal to its transpose.\n", "\n", "The inverse operation, to express the position at the local coordinate system in terms of the Global reference system, is:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_l} = \\mathbf{T_{Gl}^{-1}}\\mathbf{P_G}\n", "\\end{equation}\n", "</span>\n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\begin{bmatrix}\n", "\\mathbf{P_x} \\\\\n", "\\mathbf{P_y} \\\\\n", "\\mathbf{P_z} \\\\\n", "1\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\cdot & \\cdot & \\cdot & \\cdot \\\\\n", "\\cdot & \\mathbf{R^{-1}_{Gl}} & \\cdot  & -\\mathbf{R^{-1}_{Gl}}\\:\\mathbf{L_G} \\\\\n", "\\cdot & \\cdot & \\cdot  & \\cdot \\\\\n", "0 & 0 & 0 & 1\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "\\mathbf{P_X} \\\\\n", "\\mathbf{P_Y} \\\\\n", "\\mathbf{P_Z} \\\\\n", "1\n", "\\end{bmatrix}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example with actual motion analysis data \n", "\n", "*The data for this example is taken from page 183 of <PERSON>'s book.*  \n", "Consider the following marker positions placed on a leg (described in the laboratory coordinate system with coordinates $x, y, z$ in cm, the $x$ axis points forward and the $y$ axes points upward): lateral malleolus (**lm** = [2.92, 10.10, 18.85]), medial malleolus (**mm** = [2.71, 10.22, 26.52]), fibular head (**fh** = [5.05, 41.90, 15.41]), and medial condyle (**mc** = [8.29, 41.88, 26.52]). Define the ankle joint center as the centroid between the **lm** and **mm** markers and the knee joint center as the centroid between the **fh** and **mc** markers. An anatomical coordinate system for the leg can be defined as: the quasi-vertical axis ($y$) passes through the ankle and knee joint centers; a temporary medio-lateral axis ($z$) passes through the two markers on the malleolus, an anterior-posterior as the cross product between the two former calculated orthogonal axes, and the origin at the ankle joint center.   \n", " a) Calculate the anatomical coordinate system for the leg as described above.   \n", " b) Calculate the rotation matrix and the translation vector for the transformation from the anatomical to the laboratory coordinate system.   \n", " c) Calculate the position of each marker and of each joint center at the anatomical coordinate system.  \n", " d) Calculate the Cardan angles using the $zxy$ sequence for the orientation of the leg with respect to the laboratory (but remember that the letters chosen to refer to axes are arbitrary, what matters is the directions they represent)."]}, {"cell_type": "code", "execution_count": 31, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:16.347571Z", "start_time": "2021-11-18T00:05:16.343235Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Poition of the ankle joint center: [ 2.815 10.16  22.685]\n", "Poition of the knee joint center: [ 6.67  41.89  20.965]\n"]}], "source": ["# calculation of the joint centers\n", "mm = np.array([2.71, 10.22, 26.52])\n", "lm = np.array([2.92, 10.10, 18.85])\n", "fh = np.array([5.05, 41.90, 15.41])\n", "mc = np.array([8.29, 41.88, 26.52])\n", "ajc = (mm + lm)/2\n", "kjc = (fh + mc)/2\n", "print('Poition of the ankle joint center:', ajc)\n", "print('Poition of the knee joint center:', kjc)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:16.353559Z", "start_time": "2021-11-18T00:05:16.348570Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Versors:\n", "x = [ 0.9925 -0.119   0.029 ]\n", "y = [ 0.1204  0.9913 -0.0537]\n", "z = [-0.0224  0.0568  0.9981]\n", "\n", "Origin = [ 2.815 10.16  22.685]\n"]}], "source": ["# calculation of the anatomical coordinate system axes (basis)\n", "y = kjc - ajc\n", "x = np.cross(y, mm - lm)\n", "z = np.cross(x, y)\n", "print('Versors:')\n", "x = x/np.linalg.norm(x)\n", "y = y/np.linalg.norm(y)\n", "z = z/np.linalg.norm(z)\n", "print('x =', x)\n", "print('y =', y)\n", "print('z =', z)\n", "Oleg = ajc\n", "print('\\nOrigin =', Oleg)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:16.357535Z", "start_time": "2021-11-18T00:05:16.354477Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rotation matrix from the anatomical to the laboratory coordinate system:\n", " [[ 0.9925  0.1204 -0.0224]\n", " [-0.119   0.9913  0.0568]\n", " [ 0.029  -0.0537  0.9981]]\n", "\n", "Rotation matrix from the laboratory to the anatomical coordinate system:\n", " [[ 0.9925 -0.119   0.029 ]\n", " [ 0.1204  0.9913 -0.0537]\n", " [-0.0224  0.0568  0.9981]]\n"]}], "source": ["# Rotation matrices\n", "RGl = np.array([x, y , z]).T\n", "print('Rotation matrix from the anatomical to the laboratory coordinate system:\\n', RGl)\n", "RlG = RGl.T\n", "print('\\nRotation matrix from the laboratory to the anatomical coordinate system:\\n', RlG)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:16.360892Z", "start_time": "2021-11-18T00:05:16.358343Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Translational vector from the anatomical to the laboratory coordinate system:\n", " [ 2.815 10.16  22.685]\n"]}], "source": ["# Translational vector\n", "OG = np.array([0, 0, 0])  # Laboratory coordinate system origin\n", "LG = Oleg - OG\n", "print('Translational vector from the anatomical to the laboratory coordinate system:\\n', LG)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["To get the coordinates from the laboratory (global) coordinate system to the anatomical (local) coordinate system:  \n", "<br>\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "\\mathbf{P_l} = \\mathbf{R_{lG}}\\left(\\mathbf{P_G}-\\mathbf{L_G}\\right)\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:16.535145Z", "start_time": "2021-11-18T00:05:16.529662Z"}, "slideshow": {"slide_type": "slide"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coordinates of mm in the anatomical system:\n", " [-0.     -0.1592  3.8336]\n", "Coordinates of lm in the anatomical system:\n", " [-0.      0.1592 -3.8336]\n", "Coordinates of fh in the anatomical system:\n", " [-1.7703 32.1229 -5.5078]\n", "Coordinates of mc in the anatomical system:\n", " [ 1.7703 31.8963  5.5078]\n", "Coordinates of kjc in the anatomical system:\n", " [ 0.     32.0096  0.    ]\n", "Coordinates of ajc in the anatomical system (origin):\n", " [0. 0. 0.]\n"]}], "source": ["# position of each marker and of each joint center at the anatomical coordinate system\n", "mml  = np.dot(RlG, (mm - LG))  # equivalent to the algebraic expression RlG*(mm - LG).T\n", "lml  = np.dot(RlG, (lm - LG))\n", "fhl = np.dot(RlG, (fh - LG))\n", "mcl = np.dot(RlG, (mc - LG))\n", "ajcl = np.dot(RlG, (ajc - LG))\n", "kjcl = np.dot(RlG, (kjc - LG))\n", "print('Coordinates of mm in the anatomical system:\\n', mml)\n", "print('Coordinates of lm in the anatomical system:\\n', lml)\n", "print('Coordinates of fh in the anatomical system:\\n', fhl)\n", "print('Coordinates of mc in the anatomical system:\\n', mcl)\n", "print('Coordinates of kjc in the anatomical system:\\n', kjcl)\n", "print('Coordinates of ajc in the anatomical system (origin):\\n', ajcl)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Further reading\n", "\n", "- Read pages 1136-1164 of the 21th chapter of the [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s book](http://ruina.tam.cornell.edu/Book/index.html) about elementary introduction to 3D rigid-body dynamics  \n", "- [Rotation matrix](https://en.wikipedia.org/wiki/Rotation_matrix) - Wikipedia  \n", "- [Euler angles](https://en.wikipedia.org/wiki/Euler_angles) - Wikipedia"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Video lectures on the Internet\n", "\n", "- [Rotation in R3 around the x-axis](https://www.khanacademy.org/math/linear-algebra/matrix-transformations/lin-trans-examples/v/rotation-in-r3-around-the-x-axis) - Khan Academy\n", "- [Modern Robotics, Chapter 3.2.1: Rotation Matrices (Part 1 of 2)](https://youtu.be/OZucG1DY_sY) - Northwestern Robotics.  \n", "- [Rotations in 3D](https://youtu.be/wg9bI8-Qx2Q)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Problems\n", "\n", "1. For the example about how the order of rotations of a rigid body affects the orientation shown in a figure above, deduce the rotation matrices for each of the 4 cases shown in the figure. For the first two cases, deduce the rotation matrices from the global to the local coordinate system and for the other two examples, deduce the rotation matrices from the local to the global coordinate system.  \n", "\n", "2. Consider the data from problem 7 in the notebook [Frame of reference](https://nbviewer.org/github/BMClab/bmc/blob/master/notebooks/ReferenceFrame.ipynb) where the following anatomical landmark positions are given (units in meters): RASIS=[0.5, 0.8, 0.4], LASIS=[0.55, 0.78, 0.1], RPSIS=[0.3, 0.85, 0.2], and LPSIS=[0.29, 0.78, 0.3]. Deduce the rotation matrices for the global to anatomical coordinate system and for the anatomical to global coordinate system. \n", "\n", "3. For the data from the last example, calculate the Cardan angles using the $zxy$ sequence for the orientation of the leg with respect to the laboratory (but remember that the letters chosen to refer to axes are arbitrary, what matters is the directions they represent).  \n", "\n", "4. Write down 4*4 matrices for each of the following (from http://www.eecs.qmul.ac.uk/~sgg/cg/Exers/transformations_ex.html):  \n", " 1. To translate by the vector (1, 2, 3)  \n", " 2. To scale with respect to the origin by the amount (2, 4, 6)  \n", " 3. To rotate around the z-axis by 45 degrees (note sin 45 = cos 45 = 1/sqrt(2))  \n", " 4. To rotate around the x-axis by 45 degrees.  \n", "\n", "5. Solve the first two problems from [https://rrg.utk.edu/resources/BME473/assignments/BME473_homework_3.pdf](https://rrg.utk.edu/resources/BME473/assignments/BME473_homework_3.pdf)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON><PERSON> (2011) [Robotics, Vision and Control: Fundamental Algorithms in MATLAB](http://www.petercorke.com/RVC/). Springer-Verlag Berlin.  \n", "- <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> (2013) [Research Methods in Biomechanics](http://books.google.com.br/books?id=gRn8AAAAQBAJ). 2nd Edition. Human Kinetics.  \n", "- [Maths - Eule<PERSON> Angles](http://www.euclideanspace.com/maths/geometry/rotations/euler/).  \n", "- <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> (1994) [A Mathematical Introduction to Robotic Manipulation](http://www.cds.caltech.edu/~murray/mlswiki/index.php/Main_Page). Boca Raton, CRC Press.  \n", "- <PERSON><PERSON> (2017) [Cinemática e Dinâmica para Engenharia](https://www.grupogen.com.br/e-book-cinematica-e-dinamica-para-engenharia). Grupo GEN.  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2013) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  \n", "- <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (2009) [Robotics - Modelling, Planning and Control](http://books.google.com.br/books/about/Robotics.html?hl=pt-BR&id=jPCAFmE-logC). Springer-Verlag London.\n", "- Winter DA (2009) [Biomechanics and motor control of human movement](http://books.google.com.br/books?id=_bFHL08IWfwC). 4 ed. Hoboken, USA: Wiley.   \n", "- <PERSON><PERSON><PERSON><PERSON> (1997) [Kinematics of Human Motion](http://books.google.com.br/books/about/Kinematics_of_Human_Motion.html?id=Pql_xXdbrMcC&redir_esc=y). Champaign, Human Kinetics."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function `euler_rotmatrix.py`"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:17.420360Z", "start_time": "2021-11-18T00:05:17.407913Z"}}, "outputs": [], "source": ["# %load ./../functions/euler_rotmat.py\n", "#!/usr/bin/env python\n", "\n", "\"\"\"Euler rotation matrix given sequence, frame, and angles.\"\"\"\n", "\n", "from __future__ import division, print_function\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = 'euler_rotmat.py v.1 2014/03/10'\n", "\n", "\n", "def euler_rotmat(order='xyz', frame='local', angles=None, unit='deg',\n", "                 str_symbols=None, showA=True, showN=True):\n", "    \"\"\"Euler rotation matrix given sequence, frame, and angles.\n", "    \n", "    This function calculates the algebraic rotation matrix (3x3) for a given\n", "    sequence ('order' argument) of up to three elemental rotations of a given\n", "    coordinate system ('frame' argument) around another coordinate system, the\n", "    Euler (or Eulerian) angles [1]_.\n", "\n", "    This function also calculates the numerical values of the rotation matrix\n", "    when numerical values for the angles are inputed for each rotation axis.\n", "    Use None as value if the rotation angle for the particular axis is unknown.\n", "\n", "    The symbols for the angles are: alpha, beta, and gamma for the first,\n", "    second, and third rotations, respectively.\n", "    The matrix product is calulated from right to left and in the specified\n", "    sequence for the Euler angles. The first letter will be the first rotation.\n", "    \n", "    The function will print and return the algebraic rotation matrix and the\n", "    numerical rotation matrix if angles were inputed.\n", "\n", "    Parameters\n", "    ----------\n", "    order  : string, optional (default = 'xyz')\n", "             Sequence for the Euler angles, any combination of the letters\n", "             x, y, and z with 1 to 3 letters is accepted to denote the\n", "             elemental rotations. The first letter will be the first rotation.\n", "\n", "    frame  : string, optional (default = 'local')\n", "             Coordinate system for which the rotations are calculated.\n", "             Valid values are 'local' or 'global'.\n", "\n", "    angles : list, array, or bool, optional (default = None)\n", "             Numeric values of the rotation angles ordered as the 'order'\n", "             parameter. Enter None for a rotation whith unknown value.\n", "\n", "    unit   : str, optional (default = 'deg')\n", "             Unit of the input angles.\n", "    \n", "    str_symbols : list of strings, optional (default = None)\n", "             New symbols for the angles, for instance, ['theta', 'phi', 'psi']\n", "             \n", "    showA  : bool, optional (default = True)\n", "             True (1) displays the Algebraic rotation matrix in rich format.\n", "             False (0) to not display.\n", "\n", "    showN  : bool, optional (default = True)\n", "             True (1) displays the Numeric rotation matrix in rich format.\n", "             False (0) to not display.\n", "             \n", "    Returns\n", "    -------\n", "    R     :  Matrix Sympy object\n", "             Rotation matrix (3x3) in algebraic format.\n", "\n", "    Rn    :  Numpy array or Matrix Sympy object (only if angles are inputed)\n", "             Numeric rotation matrix (if values for all angles were inputed) or\n", "             a algebraic matrix with some of the algebraic angles substituted\n", "             by the corresponding inputed numeric values.\n", "\n", "    Notes\n", "    -----\n", "    This code uses Sympy, the Python library for symbolic mathematics, to\n", "    calculate the algebraic rotation matrix and shows this matrix in latex form\n", "    possibly for using with the IPython Notebook, see [1]_.\n", "\n", "    References\n", "    ----------\n", "    .. [1] http://nbviewer.ipython.org/github/duartexyz/BMC/blob/master/Transformation3D.ipynb\n", "\n", "    Examples\n", "    --------\n", "    >>> # import function\n", "    >>> from euler_rotmat import euler_rotmat\n", "    >>> # Default options: xyz sequence, local frame and show matrix\n", "    >>> R = euler_rotmat()\n", "    >>> # XYZ sequence (around global (fixed) coordinate system)\n", "    >>> R = euler_rotmat(frame='global')\n", "    >>> # Enter numeric values for all angles and show both matrices\n", "    >>> R, Rn = euler_rotmat(angles=[90, 90, 90])\n", "    >>> # show what is returned\n", "    >>> euler_rotmat(angles=[90, 90, 90])\n", "    >>> # show only the rotation matrix for the elemental rotation at x axis\n", "    >>> R = euler_rotmat(order='x')\n", "    >>> # zxz sequence and numeric value for only one angle\n", "    >>> R, Rn = euler_rotmat(order='zxz', angles=[None, 0, None])\n", "    >>> # input values in radians:\n", "    >>> import numpy as np\n", "    >>> R, Rn = euler_rotmat(order='zxz', angles=[None, np.pi, None], unit='rad')\n", "    >>> # shows only the numeric matrix\n", "    >>> R, Rn = euler_rotmat(order='zxz', angles=[90, 0, None], showA='False')\n", "    >>> # Change the angles' symbols\n", "    >>> R = euler_rotmat(order='zxz', str_symbols=['theta', 'phi', 'psi'])\n", "    >>> # Negativate the angles' symbols\n", "    >>> R = euler_rotmat(order='zxz', str_symbols=['-theta', '-phi', '-psi'])\n", "    >>> # all algebraic matrices for all possible sequences for the local frame\n", "    >>> s=['xyz','xzy','yzx','yxz','zxy','zyx','xyx','xzx','yzy','yxy','zxz','zyz']\n", "    >>> for seq in s: R = euler_rotmat(order=seq)\n", "    >>> # all algebraic matrices for all possible sequences for the global frame\n", "    >>> for seq in s: R = euler_rotmat(order=seq, frame='global')\n", "    \"\"\"\n", "\n", "    import numpy as np\n", "    import sympy as sym\n", "    try:\n", "        from IPython.core.display import Math, display\n", "        ipython = True\n", "    except:\n", "        ipython = False\n", "\n", "    angles = np.asarray(np.atleast_1d(angles), dtype=np.float64)\n", "    if ~np.isnan(angles).all():        \n", "        if len(order) != angles.size:\n", "            raise ValueError(\"Parameters 'order' and 'angles' (when \" + \n", "                             \"different from None) must have the same size.\")\n", "\n", "    x, y, z = sym.symbols('x, y, z')\n", "    sig = [1, 1, 1]\n", "    if str_symbols is None:\n", "        a, b, g = sym.symbols('alpha, beta, gamma')\n", "    else:\n", "        s = str_symbols\n", "        if s[0][0] == '-': s[0] = s[0][1:]; sig[0] = -1\n", "        if s[1][0] == '-': s[1] = s[1][1:]; sig[1] = -1\n", "        if s[2][0] == '-': s[2] = s[2][1:]; sig[2] = -1        \n", "        a, b, g = sym.symbols(s)\n", "\n", "    var = {'x': x, 'y': y, 'z': z, 0: a, 1: b, 2: g}\n", "    # Elemental rotation matrices for xyz (local)\n", "    cos, sin = sym.cos, sym.sin\n", "    Rx = sym.Matrix([[1, 0, 0], [0, cos(x), sin(x)], [0, -sin(x), cos(x)]])\n", "    Ry = sym.Matrix([[cos(y), 0, -sin(y)], [0, 1, 0], [sin(y), 0, cos(y)]])\n", "    Rz = sym.Matrix([[cos(z), sin(z), 0], [-sin(z), cos(z), 0], [0, 0, 1]])\n", "\n", "    if frame.lower() == 'global':\n", "        Rs = {'x': Rx.T, 'y': Ry.T, 'z': Rz.T}\n", "        order = order.upper()\n", "    else:\n", "        Rs = {'x': Rx, 'y': Ry, 'z': Rz}\n", "        order = order.lower()\n", "\n", "    R = Rn = sym.Matrix(sym.Identity(3))\n", "    str1 = r'\\mathbf{R}_{%s}( ' %frame  # last space needed for order=''\n", "    #str2 = [r'\\%s'%var[0], r'\\%s'%var[1], r'\\%s'%var[2]]\n", "    str2 = [1, 1, 1]        \n", "    for i in range(len(order)):\n", "        Ri = Rs[order[i].lower()].subs(var[order[i].lower()], sig[i] * var[i]) \n", "        R = Ri * R\n", "        if sig[i] > 0:\n", "            str2[i] = '%s:%s' %(order[i], sym.latex(var[i]))\n", "        else:\n", "            str2[i] = '%s:-%s' %(order[i], sym.latex(var[i]))\n", "        str1 = str1 + str2[i] + ','\n", "        if ~np.isnan(angles).all() and ~np.isnan(angles[i]):\n", "            if unit[:3].lower() == 'deg':\n", "                angles[i] = np.deg2rad(angles[i])\n", "            Rn = Ri.subs(var[i], angles[i]) * Rn\n", "            #Rn = sym.lambdify(var[i], Ri, 'numpy')(angles[i]) * Rn\n", "            str2[i] = str2[i] + '=%.0f^o' %np.around(np.rad2deg(angles[i]), 0)\n", "        else:\n", "            Rn = Ri * Rn\n", "\n", "    Rn = sym.simplify(Rn)  # for trigonometric relations\n", "\n", "    try:\n", "        # nsimplify only works if there are symbols\n", "        Rn2 = sym.latex(sym.nsimplify(Rn, tolerance=1e-8).n(chop=True, prec=4))\n", "    except:\n", "        Rn2 = sym.latex(Rn.n(chop=True, prec=4))\n", "        # there are no symbols, pass it as Numpy array\n", "        Rn = np.asarray(Rn)\n", "    \n", "    if showA and ipython:\n", "        display(Math(str1[:-1] + ') =' + sym.latex(R, mat_str='matrix')))\n", "\n", "    if showN and ~np.isnan(angles).all() and ipython:\n", "            str2 = ',\\;'.join(str2[:angles.size])\n", "            display(Math(r'\\mathbf{R}_{%s}(%s)=%s' %(frame, str2, Rn2)))\n", "\n", "    if np.isnan(angles).all():\n", "        return R\n", "    else:\n", "        return R, Rn\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Appendix"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2021-11-17T15:29:59.512691Z", "start_time": "2021-11-17T15:29:59.511016Z"}}, "source": ["### How to load .trc files"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Using Pandas, to load a .trc file, we must specify the parameters:  \n", "- 'sep': separator between columns  \n", "- 'header': by default, <PERSON><PERSON> will infer the header and read the first line as the header  \n", "- 'skiprows': a .trc file has 6 columns of text file before the numerica data"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:18.132182Z", "start_time": "2021-11-18T00:05:17.969945Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:18.155971Z", "start_time": "2021-11-18T00:05:18.133316Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>158</th>\n", "      <th>159</th>\n", "      <th>160</th>\n", "      <th>161</th>\n", "      <th>162</th>\n", "      <th>163</th>\n", "      <th>164</th>\n", "      <th>165</th>\n", "      <th>166</th>\n", "      <th>167</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>516.54236</td>\n", "      <td>966.88000</td>\n", "      <td>-306.10416</td>\n", "      <td>531.67438</td>\n", "      <td>981.34631</td>\n", "      <td>-560.16077</td>\n", "      <td>315.74045</td>\n", "      <td>977.08398</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.007</td>\n", "      <td>523.92200</td>\n", "      <td>967.96594</td>\n", "      <td>-308.23773</td>\n", "      <td>539.83044</td>\n", "      <td>982.78345</td>\n", "      <td>-561.77612</td>\n", "      <td>323.29425</td>\n", "      <td>977.64166</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.013</td>\n", "      <td>531.20807</td>\n", "      <td>968.92493</td>\n", "      <td>-310.12112</td>\n", "      <td>547.60663</td>\n", "      <td>984.00653</td>\n", "      <td>-563.42725</td>\n", "      <td>330.56866</td>\n", "      <td>978.15283</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0.020</td>\n", "      <td>538.24219</td>\n", "      <td>969.77612</td>\n", "      <td>-311.72064</td>\n", "      <td>555.40649</td>\n", "      <td>985.09637</td>\n", "      <td>-564.85162</td>\n", "      <td>337.63867</td>\n", "      <td>978.81207</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0.027</td>\n", "      <td>545.11420</td>\n", "      <td>970.81128</td>\n", "      <td>-313.07266</td>\n", "      <td>563.14301</td>\n", "      <td>986.00916</td>\n", "      <td>-566.06659</td>\n", "      <td>344.50589</td>\n", "      <td>979.21619</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>180</td>\n", "      <td>1.193</td>\n", "      <td>1875.04834</td>\n", "      <td>958.35535</td>\n", "      <td>-290.67004</td>\n", "      <td>1897.06970</td>\n", "      <td>960.27222</td>\n", "      <td>-546.90094</td>\n", "      <td>1678.10901</td>\n", "      <td>977.31805</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>181</td>\n", "      <td>1.200</td>\n", "      <td>1885.37988</td>\n", "      <td>958.09222</td>\n", "      <td>-291.67429</td>\n", "      <td>1907.52881</td>\n", "      <td>960.81549</td>\n", "      <td>-547.97144</td>\n", "      <td>1688.67249</td>\n", "      <td>977.12646</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>182</td>\n", "      <td>1.207</td>\n", "      <td>1895.50452</td>\n", "      <td>957.80798</td>\n", "      <td>-292.71875</td>\n", "      <td>1917.97290</td>\n", "      <td>961.49707</td>\n", "      <td>-548.99799</td>\n", "      <td>1699.17065</td>\n", "      <td>977.02045</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>183</td>\n", "      <td>1.213</td>\n", "      <td>1905.40540</td>\n", "      <td>957.61029</td>\n", "      <td>-293.84250</td>\n", "      <td>1928.34631</td>\n", "      <td>962.31494</td>\n", "      <td>-550.08704</td>\n", "      <td>1709.65186</td>\n", "      <td>976.93237</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>184</td>\n", "      <td>1.220</td>\n", "      <td>1916.91516</td>\n", "      <td>958.57813</td>\n", "      <td>-294.85443</td>\n", "      <td>1938.59363</td>\n", "      <td>963.29272</td>\n", "      <td>-551.26306</td>\n", "      <td>1720.05396</td>\n", "      <td>976.89636</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 168 columns</p>\n", "</div>"], "text/plain": ["     0      1           2          3          4           5          6    \\\n", "0      1  0.000   516.54236  966.88000 -306.10416   531.67438  981.34631   \n", "1      2  0.007   523.92200  967.96594 -308.23773   539.83044  982.78345   \n", "2      3  0.013   531.20807  968.92493 -310.12112   547.60663  984.00653   \n", "3      4  0.020   538.24219  969.77612 -311.72064   555.40649  985.09637   \n", "4      5  0.027   545.11420  970.81128 -313.07266   563.14301  986.00916   \n", "..   ...    ...         ...        ...        ...         ...        ...   \n", "179  180  1.193  1875.04834  958.35535 -290.67004  1897.06970  960.27222   \n", "180  181  1.200  1885.37988  958.09222 -291.67429  1907.52881  960.81549   \n", "181  182  1.207  1895.50452  957.80798 -292.71875  1917.97290  961.49707   \n", "182  183  1.213  1905.40540  957.61029 -293.84250  1928.34631  962.31494   \n", "183  184  1.220  1916.91516  958.57813 -294.85443  1938.59363  963.29272   \n", "\n", "           7           8          9    ...  158  159  160  161  162  163  164  \\\n", "0   -560.16077   315.74045  977.08398  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "1   -561.77612   323.29425  977.64166  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "2   -563.42725   330.56866  978.15283  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "3   -564.85162   337.63867  978.81207  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "4   -566.06659   344.50589  979.21619  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "..         ...         ...        ...  ...  ...  ...  ...  ...  ...  ...  ...   \n", "179 -546.90094  1678.10901  977.31805  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "180 -547.97144  1688.67249  977.12646  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "181 -548.99799  1699.17065  977.02045  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "182 -550.08704  1709.65186  976.93237  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "183 -551.26306  1720.05396  976.89636  ...  NaN  NaN  NaN  NaN  NaN  NaN  NaN   \n", "\n", "     165  166  167  \n", "0    NaN  NaN  NaN  \n", "1    NaN  NaN  NaN  \n", "2    NaN  NaN  NaN  \n", "3    NaN  NaN  NaN  \n", "4    NaN  NaN  NaN  \n", "..   ...  ...  ...  \n", "179  NaN  NaN  NaN  \n", "180  NaN  NaN  NaN  \n", "181  NaN  NaN  NaN  \n", "182  NaN  NaN  NaN  \n", "183  NaN  NaN  NaN  \n", "\n", "[184 rows x 168 columns]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv('./../data/walk.trc', sep='\\t', header=None, skiprows=6)\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But now the columns of the pandas dataframe don't have names and it will be easier if the columns have as names the marker's name (line 4 of the .trc file) and its direction (line 5).   \n", "The solution is to first read only the header of the .trc file to get the markers' names and directions and read a second time only to get the numeric data.  \n", "We wrote a function to do that, named 'read_trc.py' and it is stored in the functions directory of the BMC repository.  \n", "Here is how to use this function:"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:18.166697Z", "start_time": "2021-11-18T00:05:18.162849Z"}}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath\n", "\n", "from read_trc import read_trc"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:18.222167Z", "start_time": "2021-11-18T00:05:18.167915Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/walk.trc\" ...  Number of markers changed from 28 to 55.\n", "done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Frame#</th>\n", "      <th>Time</th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>L.ASISx</th>\n", "      <th><PERSON>.<PERSON></th>\n", "      <th>L.<PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th>V_R.TT_KJCz</th>\n", "      <th>V_L.TT_KJCx</th>\n", "      <th>V_L.TT_KJCy</th>\n", "      <th>V_L.TT_KJCz</th>\n", "      <th>V_R.MT2x</th>\n", "      <th>V_R.MT2y</th>\n", "      <th>V_R.MT2z</th>\n", "      <th>V_L.MT2x</th>\n", "      <th>V_L.MT2y</th>\n", "      <th>V_L.MT2z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>516.54236</td>\n", "      <td>966.88000</td>\n", "      <td>-306.10416</td>\n", "      <td>531.67438</td>\n", "      <td>981.34631</td>\n", "      <td>-560.16077</td>\n", "      <td>315.74045</td>\n", "      <td>977.08398</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.007</td>\n", "      <td>523.92200</td>\n", "      <td>967.96594</td>\n", "      <td>-308.23773</td>\n", "      <td>539.83044</td>\n", "      <td>982.78345</td>\n", "      <td>-561.77612</td>\n", "      <td>323.29425</td>\n", "      <td>977.64166</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.013</td>\n", "      <td>531.20807</td>\n", "      <td>968.92493</td>\n", "      <td>-310.12112</td>\n", "      <td>547.60663</td>\n", "      <td>984.00653</td>\n", "      <td>-563.42725</td>\n", "      <td>330.56866</td>\n", "      <td>978.15283</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0.020</td>\n", "      <td>538.24219</td>\n", "      <td>969.77612</td>\n", "      <td>-311.72064</td>\n", "      <td>555.40649</td>\n", "      <td>985.09637</td>\n", "      <td>-564.85162</td>\n", "      <td>337.63867</td>\n", "      <td>978.81207</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0.027</td>\n", "      <td>545.11420</td>\n", "      <td>970.81128</td>\n", "      <td>-313.07266</td>\n", "      <td>563.14301</td>\n", "      <td>986.00916</td>\n", "      <td>-566.06659</td>\n", "      <td>344.50589</td>\n", "      <td>979.21619</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>180</td>\n", "      <td>1.193</td>\n", "      <td>1875.04834</td>\n", "      <td>958.35535</td>\n", "      <td>-290.67004</td>\n", "      <td>1897.06970</td>\n", "      <td>960.27222</td>\n", "      <td>-546.90094</td>\n", "      <td>1678.10901</td>\n", "      <td>977.31805</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>181</td>\n", "      <td>1.200</td>\n", "      <td>1885.37988</td>\n", "      <td>958.09222</td>\n", "      <td>-291.67429</td>\n", "      <td>1907.52881</td>\n", "      <td>960.81549</td>\n", "      <td>-547.97144</td>\n", "      <td>1688.67249</td>\n", "      <td>977.12646</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>182</td>\n", "      <td>1.207</td>\n", "      <td>1895.50452</td>\n", "      <td>957.80798</td>\n", "      <td>-292.71875</td>\n", "      <td>1917.97290</td>\n", "      <td>961.49707</td>\n", "      <td>-548.99799</td>\n", "      <td>1699.17065</td>\n", "      <td>977.02045</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>183</td>\n", "      <td>1.213</td>\n", "      <td>1905.40540</td>\n", "      <td>957.61029</td>\n", "      <td>-293.84250</td>\n", "      <td>1928.34631</td>\n", "      <td>962.31494</td>\n", "      <td>-550.08704</td>\n", "      <td>1709.65186</td>\n", "      <td>976.93237</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>184</td>\n", "      <td>1.220</td>\n", "      <td>1916.91516</td>\n", "      <td>958.57813</td>\n", "      <td>-294.85443</td>\n", "      <td>1938.59363</td>\n", "      <td>963.29272</td>\n", "      <td>-551.26306</td>\n", "      <td>1720.05396</td>\n", "      <td>976.89636</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 167 columns</p>\n", "</div>"], "text/plain": ["     Frame#   Time     R.ASISx    R.ASISy    R.ASISz     L.ASISx    L.ASISy  \\\n", "0         1  0.000   516.54236  966.88000 -306.10416   531.67438  981.34631   \n", "1         2  0.007   523.92200  967.96594 -308.23773   539.83044  982.78345   \n", "2         3  0.013   531.20807  968.92493 -310.12112   547.60663  984.00653   \n", "3         4  0.020   538.24219  969.77612 -311.72064   555.40649  985.09637   \n", "4         5  0.027   545.11420  970.81128 -313.07266   563.14301  986.00916   \n", "..      ...    ...         ...        ...        ...         ...        ...   \n", "179     180  1.193  1875.04834  958.35535 -290.67004  1897.06970  960.27222   \n", "180     181  1.200  1885.37988  958.09222 -291.67429  1907.52881  960.81549   \n", "181     182  1.207  1895.50452  957.80798 -292.71875  1917.97290  961.49707   \n", "182     183  1.213  1905.40540  957.61029 -293.84250  1928.34631  962.31494   \n", "183     184  1.220  1916.91516  958.57813 -294.85443  1938.59363  963.29272   \n", "\n", "       L.ASISz     R.PSISx    R.PSISy  ...  V_R.TT_KJCz  V_L.TT_KJCx  \\\n", "0   -560.16077   315.74045  977.08398  ...          0.0          0.0   \n", "1   -561.77612   323.29425  977.64166  ...          0.0          0.0   \n", "2   -563.42725   330.56866  978.15283  ...          0.0          0.0   \n", "3   -564.85162   337.63867  978.81207  ...          0.0          0.0   \n", "4   -566.06659   344.50589  979.21619  ...          0.0          0.0   \n", "..         ...         ...        ...  ...          ...          ...   \n", "179 -546.90094  1678.10901  977.31805  ...          0.0          0.0   \n", "180 -547.97144  1688.67249  977.12646  ...          0.0          0.0   \n", "181 -548.99799  1699.17065  977.02045  ...          0.0          0.0   \n", "182 -550.08704  1709.65186  976.93237  ...          0.0          0.0   \n", "183 -551.26306  1720.05396  976.89636  ...          0.0          0.0   \n", "\n", "     V_L.TT_KJCy  V_L.TT_KJCz  V_R.MT2x  V_R.MT2y  V_R.MT2z  V_L.MT2x  \\\n", "0            0.0          0.0       0.0       0.0       0.0       0.0   \n", "1            0.0          0.0       0.0       0.0       0.0       0.0   \n", "2            0.0          0.0       0.0       0.0       0.0       0.0   \n", "3            0.0          0.0       0.0       0.0       0.0       0.0   \n", "4            0.0          0.0       0.0       0.0       0.0       0.0   \n", "..           ...          ...       ...       ...       ...       ...   \n", "179          0.0          0.0       0.0       0.0       0.0       0.0   \n", "180          0.0          0.0       0.0       0.0       0.0       0.0   \n", "181          0.0          0.0       0.0       0.0       0.0       0.0   \n", "182          0.0          0.0       0.0       0.0       0.0       0.0   \n", "183          0.0          0.0       0.0       0.0       0.0       0.0   \n", "\n", "     V_L.MT2y  V_L.MT2z  \n", "0         0.0       0.0  \n", "1         0.0       0.0  \n", "2         0.0       0.0  \n", "3         0.0       0.0  \n", "4         0.0       0.0  \n", "..        ...       ...  \n", "179       0.0       0.0  \n", "180       0.0       0.0  \n", "181       0.0       0.0  \n", "182       0.0       0.0  \n", "183       0.0       0.0  \n", "\n", "[184 rows x 167 columns]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["h, data = read_trc('./../data/walk.trc', fname2='', dropna=False, na=0.0, fmt='uni')\n", "data"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"ExecuteTime": {"end_time": "2021-11-18T00:05:18.296442Z", "start_time": "2021-11-18T00:05:18.223094Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"./../data/walk.trc\" ...  Number of markers changed from 28 to 55.\n", "done.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <th colspan=\"3\" halign=\"left\">R_ASIS</th>\n", "      <th colspan=\"3\" halign=\"left\">L_ASIS</th>\n", "      <th colspan=\"3\" halign=\"left\">R_PSIS</th>\n", "      <th>L_PSIS</th>\n", "      <th>...</th>\n", "      <th>V_R_TT_KJC</th>\n", "      <th colspan=\"3\" halign=\"left\">V_L_TT_KJC</th>\n", "      <th colspan=\"3\" halign=\"left\">V_R_MT2</th>\n", "      <th colspan=\"3\" halign=\"left\">V_L_MT2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Coordinate</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>...</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "    </tr>\n", "    <tr>\n", "      <th>XYZ</th>\n", "      <th>X1</th>\n", "      <th>Y1</th>\n", "      <th>Z1</th>\n", "      <th>X2</th>\n", "      <th>Y2</th>\n", "      <th>Z2</th>\n", "      <th>X3</th>\n", "      <th>Y3</th>\n", "      <th>Z3</th>\n", "      <th>X4</th>\n", "      <th>...</th>\n", "      <th>Z52</th>\n", "      <th>X53</th>\n", "      <th>Y53</th>\n", "      <th>Z53</th>\n", "      <th>X54</th>\n", "      <th>Y54</th>\n", "      <th>Z54</th>\n", "      <th>X55</th>\n", "      <th>Y55</th>\n", "      <th>Z55</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000</th>\n", "      <td>516.54236</td>\n", "      <td>966.88000</td>\n", "      <td>-306.10416</td>\n", "      <td>531.67438</td>\n", "      <td>981.34631</td>\n", "      <td>-560.16077</td>\n", "      <td>315.74045</td>\n", "      <td>977.08398</td>\n", "      <td>-388.89532</td>\n", "      <td>312.81592</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.007</th>\n", "      <td>523.92200</td>\n", "      <td>967.96594</td>\n", "      <td>-308.23773</td>\n", "      <td>539.83044</td>\n", "      <td>982.78345</td>\n", "      <td>-561.77612</td>\n", "      <td>323.29425</td>\n", "      <td>977.64166</td>\n", "      <td>-391.11392</td>\n", "      <td>320.88770</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013</th>\n", "      <td>531.20807</td>\n", "      <td>968.92493</td>\n", "      <td>-310.12112</td>\n", "      <td>547.60663</td>\n", "      <td>984.00653</td>\n", "      <td>-563.42725</td>\n", "      <td>330.56866</td>\n", "      <td>978.15283</td>\n", "      <td>-393.34290</td>\n", "      <td>328.17276</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020</th>\n", "      <td>538.24219</td>\n", "      <td>969.77612</td>\n", "      <td>-311.72064</td>\n", "      <td>555.40649</td>\n", "      <td>985.09637</td>\n", "      <td>-564.85162</td>\n", "      <td>337.63867</td>\n", "      <td>978.81207</td>\n", "      <td>-395.60764</td>\n", "      <td>335.90649</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.027</th>\n", "      <td>545.11420</td>\n", "      <td>970.81128</td>\n", "      <td>-313.07266</td>\n", "      <td>563.14301</td>\n", "      <td>986.00916</td>\n", "      <td>-566.06659</td>\n", "      <td>344.50589</td>\n", "      <td>979.21619</td>\n", "      <td>-397.85052</td>\n", "      <td>343.31287</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.193</th>\n", "      <td>1875.04834</td>\n", "      <td>958.35535</td>\n", "      <td>-290.67004</td>\n", "      <td>1897.06970</td>\n", "      <td>960.27222</td>\n", "      <td>-546.90094</td>\n", "      <td>1678.10901</td>\n", "      <td>977.31805</td>\n", "      <td>-385.06229</td>\n", "      <td>1678.84143</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.200</th>\n", "      <td>1885.37988</td>\n", "      <td>958.09222</td>\n", "      <td>-291.67429</td>\n", "      <td>1907.52881</td>\n", "      <td>960.81549</td>\n", "      <td>-547.97144</td>\n", "      <td>1688.67249</td>\n", "      <td>977.12646</td>\n", "      <td>-386.23868</td>\n", "      <td>1689.18640</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.207</th>\n", "      <td>1895.50452</td>\n", "      <td>957.80798</td>\n", "      <td>-292.71875</td>\n", "      <td>1917.97290</td>\n", "      <td>961.49707</td>\n", "      <td>-548.99799</td>\n", "      <td>1699.17065</td>\n", "      <td>977.02045</td>\n", "      <td>-387.41364</td>\n", "      <td>1699.72668</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.213</th>\n", "      <td>1905.40540</td>\n", "      <td>957.61029</td>\n", "      <td>-293.84250</td>\n", "      <td>1928.34631</td>\n", "      <td>962.31494</td>\n", "      <td>-550.08704</td>\n", "      <td>1709.65186</td>\n", "      <td>976.93237</td>\n", "      <td>-388.50803</td>\n", "      <td>1710.00281</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.220</th>\n", "      <td>1916.91516</td>\n", "      <td>958.57813</td>\n", "      <td>-294.85443</td>\n", "      <td>1938.59363</td>\n", "      <td>963.29272</td>\n", "      <td>-551.26306</td>\n", "      <td>1720.05396</td>\n", "      <td>976.89636</td>\n", "      <td>-389.46796</td>\n", "      <td>1720.32813</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>184 rows × 165 columns</p>\n", "</div>"], "text/plain": ["Marker          R_ASIS                            L_ASIS             \\\n", "Coordinate           X          Y          Z           X          Y   \n", "XYZ                 X1         Y1         Z1          X2         Y2   \n", "Time                                                                  \n", "0.000        516.54236  966.88000 -306.10416   531.67438  981.34631   \n", "0.007        523.92200  967.96594 -308.23773   539.83044  982.78345   \n", "0.013        531.20807  968.92493 -310.12112   547.60663  984.00653   \n", "0.020        538.24219  969.77612 -311.72064   555.40649  985.09637   \n", "0.027        545.11420  970.81128 -313.07266   563.14301  986.00916   \n", "...                ...        ...        ...         ...        ...   \n", "1.193       1875.04834  958.35535 -290.67004  1897.06970  960.27222   \n", "1.200       1885.37988  958.09222 -291.67429  1907.52881  960.81549   \n", "1.207       1895.50452  957.80798 -292.71875  1917.97290  961.49707   \n", "1.213       1905.40540  957.61029 -293.84250  1928.34631  962.31494   \n", "1.220       1916.91516  958.57813 -294.85443  1938.59363  963.29272   \n", "\n", "Marker                     R_PSIS                            L_PSIS  ...  \\\n", "Coordinate          Z           X          Y          Z           X  ...   \n", "XYZ                Z2          X3         Y3         Z3          X4  ...   \n", "Time                                                                 ...   \n", "0.000      -560.16077   315.74045  977.08398 -388.89532   312.81592  ...   \n", "0.007      -561.77612   323.29425  977.64166 -391.11392   320.88770  ...   \n", "0.013      -563.42725   330.56866  978.15283 -393.34290   328.17276  ...   \n", "0.020      -564.85162   337.63867  978.81207 -395.60764   335.90649  ...   \n", "0.027      -566.06659   344.50589  979.21619 -397.85052   343.31287  ...   \n", "...               ...         ...        ...        ...         ...  ...   \n", "1.193      -546.90094  1678.10901  977.31805 -385.06229  1678.84143  ...   \n", "1.200      -547.97144  1688.67249  977.12646 -386.23868  1689.18640  ...   \n", "1.207      -548.99799  1699.17065  977.02045 -387.41364  1699.72668  ...   \n", "1.213      -550.08704  1709.65186  976.93237 -388.50803  1710.00281  ...   \n", "1.220      -551.26306  1720.05396  976.89636 -389.46796  1720.32813  ...   \n", "\n", "Marker     V_R_TT_KJC V_L_TT_KJC           V_R_MT2           V_L_MT2            \n", "Coordinate          Z          X    Y    Z       X    Y    Z       X    Y    Z  \n", "XYZ               Z52        X53  Y53  Z53     X54  Y54  Z54     X55  Y55  Z55  \n", "Time                                                                            \n", "0.000             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.007             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.013             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.020             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "0.027             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "...               ...        ...  ...  ...     ...  ...  ...     ...  ...  ...  \n", "1.193             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.200             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.207             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.213             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "1.220             0.0        0.0  0.0  0.0     0.0  0.0  0.0     0.0  0.0  0.0  \n", "\n", "[184 rows x 165 columns]"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["h, data = read_trc('./../data/walk.trc', fname2='', dropna=False, na=0.0, fmt='multi')\n", "data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "256px"}, "toc_section_display": "block", "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}