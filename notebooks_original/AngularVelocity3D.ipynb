{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Angular velocity in 3D movements\n", "\n", "> <PERSON><PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://pesquisa.ufabc.edu.br/bmclab](http://pesquisa.ufabc.edu.br/bmclab))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["An usual problem found in Biomechanics (and Mechanics in general) is to find the angular velocity of an object. We consider that a basis <span class=\"notranslate\">\n", "$\\hat{\\boldsymbol e_1}$, $\\hat{\\boldsymbol  e_2}$</span> and  <span class=\"notranslate\">$\\hat{\\boldsymbol  e_3}$</span> is attached to the body and is known. To learn how to find a basis of a frame of reference, see [this notebook](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/ReferenceFrame.ipynb).\n", "\n", "![ref](../images/3DbodyrefMove.png)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Axis of rotation\n", "\n", "As in the planar movement, the angular velocity is a vector perpendicular to the rotation. The line in the direction of the angular velocity vector is known as the axis of rotation. \n", "\n", "The rotation beween two frames of reference is characterized by the rotation matrix $R$ obtained by stacking the versors  <span class=\"notranslate\">$\\hat{\\boldsymbol e_1}$, $\\hat{\\boldsymbol e_2}$</span> and  <span class=\"notranslate\">$\\hat{\\boldsymbol e_3}$</span> in each column of the matrix (for a revision on rotation matrices see [this](https://nbviewer.jupyter.org/github/bmclab/bmc/blob/master/notebooks/Transformation2D.ipynb) and [this](https://nbviewer.jupyter.org/github/bmclab/bmc/blob/master/notebooks/Transformation3D.ipynb) notebooks). \n", "\n", "A vector in the direction of the axis of rotation is a vector that does not changes the position after the rotation. That is:\n", "\n", " <span class=\"notranslate\">\n", "\\begin{equation}\n", "v = Rv\n", "\\end{equation}\n", "</span>\n", "\n", "\n", "This vector is the eigenvector of the rotation matrix  <span class=\"notranslate\">$R$</span> with eigenvalue equal to one.  "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Below the yellow arrow indicates the axis of rotation of the rotation between the position of the reference frame   <span class=\"notranslate\">$\\hat{\\boldsymbol i}$, $\\hat{\\boldsymbol j}$</span> and  <span class=\"notranslate\">$\\hat{\\boldsymbol k}$</span> and the reference frame of  <span class=\"notranslate\">$\\hat{\\boldsymbol e_1}$, $\\hat{\\boldsymbol e_2}$</span> and  <span class=\"notranslate\">$\\hat{\\boldsymbol e_3}$</span>."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ce939d46296e4d819d9facbe357cd3c0", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=500.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<CCSbasis.CCSbasis at 0x7f2e94c6ab20>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Math, display\n", "import matplotlib.pyplot as plt\n", "import ipympl\n", "import sympy as sym\n", "import sys\n", "sys.path.insert(1, r'../functions')  # add to pythonpath\n", "%matplotlib widget\n", "from CCSbasis import CCSbasis\n", "import numpy as np\n", "\n", "\n", "a, b, g = sym.symbols('alpha, beta, gamma')\n", "\n", "# Elemental rotation matrices of xyz in relation to XYZ:\n", "RX = sym.Matrix([[1, 0, 0], [0, sym.cos(a), -sym.sin(a)], [0, sym.sin(a), sym.cos(a)]])\n", "RY = sym.Matrix([[sym.cos(b), 0, sym.sin(b)], [0, 1, 0], [-sym.sin(b), 0, sym.cos(b)]])\n", "RZ = sym.Matrix([[sym.cos(g), -sym.sin(g), 0], [sym.sin(g), sym.cos(g), 0], [0, 0, 1]])\n", "\n", "# Rotation matrix of xyz in relation to XYZ:\n", "R = RY@RX@RZ\n", "R = sym.lambdify((a, b, g), R, 'numpy')\n", "\n", "alpha = 0*np.pi/4\n", "beta = 0*np.pi/4\n", "gamma = np.pi/4\n", "\n", "R = R(alpha, beta, gamma)\n", "\n", "e1 = np.array([[1,0,0]])\n", "e2 = np.array([[0,1,0]])\n", "e3 = np.array([[0,0,1]])\n", "\n", "basis = np.vstack((e1,e2,e3))\n", "basisRot = R@basis\n", "lv, v = np.linalg.eig(R)\n", "\n", "axisOfRotation = [np.real(np.squeeze(v[:,np.isclose(lv,1)]))]\n", "\n", "\n", "CCSbasis(Oijk=np.array([0,0,0]), Oxyz=np.array([0,0,0]), ijk=basis.T, xyz=basisRot.T, \n", "         vector=True, point = axisOfRotation)\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Computing the angular velocity\n", "\n", "The angular velocity $\\vec{\\boldsymbol\\omega}$ is in the direction of the axis of rotation (hence it is parallel to the axis of rotation) and can be described in the basis fixed in the body:\n", "\n", "<span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\boldsymbol{\\omega}} = \\omega_1\\hat{\\boldsymbol{e_1}} + \\omega_2\\hat{\\boldsymbol{e_2}} + \\omega_3\\hat{\\boldsymbol{e_3}}  \n", "\\end{equation}\n", "</span>\n", "\n", "So, we must find  <span class=\"notranslate\">$\\omega_1$, $\\omega_2$</span> and  <span class=\"notranslate\">$\\omega_3$</span>."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["First we will express the angular velocity  <span class=\"notranslate\">$\\vec{\\boldsymbol{\\omega}}$</span> in  terms of these derivatives. \n", "\n", "Remember that the angular velocity is described as a vector in the orthogonal plane of the rotation. (<span class=\"notranslate\">$\\vec{\\boldsymbol{\\omega_1}} = \\frac{d\\theta_1}{dt}\\hat{\\boldsymbol{e_1}}$, $\\vec{\\boldsymbol{\\omega_2}} = \\frac{d\\theta_2}{dt}\\hat{\\boldsymbol{e_2}}$</span> and  <span class=\"notranslate\">$\\vec{\\boldsymbol{\\omega_3}} = \\frac{d\\theta_3}{dt}\\hat{\\boldsymbol{e_3}}$</span>). "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["Note also that the derivative of the angle  <span class=\"notranslate\">$\\theta_1$</span> can be described as the projection of the vector  <span class=\"notranslate\">$\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}$</span> on the vector  <span class=\"notranslate\">$\\hat{\\boldsymbol{e_3}}$</span>. "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["This can be written by using the scalar product between these vectors:  <span class=\"notranslate\">$\\frac{d\\theta_1}{dt} = \\frac{d\\hat{\\boldsymbol{e_2}}}{dt}\\cdot \\hat{\\boldsymbol{e_3}}$</span>. \n", "\n", "![versor](../images/derivVersor.png)\n", "    \n", "    "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "fragment"}}, "source": ["Similarly, the same is valid for the angles in the other two directions:  <span class=\"notranslate\">$\\frac{d\\theta_2}{dt} = \\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot \\hat{\\boldsymbol{e_1}}$</span> and  <span class=\"notranslate\">$\\frac{d\\theta_3}{dt} = \\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}$</span>.\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["So, we can write the angular velocity as:\n", "\n", " <span class=\"notranslate\">\n", "\\begin{equation}\n", "    \\vec{\\boldsymbol{\\omega}} =  \\left(\\frac{d\\hat{\\boldsymbol{e_2}}}{dt}\\cdot \\hat{\\boldsymbol{e_3}}\\right) \\hat{\\boldsymbol{e_1}} + \\left(\\frac{d\\hat{\\boldsymbol{e_3}}}{dt}\\cdot \\hat{\\boldsymbol{e_1}}\\right) \\hat{\\boldsymbol{e_2}} + \\left(\\frac{d\\hat{\\boldsymbol{e_1}}}{dt}\\cdot \\hat{\\boldsymbol{e_2}}\\right) \\hat{\\boldsymbol{e_3}}\n", "\\end{equation}\n", "</span>"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Note that the angular velocity $\\vec{\\boldsymbol\\omega}$ is expressed in the reference frame of the object. If you want it described as a linear combination of the versors of the global basis $\\hat{\\boldsymbol{i}}$, $\\hat{\\boldsymbol{j}}$ and $\\hat{\\boldsymbol{k}}$, just multiply the vector $\\vec{\\boldsymbol\\omega}$ by the rotation matrix formed by stacking each versor in a column of the rotation matrix (for a revision on rotation matrices see [this](https://nbviewer.jupyter.org/github/bmclab/bmc/blob/master/notebooks/Transformation2D.ipynb) and [this](https://nbviewer.jupyter.org/github/bmclab/bmc/blob/master/notebooks/Transformation3D.ipynb) notebooks)."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["### 1 ) 3D pendulum bar\n", "\n", "At the file '../data/3Dpendulum.txt' there are 3 seconds of data of 3 points of a three-dimensional cylindrical pendulum. It can move in every direction and has a motor at the upper part of the cylindrical bar producing torques to move the bar. \n", " \n", "The point m1 is at the upper part of the cylinder and is the origin of the system. \n", " \n", "The point m2 is at the center of mass of the cylinder. \n", " \n", "The point m3 is a point at the surface of the cylinder. \n", "\n", "Below we compute its angular velocity.\n", "\n", "First we load the file."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "np.set_printoptions(precision=4, suppress=True)\n", "plt.rc('text', usetex=True)\n", "plt.rc('font', family='serif')\n", "\n", "data = np.loadtxt('../data/3dPendulum.txt', skiprows=1, delimiter = ',')"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["And separate each mark in a variable."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["t = data[:,0]\n", "m1 = data[:,1:4]\n", "m2 = data[:,4:7]\n", "m3 = data[:,7:]\n", "dt = t[1]"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Now, we form the basis  <span class=\"notranslate\">$\\hat{\\boldsymbol e_1}$, $\\hat{\\boldsymbol e_2}$</span> and  <span class=\"notranslate\">$\\hat{\\boldsymbol e_3}$</span>."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["V1 = m2 - m1\n", "e1 = V1/np.linalg.norm(V1,axis=1,keepdims=True)\n", "\n", "V2 = m3-m2\n", "\n", "V3 = np.cross(V2,V1)\n", "e2 = V3/np.linalg.norm(V3,axis=1,keepdims=True)\n", "\n", "e3 = np.cross(e1,e2)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Below, we compute the derivative of each of the versors."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["de1dt = np.gradient(e1, dt, axis=0)\n", "de2dt = np.gradient(e2, dt, axis=0)\n", "de3dt = np.gradient(e3, dt, axis=0)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Here we compute each of the components  <span class=\"notranslate\">$\\omega_1$, $\\omega_2$</span> and  <span class=\"notranslate\">$\\omega_3$</span> of the angular velocity  <span class=\"notranslate\">$\\vec{\\boldsymbol \\omega}$</span> by using the scalar product."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["omega1 = np.sum(de2dt*e3, axis = 1, keepdims=True)\n", "omega2 = np.sum(de3dt*e1, axis = 1, keepdims=True)\n", "omega3 = np.sum(de1dt*e2, axis = 1, keepdims=True)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["Finally, the angular velocity vector  <span class=\"notranslate\">$\\vec{\\boldsymbol \\omega}$</span> is formed by stacking the three components together."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["omega = np.hstack((omega1, omega2, omega3))"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## Problems\n", "\n", "1) Initially, the lateral malleolus, medial malleolus, fibular head and medial condyle of the leg have the following positions (described in the laboratory coordinate system with coordinates  𝑥,𝑦,𝑧  in cm, the  𝑥  axis points forward and the  𝑦  axes points upward): lateral malleolus (lm = [2.92, 10.10, 18.85]), medial malleolus (mm = [2.71, 10.22, 26.52]), fibular head (fh = [5.05, 41.90, 15.41]), and medial condyle (mc = [8.29, 41.88, 26.52]). After 0.05 seconds the markers have the following positions: lateral malleolus (lm = [2.95, 10.19, 18.41]), medial malleolus (mm = [3.16, 10.04, 26.10]), fibular head (fh = [4.57, 42.13, 15.97]), and medial condyle (mc = [8.42, 41.76, 26.90]). \n", "\n", "Find the angular velocity of of the leg."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["## References\n", "\n", "\n", "- <PERSON>, <PERSON><PERSON> (1985) [Dynamics: Theory and Applications](https://ecommons.cornell.edu/handle/1813/638). McGraw-Hill, Inc\n"]}], "metadata": {"celltoolbar": "Slideshow", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}