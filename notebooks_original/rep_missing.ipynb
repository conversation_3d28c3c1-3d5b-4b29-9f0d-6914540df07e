{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replace missing data in different ways\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "%matplotlib notebook\n", "import sys, os\n", "%load_ext autoreload\n", "%autoreload 2\n", "sys.path.insert(1, r'./../functions')\n", "from rep_missing import rep_missing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The function `rep_missing.py` replaces missing data in different ways.  \n", "\n", "The signature of `rep_missing.py` is:\n", "```python\n", "y = rep_missing(x, value=np.nan, new_value='interp', max_alert=100)\n", "```"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function rep_missing in module rep_missing:\n", "\n", "rep_missing(x, value=nan, new_value='interp', max_alert=100)\n", "    Replace missing data in different ways.\n", "    \n", "    Parameters\n", "    ----------\n", "    x : 1D numpy array_like\n", "        data\n", "    value : number, optional. Default = np.nan\n", "        Value to be found in x marking missing data\n", "    new_value : number or string, optional. Default = 'interp'\n", "        Value or string for the method to use for replacing missing data:\n", "        'delete': delete missing data\n", "        new_value: replace missing data with new_value\n", "        'mean': replace missing data with the mean of the rest of the data\n", "        'median': replace missing data with the median of the rest of the data\n", "        'interp': replace missing data by linear interpolating over them.\n", "    max_alert : number, optional. Default = 100\n", "        Minimal number of sequential data for a message to be printed with\n", "        information about the continuous missing data.\n", "        Set to 0 to not print any message.\n", "        \n", "    Returns\n", "    -------\n", "    y : 1D numpy array_like\n", "        1D array similar to x but with missing data replaced according\n", "        to value or method in new_value.\n", "            \n", "    References\n", "    ----------\n", "    .. [1] http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/rep_missing.ipynb\n", "    \n", "    Examples\n", "    --------\n", "    >>> x = [1, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0]\n", "\n"]}], "source": ["help(rep_missing)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["x = [1, 2, np.nan, 4, 20, np.nan, np.nan, 0, 1]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1. ,  2. ,  nan,  4. , 20. ,  nan,  nan,  5.6,  1. ])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["rep_missing(x, 0, new_value='mean')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["np.s"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 9, 11]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True, min_seq=2)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([<PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse,\n", "       False, False, False])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 10)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([], shape=(0, 2), dtype=int64)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 10, index=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"800\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[4, 6]])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 1, index=True, min_seq=2, show=True)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"800\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([ True, False, False, False,  True,  True,  True, False,  True,\n", "       False, False, False])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 1, index=False, min_seq=1, show=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"800\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 9, 11]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True, min_seq=2, show=True)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential data equal or longer than 2: ([2], [1 3])\n", "Sequential data equal or longer than 2: ([2], [ 9 11])\n"]}, {"data": {"text/plain": ["array([[ 1,  3],\n", "       [ 7,  7],\n", "       [ 9, 11]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["detect_seq(x, 0, index=True, max_alert=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function `rep_missing.py`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %load ./../functions/rep_missing.py\n", "#!/usr/bin/env python\n", "\n", "\"\"\"Replace missing data in different ways.\"\"\"\n", "\n", "import numpy as np\n", "from detect_seq import detect_seq\n", "try:\n", "    from tnorm import tnorm\n", "except:\n", "    print('Function tnorm.py not found. Method \"interp\" not available.')\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = 'rep_missing.py v.1.0.0 2019/03/17'\n", "\n", "\n", "def rep_missing(x, value=np.nan, new_value='interp', max_alert=100):\n", "    \"\"\"Replace missing data in different ways.\n", "\n", "    Parameters\n", "    ----------\n", "    x : 1D numpy array_like\n", "        data\n", "    value : number, optional. Default = np.nan\n", "        Value to be found in x marking missing data\n", "    new_value : number or string, optional. Default = 'interp'\n", "        Value or string for the method to use for replacing missing data:\n", "        'delete': delete missing data\n", "        new_value: replace missing data with new_value\n", "        'mean': replace missing data with the mean of the rest of the data\n", "        'median': replace missing data with the median of the rest of the data\n", "        'interp': replace missing data by linear interpolating over them.\n", "    max_alert : number, optional. Default = 100\n", "        Minimal number of sequential data for a message to be printed with\n", "        information about the continuous missing data.\n", "        Set to 0 to not print any message.\n", "        \n", "    Returns\n", "    -------\n", "    y : 1D numpy array_like\n", "        1D array similar to x but with missing data replaced according\n", "        to value or method in new_value.\n", "            \n", "    References\n", "    ----------\n", "    .. [1] http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/rep_missing.ipynb\n", "\n", "    Examples\n", "    --------\n", "    >>> x = [1, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0]\n", "\n", "    \"\"\"\n", "    \n", "    y = np.asarray(x)\n", "\n", "    idx = detect_seq(y, value, index=False, max_alert=max_alert)\n", "\n", "    if idx.any():\n", "        if new_value == 'delete':\n", "            y = y[~idx]\n", "        elif np.isreal(new_value) or np.iscomplex(new_value):\n", "            y[idx] = new_value\n", "        elif new_value == 'mean':\n", "            y[idx] = np.nanmean(y)\n", "        elif new_value == 'median':\n", "            y[idx] = np.nanmedian(y) \n", "        elif new_value == 'interp':\n", "            y[idx] = np.nan\n", "            y, t, indie = tnorm(y, step=0, k=1, smooth=0, nan_at_ext='replace')\n", "            \n", "    return y\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}