{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to data analysis in electromyography\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Electromyography (EMG), from Latin, literally means recording of muscle electricity. Nowadays, a more accurate definition is that EMG is the technique employed to register the electrical signal related to muscle activation.   \n", "\n", "This electrical signal represents the electrical current generated by the ionic flow across the membranes of muscle fibers when the motor unit is fired. A motor unit is the functional unit of a muscle and consists of an alpha motor neuron and the muscle fibers innervated by this motor neuron. This electrical signal can be captured by invasive needle or wire electrodes placed directly near the muscle fibers (intramuscular EMG) of by electrodes placed on the skin where it is captured the electrical signal that propagates through the tissues of the human body (surface EMG). The use of surface EMG is much more common in human motion studies and here will see an introduction to the analysis of the surface EMG signal.   \n", "\n", "For a typical muscle activation during voluntary human movement, the measured surface EMG signal is the result of an algebraic summation of many motor unit action potentials. A typical surface EMG signal has a random characteristic with zero mean, about 1 mV of peak-to-peak amplitude, and frequencies between 5-10 and 400-450 Hz, but these values can be affected by many factors including intensity of the muscle activation, the distance from the motor units to the skin surface, the geometry of the electrodes, etc.  \n", "\n", "Let's load a surface EMG signal from a voluntary contraction and analyze it."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:57:11.937039Z", "start_time": "2017-12-30T07:57:11.685787Z"}}, "outputs": [], "source": ["import numpy as np\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:57:12.971415Z", "start_time": "2017-12-30T07:57:12.925957Z"}}, "outputs": [], "source": ["# load data file\n", "time, data = np.loadtxt('./../data/emg.csv', delimiter=',', unpack=True)\n", "freq = 1/np.mean(np.diff(time))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And let's plot the data:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:57:17.969857Z", "start_time": "2017-12-30T07:57:17.787316Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c9ff18c18>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "fig, ax = plt.subplots(1, 1, figsize=(8, 3))\n", "ax.plot(time, data, 'b')\n", "ax.set_xlabel('Time [s]', fontsize=12)\n", "ax.set_ylabel('EMG amplitude [V]', fontsize=12)\n", "ax.set_xlim(time[0], time[-1])\n", "ax.set_title('Raw data', fontsize=16)\n", "plt.locator_params(axis = 'both', nbins = 4)\n", "plt.grid()\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filtering\n", "\n", "Any bioelectrical signal is contaminated by noise, be it from other electrical sources in the human body, from external sources, or by the own process of measurement.\n", "To get rid of part of this noise, we can apply a band-pass filter to only pass signals with frequencies in the desired range.\n", "A common choice for filtering EMG data it's the <PERSON><PERSON>worth filter with zero lag. For more about data filtering [click here](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/DataFiltering.ipynb).  \n", "Let's employ a Butterworth filter with band pass at 10-400 Hz, second order, and zero lag:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:57:27.753924Z", "start_time": "2017-12-30T07:57:27.562348Z"}}, "outputs": [], "source": ["# band-pass Butterworth filter\n", "from scipy.signal import butter, filtfilt\n", "b, a = butter(2, ([10, 400]/(freq/2)), btype = 'bandpass')\n", "dataf = filtfilt(b, a, data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the high-pass filter removes any offset in the signal (a constant value or DC component can be thought as a signal with frequency equals to zero)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:57:33.236016Z", "start_time": "2017-12-30T07:57:32.964800Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c954e00b8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "fig, (ax1,ax2) = plt.subplots(2, 1, sharex = True, sharey = True, figsize = (8, 5))\n", "ax1.plot(time, data, 'r')\n", "ax1.set_title('Raw data', fontsize=16)\n", "ax1.grid()\n", "ax2.plot(time, dataf, 'b')\n", "ax2.set_xlabel('Time [s]', fontsize=12)\n", "ax2.set_xlim(time[0], time[-1])\n", "ax2.set_title('Band-pass filter at [10 400] Hz', fontsize=16)\n", "fig.text(0, 0.5, 'EMG amplitude [V]', ha='center', va='center',\n", "         rotation='vertical', fontsize=12)\n", "plt.locator_params(axis = 'both', nbins = 4)\n", "ax2.grid()\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Linear envelope\n", "\n", "A common processing of the EMG signal is to calculate an activation level of the signal, a process called linear envelope. The linear envelope consists in two steps: the signal is full-wave rectified by computing the absolute value of the signal; then the rectified signal is low-pass filtered with a cutoff frequency typically at the range of 3 to 8 Hz, depending of the contraction muscle characteristics and the specific application for the linear envelope processing. This last step can also be reproduced with a moving average of the rectified signal with a moving window of 100 to 200 ms of duration.   Alternatively, instead of the two previous steps, it can be applied a moving root mean square (RMS) with a moving window of 100 to 200 ms of duration to the original signal.\n", "\n", "Let's calculate the linear envelope. First, the full-wave rectification:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:58:28.785212Z", "start_time": "2017-12-30T07:58:28.780676Z"}}, "outputs": [], "source": ["# full-wave rectification\n", "datafr = abs(dataf)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Second, a low-pass filter at 8 Hz using a <PERSON><PERSON>worth filter of second order and zero lag:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:58:30.268448Z", "start_time": "2017-12-30T07:58:30.260191Z"}}, "outputs": [], "source": ["# low-pass Butterworth filter\n", "b, a = butter(2, (8/(freq/2)), btype = 'low')\n", "datafrle = filtfilt(b, a, datafr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's plot the results of each operation:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:58:32.456450Z", "start_time": "2017-12-30T07:58:32.018231Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c9511fb38>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "fig, (ax1,ax2,ax3,ax4) = plt.subplots(4, 1, sharex = True, sharey = True, figsize = (8, 7))\n", "ax1.plot(time, data, 'r')\n", "ax1.set_title('Raw data', fontsize=12)\n", "ax1.set_xlim(time[0], time[-1])\n", "ax1.grid()\n", "ax2.plot(time, dataf, 'b')\n", "ax2.set_title('Band-pass filter at [10 400] Hz', fontsize=12)\n", "ax2.grid()\n", "ax3.plot(time, datafr, 'b')\n", "ax3.set_title('Band-pass filter at [10 400] Hz and rectification', fontsize=12)\n", "ax3.grid()\n", "ax4.plot(time, datafrle, 'b', linewidth=2)\n", "ax4.set_xlabel('Time [s]', fontsize=12)\n", "ax4.set_title('Band-pass filter at [10 400] Hz, rectification and low-pass filter at 8 Hz',\n", "              fontsize=12)\n", "fig.text(0, 0.5, 'EMG amplitude [V]', ha='center', va='center',\n", "         rotation='vertical', fontsize=12)\n", "plt.locator_params(axis = 'both', nbins = 4)\n", "ax4.grid()\n", "plt.tight_layout(h_pad=.1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:58:36.134163Z", "start_time": "2017-12-30T07:58:35.965756Z"}}, "outputs": [{"data": {"image/png": "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****************************************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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c9515fc88>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "fig, ax = plt.subplots(1, 1, figsize=(8, 3))\n", "ax.plot(time, data, 'r')\n", "ax.plot(time, datafrle, 'b', linewidth=2)\n", "ax.set_xlabel('Time [s]', fontsize=12)\n", "ax.set_ylabel('EMG amplitude [V]', fontsize=12)\n", "ax.set_title('Band-pass filter at [10 400] Hz, rectification and low-pass filter at 8 Hz',\n", "          fontsize=12)\n", "ax.set_xlim(time[0], time[-1])\n", "plt.locator_params(axis = 'both', nbins = 4)\n", "plt.grid()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that to calculate the linear envelope of the EMG signal, we (1) removed all frequencies below 10 Hz and above 400 Hz; (2) rectified the signal; and (3) removed all frequencies above 5 Hz.  \n", "\n", "How it is possible to have any signal with frequencies below 5 Hz after the third step if we removed all frequencies below 10 Hz in the first step? The trick is that the rectification operation creates new frequencies in the signal.  \n", "\n", "The band-pass filtering in the first step is not part of the linear envelope processing. One can calculate the linear envelope directly for the raw signal. However if we do that for an raw EMG signal with an offset, its linear envelope will always have values much greater than zero suggesting a muscle activation where there was not.\n", "\n", "Let's write a function that calculates the linear envelope of a signal and allows the user to input different parameters for the filtering:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:58:44.618666Z", "start_time": "2017-12-30T07:58:44.586723Z"}}, "outputs": [], "source": ["def linear_envelope(x, freq=1000, fc_bp=[10, 400], fc_lp=8):\n", "    r\"\"\"Calculate the linear envelope of a signal.\n", "\n", "    Parameters\n", "    ----------\n", "    x     : 1D array_like\n", "            raw signal\n", "    freq  : number\n", "            sampling frequency\n", "    fc_bp : list [fc_h, fc_l], optional\n", "            cutoff frequencies for the band-pass filter (in Hz)\n", "    fc_lp : number, optional\n", "            cutoff frequency for the low-pass filter (in Hz)\n", "\n", "    Returns\n", "    -------\n", "    x     : 1D array_like\n", "            linear envelope of the signal\n", "\n", "    Notes\n", "    -----\n", "    A 2nd-order Butterworth filter with zero lag is used for the filtering.  \n", "\n", "    See this notebook [1]_.\n", "\n", "    References\n", "    ----------\n", "    .. [1] https://github.com/demotu/BMC/blob/master/notebooks/Electromyography.ipynb\n", "\n", "    \"\"\"\n", "    \n", "    import numpy as np\n", "    from scipy.signal import butter, filtfilt\n", "    \n", "    if np.size(fc_bp) == 2:\n", "        # band-pass filter\n", "        b, a = butter(2, (fc_bp/(freq/2.)), btype = 'bandpass')\n", "        x = filtfilt(b, a, x)   \n", "    if np.size(fc_lp) == 1:\n", "        # full-wave rectification\n", "        x = abs(x)\n", "        # low-pass Butterworth filter\n", "        b, a = butter(2, (fc_lp/(freq/2.)), btype = 'low')\n", "        x = filtfilt(b, a, x)\n", "    \n", "    return x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Onset detection\n", "\n", "A common operation in the analysis of EMG data is to select the period of muscle activation based on the amplitude of the EMG signal. Of course this can be done manually by showing the EMG signal on a window and the expert user manually selects the events with mouse clicks, but a lot of research has been conducted to propose automatic methods to perform this task.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Amplitude-threshold method\n", "\n", "The simplest method to automatically detect the onset in the EMG signal is based on amplitude threshold, where the EMG signal is considered to be 'on' when it is above a certain threshold. This threshold is usually proportional to the amplitude of the baseline (the part of the EMG signal that we know there is no muscle activation), for example, a threshold equals to three times the standard deviation of the baseline. To avoid the detection of spikes in the EMG signal, a second threshold can be used imposing that the signal should be above the threshold for at least a certain period (number of samples).  \n", "\n", "The raw EMG signal can not be analyzed for the onset detection based on amplitude threshold because its amplitude varies from positive to negative very fast. The linear envelope of the EMG signal is usually analyzed or the amplitude detection is performed in terms of the standard deviation or RMS of the signal.\n", "\n", "The text [\n", "Detection of onset in data](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/DetectOnset.ipynb) presents a Python code to perform onset detection. Let's use that code:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:59:45.720685Z", "start_time": "2017-12-30T07:59:45.698300Z"}}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, r'./../functions')  # add to pythonpath\n", "from detect_onset import detect_onset"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:59:47.397596Z", "start_time": "2017-12-30T07:59:47.201276Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c94f83a20>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 399,  859],\n", "       [1388, 1879],\n", "       [2410, 2918]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["threshold=2\n", "window=50\n", "data2 = linear_envelope(data, freq, fc_bp=[20, 400], fc_lp=20)\n", "inds = detect_onset(data2, threshold=threshold*np.std(data[0:200]),\n", "                    n_above=window, n_below=10, show=True)\n", "inds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The Teager–Kaiser Energy operator to improve onset detection\n", "\n", "The Teager–Kaiser Energy (TKE) operator has been proposed to increase the accuracy of the onset detection by improving the SNR of the EMG signal (<PERSON> et al., 2007).   \n", "The TKE operator processes the data in the following way:\n", "\n", "$$ y[n]=x^2[n]-x[n-1]\\cdot x[n+1] $$\n", "\n", "Where $x[n]$ is the sample $n$ of the signal $x$.\n", "\n", "Let's write a function that implements the TKE operator:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:00:09.358238Z", "start_time": "2017-12-30T08:00:09.343658Z"}}, "outputs": [], "source": ["def tkeo(x):\n", "    r\"\"\"Calculates the Teager–Kaiser Energy operator.\n", "\n", "    Parameters\n", "    ----------\n", "    x : 1D array_like\n", "        raw signal\n", "\n", "    Returns\n", "    -------\n", "    y : 1D array_like\n", "        signal processed by the Teager–Kaiser Energy operator\n", "\n", "    Notes\n", "    -----\n", "\n", "    See this notebook [1]_.\n", "\n", "    References\n", "    ----------\n", "    .. [1] https://github.com/demotu/BMC/blob/master/notebooks/Electromyography.ipynb\n", "\n", "    \"\"\"\n", "    x = np.asarray(x)\n", "    y = np.copy(x)\n", "    # Teager–Kaiser Energy operator\n", "    y[1:-1] = x[1:-1]*x[1:-1] - x[:-2]*x[2:]\n", "    # correct the data in the extremities\n", "    y[0], y[-1] = y[1], y[-2]\n", "    \n", "    return y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And let's test it (with the same parameters as before):"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:00:13.503467Z", "start_time": "2017-12-30T08:00:13.323596Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c9514b630>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([[ 397,  872],\n", "       [1379, 1896],\n", "       [2411, 2983]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["threshold = 2\n", "window = 50\n", "data3 = tkeo(dataf)\n", "data4 = linear_envelope(data3, freq, fc_bp=[20, 400], fc_lp=20)\n", "inds2 = detect_onset(data4, threshold=threshold*np.std(data3[0:200]),\n", "                     n_above=window, n_below=10, show=True)\n", "inds2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Amplitude estimation\n", "\n", "Different measures can be used to describe the amplitude of the EMG signal:  \n", "\n", "* Mean, median   \n", "* Standard deviation (SD)\n", "* Root mean square (RMS)   \n", "* Maximum, minimum   \n", "* Etc.\n", "\n", "From the above, RMS is probably the less usual and it defined as:\n", "\n", "$$ RMS(x) = \\sqrt{\\frac{1}{N}\\sum_{i=1}^{N}\\; x_i^2 } $$\n", "\n", "And in Python/Numpy:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:00:27.706431Z", "start_time": "2017-12-30T08:00:27.699265Z"}}, "outputs": [{"data": {"text/plain": ["0.020019669235775334"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["rms = np.sqrt(np.mean(dataf**2)) # root mean square, the name makes sense.\n", "rms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["All the measures above have the same unit as the original EMG signal and they can be applied directly to the raw or to the linear evelope of the EMG signal."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Integration (area)\n", "\n", "The area of the signal-versus-time curve (integral) is another measure used in EMG processing.   \n", "The definition of the integral for a continuous function is:\n", "\n", "$$ \\text{Area}= \\int_{t_i}^{t_f}x(t)dt $$\n", "\n", "For discrete data, as the EMG signal, the integral can be calculated by numerical integration using the rectangle rule:\n", "\n", "$$ \\text{Area} \\;\\approx\\; \\Delta t \\sum_{i=1}^{N}\\:x(t_i) $$\n", "\n", "Or more accurately, using the trapezoidal rule:\n", "\n", "$$ \\text{Area} \\;\\approx\\; \\Delta t \\sum_{i=1}^{N-1}\\frac{x(t_i)+x(t_{i+1})}{2} $$\n", "\n", "And in Python/Numpy:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:00:50.130265Z", "start_time": "2017-12-30T08:00:50.119734Z"}}, "outputs": [{"data": {"text/plain": ["3.07845984087131e-05"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["x = dataf\n", "Dt = 1/freq  # time increment between samples\n", "Arect = Dt*sum(x)\n", "from scipy.integrate import trapz\n", "Atrap = Dt*trapz(x)\n", "Atrap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The integrals above are single numbers, the total area under the EMG curve.   \n", "If we want to calculate the integral as a function of time, we have to use the cumulative integration:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:00:54.943972Z", "start_time": "2017-12-30T08:00:54.940057Z"}}, "outputs": [], "source": ["from scipy.integrate import trapz, cumtrapz"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:00:55.849412Z", "start_time": "2017-12-30T08:00:55.844219Z"}}, "outputs": [], "source": ["Arect = Dt*np.cumsum(x)\n", "Atrap = Dt*cumtrapz(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As the raw EMG signal has zero mean, one is usually interested in the area under the curve of the linear envelope.   \n", "Let's experiment with these integration measures:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:06.357862Z", "start_time": "2017-12-30T08:01:06.348353Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total area by the rectangle rule: 0.040508 Vs\n", "Total area by the trapezoid rule: 0.040506 Vs\n"]}], "source": ["Arect = sum(datafrle)/freq\n", "print('Total area by the rectangle rule: %f Vs'%Arect)\n", "Atrap = trapz(datafrle)/freq\n", "print('Total area by the trapezoid rule: %f Vs'%Atrap)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the 'Vs' above is not the plural of V; it's Volts times seconds.   \n", "And now the cumulative integration:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:12.019553Z", "start_time": "2017-12-30T08:01:12.013918Z"}}, "outputs": [], "source": ["Arect2 = np.cumsum(datafrle)/freq\n", "Atrap2 = cumtrapz(datafrle, initial=0)/freq"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:13.817501Z", "start_time": "2017-12-30T08:01:13.557544Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjgAAAFgCAYAAAC2QAPxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMS4xLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvAOZPmwAAIABJREFUeJzs3XeYk1X2wPHvoUvvi1gAQaU4gDRFVERULAi4oqJrd22/tey6rrv2srqWtYvuLooFO2IDbCiKBREZrBSlI0MvA9LbnN8fJ3FCSDLJzCRvZuZ8nidPJnnfvDmZySTnvffce0VVcc4555wrTyoFHYBzzjnnXGnzBMc555xz5Y4nOM4555wrdzzBcc4551y54wmOc84558odT3Ccc845V+54guNcBSYi/xWRmzPwPLeJyAsJtlcXkRki0ix0+1kRuTP08xEi8nO6Y8xmIqIi0ibNzzFARF5J53M4l0me4DiXISKyQEQ2i8iGiMvQ0LbzQ19iD0Y9ZlDo/mcj7qsmIreIyM8islFEFovIeyJyXKoxqeplqvrPEr+4krsE+ExVl0VvUNXPVfXAAGKqUFR1NHCQiHQMOhbnSoMnOM5l1smqWjvickXEtrnAGSJSJeK+c4FZUccYBQwMbWsAtAIeAU5KY9zpdinwfNBBRIr6O1QUL2PJpnNlnic4zmWPZcCPQD8AEWkIHAaMDu8gIscAxwIDVXWyqm4LXd5X1atjHVTMQyKyQkTWicgPInJQaNtvXUGh29eJyFIRWSIif4zsGgnt+7iIvCMi60Vksoi0jnjsIyKySER+FZGpInJEMi9aRPYFWgOT42w/SkTyIm4vEJFrQ69jnYi8KiI1Irb3F5HvRGStiHwZ2SIhIv8Qkbmh+GeIyCkR284XkYmh39Ua4LYYsVSKOMZqERkZ+jshIi1Dv6/zROQXEVklIjeGtjUPtd41jDjWwaF9qoZuXygiM0UkX0Q+EJEWcX4f9URkhIisFJGFInKTiFSKeg2PhX43P4lI36jHDg/9jReLyJ0iUjni8BMo24myc7/xBMe57DICa5kBGAK8DWyN2H4MMFlV86IfmMBxwJHAAUB94AxgdfROInI8cE3oOdoAvWMc60zgdqzlaA5wV8S2KUBnoCHwEvBaZOKRQA4wT1V3JPl6AE4HjsdarzoC54deQxfgaaxFqBHwP2C0iFQPPW4ucARQL/Q6XhCRPSOOewgwD2ga9drCrgIGYb+b5kA+8HjUPocDBwJ9gVtEpJ2qLgEmAadG7HcWMEpVt4vIIOAG4PdAE+BzrDUllsdC8e8XiuNc4IIYr6ExcCvwRkRi9RywA/v7Hoy9N/4Y8diZQEsRqRvnuZ0rMzzBcS6z3gq1LIQvF0dtfxM4SkTqYV9cI6K2N8ZaegBr5QkdZ52IbInznNuBOkBbQFR1pqoujbHf6cAzqjpdVTdhCUC0N1T161Ay8iKW0ACgqi+o6mpV3aGqDwDVsS/6otQH1iexX6RHVXWJqq4BxkTEcTHwv1Dr1k5VfQ5LEA8Nxfha6HEFqvoqMBvoEXHcJar6WOg1bI7xvJcCN6pqnqpuxVp5Bkd1Z92uqptV9Xvge6BT6P6XsAQREREsgX0p4rh3h/42O4B/AZ2jW3FCrS1nANer6npVXQA8AJwTsdsK4GFV3R56jT8DJ4nI74ATgD+r6kZVXQE8FIojLPx3qB/jtTtXpniC41xmDVLV+hGXJyM3hr5U3wFuAhqr6sSox68G9ozYf42q1ge6YgnFblT1Y2Ao1tKwXESGxTlDbw4siri9KMY+kUXAm4Da4Rsi8tdQF8s6EVmLtTI0jhVTlHwsAUtFvDhaAH+NTCKBfbDXhoicG9F9tRY4KCrGWK85UgvgzYjHzwR2Ar9LIrZRQE8RaY61qCnWUhM+7iMRx10DCLBX1PM3BqoBCyPuWxi132LddRXlhaHX3wKoCiyNeJ7/Ya1VYeG/w9r4vwLnygZPcJzLPiOAvxK76HY80F1E9k7lgKr6qKp2BTpgXVV/i7HbUiDyuPske/xQvc3fsVagBqGkax32JV2UH4D9pHSKehcBd0UlkTVV9eVQa8iTwBVAo1CM06Ji1BjHjD7+CVHHr6Gqi4sKTFXXAuOw39FZwMsRicgi4NKo4+6hql9GHWYV1iIX2bKzLxD5/HuFWogity8JPcdWLHEOP0ddVe0QsW87YIGq/lrU63Eu23mC41z2+RQrJH4seoOqjgM+wbq6DhEbMl6VUBdMLCLSPbRvVWAjsAVrdYg2ErhARNqJSE3glhRiroPVdqwEqojILUBSdRyheqLorqLiehK4LPR6RURqichJIlIHqIUlMCsBROQCrAUnFf8F7gp3HYlIExEZmMLjX8K6Hk+lsHsqfNzrRaRD6Lj1ROS06Aer6k7s73SXiNQJxXENEDnHUFPgKhGpGjpGO+DdULfkOOABEakbKphuLSKRtVa9gfdSeD3OZS1PcJzLrDGy6zw4b0bvoGZ8qL4klt8DY7EvtbXAfOAPWNFtLHWxL/58rLtiNXB/jOd9D3gUS6DmYEWxsGuRczwfYF+Ms0LPsYWiu3si/Y9d60iKRVVzsTqcodjrnUOoAFlVZ2D1KpOA5Vhxc3QXYFEewUa1jROR9cBXWFFvskYD+wPLQzU64bjfBO4FXhGRX7GWpRPiHONKLFGdB3yBJUpPR2yfHHqOVVih9GBVDReVn4t1cc3Afj+jiOjyxGqE/pfC63Eua8muXbXOOWdEpB32RVs9xRFOxXmu6sC3QN84BdAuCSJyPvBHVT28GI89GThHVU8v9cCcC4C34DjnfiMip4S6vRpgLQpj0p3cAKjqVlVt78lNcFR1jCc3rjzxBMc5F+lSrEZlLlanc3mw4TjnXPF4F5Vzzjnnyh1vwXHOOedcuVNuFpNr3LixtmzZMugwnHPOOZdGU6dOXaWqTYrar9wkOC1btiQ3NzfoMJxzzjmXRiKysOi9vIvKOeecc+WQJzjOOeecK3c8wXHOOedcueMJjnPOOVeUL76A8eODjsKloNwUGTvnnHNp8d13cMQR9vPHH0OfPsHG45LiLTjOOedcIk+H1jKtWRPuuSfYWFzSPMFxwVuyBNauDToK55zbnSq89RYMGAB/+Qt89BEsWxZ0VC4JnuC4YI0dC/vuC/vtB7NmBR2Nc87taskSWLQI+vaF006DggJ4//2go3JJ8ATHBWfnTrjqKmjRwj40/va3oCNyzrldffONXXftCjk50KSJ1eG4rOdFxi4448bB/PkwciT88APceScsXgx77RV0ZM45Z775BkSgUyeoVMkKjMePt64rkaCjcwl4C44LzttvQ+3aMHAg/OEPdt/rrwcbk3PORfr2WzjwQPusAujd27qtfvkl2LhckTzBccFQhffeg2OOgWrVoG1bu7z3XtCROedcodmzoV27wts9etj1118HE49Lmic4Lhg//WRnQCecUHjf0UfbZFrbtwcXl3POhanCvHnQqlXhfR07QvXqnuCUAZ7glDf33w9HHQVz5gQdSWKff27XffsW3nfUUbBhA0ydGkhIzjm3i2XLYMsWG+UZVq0aHHywJzhlgCc45cmCBXDddfDpp3DttUFHk9jkydC48a4fHL172/WECYGE5Jxzu5g3z64jP6fAuqlyc2HHjszH5JLmCU558uab1qR61lkwejSsXBl0RPFNnmwfEpGjEJo2hTZtbJtzzgUtXoLTtSts2mT1OS5reYJTnnz5JbRsCddcY4nOu+8GHVFs69fDjBmFxXqRuneHKVMyH5NzzkWbP99Owlq02PX+jh3t+scfMx+TS5onOOXJpEnQsyd06QK/+x18+GHQEcWWm2sJ2CGH7L6te3ebC2fp0szH5ZxzkebNs3m5atTY9f527aByZZu/y2WtuBP9icjvk3j8FlWN20wgIscDjwCVgadU9Z6o7dWBEUBXYDVwhqouEJEewLDwbsBtqvpmEvFUXOvWWWLQubOdcRxxBEycGHRUsYU/FA4+ePdt3bvb9ZQptvaLc84FZd683bunwEZRHXCAt+BkuUQzGT8JvI0lGPEcCcRMcESkMvA4cCyQB0wRkdGqOiNit4uAfFVtIyJDgHuBM4BpQDdV3SEiewLfi8gYVfWKrnjCo6b239+ue/WCUaOyc2bgmTOhQQOruYl28MF2ZuQJjnMuaPPmwbHHxt7WsaPXC2a5RAnOe6p6YaIHi8gLCTb3AOao6rzQvq8AA4HIBGcgcFvo51HAUBERVd0UsU8NQBPF4ShMcNq0sevDD7frL76AM84IJqZ4Zs60Jt5Y05zXqgUdOngdjnMuWFu22Ali5Bw4kXJy4NVX4ddfoW7dzMbmkpKoBueCoh6sqmcn2LwXsCjidl7ovpj7hFpn1gGNAETkEBGZDvwIXBar9UZELhGRXBHJXZnNI4YyIZzgtG5t1507Q82aVpeTbcIJTjzduxfW6TjnXBAWLLDrWF1UUFhoPG1aRsJxqUuU4CwWkSdF5GiRYq0oFusx0d9YcfdR1cmq2gHoDlwvIjV221F1mKp2U9VuTZo0KUaI5cjs2dYVVbOm3a5SBbp1y74EZ/VqG76eKMHp2tX2W7Qo/j7OOZdO8YaIh+Xk2LUXGmetRAlOOyAXuBlYJCIPi0iMYS9x5QH7RNzeG1gSbx8RqQLUA9ZE7qCqM4GNwEEpPHfFs2DB7k2pPXvaQnFbtgQSUkwzZ9p1ogSnSxe7/uab9MfjnHOxFJXgtGgBdep4oXEWi5vgqOpqVf2fqvbB6mnmAw+LyFwRuSuJY08B9heRViJSDRgCjI7aZzRwXujnwcDHqqqhx1QBEJEWwIHAglReWIWzfDk0a7brfYceaus6ZVOikEyC07GjFRr7kg3OuaDMmwd77GFTbsQiAgcd5F1UWSypeXBUdQkwHPgPsB74YxKP2QFcAXwAzARGqup0EblDRMLDY4YDjURkDnAN8I/Q/YdjI6e+A94E/k9VVyX/siqg5ct3/0c89FC7/uqrzMcTz8yZ9qERPXFWpD32gPbtsysxc85VLPPnW+tNogqNnBxrwfF6wayUaBQVobqXk4EzgV7A+8D1wLhkDh6aI+fdqPtuifh5C3BajMc9DzyfzHM4YNs2yM/fPcFp1sxmNs6mOpyZM+HAA6FSEbl1ly7w/vv2wVGsEjDnnCuBeHPgRMrJgWHDYMmS7JuOw8VvwRGRl4BfsHlpXgJaqOp5qvqequ7MVIAuCeERZLGaUnv2zL4WnETdU2Fdu1qrlM9o7JzLNFVLcOINEQ8LFxp7HU5WSnQa/QHQWlUHq+qoUGuLy0bLl9t1rInzDj0U8vLsErSNG2HhwuQSnHChsdfhOOcybcUK2LChcNqNeDzByWqJEpzVqro+0YNFpH8px+OKI5zgxGvBgexoxfn5Z7tOJsEJLznhdTguke3b4dxzrTv2ySeDjsaVF3Pn2nV44tR4GjaE5s09wclSiRKcf4vIwSLSJd4F+FemAnUJJEpwOnWyheKyIcFJZgRVWK1a0Latt+C4xIYOheeft/fLZZfB998HHZErD6Jnhk/koIM8wclSiYqMlwMPFvH42aUYiyuuRAlOtWpWz5INhcYzZ9rw7/B6WUXp2hU++SS9Mbmya/t2uOceOOYYGDnSvoxuvRXeeivoyFxZN2eODYRo2bLofXNyLNHescMmWHVZI9E8OEepap8iLqdmMlgXx4oVdgZbq1bs7Yceai0h27ZlNq5oM2dan3a1asnt36WLrQUTTuCci/T++/bev/JKW7z1kktgzJjsqDdzwdixA+68Ey66qHCpheKYOxf23Te5z6qcHNi61WaTd1klqXlwXJaLNQdOpJ497R/wu+8yF1MsyY6gCuva1a69DsfFMmIENGkCJ5xgty++GAoK4IVEawC7cu2OO+Dmm+GZZ+C444o/i/ucOcl1T4EXGmcxT3DKg+XLY4+gCgtP+Pfll5mJJ5Zt2+wMp0OH5B/TubNdex2Oi7Z1K7z3Hpx6KlStavftt5+tv+ZdVBXTihVw771w1lkwbpx93gwdWrxjpZLgtG9v3Vm+JlXW8QSnPCiqBWevveyf9aOPMhdTtDlzrPk4lRacunXhgAO8Bcft7tNPbdqB/lEDOQcNgsmTbeI1V7GMGGEnUjfeaHVZvXvD44/DzhSnbVuzxi7JJjg1algrztdfpx6zS6siExwRqSkiN4vIk6Hb+/vw8CxTVIID0K+fFexu3ZqZmKKFR1C1b5/a47p08RacoKlaq8jYsdkzJf3Ysbakx9FH73r/oEF2PTp62TtX7j3/vHXHhz9j/vQnq8P58MPUjhPuajoohfWdDzvMRqqmmky5tEqmBecZYCsQmlCFPODOtEXkUrNzJ6xaVXSCc9xxsGlTcN1UM2bYvDZt26b2uC5d4Jdf7DW6YNx9N5xyCpx8sv0cNFVLcPr2tSQnUvv21lU1ZkwwsblgLF5sXUSnnFJ434ABVnw+YkRqxwonOOHammQcdhisX2+fcy5rJJPgtFbV+4DtAKq6GfDFgbLF6tVWWFlUgtOnjw1h/OCDzMQVbcYMG3JZs2Zqj/NC42Dl51tS078/DB4Mt91WOEdIUH76yRZCjO6eAkuiTz4Zxo+3LixXMYQ/144/vvC+6tVhyBBrffz11+SP9cMPNoHfnnsm/5jDDrPrL75I/jEu7ZJJcLaJyB6AAohIa6xFx2WDRMs0RKpTB4480v7Zg+hmmDEj9e4pgIMPtmtPcIIxapRNWX/77fDYY5ZAPFjU9FhpNnasXZ90Uuzt/ftbV+z48ZmLyQXr/fdtRuHobqVzz4XNm+H115M/1g8/WOtNKov8tmplJ3Dvv5/8Y1zaJZPg3IqtIr6PiLwIjAeuS2tULnmJJvmLdsYZtlxCpmd73bHDnrc4CU6DBtbl4HU4wXj3XWjRwhLNZs3gnHPg2Wdh3brgYho71mbo3nvv2NuPPNIK1L2bqmLYudPqbI4/fvek5JBDbGLRZLupNm+2k6lDDkktBhFLuD/6qPhD012pKzLBUdUPgd8D5wMvA91UdUJ6w3JJSyXB+f3vrZvq5ZfTG1O0+fPtjDqVEVSRunTxFpwgbNtmH9gnnlj4xXHJJfYlMHJkMDHl58PEibG7p8KqVbOi+nfese5bV75NmwZr11o3fDQRa8WZMMEW+i1Kbq7NkH344anH0b+/1Tl6y2HWiJvgRK051QJYCiwB9g3d57JBKglO48Z2ljNiRGZHU4WL9lKZAydS164wb559ubnMmTbNuqeOOqrwvu7drSXumWeCiemDD+yMPV73VFj//rB0qSfGFUF44ESvXrG3n322XSczAeTnn9t1uKYmFX36QKNG1sLpskKiFpwHQpfHgcnAMODJ0M+Ppj80l5Tly+2MtV695Pa/6ipYtgxeeSW9cUWaOtXWoEplVEKkLqF8+ttvSy8mV7Rwt2C40BvsjPj8821ts/Dq8Jk0dqwl6j16JN7vxBNt8rVwvY4rvyZOtILgeOtGtWxpc+L85z820imRsWNtgtFGjVKPo3p1ay16+22bdNAFLtFaVH1UtQ+wEOiiqt1UtStwMBDwMAr3mxUrrPUm2YK4Y46xQrx777XamEyYOtVab6KH9CYrnOD42XhmTZ1qifN+++16/znnWMKa6TPVnTutiPOEE+z5E2nc2M7CR43Knrl7XHpMnGh/60SfgXffbZM/nnde/BqZvDxL3E8twRKLF19sXVxPP138Y7hSk0yRcVtV/W2RDVWdBnROX0guJUUt0xBNxNZrmTkT/ve/9MUVpmpflJGtAKlq3NgWvvNC48yaOtWSy+gvjmbNLMkYMSJzSTLYDMWrVxfdPRV29tkwfbonxuXZkiU2mV+87qmwnj3hoYfgzTetQH3ChN33eeIJa/X7wx+KH0+7djb55BNPZPZ/w8WUTIIzU0SeEpGjRKR3aEbjmekOzCUpmVmMow0aZC05114LL75o67b873+W+DzwQHLFeMlatMgm6StJggP2eP+iypydO612qkuccrsLLrAvl3HjMhfTO+9Yy02/fsntf8YZ1m3gNRHlV7j+Jpmamauvtvfrjh1WL3P++bBypW1bsAAeecQGYrRqVbKYrrrKPvd8TbTAJZPgXABMB64G/gzMCN3nskFxEhwRG0l1wAF2ltuvH1x2Gdx6qyU9OTmlN5Q8Vh1HcXTpArNmpTZhlyu+hQsTj3zr399a1jJZbPzOOza6pX795PavX99mtn3xRRv55cqfL7+0taDC82UV5dhjLXG//np7X7RtC5dfbq0uVarAv/9d8pj697ck6VEvVQ1aMsPEt6jqQ6p6SujykKr6QP9soFpYg5Oqxo1tSOT48dZc+8svhSt+16xpZzulIVxg3KlTyY4TLiqdPLnkMbmizZpl1wccEHt7tWpWi/P225lZRmP+fEu6Ew0Pj+Wyy2z03YsvpicuF6yJE21kX7VqyT+mZk3417/gu+8sYR4xwuoD33svfqFyKipXhiuusBFZPjAiUMkstjlfROZFXzIRnCvC2rVW0FacBAegalU7c+ndG/bZx263aQN//aut1lwao2SmTrVhxcUtMA477DD74IjVd+5KX1EJDlg31fbt8NJL6Y/ntdfsevDg1B535JHQsaN1P3ixcfkSnpSvqPqbeDp0sAR940ar1SrO0PB4LrzQEilvxQlUMl1U3YDuocsR2BDxJCYUcGmX7DINqTrnHOvGKulkbqVRYBxWuzZ062aJl0u/WbNsNuBE762cHPubZGLEyMiR1oqX6hm2iLVGTpvmyXF5M2WK1dOUZmJSWurXtxFbL73kQ8YDlEwX1eqIy2JVfRg4OgOxuaKkMslfKpo1sz7tjz4q2XHy8qyIrzQSHLAJ577+2mYLdek1a5a13hQ1/cAFF1jXUTqb4ufOtUT59NOL9/izzrIu2YcfLt24XLAmTrTrnj2DjSOeK6+0bv9hw4KOpMJKpouqS8Slm4hcBtTJQGyuKOlKcMBGWU2aZDPZFldpFRiH9e5tXSKTJpXO8Vx8s2fbGj5FOfNMG6mUzlac55+3RCvV7qmwGjWskHT06MJZtV3Z98UXVgTfuHHQkcTWrh0cd5wPGQ9QMl1UD0Rc7ga6AEmdSonI8SLys4jMEZF/xNheXUReDW2fLCItQ/cfKyJTReTH0LW3GMWSzgSnTx9LJkpS1Dt1qs0rUdIC47BevbwOJxMKCqz1rUWLovdt0KBwpFI6FhncsQOefNJG+iUTTzx//rN1ud16a+nF5oJTUGAtOMVZMyqTLr7YlgwJtza5jEomwbkoPKuxqh6rqpcA24p6kIhUxpZ5OAFoD5wpItHLSV8E5KtqG+Ah4N7Q/auAk1U1BzgPeD65l1PBLF9uX/jFmVa8KOFRS1OmFP8Y4QLjmjVLJ6a6da01yBOc9FqxwhKLffZJbv8LL7SRSqNHl34sr75q8+1cdlnJjtOwIfzlLzbR29dfl05sLjjTp9uK9tme4PTrZyO83n476EgqpGQSnFFJ3hetBzBHVeep6jbgFWBg1D4DgecijtlXRERVv1XVJaH7pwM1RKR6Es9ZsaxYAU2aWCtJaWvYEFq3Ln6CU5oFxpH69LFWJa/DSZ+8PLvee+/k9j/6aEuGSntOnPXr4eabrQXw5JNLfrxrroHmzW1F9O3bS348F5wvvrDrI44INo6i1KkDfftaguOj+DIu0WribUXkVKCeiPw+4nI+UCOJY+8FLIq4nRe6L+Y+qroDWAdEN0ecCnyrqhlc/rqMSHWZhlR17178BGfxYkvA0pHgbN/uTb7plGqCU7myzQr7wQdWEBzLzp3w4Yfw2GM2OVtRH/bffWdfDAsX2lDb0kji69aFoUOtKNq7qsq2Tz+1ZLU05q1JtwEDYN68YBanreASfWocCPQH6gMnR1y6ABcncexYwy+iP9US7iMiHbBuq0tjPoHIJSKSKyK5K8NTblckxZnFOBU9etiU48uWpf7Y0i4wDuvVy2Yc/fjj0j2uK7QodF6SbIID1oVUpUrseT8WL7YRcMcdZ9PY9+pla1nFSoY2bLB5mLp1s+Rm5Eiby6a0nHKK1UXcfXfR3Qaq8Kc/2UrV991XejG4ktmxw5Lpfv2SX2Q4SMcea9fjxwcbRwWUaDXxt1X1AqC/ql4QcblKVb9M4th5QGQn/t7Aknj7iEgVoB6wJnR7b+BN4FxVjXlaqKrDQqucd2vSpEkSIZUz6U5wune369zc1B8bLjDuXMrrstaubYnXJ5+U7nFdobw8qxtIZXRK8+YwZIiNpopMiMeNs/fAt9/C8OF27IcespFwOTlwzz1WS1FQYJP5tW8PDz4If/wj/PRTyVZ2jufRRy2BOvdcmDEj/n7PPmsjYPbYA/7+d/jqq9KPxaXuq69sktMTTww6kuTst58VyHuCk3GJuqiuC/14log8Gn1J4thTgP1FpJWIVAOGANFViKOxImKAwcDHqqoiUh94B7heVb0vIhbV9Cc4nTvbGVJxFrmcOtWGSZZWgXGkPn0s6Vq/vvSP7SwJ2Wuv1LuFbrzR5v245BJrffnb3+D4421epdxcK0beay8b0TRjhrXoXH+9JVL169s8N/XrWxfWf/9rI7TSoUYNeP11S1xOOqlwNGKkZcusJemII+CHHywun5U2O4weba2F4ZaRbCdi3a2ffGJdtS5jEn2ChVcMzwWmxrgkFKqpuQL4IHSskao6XUTuEJEBod2GA41EZA5wDRAeSn4F0Aa4WUS+C13SWGxSBm3YYFOVpzPBqV0bDjww9QQnXQXGYX362AfF55+n5/gVXV5eat1TYQceaKvRjxljtRH332/JzuTJtqhhpL32KhzR9Pe/26yvr71m77VMTNy27772Rbl8OQwcuPtinFdfbVP4P/mk/R+cdhqMHevFyUHbudOmJDjxRKhXL+hokte3r7U6+dpUGVUl3gZVHRO6fi7ePkVR1XeBd6PuuyXi5y3AaTEedydwZ3Gft0IIT/+dziJjsFW8P/sstccsWWJfHOlKcA47zLpQPvmk7DRTlyV5eXDIIcV77BVXWNfTd99ZItqxY/x9RawbNNwVmmk9etiX5amnWnfVq69aq9Wbb1rtz513WtIG9j578klrXerdO5h4nSWZS5ZYQlyWHB2aym38eOsedRmRqItqjIiMjnfJZJAuhnTdu+OOAAAgAElEQVRO8hepa1f7wktlPZXwyKt0/SPvsYed5XuhcelTtb93snPgxNK7t7WAJEpussUpp1hL06hRVgD97rtw0UX2vv/b3wr3O/poS8hSTfZd6dm5E26/3WpaSmPagExq1swW9/Q6nIyK24ID3J+xKFzqMpXgdOli1998Y/UUyfjqK1uZPPzYdDjySLjrLutGqFUrfc9T0axaZXU0xemiKqv+8hdYsMCGsD/+uBWEjhxprYRhdetaN5tPEhiMggKr8fr2W1vAsmrVoCNK3bHHWm3Zpk3pqU10u0k0iurT8AWYBORjI5wmhe5zQcpUgnPwwXadSh3OV1/Z42okM11SMR1yiH3oFacA2sVXnCHiZZ2IFRBPnmzFxz/+aK0E0cLzQvmEbZm1YIGtjXfvvVaoPmRI0BEVz0kn2XImJV3E2CUtmcU2TwLmAo8CQ4E5InJCugNzRQgnOOkeHl+vHrRpUzivTVF27LARM4cemt64wnUbfkZdulKd5K886dEDfv97m302lu7d7f9u0aLY213pUoWnnrKartxc+/mpp8rG3DexHHmkvbfGjg06kgojURdV2ANAH1WdAyAirbEh3O+lMzBXhOXLbQ2qTDTVdu2a/KKb06dbt1G6E5ymTa0rwROc0lWRE5yihBeNnT7dRmG59FmwwFaAf/99K1Z/5pmSLbaaDapVs8kJx4yxeqLKlYOOqNxLZqKLFeHkJmQekELFqUuLFSvSP4IqrEsX+8BZvbrofcOToaU7wYGSLSXhYsvLszlGMvXeKkvah9YKTjQ5oCuZHTtsoscOHWwaiMcesy6dsp7chA0ZYnMsveftA5mQTIIzXUTeFZHzReQ8YAwwJbw2VZrjc/Gke5K/SOHh3snM4fDFF/blmIk1Ynr0gPnzoSIu05EueXk2K7GfXe6uUSP7n5s+PehIyqf8fFvS469/tVFrM2bYtAPpWEw4KAMG2NIfDz7otVwZkMw7pwawHOgNHAWsBBpi61L1T1tkLrFMJjjhQuOi6nBUbW6aPn0y00/eo4ddeytO6SnuJH8VRfv23oKTDqpwzjn2v/ziizYJY3nsBqxa1Wbv/uQTWwpE1aa7OPVUW6PtuWJPO+diKLIGJ7Qelcs2mUxwGjaEVq2KHrE0e7YtrNinT2bi6tLFEqmpU33Cv9KSl5fe4f1lXfv2MGKEfTGV1WLXbPT22/DOO/Dww3DWWUFHk16XX26j9S680Fqr8vNtuZJmzeD882HpUvjHP4o8jCtakQmOiLQCrgRaRu6vqgPiPcal2ebN8Ouv9g+RKV26FN2CE14AMzxrZ7rVqQOtW8P332fm+co7VRshNMD/tePq0MHWQFu82Fu6StO999pJ1J/+FHQk6VeliiVzjz1mJ4V9+sDgwda6c/bZcMMNNg1Gpk4Uy7FkRlG9ha0ZNQYoSG84LinhIeKZTHC6drWzjvz8+IsgfvyxrTHUpk3m4urUyROc0rJmjc3T4V/c8bVrZ9czZvjvqbTMmGGDEx56yL78K4JatWK30jz1lA2Jv/RSm4+pevXMx1aOJFODs0VVH1XVT6Im/3NBWbbMrjOZ4PTqZdcTJsTevn07jBtns3Vmsum+UyeYO9cWH3Ul40PEixZOcH7+Odg4ypPXXrPPjDPOCDqS4NWqBUOHWsuOr15fYskkOI+IyK0i0lNEuoQvaY/MxRdEgnPoobaq8ocfxt7++ee2Wu7AgZmLCSzBUbWzHVcynuAUrWlTqF8fZs4MOpLyY9QoOPxwG13kbK6cfv3g3/+2OcVcsSWT4OQAFwP3YJP+PYCvUxWsIBKcatWsT/iDD2JvHz3amlOPPTZzMUHh5GveTVVynuAUTcTWpPrpp6AjKR+WLIFp07zuK9rNN9v0F08+GXQkZVoyCc4pwH6q2ltV+4QuGaoidTEtW2YftOlepiHaiSfCvHnwww+73r9zJ7zxhq0Xk+mFL/fd15aT8ASn5PLybM6RTCbOZZEnOKXn01C1gxfU7qpXL2s1/+9/fb6cEkgmwfkeqJ/uQFwKli2z5CbTBXmDB9tzPv/8rvePH2+jb849N7PxgCV6HTt6glMawpP8VZRCz+Jq29aG8q5bF3QkZd+ECbZSe+fOQUeSfS6+2Gq9vvwy6EjKrGQSnN8BP4nIByIyOnxJd2AugWXLgjnLbtzYWnFeeMFG24QNG2Zz5WS6/iasUydrVSrwQX4lsmiRjYJzibVta9deaFxyEybYIpQ+c/buTj/d6h6ffjroSMqsZBKcW7Fuqn9RWIPzQDqDckUIKsEBuOoqe/7hw+32tGnWPXXJJcENaezUyYrx5s8P5vnLi0WLYJ99go4i+4UTHO+mKpmlS2HWLFuewe2udm1b3f6NN2Dr1qCjKZOKTHAih4b7MPEsEWSCc/TR9oF0ww227tQf/2hNzNdeG0w8YF1U4COpSiI8yZ8nOEXbbz+blM0TnJKZPNmuDzss2Diy2ZAhNjp13LigIymTikxwRORQEZkiIhtEZJuI7BSRXzMRnItBNdgERwSeecZaa444wtaOGT7cFiIMSocOFld08bNL3po1NkO2JzhFq1rVJrP0BKdkcnOt3svrb+I75hjr/n/11aAjKZOSqSYcCgwBXgO6AecC+6czKJfAunXWXBnkSJeWLa2od9Qom1I8vOhlUGrVsrNqb8EpvkWL7NoTnOT4SKqSmzIFDjoI9tgj6EiyV9WqthDnyy/bCYj/rlKS1Dr0qjoHqKyqO1X1GWxVcReE8Bw4mVpoM54994Qrrww+uQnLyfEEpyQ8wUlN27Y22+z27UFHUjapWgtOt25BR5L9hgyxmdrffTfoSMqcZBKcTSJSDfhORO4Tkb8AGZ7sxP0miEn+yoKOHe0LZ/PmoCMpm8KT/HmCk5y2bWHHDpsXyqVu/nzrFu3ePehIsl/v3nZC+8orQUdS5iST4JwT2u8KYCOwD3BqOoNyCSxZYtee4OwqJ8eGic+YEXQkZdOiRVYPEXTLYFnhI6lKZsoUu/YWnKJVrgynnQZjx8KvXv6aimRGUS1U1S2q+quq3q6q14S6rFwQ/Ew7tpwcu/ZuquIJz4Hj85Ek58AD7doTnOLJzbWBCgcdFHQkZcMf/mBzj40aFXQkZUpSNTguiyxaZEsT1KkTdCTZpU0bqFHDE5zi8iHiqalXz+rQPMEpnilTbP6qatWCjqRsOOQQOOAAeO65oCMpUzzBKWvy8vyLKJbKlW24uA8VLx5PcFLXrp0nOMVRUABTp3r9TSpE4Lzz4LPPfELTFKQ1wRGR40XkZxGZIyL/iLG9uoi8Gto+WURahu5vJCKfhObeGZrOGMucRYt8ted4fCRV8RQUeOJcHOGh4r4YYmp+/tlGBXmCk5qzz7ZE59lng46kzIib4IjI4SJybsTtUSLycehS5GriIlIZeBw4AWgPnCki7aN2uwjIV9U2wEPAvaH7twA3AwFOj5ul/Ew7vpwcWL4cVqwIOpKyZeVK2LbN31epatvWZpn191tqcnPt2guMU7PvvrYW4H//u+tagC6uRC04twO5EbcPBP4G3AZcl8SxewBzVHWeqm4DXgGiV2McCIQ7FUcBfUVEVHWjqn6BJToubOtW+zD1FpzYvNC4eMJDnVu2DDSMMic8kmrmzGDjKGumTLHJOcO/P5e8v/7VvgNeeCHoSMqERAlOXVWNHHM7W1WnqupnQDIVrnsBiyJu54Xui7mPqu4A1gFJz/kvIpeISK6I5K5cuTLZh5VdixfbtZ9px+ZrUhVPOMFp3TrYOMoaHypePLm50KWLj9grjqOOgoMPhvvvt3mYXEKJEpz6kTdU9fcRN5OZLENi3BfdWZ3MPnGp6jBV7aaq3Zo0aZLsw8qu8BBxb8GJ7Xe/gyZNPMFJ1dy5dt2qVbBxlDV7720rPk+fHnQkZceOHfDtt949VVwicMstVsf01FNBR5P1EiU4P4nISdF3ikh/4Ockjp2HTQoYtjewJN4+IlIFqAesSeLYFZNPp180LzRO3bx5NgdOjRpBR1K2iNhCkd9+G3QkZceMGVY/4glO8Q0caLMb33wzrF4ddDRZLVGC8xfgQRF5RkSuDF2eBR4MbSvKFGB/EWkVWuphCDA6ap/RwHmhnwcDH6v6kIS4wl0JLVoEG0c2y8mBadNg586gIyk75s717qni6tIFvvvO32/J8gLjkhOBRx6xWY0vushH8SUQN8EJzVbcEfgcaBm6fAZ0VNVZRR04VFNzBfABMBMYqarTReQOERkQ2m040EhE5gDXAL8NJReRBVgydb6I5MUYgVXxzJsHzZv7irKJdOxo61H5GkHJmzvXVmN3qevSBTZutHXQXNFyc6FuXZuY0xVfp05wzz3w9tvw6KNBR5O1qiTaqKpbgaeLe3BVfRd4N+q+WyJ+3gKcFuexLYv7vOWWn2kXLXIk1f77F73/li2Fi9ideaZNH1+RbNoES5f6+6q4unSx62++8VFBycjNha5doZLPMVtiV18NEybAtdfanEKHHRZ0RFkn0Tw460Xk1xiX9SLiK34FwROconXoYE24ycxovGUL9OsHF1xgl4EDK15XQ7ily1twiqddO6td+uaboCPJftu2wfffe/dUaalUyZZu2HdfW4zT52PaTaI0ejwwA7gTyFHVuqFLHVWtm5nw3G82b7aVxD3BSaxmTTuTDq9WnMi//mVTnz/9NAwdCh98AA8/nP4Ys0l49fV27YKNo6yqUsW6RT3BKdrUqZbkHHJI0JGUH/Xrw+uvw5o11gJd0U7QipCoBmcQ0A9YCQwTkU9F5P9EpGHGonOFfK6S5B12GHz5pS1BEM+SJXDvvXDWWdZ683//ByedBLffbh8WFcX06XYm6N0rxde1q3W9+JdLYp9/bteHHx5sHOVN587w+OPw8ccwbFjQ0WSVhB2hqrpOVZ/Bllv4L3AHcH4G4nLRwkWMnuAU7fDDbQr9RDPMPvigfSH98592WwTuvhvWr7dtFcX06fae8sL14uvVy943vtBrYp9/biti/y6ZadRcSi64wCYBvOmminWCVoSECY6IHCYijwHfAL2AU1S1An36Z5HwZGLtfTBZkXr1suuJE2NvX73a1nM588xda09ycqwv+5FHKs6HxPTpVrfkiu+II+w63ELhdldQYP+P4d+VK13hoeP5+Xai5oDERcYLgCeAxcAl2GiqjSLSRUS6ZCY895tp02ym2dq1g44k+7VpA02bWn1NLI89ZkN7/7HbAvc2S+iGDfDQQ+mNMRts3Wotg57glMy++9rcVJ7gxPfDD/bl6wlO+nTsCOeea59vixYVvX8FkKgFZwGQj9Xh3As8EHG5P+2RuV1Nm+ZfRMkSsdFR77+/+3otGzfaB8DAgbF/nwcdBIMH29wS+fmZiTcoP/xg3XSdOgUdSdl3xBGW4Pika7G9955dH3dcsHGUd3fcYde33hpsHFkiUZHxUaraJ87l6EwGWeFt22YL+h10UNCRlB0DBlhX1KRJu94/fLh1P113XfzH3nyzzRJa3kdUTZ5s1z6qpeSOPBKWL/eVxeN5911bJHLPPYOOpHzbd1/4059s+Pi0aUFHE7hEXVTXRfx8WtS2f6UzKBdl1ixrifAEJ3n9+kG1avDaa4X3bdkCDzxgRciJJsXq2BFOOcX6tNeuTX+sQZk8GZo187XNSsPxx9v12LHBxpGNVq60UY0nnhh0JBXDDTdYKcMNNwQdSeASdVENifj5+qhtx6chFhdP+EzbJ8hKXp061tX07LPWGgOWsPzyC9x2W9GPv+UWWLfO5sopryZPttYbkaAjKfv22ceG63qCs7tXXrEi4yFDit7XlVyjRlZfOGZMha8LS5TgSJyfY9126fTVV9CggQ2xdMn7y19s+O7111tX1W23WddV375FP7ZzZ7jwQis2Lo+rky9dagXGPr176enf30YKVZQReMl67jnrnvIW6My5+mrrDvz73yt0XViiBEfj/BzrtkunSZPg0EP9TDtV3brBVVfBE0/YF3mzZqlNhHXffVCvniU6W7emL87i2LSpZN1n48bZtRd9lp4BA6yl4u23g44ke3z2mc1gfNFFQUdSsdSsaZOWTpoEzzwTTAwFBfDhh9ZV9sc/2gnn0KHw9ddWV5oBonGyOxHZCWzEWmv2ADaFNwE1VLVqRiJMUrdu3TQ3NzfoMHa1aJF9kSxcaF+UxxyT+oiV5cvti/muu7xPtThU4cUXYfFi+5Bt3Di1x7/xBpx6qs10/PjjRe8/dy58950lRPvvb2euVRKuaZu8X36Bl1+Gl14qnFTugAPgr3+1D5BUFjA86yyb+XTJEl/4sLSo2ozQzZrBp58GHU3RfvjBlinJzYXKle2E4PTToUeP0jmZUrXPvGnTYMECn0wy03bssFrEzz+Ht97KbA3UV1/ZieHMmfb516SJdflvCqURNWpA794waJCdGDRvntLhRWSqqhZds6Gq5eLStWtXzRp5eaqXXKJaubKq/ZsXXo4+WvXHH5M/1ogR9ripU9MXr0vs2mvtb3DHHaoFBbtv37RJdfhw1R49dv9777mn6g03qM6evetjdu5UXbBAdcIE1XHjVL/8UvWnn1RXrlTdvt322b5ddeZM1SeeUD3yyMJj9uxpsdx3n/0Mtn3ZsuRez8aNqrVrq150Ucl+L253//qX/T2i/97Z5KefVE8/3eKsXl21d2/VXr1Uq1Wz+9q1U33gAXsvlsRzz9nxHn20VMJ2xbBmjerBB6uKqF54oepXX9lnTzq98IK9r1q1Un3pJfu8UbXPzkWLVF97TfXqq1Vbty78TDv0UPs8S/L/BsjVJPKCwBOT0rpkRYKzapXq3/6mWqOGatWqqldcoTp9uv1hly+3D42GDVWrVFF9+OHYX5bRzjhDtWnT9L8pXXw7dqiee679uwwapJqbq7puneq336reeKNq48a2rUMH1Ycesu0zZtg/94knqlaqZNsPPtgef+SRqnXr7p4MRV5q1971dtu2qv/8p+rcubvGVlCg+swzqnvsodq8ueqkSUW/npdesmN+8kk6flsVW16e/X9fcUXJjlNQYCdC996retRR9hnQqpXqCSfYl8Pdd9vlH/9QvfRSS1hOO031T39S/c9/VH/4YffPjOXLVa+80uKrXVv1ppvsCzBs7VpL1MNJc7VqqkOGqH78cXKfVZE++cQ+Bw8/3P5/XHA2bFD9y18s6QD7DjrmGNXrrlN9+WVLeEvjb7Rzp53MgSXNq1Yl3r+gQHXaNNU771Tt2rXwsy4nR/WWW1S/+y7u+y7ZBCduF1VZE2gX1bRpMGIE/Oc/NpHc2Wdb/2erVrvvu3q1dZW8/bYtFTB8ePym23XrrLn7ggusjsQFp6DAhpjfcosNNw8TgZNPtv7l3r1jN+0vXmzvj48+ghUrrLsyJ8e6r/bbz5pr16+3iQVXr7bLunW2UvA++9iw9v33T9xt8P33NrQ9L88WEf3zn2Pvr2pdEPn5Nv2Ad0+VvksvtdF7s2fbvCTxqNrfeeVKWLXK/u5Ll9qQ6vHjC2ej7djR/mYbN1qT/+zZ9jNY83+DBnYRgWXL7JgADRvae7JFC+s6/eAD67a45BL7fGraNH5s06bBk0/a+3btWpsd/OKL4bzzEq8ltXmzFebfequtcfb559Y94YKXn2/zEX36qdVF/fgjbN9u22rXtq6iiy+O/zmWyIYNNovym2/aMYYOtWk6UrFwoXWlvfmmvW8KCqBuXeuGP/BAG/jRrRt064bUqZNUF1X5TXC2brX6l2nTCj8MYmnY0P55O3SwhCTWB35BgdU/TJ9uE+4tWGB/jPBl3Tp73Gmn2SRxRc04XFAA99xjC6N162bJTqwJsJ54wiZt+vpr6N49qd+DS7M1a+xDYulS+5sdc4wlodkgP9/6vd96y5KuYcN2j23UKHufDhtmH0Su9C1caGvGHXaYJRWRnyk7d1rh5ZNP2rZYn02NGtmXzAkn2Pw6e++9+z6bNtlxq1ff9ctIFebPty+ITz+1It/ly63G4fjjrZbswAOTfy2bN8Prr1u8n31mCVXfvtCnj312NWtmz7loEXzyCTz/vCVZp54KTz1lSbrLTtu2wYwZVjP45Zc2Z9jatXbyddVV8Ic/JFc3NW+enVxNm2YngVdfXfIarpUrbZj7t9/aidjMmYUJf+XKyM6dFTjB+eILOOccS0TACuhiUbVkI6xmTUtO2reHWrUscQn/cjdsKNyvbl1o2dLOjFq2tMcMGpT6KrlvvWVvooYNYfRoO6MP27zZztr32cfefD6CyiVD1c6err3WPpzuuMNaDGvVsmLSfv0skZ80Capm1TiB8mXYMGvJOf10m0tpyxZLFJ5+2hKgxo0t0Qyvm9aokV2aNLHPlWxsWfvpJ2txfu+9wsV/I1WubIWs11xjK1u7smXzZpuz6JFHrEW4YUNr7bv88tgtkQUFNkLr2mvt++mllwonvEyHlSthyhSYNAm5884KmuB8+aWdVe+1lzWV9uljH+6xqNpZ7+zZln2GLzNmWAtQ7dqFzWM5OTaPQ7t21hxcWr77rnBZgeHD4YwzLK6LLrJm7gkT7GzOuVTMmmWtfx99ZO/jFi0sUd97bxs91bp10BGWb6rWVXjTTdZqE9a3r7WcDRpkrS9l1YoV9n5atswSmyZN7AStbt2gI3MlpWotgI8+at1F4W7tfv3s+1DVvidfe81ab3r1sq7M/fbLWIjJjqIqXwnO+PGWiFSvbhNuJepjzibLltkH3uTJ9iYJNzPffHPh4mnOpUrVWjNfftnqgDp1sqbnVIfKu+KbO9fqaapXh6OP9mUxXNmycKF1O44ZY6USYVWrWm3g5ZfbjPEZ7mGomAlO9+7WV/zll5ZxliXbt1sTXzhjPussa81xzjnngrZpk9WiVqpkiXqA8xpVvAQnJ0dzZ860jPKxx4IOxznnnHNpkGyCk4WVbMW0eLENt73ppqAjcc4551zAyk+Ck59vc3+kOpLJOeecc+VO+UlwqlSx4WrOOeecq/DKT4KTk+OTSjnnnHMOSHOCIyLHi8jPIjJHRP4RY3t1EXk1tH2yiLSM2HZ96P6fRaRfkU+WjRNjOeeccy4QacsKRKQy8DhwAtAeOFNE2kftdhGQr6ptgIeAe0OPbQ8MAToAxwNPhI7nnHPOOVekdDZ79ADmqOo8Vd0GvAIMjNpnIPBc6OdRQF8RkdD9r6jqVlWdD8wJHc8555xzrkjpTHD2AhZF3M4L3RdzH1XdAawDGiX5WETkEhHJFZHclStXlmLozjnnnCvLqqTx2LHmbo6eVTDePsk8FlUdBgwDEJH1IvJzqkE6l4TGwKqgg3Dllr+/XLqU1/dWi2R2SmeCkwdELryyN7Akzj55IlIFqAesSfKx0X5OZmZD51IlIrn+3nLp4u8vly4V/b2Vzi6qKcD+ItJKRKphRcOjo/YZDZwX+nkw8LHa2hGjgSGhUVatgP2Br3HOOeecS0LaWnBUdYeIXAF8AFQGnlbV6SJyB5CrqqOB4cDzIjIHa7kZEnrsdBEZCcwAdgB/UtWd6YrVOeecc+VLuVlsU0QuCdXkOFeq/L3l0snfXy5dKvp7q9wkOM4555xzYT79r3POOefKHU9wnHPOOVfueILjnHPOuXLHExznnHPOlTue4DjnnHOu3PEExznnnHPljic4zjnnnCt3PMFxzjnnXLnjCY5zzjnnyh1PcJxzzjlX7niC45xzzrlyxxMc51zKROQ2EXmhmI/tJSKzRWSDiAxKYv+WIqIiUqU4z1cWhH4X+2XgeRaIyDHpfh7nsoEnOM5liVS+fERkgoj8Md0xpckdwFBVra2qb0VvTOeXsIgcJSIFoYQi8tIztH1CKJnqFPW4t0L3HxVx3/4i8oqIrBSRX0NJ22MisneqcYV+F/NK/AKdc7/xBMc5t4sMtJS0AKan+TkSWRJKKCIvkyK2zwLODd8QkUbAocDKiPvaAJOBJcDBqloX6AXMBQ7PxItwziXmCY5zWUhEzheRL0TkfhHJF5H5InJCaNtdwBHA0FDrw9DQ/W1F5EMRWSMiP4vI6RHHayQiY0ItDVNE5E4R+SJiu4rIn0RkNjA7dN8jIrIo9JipInJECvFfLCJzQrGMFpHmofvnAvsBY0KxV4963PPAvhHbr4vY/AcR+UVEVonIjRGPqSQi/xCRuSKyWkRGikjDZGON4UXgDBGpHLp9JvAmsC1in9uAiap6jarmAajqClV9WFVfifM7aSMin4rIutBreDVim4aSpmT/VpeFWozyReRxEZHQttYi8nHo97BKRF4Ukfol+F04V2Z5guNc9joE+BloDNwHDBcRUdUbgc+BK0KtD1eISC3gQ+AloCn2pfyEiHQIHetxYCPQDDgvdIk2KPSc7UO3pwCdgYah474mIjWKClpEjgbuBk4H9gQWAq8AqGpr4Bfg5FDsWyMfq6rnRG2/L2Lz4cCBQF/gFhFpF7r/qlDsvYHmQH7o9RbXEmAGcFzo9rnAiKh9jgFeT/G4/wTGAQ2AvYHH4uyXzN+qP9Ad6IT9nvuF7hfsd98caAfsgyVjzlU4nuA4l70WquqTqroTeA5LFn4XZ9/+wAJVfUZVd6jqN9gX8OBQS8SpwK2quklVZ4SOF+1uVV2jqpsBVPUFVV0dOt4DQHUswSjKH4CnVfWbUAJzPdBTRFom/cpju11VN6vq98D32Jc7wKXAjaqaF3q+27DXHa+rrbmIrI261IraZwRwrogcCNSP6sICSzqXhW+IyBWh42wQkSfjPO92rHuuuapuUdUvondI4W91j6quVdVfgE+wRBRVnaOqH6rqVlVdCTyIJX7OVTie4DiXvX77AlXVTaEfa8fZtwVwSOSXNpZoNAOaAFWARRH7L4pxjF3uE5G/isjMUJfKWqAe9sVelOZYq0049g3AamCvJB6byLKInzdR+LtoAbwZ8bpnAjuJnwwuUdX6UZeNUfu8ARwNXAk8H+MYq7GEEwBVHaqq9YGHgapxnvc6rIXlaxGZLvW4i5QAACAASURBVCIXxtgn2b9VzN+FiDQNFT4vFpFfgRdI7m/mXLnjCY5zZZNG3V4EfBr1pV1bVS/HimN3YN0iYfskOmao3ubvWPdHg9CX9zrsC7ooS7CkI3ysWkAjYHESj90ljiQtAk6Ieu01VDXZ59s9AEso3wMuJ3aCMx74fYrHXKaqF6tqc6zV6Ylw3U2EZP9W8dyN/f46hgqfzya5v5lz5Y4nOM6VTcuxYt2wscABInKOiFQNXbqLSLtQF9cbwG0iUlNE2hIxSiiOOtgX7UqgiojcAtRNMraXgAtEpHOoiPhfwGRVXVDM11aU/wJ3iUgLABFpIiIDU3h8PDcAvePEfRtwhIg8KCJ7hZ63MVb3EpOInCaFQ8jzsURkZ+Q+xfxbRaoDbADWhuL6WwqPda5c8QTHubLpEazOJF9EHlXV9VhR7BCsBWUZcC9WNwNwBdbFtAxrkXgZ2LrbUQt9gLVgzMK6m7YQu6tkN6o6HrgZqwFaCrQOxZWsu4GbQl1O1yax/yPAaGCciKwHvsKKpeNpLrvPg3NqjNexJFadTGjbLGzo+N7A96HnnYj97m+O87zdgckisiEU79WqOj/Gfqn+rSLdDnTBWtvewZIl5yokUU21Ndg5V9aJyL1AM1WNNULHZRH/WzlXPN6C41wFIDZHTkcxPYCLsLldXJbxv5VzpaPcru3inNtFHayrozmwAngAeDvQiFw8/rdyrhR4F5Vzzjnnyh3vonLOOedcuVNuuqgaN26sLVu2DDoM55xzzqXR1KlTV6lqk6L2KzcJTsuWLcnNzQ06DOecc86lkYgsLHov76JyzjnnXDnkCY5zzjnnyp20JjgicryI/Cwic0TkHzG2VxeRV0PbJ0evNiwi+4ZmGU1mNlPnnHPOOSCNNTgiUhl4HDgWyAOmiMhoVZ0RsdtFQL6qthGRIdjU8mdEbH8Imy7eOeecc+Xc2g2b+WnRCuYsXcn8FStYtGYFy35dzaqNa1i7dQ3rd6xJ+ljpLDLuAcxR1XkAIvIKMBCITHAGYovWAYwChoqIqKqKyCBgHrAxjTE655xzLk02bN7Gz4tWMWfpCuYtX8Eva1awdO1KVmxcweotK1i3YyUbWcGWyivYUX0lVNsQ+0CVKiOVGlJVGiT93OlMcPZi18X58th9Abzf9lHVHSKyDmgkIpuBv2OtP9495ZxzzgVk67ad/LJiLb+sXEveqnyWrs1n+bp8Vm5Yy+qN+azdks+6rfls2JnPpoK1bCGfbZXz2V51NdRYG/ugUoVK0pRqNKGmNqWRtqFBQROaVGpKszpN2btBE1o0aUqbPZvSpnkj9mlSl0qVxB76oCQVdzoTnFgRRE+bHG+f24GHVHWDSPwXIiKXAJcA7LvvvsUM0znnnCv/Nmzazqy81SxcsYa81WtYkr+G5b+uYeWGNazevPq3LqBNBWvYLGvYVnkNO6utgeq/Jj6wVEWkAVW0AdW1AXvQhEYF+1NfG9FYLGHZq34TWjZpyn6/a8qBezehxe/q/5awpEs6E5w8YJ+I23sDS+LskyciVYB6wBqspWewiNwH1AcKRGSLqg6NfLCqDgOGAXTr1s3XnHDOOVchLVz2KxNnzOPbBfOYsXQui35dRP625azX5WyutJzt1ZejNfLjH+C3LqCGVJeG1GFPatOButqA+jSgwR4NaFK7AU3rNKBZ/fo0b9iAfZo0oEXTBjSqu0fak5XiSGeCMwXYX0RaAYuBIcBZUfuMBs4DJgGDgY/VFsc6IryDiNwGbIhObsqKypUrk5OTw44dO2jVqhXPP/889evXT/k4b731FgcccADt27cv9RiPOuoo7r//frp161bqx3bOOVd65i5ey9gpPzJx9o9MW/kjedt+ZEP1n9Gaq3bZTyrXpWqlZuxR0JSm0oEGHE0T+R1N6zRmz3qN2LN+Q/Zu1JB9GjekVbOG7NmgblYmKSWRtgQnVFNzBfABUBl4WlWni8gdQK6qjgaGA8+LyBys5WZIuuIJyh577MF3330HwHnnncfjjz/OjTfemPJx3nrrLfr375+WBMc551z22blTGfX5NN6c8iWTl35JnnzJjrpzftsuVepRZ3sObTmF/aq1pv2erenaqjWHtd+PfZrUCzDy7JDWpRpU9V3g3aj7bon4eQtwWhHHuC0twQWgZ8+e/PDDD7/d/ve//83IkSPZunUrp5xyCrfffjsAI0aM4P7770dE6NixI5dffjmjR4/m008/5c477+T111/n448/ZtiwYWzbto02bdrw/PPPU7NmTc4//3zq1q1Lbm4uy5Yt47777mPw4MEUFBRwxRVX8Omnn9KqVSsKCgq48MILGTx48C4xjhs3jltvvZWtW7fSunVrnnnmGWrXrp3R35NzzlVUK/I38ciYj3n9xzHMlrEU1LLKjkpVm9Js+2F0qnoRvdp0pN/BOXRpvXe5a3UpTeVmLaqi/PnPEGpIKTWdO8PDDye3786dOxk/fjwXXXQRYInE7Nmz+frrr1FVBgwYwGeffUajRo246667mDhxIo0bN2bNmjU0bNiQAQMG0L9//98Skvr163PxxRcDcNNNNzF8+HCuvPJKAJYuXcoXX3zBTz/9xIABAxg8eDBvvPEGCxYs4Mcff2TFihW0a9eOCy+8cJcYV61axZ133slHH31ErVq1uPfee3nwwQe55ZZbcM45lx5fTV/MI++9w/hFY1hZZzxU3QzVarP3luPo1/gkzuvdm17t9/NkJkUVJsEJyubNm+ncuTMLFiyga9euHHvssYAlOOPGjePggw8GYMOGDcyePZvvv/+ewYMH07hxYwAaNmwY87jTpk3jpptuYu3atWzYsIF+/fr9tm3QoEFUqlSJ9u3bs3z5cgC++OILTjvtNCpVqkSzZs3o06fPbsf86quvmDFjBr169QJg27Zt9OzZs/R+Gc4559i+o4ARH37DM1+OZer6MWxp8A0AVaq1pHPBHznzoP7834m9qb1H9YAjLdsqTIKTbEtLaQvX4Kxbt47+/fvz+OOPc9VVV6GqXH/99Vx66aW77P/oo4+SaGh82Pnnn89bb71Fp06dePbZZ5kwYcJv26pXL/ynsJrtwutEVJVjjz2Wl19+OclX55xzLhnbthfw37GTeGrSa0zX1ymonQeVhbqVenJ0tbv5v2NO5sRu7ZP6/HfJ8cU2M6RevXo8+uij3H///Wzfvp1+/frx9NNPs2GDzdq4ePFiVqxYQd++fRk5ciSrV68GYM0am5a6Tp06rF+//rfjrV+/nj333JPt27fz4osvFvn8hx9+OK+//joFBQUsX758l4Qo7NBDD2XixInMmWNFbJs2bWLWrFklfenOOVdhjZ+6gENvuJE9btiXq3/4//buPDzK6uzj+Pee7JA9GcISRFYrYAUaoVaroqJWcanSCi4v7hXrXhe0pYpaRVt3aZVWNqWA+1KtULVqtVYEREVQWWWHycISyJ77/SMjb7QQgi+TSSa/z3XNlWef33AN4eY85znncD5N+hN5tQMY1X4KS3+xiS33vccrN43mpEP6qLjZx1pNC05z0L9/fw4++GBmzJjBueeey+LFi3feAkpNTeXJJ5+kT58+/PrXv+bII48kLi6O/v37M3nyZIYPH87FF1/MQw89xDPPPMPtt9/OoEGD6NKlCwcddNA3ip9dOeOMM3jjjTfo27cvvXr1YtCgQWRkfLOXfTAYZPLkyYwYMYKKigoA7rjjDnr16hWZPxARkRi0o7ya30z9G5MXPkZJ9ixIMNpX/IQz8+9m9Okn0z4rPdoRWwVrzK2LlqCgoMDnzp0b7RjNWmlpKampqRQVFTFw4EDee+892rdvH+1YIiIxYW1oO5f9eSKvFN1PTfoK4nZ05Ki0i7h7+IX8oIdG299XzGyeu+9x4Da14LQiQ4cOZfPmzVRWVjJmzBgVNyIi+8CilUVc8NgDfOB/hJRiMvxH/LL7H7hl+Ckkxuuf2WjRn3wrsqt+NyIi8t2s2lDKOY88wL9qfw9J28jffiq3H3495x3zo2hHE1TgiIiI7JXqaueih55k6obr8bYbyS89lcdOv4MTC/pGO5rUowJHRESkkZ575wvOe/oytuW+SXrNIMYf9QLnHPnDaMeSXVCBIyIisgdbS6s48a5xvBe4A0trwwXtHmXCmIuJC2i0leZqtwWOmX2yu331hNz9mH2YR0REpFl5/NX5XDbrAiqzP6bbjjN59coHOKCTHtJo7hpqwYkDTmxgvwEv7ds4saWoqIhjjqmr/zZs2EBcXBzBYBCAOXPmkJiYGM14fPDBB8yYMYP777//v/bl5+ezcOFCMjMzo5BMRCT6NhTt4OTf/465SXcTlxxk7IEv8NufnxrtWNJIDRU4v3D3rxo62cwu28d5YkpOTg4LwjN83nrrraSmpnLdddd94xh3x90JRKGZc9CgQQwaNKjJ31dEpDmrqq5l1PjpTFozmtrUNXyvfCSzr7+fzrlZ0Y4me2G3/6q6+7vf3mZmWWb2/YaOkT1bunQpffv25dJLL2XAgAGsX7+eSy65hIKCAvr06cNtt92289j8/HxGjx7NwIEDGTRoEMuXLwdg48aNnH766RQUFDBw4ED+85//AHD88cfTr18/+vXrR3p6OtOmTaOsrIyRI0dy0EEHMWDAAN555x0AXn/9dU477TQAQqEQQ4YMYcCAAYwaNapRc1eJiMSSbTsqueDBKaRe14/HN59DSm07Jhz6NovvmqzipgXaYydjM3sLOCV87AIgZGZvu/u1Ec62T1392tUs2LBgn16zX/t+PHDCd5vFc9GiRUyaNIlHH30UgHHjxpGdnU11dTWDBw9m2LBh9O7dG4CsrCzmzJnDxIkTufbaa3nhhRe48sorueGGG/jhD3/IypUrGTp0KAsXLmTWrFlA3S2wiy++mJNPPpmHHnqIxMREPv30Uz777DNOPPFElixZ8o08t9xyC4MHD+bmm2/mxRdf3JlLRCTWrdxUzKi/PMbszQ9T23Y9yXF9uTL/Ce47/yx1Im7BGvMUVYa7bzWzi4BJ7n5LIzsgSwO6d+/OIYccsnN9+vTpPP7441RXV7Nu3ToWLVq0s8AZMWIEAGeffTajR48G6lpfvvjii53nl5SUUFZWRkpKCps2bWLkyJE8++yzpKen8+6773L99dcD0KdPHzp27LhzQs2vvfPOO7z66qsAnHrqqaSlpUXuw4uINANvLljGtTMf4OO4iZCwg+zKIVx3yCRuPOM4AgFNfNnSNabAiTezDsDPgV9HOE/EfNeWlkhp27btzuUlS5bw4IMPMmfOHDIzMznnnHMoLy/fuX9XM8y6+y47KldXV3PmmWdy++237yyQGnu7STPZikisc4cJf5vL2DfGsT7zOUiIp9v2s/jdMdcyfPD393wBaTEa0/Z2GzALWOruH5pZN2DJHs6RvbB161bS0tJIT09n/fr1O28zfW3mzJlAXSvPYYcdBsCxxx7L+PHjdx7zdWfm66+/nkMOOYRhw4bt3HfEEUcwbdo0ABYvXsz69evp0aPHN96j/jEvv/zyHmcnFxFpScrLndGPvkXGL4/n0vmHsKHN6/yo9kbmnbuSZfdOVnETgxoaB6fA3ee6+9PA019vd/flwBlNEa61GDBgAL1796Zv375069ZtZxHztR07djBw4EDMjOnTpwMwfvx4Ro0axaRJk3b223nooYd44IEH6Nu3L7Nnzwbgzjvv5IorruAXv/gFBx10EAkJCUydOvW/Wn7Gjh3LiBEjeOqppxg8eDCdOnVqmg8vIhJBmzc7Vz7yCtPX3kl1+/eJz8jj9PRx/PH8UeRlpkc7nkSQ7e72hZl9BKQC04Hp7r64KYPtrYKCAp87d260Y+xzGo9GRGTvbQxVM+qRp3mx6C5qg5+SXNGFiw+8gXFnnk+bxJRox5P/BzOb5+4Fezputy047t7fzA4AhgPPmlkldcXOjD2NjyMiIhINazZUcNFDU5m94248axlpaQdyfb+pjB46nIS4hGjHkybUYCdjd/8CGAuMNbODqSt23jSzDe5+WEPnyr6xZs2aaEcQEWn2Vqwr5cLxE3ir4l48bR3ZtQX89ofPccVxpxIwPerdGjVqsk0zCwDtgDygLRCKZCgREZHG+GjJRn45eTzv14yHlGLyqgdz55FTOP/IY/RkaCvXYIFjZj8GRgCnAQuBGcA17r6lCbKJiIj8l9paZ8rsj7j9tfGsSHsSEqrIrzyZe44ZzYjDD412PGkmGnqKajWwirqiZqy7b2yyVCIiIt/y3ifrGPv8NN7ePJXKzIWQmsLBtRdy77CrOebgXtGOJ81MQy04h6szsYiIRNNnKwq5/enn+fuqp9ma8wYEakm3HzIs44/cdfaZ7BfMjnZEaaYaKnDOB25t6GQzu9XdGzxGRERkbyxYspFxLz7P7DXPUJLxFgRqSEzpztFJoxlzyv9wVN8Doh1RWoCGCpyLzGxrA/uNuqeqbt2niUREpNX598K1/P7lF3hzwzNszXwHArUkxvXiMEZz7ZBh/PTQg9VpWPZKQwXOn4E9zbj4532YRUREWoktpRVMmj2XmfNeZcGOVyjP/BiA5IQDGRz3G64aMoxTfthXRY18Zw0N9De2KYOIiEjsKC+HUMhZtaGUpRs2snjjMpYWL+Hz0JesrPyQ7enzIb4S4uPI4DCOThzHVSeczHH9e0c7usSIRo2DIyIirVdtLWwsrGTp2mK+2lTC6sJi1hYXs3FLMaHtxRSVFbKlsojS2iLKKKQyvoja5EJIKaorYupLbEtGTX8G+lUM6XEol55wFPk5WdH5YBLTVOCIiLQyoc07WLK2kOUbCvkqFGJNSSEbthQS2lFIcXkhW6oK2V5bRLkVUxVfTG1SMSRu3/XFkoHEOOKrskmqySXNckiP605m0kBy2+TSLi2H/KwgB7bvTv8uPendOY9AQLedJPJU4IiItGBlFVUsXVfEsvWFrNxUyOqiQtZvKWRjaYiiskI2VxZSWlPIDiukMj5ETVIhJJTt+mIWwAI5JARySCaX7EAX0uP6kxmfTW5yNsG0LDpkZpOfk81+7bLp1j6bDplZZCRnaDoEaXYaGujvYWDXU40D7n5lRBKJiLRCVVWwobCClRtKWB0qYW1xCWtLCtm4rZDQ9rqWlc1VhZTWhOqKlbhCqhML8eTNu7+opRNPkCTLJY0OpPlBZHkuufG5tE8L0jEzl865uXTNy6VHh1y65GURH6dCRWJDQy04c5sshYhIjKisqmXp2hKWry9iVWEh60pK2LilhFBpCcU7SiipKGZbVQnba0ooo4TKQAnV8SV4csnuW1YCQGIS8R4kkVxSPJdcupLhuWSTS7u2uXTIyKVzTpD9g7l065BLz045pKYkNulnF2lOGnqKakpTBhERaSlqapzZc5fy/Jw5LNjwMWu2L6eE5VQkr8aTiiFQu+sT48Fq04gji8RAFilkkRPXi7T4LDISsshuk0UwNYt26Vl0zMpiv3DrSre8IKlJbfTItMhe2GMfHDMLAjcCvanrTgaAux8dwVwiIs3Klq213PvMv3hywXRWpjyHtwnV7QgkkRjXlSzvRo4PIseC5LXNoX1GLh2zcsjPzqZzMIv987LolJNJQpy6Poo0hcb8TZsGzAROAi4FRgKhSIYSEWkOysudh5+dx4T3p7M0aSakr8XS27B/5ckcmX0spw8axPH9e5OYEBftqCLyLY0pcHLc/XEzu8rd3wbeNrO3Ix1MRCQa3GHG659z7+zpfFQ1ndqsJZCdQNeaEzi39++57uRTSEtuG+2YIrIHjSlwqsI/15vZScA6ID9ykUREmt57n6xj7HPTebv4r1TmzIe2Rl7ZYIZ3vYGbf3o67dI0a7VIS9KYAucOM8sAfgU8DKQD10Q0lYhIE1i5fgu/nfEsL66Yxtbsf4I5aYkFnJF1P2N/diY9O3SIdkQR+Y4aLHDMLA7o6e5/A7YAg5sklYhIhJSV13DX07OZOH8ia1NfhvgKEpO6c0z8GMacdhZH9j0g2hFFZB9osMBx9xozOwW4v4nyiIhExFsLvuLmpyfyQcVEatPWYEm59Ku5hGsGn805Rw3U9AEiMaYxt6j+bWaPUPck1c7JSNx9fsRSiYjsA5WVzl0z3uLhefdQlDULEiCv4jgu6H4/v/nZKbRJ0kB4IrGqMQXOj8I/b6u3zQGNgyMizVJFhXPjhNk8uvh2KvLeI5DcnqMCY7hn+AUc0qtLtOOJSBPYY4Hj7up3IyItQkWF86vHXuHPX95OZXAOien5XNzpYe4750JSk1OiHU9EmlBjRjK+dhebtwDz3H3Bvo8kIrJ3yitqufrRF5i49A6qcj8iqe3+XNH1Me4ZMZLkhKRoxxORKGjMLaqC8Ovl8PpJwIfApWb2tLvfE6lwIiINKSuv4cpHn2HKijuoyl5IUkoPru02ibtGnE1ifEK044lIFDVqJGNggLuXApjZLcAzwBHAPEAFjog0qdId1Vz+2Aymrfod1Zmfk5J4INf0eJI7hp+puZ5EBIBAI47ZD6ist14FdHH3MqCioRPN7AQz+8LMlprZ6F3sTzKzmeH9H5jZ/uHtQ8xsnpl9Gv6pDs0iwsbiHfz0rkfIHNOTKVvPJTEugZt7zmTruE+5++yzVdyIyE6N+W3wV+A/ZvZieP1kYLqZtQUW7e6k8CCB44EhwBrgQzN7yd3rn3MhUOLuPcxsOHA3cCZQCJzs7uvMrC8wC+i0l59NRGLE63PWMub5v/BB7SN4m0IyOJTrej/ATWecTFygMf9PE5HWpjFPUd1uZq8ChwMGXOruc8O7z27g1IHAUndfDmBmM4BT+WZRdCpwa3j5GeARMzN3/6jeMZ8ByWaW5O4NthiJSOzYVFjNmKmvMnPJn9nS7lVIrqX91pO4ddCNXHL84ZhpYD4R2b3GtuemAFvdfZKZBc2sq7uv2MM5nYDV9dbXAIN2d4y7V5vZFur6/BTWO+YM4KNdFTdmdglwCcB+++3XyI8iIs1VTQ1MeXkZf3h9IouTJ0HaeuIzOjCkzWh+N+wCDunePdoRRaSFaMxj4rdQ9xTVAcAkIAF4EjhsT6fuYpvvzTFm1oe621bH7eoN3H0CMAGgoKDg29cWkRZiwaJSxkx/hn9smkJFx7cgJ8D+1Sdy1cCLufz4E4kPqG+NiOydxvzW+CnQH5gPEO4Xk9aI89YAneut5wPrdnPMGjOLBzKAYgAzyweeB/7H3Zc14v1EpAXZsrWWO558i6kfT2FT7rOQuJ022d0Z3u527hh2Ht2D+dGOKCItWGMKnEp3dzNzgHDn4sb4EOhpZl2BtcBw4KxvHfMSMBJ4HxgGvBl+r0zgFeAmd3+vke8nIs2cO0yftYS7X5vKp4GpeMYqArnpDEw5i5tPHMkp/X+kvjUisk80psB5ysweAzLN7GLgAuAvezop3KfmcuqegIoDJrr7Z2Z2GzDX3V8CHgeeMLOl1LXcDA+ffjnQAxhjZmPC245z90178+FEpHn4bNkWfj3tKWZtnEx5u39DRoD8yiFcdNA4rh96Gm0SNY2CiOxb5r7nritmNoS6fjAGzHL3f0Q62N4qKCjwuXPn7vlAEWkSpdtruGP6P5i8YAobM1+AhHLabD+QkzuP5I6fn0OPPI38ICJ7z8zmuXvBno5rTCfju939RuAfu9gmIrKTO8x8cxF3vzaFj3kST11HID2LQ+Iv5OYTR3JqQYFuQYlIk2jMLaohwLeLmZ/sYpuItFIr12/jhqkz+NvaxynL+QDaxNGp7EQuOvAhbjxtKCmJmvBSRJrWbgscMxsFXAZ0M7NP6u1KA9TxV6SVc4fH/z6XcW/8kWXJMyFxBymJfRiWdi93Dj+bnh3zoh1RRFqxhlpw/gr8HbgLqD+P1DZ3L45oKhFptqqqnDGTZ/PIgrvZ3u6fkJxK75qzuPmoizjriIG6BSUizcJuCxx33wJsAUaE55XKCx+famap7r6qiTKKSDOwbXs1Vz32NNNW3kNlzgLiUzsyLP33PDTyEjpkp0c7nojINzSmk/Hl1M0XtRGoDW924PuRiyUizcW60A4ufXQSr5TcS23GCpKTvscV+z3O3Wefrb41ItJsNaaT8dXAAe5eFOkwItJ8fLJsE6Me/xPv14zH24TIDBzK9X3v58afagZvEWn+GlPgrKbuVpWIxLiq6lruf+4dHn3/CVakToOkCjqWnsTth9/I+UdrBm8RaTkaU+AsB94ys1eAnTN6u/t9EUslIhHj7pRWlrJhW4gv1oRY8OUmPl65mjnr32N1/D/x1PXQpi19q8/j/tOv4dj+B0Q7sojIXmtMgbMq/EoMv0SkGXF3Qlu3smRdiOUbQnxVGGJ18SY2bAsR2h6iuCLE1uoQ29lEWSBEdWIIj6v4r+sEEtvTpfYITss/nTE/H0p2WmOnnRMRaX72WOC4+9imCCIidWpqnFWbtvDl2k0s3xhiVWGItSUhNmwLUVi2iZLKENtqQ5QRoiI+RE1SCOIrd32xyrZYeZDEqiDJNR0I2vdJjw+SndSO3JQgHTOD9O0aZGCf9vyge2cCAd2CEpHY0NBAfw+4+9Vm9jJ1T019g7ufEtFkIjGivLKapWuLWbIuxIqNIVYXh1i/uZCNpSGKykJsrqorWMqtkMqEELXJhRBXtZurpRHvQZJqg7T1fNrTn6y4dgQTg7RPqytY9ssJ0i0vSI+OQfLz2pCQ0KQfV0SkWWioBeeJ8M8/NEUQkZbAHYq2lLFkXSHL1of4KlTImuIQ67fW3Q4qKg+xtbqQUg9R/vXtoOQSsF1MamtggUziLUiyBcm0rmQwkByCBJOCdMgIkp8dZP9gkJ4d29GzUy5ZaclN/6FFRFqghgb6mxf++XbTxRGJHndYvq6EdxctY/6KZSzauIyvti6juGotOyikMr6w7nZQ4o5dXyAQRyCQS4LlkkKQPL5PJkFyLEheapCOGbnkZwfpmhekZ8cgPTrmkJKk5hURkUhoTCdjkZixJrSNeUtW6WxEPQAADglJREFU8+lXq/liw2qWFi1nzY5lFPkyypKXQfLmbxwfCOSRYp1JtXakWx+yLJfc+Fzy0oJ0zMwlPyeXbnlBenUK0iUvU+PDiIg0EypwJKZsKtnBS/9ZyNwVS1hWuIq1pasorFzFVltFRfJqSP7WkE6BeBIC+5NR043uPpBuSd3p07E7Bd278+M+3Qhm6kkiEZGWSAWOtGgr1m3hkVff5NXPZ7O85h0q0z+HQO3O/RbIITnQmSy60Y4jyU/oTPfgfhzYsTP9unVmQI9OJCXor4GISKxp6Cmqw4Fu7j41vP4MkB3efYe7v9kE+UT+y4aiHdwy/UWeXfIkRRmzIa4aklLJKzuCAwPDOLRbP3584PcY2Gs/ctLVAiMi0ho19F/XscAV9dYPAM4D2gI3AypwpElNe/0Tbnn1IZYlz4SkUuKTOnOoXcu5h5zEecccqg67IiKyU0MFTrq7L6q3vuTrJ6vM7K7IxhKpU13t3DT5ZR775D625bwNbVL4XvUIrhz4P1xy/I/VqVdERHapoQIns/6Ku59ebzUvMnFE6tTUOL+d+hr3LRhDefY84pO6cFqb3/PAyAvo0i57zxcQEZFWraEC53MzO8ndX6m/0cyGAl9ENpa0ZjP+uZCLn7+M0px/kZDYlVEdJvHABeeQGK/OwCIi0jgN/YtxDfCKmQ0D5oe3/QD4ETA00sGk9dlQtIOhv7+deYl/wNpkcF7un/jjTReQkqg5XkVEZO80NJLxUjP7PnA20Ce8+R3gUncvb4pw0jq4w28mv8bdCy+jJn0FB5afz9+uvodu7XOjHU1ERFqoBtv83b0CmNhEWaQVmrNoPac9eg3rc2aSZAfw8MB/MuonR0U7loiItHANjYOzjV3MIg4Y4O6eHrFUEvPKymsYfu+jvFT6a8go54Sk23jmxhtom5wU7WgiIhIDGmrBeQNoDzwHzHT3r5omksS6P744h2vfHEVF9nxyq47h6bP+yFEH9Yp2LBERiSEN9cE5zcwygNOBCWaWDMwEZrh7cVMFlNjx0ntfctlTt7I2awZxye25odsMxp3zc8ws2tFERCTG7KkPzhZgkplNAc4EHgaSgfuaIJvEiGmvf8JvXrmflelPQFoSRwRG89RNo8nL1F1OERGJjAYLHDP7ETAC+DHwLvBTd/9XUwSTlq1oSzm3/vVlpiweXzcCcdsUDvHLmXrpTXwvX+NEiohIZDXUyXglsBmYAVwCVIe3DwBw9/m7O1dap43FOxj/yts88dFMViY/D0lbNQKxiIhERUMtOCupe4rq+PCrPgeOjlAmaSEqq2qZ/uYnTPtgNh8WzWZz+rsQX4ElZ3BAzRlcfPAIrhx6NAnxcdGOKiIirUxDnYyPasIc0gK4wxvzlzPxrTd4Z/UbrEt6E28TAiA5vi+H8Et+2mcIl590FGkpyVFOKyIirVlDt6hucPd7wss/c/en6+27091vboqAEl1V1bX8+dU5THjvGRZWP09N+nIA4pI60J3jOa7jsYw6bgh9u3SMclIREZH/09AtquHAPeHlm4Cn6+07AVCBE8M+XVrEVVP+wjtlf6Im7StISiCv+liOSruG8486huP6f0+Pd4uISLPVUIFju1ne1brEAHeY/NoCbpv1MCtT/woJ5eT4YIZ3uIPRpw8lPzcz2hFFREQapaECx3ezvKt1acG2ba/ihknP88QXD7M9911IbcPBjGTcyZdzwoC+0Y4nIiKy1xoqcA42s63UtdakhJcJr6sHaQyY/+VGrn5iAu+VP0pt6joSkrvxs7T7uG/keeTnZEU7noiIyHfW0FNUerY3Rj379mKuf+EPrEh9EuIraVd7PFf1mcCNp/+EuEAg2vFERET+3xocyVhihzs8+Px7/O6teyjMeQnapnBw7UXce+qVHNPvgGjHExER2adU4LQCM95YxKjnf8Xm4GtYmxyOjb+Vx37xS7q1z412NBERkYhQgRPDFi4rYtj4W/ki7U9YehrD0u5lwq8uJSu1TbSjiYiIRJQKnBi0tbSKEff9iVd33AppWxjgl/Ls1WPZv51abEREpHVQgRND3OHmSX/nD59eS3Xm57SrHsKUn9+nR71FRKTVUYETI6a9/ilXvHgDJbmvkRjfkzv7vszo00/SaMMiItIqqcBp4Z5+axHXvTCOVZlPYqkZ/Cz9Pqbc9EtSEhOjHU1ERCRqVOC0MDU1zj/mLWPCm6/x+rqn2ZbzDqQmc5hdz7RrbqRLu+xoRxQREYk6FTjNwOZtFSxdV8SqUAlrCotZv7mEjVtLKNxeTPGOErZUlLC1upiSmtVsa/MpJG8GIDGxF6ek3M19l5xP9w7BKH8KERGR5kMFzj5UVl7Dyo2b+WpTMWsKS1hXUsKGLcWEthWzaXshJeVFbKkqorS2kDKKqIwvpCaxCJJKd3/ROMPiMomvzSbFO9CX4fTLPJiLjzmWI/r2aLoPJyIi0oJEtMAxsxOAB4E44C/uPu5b+5OAqcAPgCLgTHdfGd53E3AhUANc6e6zIpn1a1XVtawNbWPlpmJWh+qKlPWbiwltK6FoewnFZcVsqSqhtLqEHV5MuZVQFVdCTWIxJG3d/YUDYHEZxNfkkuQ5pFkeadabDMshNy6X3LY5tM/IJi8ji07Z2ewXzKJLu2w65aZr+gQREZG9FLECx8zigPHAEGAN8KGZveTui+oddiFQ4u49zGw4cDdwppn1BoYDfYCOwOtm1svdaxrz3hWVNWwoLmVd8da6IqW4rkipu+0TLlIqSthWXcL22mLKKaEyroSahGI8aTMEand/8bhEAjVZJASySarNIsM6kmp9SSeLTMsip002wbQs8tLrCpVOOXWFSrf22SQnJnyXP0oRERHZS5FswRkILHX35QBmNgM4Fahf4JwK3BpefgZ4xOqeaz4VmOHuFcAKM1savt77u3uz+WsWErixPR6/DRJ3NJwsEMACWcQHskjybNqQTdC6k27ZZFgWOcnZ5KbWFSkds+qKlP2CWXRtn01m2xQ9ei0iItLMRbLA6QSsrre+Bhi0u2PcvdrMtgA54e3/+da5nb79BmZ2CXAJQFxuBj05hbakkUYa6clpZCan0y49i/aZWeRnZ9M5mEWXvCzaZ6Xpto+IiEgMi2SBs6tmDm/kMY05F3efAEwAKCgo8Ll3T9jbjCIiIhKDItmMsQboXG89H1i3u2PMLB7IAIobea6IiIjILkWywPkQ6GlmXc0skbpOwy9965iXgJHh5WHAm+7u4e3DzSzJzLoCPYE5EcwqIiIiMSRit6jCfWouB2ZR95j4RHf/zMxuA+a6+0vA48AT4U7ExdQVQYSPe4q6DsnVwC8b+wSViIiIiNU1mLR8BQUFPnfu3GjHEBERkQgys3nuXrDH42KlwDGzbcAX0c4hMSkXKIx2CIlZ+n5JpMTqd6uLu+9xfqJYmqrhi8ZUdCJ7y8zm6rslkaLvl0RKa/9uaTAYERERiTkqcERERCTmxFKBo1H+JFL03ZJI0vdLIqVVf7dippOxiIiIyNdiqQVHREREBFCBIyIiIjEoJgocMzvBzL4ws6VmNjraeSQ2mNlEM9tkZgujnUVii5l1NrN/mtliM/vMzK6KdiaJDWaWbGZzzOzj8HdrbLQzRUuL74NjZnHAl8AQ6ibp/BAY4e6LohpMWjwzOwIoBaa6e99o55HYYWYdgA7uPt/M0oB5wGn6vSX/X2ZmQFt3LzWzBOBd4Cp3/0+UozW5WGjBGQgsdffl7l4JzABOjXImiQHu/g51c6SJ7FPuvt7d54eXtwGLgU7RTSWxwOuUhlcTwq+W3ZLxHcVCgdMJWF1vfQ36RSEiLYSZ7Q/0Bz6IbhKJFWYWZ2YLgE3AP9y9VX63YqHAsV1sa5XVqoi0LGaWCjwLXO3uW6OdR2KDu9e4ez8gHxhoZq3yFnssFDhrgM711vOBdVHKIiLSKOH+Ec8C09z9uWjnkdjj7puBt4ATohwlKmKhwPkQ6GlmXc0sERgOvBTlTCIiuxXuCPo4sNjd74t2HokdZhY0s8zwcgpwLPB5dFNFR4svcNy9GrgcmEVdR72n3P2z6KaSWGBm04H3gQPMbI2ZXRjtTBIzDgPOBY42swXh14nRDiUxoQPwTzP7hLoGgH+4+9+inCkqWvxj4iIiIiLf1uJbcERERES+TQWOiIiIxBwVOCIiIhJzVOCIiIhIzFGBIyIiIjFHBY6IiIjEHBU4ItJkzCyn3rgvG8xsbb31f0fg/c4zs5CZ/aWBY1LC719pZrn7OoOIREd8tAOISOvh7kVAPwAzuxUodfc/RPhtZ7r75Q1kKgP6mdnKCOcQkSakFhwRaRbMrDT88ygze9vMnjKzL81snJmdbWZzzOxTM+sePi5oZs+a2Yfh12GNeI8+4essMLNPzKxnpD+XiESHWnBEpDk6GDgQKAaWA39x94FmdhVwBXA18CBwv7u/a2b7UTddy4F7uO6lwIPuPi08d11cxD6BiESVChwRaY4+dPf1AGa2DJgd3v4pMDi8fCzQu27eSgDSzSzN3bc1cN33gV+bWT7wnLsv2ffRRaQ50C0qEWmOKuot19Zbr+X//mMWAA51937hV6c9FDe4+1+BU4AyYJaZHb2Pc4tIM6ECR0RaqtnAzs7DZtZvTyeYWTdgubs/BLwEfD9y8UQkmlTgiEhLdSVQEO4svIi6/jV7ciaw0MwWAN8DpkYyoIhEj7l7tDOIiESEmZ0HFDT0mHi9Y1eGjy2MdC4RiTy14IhILCsDftKYgf6ABOr6+IhIDFALjoiIiMQcteCIiIhIzFGBIyIiIjFHBY6IiIjEHBU4IiIiEnP+F9b5R6iPKMEgAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c94edfeb8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "fig, (ax1, ax2) = plt.subplots(2, 1, sharex=True, figsize=(8, 5))\n", "ax1.plot(time, datafrle, 'r')\n", "ax1.set_title('EMG signal (linear envelope)')\n", "ax1.set_ylabel('EMG amplitude [V]')\n", "ax1.set_xlim(time[0], time[-1])\n", "ax2.plot(time, Arect2, 'b', label='Rectangle')\n", "ax2.plot(time, Atrap2, 'g', label='Trapezoid')\n", "ax2.set_xlabel('Time [s]')\n", "ax2.set_title('Integral of the EMG signal')\n", "ax2.set_ylabel('EMG integral [Vs]')\n", "ax2.legend(loc='upper left', frameon=False)\n", "plt.locator_params(axis = 'both', nbins = 4)\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the value of the total area is equal to the last value of the cumulative integration from the graph above.   \n", "\n", "The trapezoidal rule is more accurate than the rectangle rule to calculate the integration of a function.  \n", "So, why they resulted in the same values?   \n", "The trick is that using the rectangle rule, the area is superestimated in the ascending part and subestimated in the descending part of the curve. Since the EMG signal has zero mean, i.e., equal amounts of ascending and descending parts, the errors are cancelled out.\n", "See that the rectangle and trapezoidal rules are indeed different:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:19.608418Z", "start_time": "2017-12-30T08:01:19.598385Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Integral by the rectangle rule: [  0.   2.   6.  12.  20.]\n", "Integral by the trapezoid rule: [  0.   1.   4.   9.  16.]\n"]}], "source": ["x = [0, 2, 4, 6, 8]\n", "print('Integral by the rectangle rule:', np.cumsum(x)*1.0)\n", "print('Integral by the trapezoid rule:', cumtrapz(x,initial=0))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But if x has zero mean, the total values of the integral using the cumsum and cumtrapz methods will be the same:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:23.743836Z", "start_time": "2017-12-30T08:01:23.733056Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Integral by the rectangle rule: [-4. -6. -6. -4.  0.]\n", "Integral by the trapezoid rule: [ 0. -3. -4. -3.  0.]\n"]}], "source": ["x = x - np.mean(x)\n", "print('Integral by the rectangle rule:', np.cumsum(x)*1.0)\n", "print('Integral by the trapezoid rule:', cumtrapz(x, initial=0))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Integrator with time reset\n", "\n", "Another measure used in EMG processing is to calculate the integral but reset the integrator from time to time:"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:35.751158Z", "start_time": "2017-12-30T08:01:35.742041Z"}}, "outputs": [], "source": ["nreset = 400 # reset after this amount of samples\n", "area = []\n", "for i in range(int(np.ceil(np.size(datafrle)/nreset))):\n", "    area = np.hstack((area, cumtrapz(datafrle[i*nreset:(i+1)*nreset], initial=0)/freq))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And let's plot the data:"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:37.964687Z", "start_time": "2017-12-30T08:01:37.716667Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c94de1828>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot data\n", "fig, (ax1, ax2) = plt.subplots(2, 1, sharex=True, figsize=(8, 5))\n", "ax1.plot(time, data, 'r')\n", "ax1.set_title('EMG signal (linear envelope)')\n", "ax1.set_ylabel('EMG amplitude [V]')\n", "ax1.set_xlim(time[0], time[-1])\n", "ax2.plot(time, area, 'y', label='Trapezoid')\n", "ax2.set_xlabel('Time [s]')\n", "ax2.set_title('Integral of the EMG signal with time reset (t = %s ms)' %nreset)\n", "ax2.set_ylabel('EMG integral [Vs]')\n", "plt.locator_params(axis='both', nbins=4)\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Frequency estimation\n", "\n", "### Power Spectral Density"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:01:55.289361Z", "start_time": "2017-12-30T08:01:54.870284Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c94f471d0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from psd import psd\n", "fpc<PERSON>le, mpf, fmax, <PERSON><PERSON><PERSON>, f, P = psd(dataf, fs=freq)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Short-Time Fourier Transform"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:02:04.846580Z", "start_time": "2017-12-30T08:02:04.645130Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c94c454e0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax1 = plt.subplots(1, 1, figsize=(8, 4))\n", "P, freqs, t, im = plt.specgram(dataf, NFFT=128, Fs=freq, noverlap = 64, cmap=plt.cm.jet)\n", "# P: array of shape (len(times), len(freqs)) of power,\n", "# freqs: array of frequencies, \n", "# bins: time points the spectrogram is calculated over,\n", "# im: matplotlib.image.AxesImage instance\n", "ax1.set_title('Short-Time Fourier Transform', fontsize=18)\n", "ax1.set_xlabel('Time [s]')\n", "ax1.set_ylabel('Frequency [Hz]')\n", "ax1.set_xlim(t[0], t[-1])\n", "plt.tight_layout()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T08:02:08.462092Z", "start_time": "2017-12-30T08:02:07.822172Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8c94bd0860>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from mpl_toolkits.mplot3d import Axes3D\n", "\n", "t2, freqs2 = np.meshgrid(t, freqs)\n", "\n", "fig = plt.figure(figsize=(10, 6))\n", "ax = fig.gca(projection='3d')\n", "surf = ax.plot_surface(t2, freqs2, P, rstride=1, cstride=1, cmap=plt.cm.jet,\n", "                       linewidth=0, antialiased=False)\n", "\n", "ax.set_xlim(t[0], t[-1])\n", "ax.set_ylim(0, 500)\n", "ax.set_zlim(np.min(P), np.max(P))\n", "ax.set_xlabel('Time [s]', fontsize=12)\n", "ax.set_ylabel('Frequency [Hz]', fontsize=12)\n", "ax.set_zlabel('Power', fontsize=12)\n", "ax.set_title('Short-Time Fourier Transform', fontsize=16, y=1.04)\n", "ax.view_init(30, 60)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON> (1997) [The use of electromyography in biomechanics](http://delsys.com/KnowledgeCenter/Tutorials_Technical%20Notes.html). Journal of Applied Biomechanics,  13, 135-163.\n", "- <PERSON> (2006) [The ABC of EMG: A Practical Introduction to Kinesiological Electromyography](http://www.noraxon.com/docs/education/abc-of-emg.pdf). Noraxon U.S.A., Inc.\n", "- <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> (2007) [<PERSON><PERSON>–Kaiser Energy Operation of surface EMG improves muscle activity onset detection](http://www.ncbi.nlm.nih.gov/pubmed/17473984). Annals of Biomedical Engineering, 35, 1532–1538. \n", "- <PERSON><PERSON><PERSON><PERSON> (1999) [Standards for Reporting EMG data](http://www.isek-online.org/standards_emg.html). Journal of Electromyography and Kinesiology, February 1999; 9(1):III-IV.\n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON> (2004) [Electromyography: Physiology, Engineering, and Non-Invasive Applications](http://books.google.com.br/books?id=SQthgVMil3YC). Wiley-IEEE Press.\n", "- Winter DA (2009) [Biomechanics and motor control of human movement](http://books.google.com.br/books?id=_bFHL08IWfwC&printsec=frontcover&source=gbs_ge_summary_r&cad=0#v=onepage&q&f=false). 4 ed. Hoboken, EUA: Wiley."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}