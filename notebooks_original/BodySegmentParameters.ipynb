{"cells": [{"cell_type": "markdown", "metadata": {"id": "XYXgBRZBO6aV"}, "source": ["# Body segment parameters\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://pesquisa.ufabc.edu.br/bmclab/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"id": "1jKqnghsO6aZ"}, "source": ["<figure><img src='https://upload.wikimedia.org/wikipedia/commons/thumb/2/22/<PERSON>_<PERSON>_Vitruve_Luc_Viatour.jpg/353px-<PERSON>_<PERSON>_Vitruve_Luc_Viatour.jpg' width=\"240\" alt=\"Vitruvian Man\" style=\"float:right;margin: 20px 0 0 50px;\"/></figure>\n", "<br><br>\n", "<i>\"Le proporzioni del corpo umano secondo Vitruvio\"</i>, also known as the <a href=\"https://en.wikipedia.org/wiki/Vitruvian_Man\" target=\"_blank\">Vitruvian Man</a>, drawing by <a href=\"https://en.wikipedia.org/wiki/<PERSON>\" target=\"_blank\"><PERSON></a> circa 1490 based on the work of <a href=\"https://en.wikipedia.org/wiki/Vitruvius\" target=\"_blank\"><PERSON></a> (1st century BC), depicting a man in supposedly ideal human proportions (image from <a href=\"https://en.wikipedia.org/wiki/Vitruvian_Man\" target=\"_blank\">Wikipedia</a>).\n", "<br><br>\n", "In fact, <PERSON>'s drawing does not follow the proportions according <PERSON><PERSON><PERSON><PERSON><PERSON>, but rather the proportions he found after his own anthropometrical studies of the human body. <PERSON> was unable to fit a human body inside a circle and a square with the same center, one of the <PERSON><PERSON><PERSON><PERSON><PERSON>' claims.\n", "<br><br>\n", "This is a remarkable historical evidence of not complying with established common knowledge and relying on experimental data for acquiring knowledge about nature, a <i>tour de force</i> for the scientific method."]}, {"cell_type": "markdown", "metadata": {"toc": 1, "id": "c-MFs5IoO6aa"}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1><br>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Estimation-of-body-segment-parameters\" data-toc-modified-id=\"Estimation-of-body-segment-parameters-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Estimation of body segment parameters</a></span><ul class=\"toc-item\"><li><span><a href=\"#De<PERSON><PERSON>'s-model-adapted-by-Winter\" data-toc-modified-id=\"<PERSON><PERSON><PERSON>'s-model-adapted-by-Winter-2.1\"><span class=\"toc-item-num\">2.1&nbsp;&nbsp;</span><PERSON><PERSON><PERSON>'s model adapted by Winter</a></span></li><li><span><a href=\"#<PERSON>atsiorsky's-model-adjusted-by-de-Leva\" data-toc-modified-id=\"<PERSON><PERSON>iorsky's-model-adjusted-by-de-Leva-2.2\"><span class=\"toc-item-num\">2.2&nbsp;&nbsp;</span>Zatsiorsky's model adjusted by de Leva</a></span></li><li><span><a href=\"#Differences-between-the-anthropometric-models-from-Dempster-and-Zatsiorsky\" data-toc-modified-id=\"Differences-between-the-anthropometric-models-from-Dempster-and-Zatsiorsky-2.3\"><span class=\"toc-item-num\">2.3&nbsp;&nbsp;</span>Differences between the anthropometric models from Dempster and Zatsiorsky</a></span></li></ul></li><li><span><a href=\"#Center-of-mass\" data-toc-modified-id=\"Center-of-mass-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Center of mass</a></span></li><li><span><a href=\"#Moment-of-inertia\" data-toc-modified-id=\"Moment-of-inertia-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Moment of inertia</a></span><ul class=\"toc-item\"><li><span><a href=\"#Radius-of-gyration\" data-toc-modified-id=\"Radius-of-gyration-4.1\"><span class=\"toc-item-num\">4.1&nbsp;&nbsp;</span>Radius of gyration</a></span></li><li><span><a href=\"#Parallel-axis-theorem\" data-toc-modified-id=\"Parallel-axis-theorem-4.2\"><span class=\"toc-item-num\">4.2&nbsp;&nbsp;</span>Parallel axis theorem</a></span></li></ul></li><li><span><a href=\"#Center-of-buoyancy\" data-toc-modified-id=\"Center-of-buoyancy-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Center of buoyancy</a></span></li><li><span><a href=\"#Further-reading\" data-toc-modified-id=\"Further-reading-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>Further reading</a></span></li><li><span><a href=\"#Video-lectures-on-the-internet\" data-toc-modified-id=\"Video-lectures-on-the-internet-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Video lectures on the internet</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "rkfhb8-gO6ab"}, "source": ["## Estimation of body segment parameters\n", "\n", "Body segment parameters (BSP) of the human body, such as length, area, volume, mass, density, center of mass, moment of inertia, and center of volume, are fundamental for the application of mechanics to the understanding of human movement. Anthropometry is the field concerned with the study of such measurements of the human body. Frequently, one cannot measure most of these parameters of each segment of an individual and these quantities are estimated by indirect methods. The main indirect methods are based in data of cadavers (e.g. <PERSON><PERSON>'s model), body image scanning of living subjects (e.g., <PERSON><PERSON><PERSON><PERSON>'s model), and geometric measurements (e.g., <PERSON><PERSON>'s model).    \n", "\n", "For reviews available online of the different methods employed in the estimation of BSP, see [<PERSON><PERSON><PERSON> et al. (1964)](http://www.oandplibrary.org/al/1964_01_044.asp) and [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1995)](http://citeseerx.ist.psu.edu/viewdoc/summary?doi=*********.5223).  \n", "\n", "Let's look on how to estimate some of the BSP using the anthropometric model of <PERSON><PERSON><PERSON> (1955) with some parameters adapted by <PERSON> (2009) and the model of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>, 2002), from now on, <PERSON><PERSON><PERSON><PERSON>, with parameters adjusted by <PERSON> (1996). There is at least one Python library for the calculation of human body segment parameters, see <PERSON><PERSON><PERSON> et al. (2014), it implements the Yeadon human inertia geometric model, but we will not use it here.\n", "\n", "For a table with BSP values, also referred as anthropometric table, typically:   \n", "\n", "+ The mass of each segment is given as fraction of the total body mass.   \n", "+ The center of mass (CM) position in the sagittal plane of each segment is given as fraction of the segment length with respect to the proximal or distal joint position.\n", "+ The radius of gyration (Rg) around the transverse axis (rotation at the sagittal plane) and around other axes of each segment is given as fraction of the segment length with respect to (w.r.t.) the center of mass or w.r.t. the proximal or w.r.t. the distal joint position.\n", "\n", "For a formal description of these parameters, see the notebook [Center of Mass and Moment of Inertia](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/CenterOfMassAndMomentOfInertia.ipynb)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T15:00:33.785428Z", "start_time": "2020-04-17T15:00:33.779050Z"}, "id": "8VvQ73DMO6ab"}, "outputs": [], "source": ["# Import the necessary libraries\n", "import numpy as np\n", "import pandas as pd\n", "from IPython.display import display, Math, Latex\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "pd.set_option('max_colwidth', 100)"]}, {"cell_type": "markdown", "metadata": {"id": "77NnQx7oO6ad"}, "source": ["### <PERSON><PERSON><PERSON>'s model adapted by <PERSON>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.841434Z", "start_time": "2020-04-17T14:59:00.831073Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 665}, "id": "IAP8B4oaO6ad", "outputId": "66444dfb-a477-4774-9792-3db14ade3bcf"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\text{BSP segments from <PERSON><PERSON><PERSON>'s model adapted by Winter (2009):}"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["            Segment       Definition    Mass  CM prox  CM dist  Rg CM  \\\n", "0              Hand         WJC-KNU2  0.0060    0.506    0.494  0.297   \n", "1           Forearm         EJC-STYL  0.0160    0.430    0.570  0.303   \n", "2         Upper arm          SJC-EJC  0.0280    0.436    0.564  0.322   \n", "3      Forearm hand         EJC-STYL  0.0220    0.682    0.318  0.468   \n", "4         Total arm         SJC-STYL  0.0500    0.530    0.470  0.368   \n", "5              Foot         LMAL-MT2  0.0145    0.500    0.500  0.475   \n", "6               Leg         KJC-MMAL  0.0465    0.433    0.567  0.302   \n", "7             Thigh          GTR-KJC  0.1000    0.433    0.567  0.323   \n", "8         Head neck     C7T1-RIB1EAR  0.0810    1.000    0.000  0.495   \n", "9            Thorax  C7T1-T12L1-DIAP  0.2160    0.820    0.180    NaN   \n", "10          Abdomen        T12L1-GTR  0.1390    0.440    0.560    NaN   \n", "11           Pelvis         L4L5-GTR  0.1420    0.105    0.895    NaN   \n", "12            Trunk          GTR-SJC  0.4970    0.500    0.500    NaN   \n", "13  Trunk head neck          GTR-SJC  0.5780    0.660    0.340  0.503   \n", "14              HAT          GTR-SJC  0.6780    0.626    0.374  0.496   \n", "15         Foot leg         KJC-MMAL  0.0610    0.606    0.394  0.416   \n", "16        Total leg         GTR-MMAL  0.1610    0.447    0.553  0.326   \n", "17   Thorax abdomen        C7T1-L4L5  0.3550    0.630    0.370    NaN   \n", "18   Abdomen pelvis        T12L1-GTR  0.2810    0.270    0.730    NaN   \n", "\n", "    Rg prox  Rg dist  \n", "0     0.587    0.577  \n", "1     0.526    0.647  \n", "2     0.542    0.645  \n", "3     0.827    0.565  \n", "4     0.645    0.596  \n", "5     0.690    0.690  \n", "6     0.528    0.643  \n", "7     0.540    0.653  \n", "8     0.116      NaN  \n", "9       NaN      NaN  \n", "10      NaN      NaN  \n", "11      NaN      NaN  \n", "12      NaN      NaN  \n", "13    0.830    0.607  \n", "14    0.798    0.621  \n", "15    0.735    0.572  \n", "16    0.560    0.650  \n", "17      NaN      NaN  \n", "18      NaN      NaN  "], "text/html": ["\n", "  <div id=\"df-e563ab71-d244-4c99-8902-21c980f5c048\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Segment</th>\n", "      <th>Definition</th>\n", "      <th>Mass</th>\n", "      <th>CM prox</th>\n", "      <th>CM dist</th>\n", "      <th>Rg CM</th>\n", "      <th>Rg prox</th>\n", "      <th>Rg dist</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Hand</td>\n", "      <td>WJC-KNU2</td>\n", "      <td>0.0060</td>\n", "      <td>0.506</td>\n", "      <td>0.494</td>\n", "      <td>0.297</td>\n", "      <td>0.587</td>\n", "      <td>0.577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Forearm</td>\n", "      <td>EJC-STYL</td>\n", "      <td>0.0160</td>\n", "      <td>0.430</td>\n", "      <td>0.570</td>\n", "      <td>0.303</td>\n", "      <td>0.526</td>\n", "      <td>0.647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Upper arm</td>\n", "      <td>SJC-EJC</td>\n", "      <td>0.0280</td>\n", "      <td>0.436</td>\n", "      <td>0.564</td>\n", "      <td>0.322</td>\n", "      <td>0.542</td>\n", "      <td>0.645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Forearm hand</td>\n", "      <td>EJC-STYL</td>\n", "      <td>0.0220</td>\n", "      <td>0.682</td>\n", "      <td>0.318</td>\n", "      <td>0.468</td>\n", "      <td>0.827</td>\n", "      <td>0.565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Total arm</td>\n", "      <td>SJC-STYL</td>\n", "      <td>0.0500</td>\n", "      <td>0.530</td>\n", "      <td>0.470</td>\n", "      <td>0.368</td>\n", "      <td>0.645</td>\n", "      <td>0.596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Foot</td>\n", "      <td>LMAL-MT2</td>\n", "      <td>0.0145</td>\n", "      <td>0.500</td>\n", "      <td>0.500</td>\n", "      <td>0.475</td>\n", "      <td>0.690</td>\n", "      <td>0.690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Leg</td>\n", "      <td>KJC-MMAL</td>\n", "      <td>0.0465</td>\n", "      <td>0.433</td>\n", "      <td>0.567</td>\n", "      <td>0.302</td>\n", "      <td>0.528</td>\n", "      <td>0.643</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Thigh</td>\n", "      <td>GTR-KJC</td>\n", "      <td>0.1000</td>\n", "      <td>0.433</td>\n", "      <td>0.567</td>\n", "      <td>0.323</td>\n", "      <td>0.540</td>\n", "      <td>0.653</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Head neck</td>\n", "      <td>C7T1-RIB1EAR</td>\n", "      <td>0.0810</td>\n", "      <td>1.000</td>\n", "      <td>0.000</td>\n", "      <td>0.495</td>\n", "      <td>0.116</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Thorax</td>\n", "      <td>C7T1-T12L1-DIAP</td>\n", "      <td>0.2160</td>\n", "      <td>0.820</td>\n", "      <td>0.180</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Abdomen</td>\n", "      <td>T12L1-GTR</td>\n", "      <td>0.1390</td>\n", "      <td>0.440</td>\n", "      <td>0.560</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>L4L5-GTR</td>\n", "      <td>0.1420</td>\n", "      <td>0.105</td>\n", "      <td>0.895</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Trunk</td>\n", "      <td>GTR-SJC</td>\n", "      <td>0.4970</td>\n", "      <td>0.500</td>\n", "      <td>0.500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Trunk head neck</td>\n", "      <td>GTR-SJC</td>\n", "      <td>0.5780</td>\n", "      <td>0.660</td>\n", "      <td>0.340</td>\n", "      <td>0.503</td>\n", "      <td>0.830</td>\n", "      <td>0.607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>HAT</td>\n", "      <td>GTR-SJC</td>\n", "      <td>0.6780</td>\n", "      <td>0.626</td>\n", "      <td>0.374</td>\n", "      <td>0.496</td>\n", "      <td>0.798</td>\n", "      <td>0.621</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Foot leg</td>\n", "      <td>KJC-MMAL</td>\n", "      <td>0.0610</td>\n", "      <td>0.606</td>\n", "      <td>0.394</td>\n", "      <td>0.416</td>\n", "      <td>0.735</td>\n", "      <td>0.572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Total leg</td>\n", "      <td>GTR-MMAL</td>\n", "      <td>0.1610</td>\n", "      <td>0.447</td>\n", "      <td>0.553</td>\n", "      <td>0.326</td>\n", "      <td>0.560</td>\n", "      <td>0.650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td><PERSON>ax abdomen</td>\n", "      <td>C7T1-L4L5</td>\n", "      <td>0.3550</td>\n", "      <td>0.630</td>\n", "      <td>0.370</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td><PERSON><PERSON><PERSON> pelvis</td>\n", "      <td>T12L1-GTR</td>\n", "      <td>0.2810</td>\n", "      <td>0.270</td>\n", "      <td>0.730</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-e563ab71-d244-4c99-8902-21c980f5c048')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-e563ab71-d244-4c99-8902-21c980f5c048 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-e563ab71-d244-4c99-8902-21c980f5c048');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-7e25a143-f3e8-4fec-904c-8d52b0ef1383\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-7e25a143-f3e8-4fec-904c-8d52b0ef1383')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-7e25a143-f3e8-4fec-904c-8d52b0ef1383 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "BSP_Dmarks", "summary": "{\n  \"name\": \"BSP_Dmarks\",\n  \"rows\": 19,\n  \"fields\": [\n    {\n      \"column\": \"Segment\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 19,\n        \"samples\": [\n          \"Hand\",\n          \"Foot\",\n          \"<PERSON>elvis\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Definition\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 14,\n        \"samples\": [\n          \"T12L1-GTR\",\n          \"GTR-SJC\",\n          \"WJC-KNU2\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Mass\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.20413464840237822,\n        \"min\": 0.006,\n        \"max\": 0.678,\n        \"num_unique_values\": 19,\n        \"samples\": [\n          0.006,\n          0.0145,\n          0.142\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"CM prox\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.19339460614042608,\n        \"min\": 0.105,\n        \"max\": 1.0,\n        \"num_unique_values\": 17,\n        \"samples\": [\n          0.506,\n          0.43,\n          0.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"CM dist\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.19339460614042608,\n        \"min\": 0.0,\n        \"max\": 0.895,\n        \"num_unique_values\": 17,\n        \"samples\": [\n          0.494,\n          0.57,\n          0.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg CM\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.08496356685254976,\n        \"min\": 0.297,\n        \"max\": 0.503,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          0.416,\n          0.503,\n          0.297\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg prox\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.18807118128721695,\n        \"min\": 0.116,\n        \"max\": 0.83,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          0.735,\n          0.83,\n          0.587\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg dist\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.03889340522115505,\n        \"min\": 0.565,\n        \"max\": 0.69,\n        \"num_unique_values\": 12,\n        \"samples\": [\n          0.572,\n          0.621,\n          0.577\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}], "source": ["BSP_Dmarks = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data/BSP_DempsterWinter.txt', sep='\\t')\n", "display(Latex('\\\\text{BSP segments from Dempster\\'s model adapted by Winter (2009):}'))\n", "display(BSP_Dmarks)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.854697Z", "start_time": "2020-04-17T14:59:00.842810Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 696}, "id": "r2cBJSBaO6ae", "outputId": "f1ae84ef-f04b-476d-8f2d-7037b6a694a2"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\text{BSP values from <PERSON><PERSON><PERSON>'s model adapted by Winter (2009):}"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["                      Definition    Mass  CM prox  CM dist  Rg CM  Rg prox  \\\n", "Segment                                                                      \n", "Hand                    WJC-KNU2  0.0060    0.506    0.494  0.297    0.587   \n", "Forearm                 EJC-STYL  0.0160    0.430    0.570  0.303    0.526   \n", "Upper arm                SJC-EJC  0.0280    0.436    0.564  0.322    0.542   \n", "Forearm hand            EJC-STYL  0.0220    0.682    0.318  0.468    0.827   \n", "Total arm               SJC-STYL  0.0500    0.530    0.470  0.368    0.645   \n", "Foot                    LMAL-MT2  0.0145    0.500    0.500  0.475    0.690   \n", "Leg                     KJC-MMAL  0.0465    0.433    0.567  0.302    0.528   \n", "Thigh                    GTR-KJC  0.1000    0.433    0.567  0.323    0.540   \n", "Head neck           C7T1-RIB1EAR  0.0810    1.000    0.000  0.495    0.116   \n", "Thorax           C7T1-T12L1-DIAP  0.2160    0.820    0.180    NaN      NaN   \n", "Abdomen                T12L1-GTR  0.1390    0.440    0.560    NaN      NaN   \n", "Pelvis                  L4L5-GTR  0.1420    0.105    0.895    NaN      NaN   \n", "Trunk                    GTR-SJC  0.4970    0.500    0.500    NaN      NaN   \n", "Trunk head neck          GTR-SJC  0.5780    0.660    0.340  0.503    0.830   \n", "HAT                      GTR-SJC  0.6780    0.626    0.374  0.496    0.798   \n", "Foot leg                KJC-MMAL  0.0610    0.606    0.394  0.416    0.735   \n", "Total leg               GTR-MMAL  0.1610    0.447    0.553  0.326    0.560   \n", "Thorax abdomen         C7T1-L4L5  0.3550    0.630    0.370    NaN      NaN   \n", "Abdomen pelvis         T12L1-GTR  0.2810    0.270    0.730    NaN      NaN   \n", "\n", "                 Rg dist  \n", "Segment                   \n", "Hand               0.577  \n", "Forearm            0.647  \n", "Upper arm          0.645  \n", "Forearm hand       0.565  \n", "Total arm          0.596  \n", "Foot               0.690  \n", "Leg                0.643  \n", "Thigh              0.653  \n", "Head neck            Na<PERSON>  \n", "Thorax               NaN  \n", "Abdomen              <PERSON>  \n", "<PERSON><PERSON><PERSON>  \n", "Trunk                NaN  \n", "Trunk head neck    0.607  \n", "HAT                0.621  \n", "Foot leg           0.572  \n", "Total leg          0.650  \n", "Thorax abdomen       NaN  \n", "Abdomen pelvis       <PERSON>  "], "text/html": ["\n", "  <div id=\"df-eb3050d8-e30c-4dbd-b430-9f276f888d53\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Definition</th>\n", "      <th>Mass</th>\n", "      <th>CM prox</th>\n", "      <th>CM dist</th>\n", "      <th>Rg CM</th>\n", "      <th>Rg prox</th>\n", "      <th>Rg dist</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Segment</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Hand</th>\n", "      <td>WJC-KNU2</td>\n", "      <td>0.0060</td>\n", "      <td>0.506</td>\n", "      <td>0.494</td>\n", "      <td>0.297</td>\n", "      <td>0.587</td>\n", "      <td>0.577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm</th>\n", "      <td>EJC-STYL</td>\n", "      <td>0.0160</td>\n", "      <td>0.430</td>\n", "      <td>0.570</td>\n", "      <td>0.303</td>\n", "      <td>0.526</td>\n", "      <td>0.647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper arm</th>\n", "      <td>SJC-EJC</td>\n", "      <td>0.0280</td>\n", "      <td>0.436</td>\n", "      <td>0.564</td>\n", "      <td>0.322</td>\n", "      <td>0.542</td>\n", "      <td>0.645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm hand</th>\n", "      <td>EJC-STYL</td>\n", "      <td>0.0220</td>\n", "      <td>0.682</td>\n", "      <td>0.318</td>\n", "      <td>0.468</td>\n", "      <td>0.827</td>\n", "      <td>0.565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Total arm</th>\n", "      <td>SJC-STYL</td>\n", "      <td>0.0500</td>\n", "      <td>0.530</td>\n", "      <td>0.470</td>\n", "      <td>0.368</td>\n", "      <td>0.645</td>\n", "      <td>0.596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Foot</th>\n", "      <td>LMAL-MT2</td>\n", "      <td>0.0145</td>\n", "      <td>0.500</td>\n", "      <td>0.500</td>\n", "      <td>0.475</td>\n", "      <td>0.690</td>\n", "      <td>0.690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Leg</th>\n", "      <td>KJC-MMAL</td>\n", "      <td>0.0465</td>\n", "      <td>0.433</td>\n", "      <td>0.567</td>\n", "      <td>0.302</td>\n", "      <td>0.528</td>\n", "      <td>0.643</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Thigh</th>\n", "      <td>GTR-KJC</td>\n", "      <td>0.1000</td>\n", "      <td>0.433</td>\n", "      <td>0.567</td>\n", "      <td>0.323</td>\n", "      <td>0.540</td>\n", "      <td>0.653</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Head neck</th>\n", "      <td>C7T1-RIB1EAR</td>\n", "      <td>0.0810</td>\n", "      <td>1.000</td>\n", "      <td>0.000</td>\n", "      <td>0.495</td>\n", "      <td>0.116</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>C7T1-T12L1-DIAP</td>\n", "      <td>0.2160</td>\n", "      <td>0.820</td>\n", "      <td>0.180</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Abdomen</th>\n", "      <td>T12L1-GTR</td>\n", "      <td>0.1390</td>\n", "      <td>0.440</td>\n", "      <td>0.560</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>L4L5-GTR</td>\n", "      <td>0.1420</td>\n", "      <td>0.105</td>\n", "      <td>0.895</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk</th>\n", "      <td>GTR-SJC</td>\n", "      <td>0.4970</td>\n", "      <td>0.500</td>\n", "      <td>0.500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk head neck</th>\n", "      <td>GTR-SJC</td>\n", "      <td>0.5780</td>\n", "      <td>0.660</td>\n", "      <td>0.340</td>\n", "      <td>0.503</td>\n", "      <td>0.830</td>\n", "      <td>0.607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HAT</th>\n", "      <td>GTR-SJC</td>\n", "      <td>0.6780</td>\n", "      <td>0.626</td>\n", "      <td>0.374</td>\n", "      <td>0.496</td>\n", "      <td>0.798</td>\n", "      <td>0.621</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Foot leg</th>\n", "      <td>KJC-MMAL</td>\n", "      <td>0.0610</td>\n", "      <td>0.606</td>\n", "      <td>0.394</td>\n", "      <td>0.416</td>\n", "      <td>0.735</td>\n", "      <td>0.572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Total leg</th>\n", "      <td>GTR-MMAL</td>\n", "      <td>0.1610</td>\n", "      <td>0.447</td>\n", "      <td>0.553</td>\n", "      <td>0.326</td>\n", "      <td>0.560</td>\n", "      <td>0.650</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> abdomen</th>\n", "      <td>C7T1-L4L5</td>\n", "      <td>0.3550</td>\n", "      <td>0.630</td>\n", "      <td>0.370</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON> pelvis</th>\n", "      <td>T12L1-GTR</td>\n", "      <td>0.2810</td>\n", "      <td>0.270</td>\n", "      <td>0.730</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-eb3050d8-e30c-4dbd-b430-9f276f888d53')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-eb3050d8-e30c-4dbd-b430-9f276f888d53 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-eb3050d8-e30c-4dbd-b430-9f276f888d53');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-6a6a2462-eb3c-49f1-b7f7-d2d0a07619c0\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-6a6a2462-eb3c-49f1-b7f7-d2d0a07619c0')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-6a6a2462-eb3c-49f1-b7f7-d2d0a07619c0 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "bsp_D", "summary": "{\n  \"name\": \"bsp_<PERSON>\",\n  \"rows\": 19,\n  \"fields\": [\n    {\n      \"column\": \"Segment\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 19,\n        \"samples\": [\n          \"Hand\",\n          \"Foot\",\n          \"<PERSON>elvis\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Definition\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 14,\n        \"samples\": [\n          \"T12L1-GTR\",\n          \"GTR-SJC\",\n          \"WJC-KNU2\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Mass\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.20413464840237822,\n        \"min\": 0.006,\n        \"max\": 0.678,\n        \"num_unique_values\": 19,\n        \"samples\": [\n          0.006,\n          0.0145,\n          0.142\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"CM prox\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.19339460614042608,\n        \"min\": 0.105,\n        \"max\": 1.0,\n        \"num_unique_values\": 17,\n        \"samples\": [\n          0.506,\n          0.43,\n          0.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"CM dist\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.19339460614042608,\n        \"min\": 0.0,\n        \"max\": 0.895,\n        \"num_unique_values\": 17,\n        \"samples\": [\n          0.494,\n          0.57,\n          0.5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg CM\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.08496356685254976,\n        \"min\": 0.297,\n        \"max\": 0.503,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          0.416,\n          0.503,\n          0.297\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg prox\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.18807118128721695,\n        \"min\": 0.116,\n        \"max\": 0.83,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          0.735,\n          0.83,\n          0.587\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg dist\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.03889340522115505,\n        \"min\": 0.565,\n        \"max\": 0.69,\n        \"num_unique_values\": 12,\n        \"samples\": [\n          0.572,\n          0.621,\n          0.577\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}], "source": ["bsp_D = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data/BSP_DempsterWinter.txt', index_col=0, sep='\\t')\n", "display(Latex('\\\\text{BSP values from Dempster\\'s model adapted by Winter (2009):}'))\n", "display(bsp_D)"]}, {"cell_type": "markdown", "metadata": {"id": "rfzE36uAO6af"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>'s model adjusted by <PERSON>\n", "\n", "The segments defined in the <PERSON><PERSON><PERSON><PERSON>'s model (<PERSON><PERSON><PERSON><PERSON>, 2002) adjusted by <PERSON> (1996) are illustrated in the next figure.\n", "\n", "<figure><center><img src='https://github.com/BMClab/BMC/blob/master/images/BSP_ZdeLeva.png?raw=1' alt='Zatsiorsky anthropometric model'/></center><figcaption><center><i>Figure. Segment definition employed in the anthropometric model of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>, 2002) adjusted by <PERSON> (1996).<br>Image from a <a href=\"http://motionanalysis.com/\" target=\"_blank\">Motion Analysis Corporation</a> manual.</i></center></figcaption></figure>"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.865379Z", "start_time": "2020-04-17T14:59:00.855765Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 909}, "id": "sWuJJzMBO6af", "outputId": "f1a73b18-db42-44eb-f10f-d6d70b59b75a"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\text{BSP landmarks from <PERSON><PERSON><PERSON><PERSON>'s model adjusted by <PERSON> (1996):}"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["        Landmark Name Abbreviation  \\\n", "0            Acromion         ACRO   \n", "1          Acropodion         TTIP   \n", "2   Bispinous breadth           BB   \n", "3           Cervicale         CERV   \n", "4     Dactylion (3rd)         DAC3   \n", "5              Gonion         GONI   \n", "6         Iliospinale         ASIS   \n", "7            Malleoli   MMAL, LMAL   \n", "8   Metacarpale (3rd)         MET3   \n", "9          Mid-gonion         MIDG   \n", "10            Mid-hip         MIDH   \n", "11       Mid-shoulder         MIDS   \n", "12          Omphalion         OMPH   \n", "13           Pternion         HEEL   \n", "14            Radiale         RADI   \n", "15   Sphyrion (tibia)         TSPH   \n", "16  Sphyrion fibulare         FSPH   \n", "17            Stylion         RSTY   \n", "18      Suprasternale         SUPR   \n", "19   Tibiale (medial)         MTIB   \n", "20   Tibiale laterale         LTIB   \n", "21      Trochanterion         TROC   \n", "22             Vertex         VERT   \n", "23            Xiphion         XYPH   \n", "\n", "                                                                      Description  \n", "0   Most lateral point on the lateral edge of the acromial process of the scapula  \n", "1                                                              Tip of longest toe  \n", "2                                                       Distance between two ASIS  \n", "3                          Superior tip of the spine of the 7th certical vertebra  \n", "4                                                               Tip of 3 rd digit  \n", "5                           Most lateral point on the posterior angle of mandible  \n", "6                     Inferior point of one of the anterior superior iliac spines  \n", "7                            Medial and lateral bony projections of the malleolus  \n", "8     Distal palpable point on the metacarpal of the 3rd digit on the dorsal hand  \n", "9                                                   Point midway between 2 gonion  \n", "10                                       Point midway between 2 hip joint centers  \n", "11                                  Point midway between 2 shoulder joint centers  \n", "12                                                                Center of navel  \n", "13                                                    <PERSON><PERSON><PERSON> point of the heel  \n", "14                                 Lateral tip on the proximal head of the radius  \n", "15                           Distal tip of the tibia – distal to medial malleolus  \n", "16                         Distal tip of the fibula – distal to lateral malleolus  \n", "17                                Distal dip of the styloid process of the radius  \n", "18                          Most caudal point on the jugular notch on the sternum  \n", "19     Most proximal point on the medial superior border of the head of the tibia  \n", "20   Most proximal point on the lateral superior border of the head of the fibula  \n", "21                         Superior border on the greater trochanter of the femur  \n", "22                                                     Uppermost part of the head  \n", "23                                                   Lowermost end of the sternum  "], "text/html": ["\n", "  <div id=\"df-510bade2-9f65-4548-b09c-b1ea8f1454b7\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Landmark Name</th>\n", "      <th>Abbreviation</th>\n", "      <th>Description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A<PERSON>romion</td>\n", "      <td>ACRO</td>\n", "      <td>Most lateral point on the lateral edge of the acromial process of the scapula</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Acropodion</td>\n", "      <td>TTIP</td>\n", "      <td>Tip of longest toe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bispinous breadth</td>\n", "      <td>BB</td>\n", "      <td>Distance between two ASIS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Cervicale</td>\n", "      <td>CERV</td>\n", "      <td>Superior tip of the spine of the 7th certical vertebra</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON> (3rd)</td>\n", "      <td>DAC3</td>\n", "      <td>Tip of 3 rd digit</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Gonion</td>\n", "      <td>GONI</td>\n", "      <td>Most lateral point on the posterior angle of mandible</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>ASIS</td>\n", "      <td>Inferior point of one of the anterior superior iliac spines</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>MMAL, LMAL</td>\n", "      <td>Medial and lateral bony projections of the malleolus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Met<PERSON><PERSON><PERSON> (3rd)</td>\n", "      <td>MET3</td>\n", "      <td>Distal palpable point on the metacarpal of the 3rd digit on the dorsal hand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Mid-gonion</td>\n", "      <td>MIDG</td>\n", "      <td>Point midway between 2 gonion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Mid-hip</td>\n", "      <td>MIDH</td>\n", "      <td>Point midway between 2 hip joint centers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Mid-shoulder</td>\n", "      <td>MIDS</td>\n", "      <td>Point midway between 2 shoulder joint centers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>OMPH</td>\n", "      <td>Center of navel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Pternion</td>\n", "      <td>HEEL</td>\n", "      <td><PERSON><PERSON><PERSON> point of the heel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Radiale</td>\n", "      <td>RADI</td>\n", "      <td>Lateral tip on the proximal head of the radius</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td><PERSON><PERSON><PERSON> (tibia)</td>\n", "      <td>TSPH</td>\n", "      <td>Distal tip of the tibia – distal to medial malleolus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Sphyrion fibulare</td>\n", "      <td>FSPH</td>\n", "      <td>Distal tip of the fibula – distal to lateral malleolus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>RSTY</td>\n", "      <td>Distal dip of the styloid process of the radius</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Suprasternale</td>\n", "      <td>SUPR</td>\n", "      <td>Most caudal point on the jugular notch on the sternum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Tibi<PERSON> (medial)</td>\n", "      <td>MTIB</td>\n", "      <td>Most proximal point on the medial superior border of the head of the tibia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Tibiale laterale</td>\n", "      <td>LTIB</td>\n", "      <td>Most proximal point on the lateral superior border of the head of the fibula</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Trochanterion</td>\n", "      <td>TROC</td>\n", "      <td>Superior border on the greater trochanter of the femur</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Vertex</td>\n", "      <td>VERT</td>\n", "      <td>Uppermost part of the head</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Xiphi<PERSON></td>\n", "      <td>XYPH</td>\n", "      <td>Lowermost end of the sternum</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-510bade2-9f65-4548-b09c-b1ea8f1454b7')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-510bade2-9f65-4548-b09c-b1ea8f1454b7 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-510bade2-9f65-4548-b09c-b1ea8f1454b7');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-cc217688-c166-477b-8e96-e5c866fbe5a6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cc217688-c166-477b-8e96-e5c866fbe5a6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-cc217688-c166-477b-8e96-e5c866fbe5a6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "BSP_Zmarks", "summary": "{\n  \"name\": \"BSP_Zmarks\",\n  \"rows\": 24,\n  \"fields\": [\n    {\n      \"column\": \"Landmark Name\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 24,\n        \"samples\": [\n          \"Metacarpale (3rd)\",\n          \"Sphyrion fibulare\",\n          \"Acromion\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Abbreviation\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 24,\n        \"samples\": [\n          \"MET3\",\n          \"FSPH\",\n          \"ACRO\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Description\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 24,\n        \"samples\": [\n          \"Distal palpable point on the metacarpal of the 3rd digit on the dorsal hand\",\n          \"Distal tip of the fibula \\u2013 distal to lateral malleolus\",\n          \"Most lateral point on the lateral edge of the acromial process of the scapula\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}], "source": ["BSP_Zmarks = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data/BSPlandmarks_ZdeLeva.txt', sep='\\t')\n", "display(Latex('\\\\text{BSP landmarks from <PERSON><PERSON>iorsky\\'s model adjusted by <PERSON> (1996):}'))\n", "display(BSP_Zmarks)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.877695Z", "start_time": "2020-04-17T14:59:00.866310Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 759}, "id": "eZC7ChNLO6ag", "outputId": "367a2217-984a-4193-b8ff-fc1d2b369d6b"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\text{BSP female values from <PERSON><PERSON><PERSON><PERSON>'s model adjusted by <PERSON> (1996):}"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["                Endpoints    Mass  CM Long  Rg Sag  Rg Trans  Rg Long\n", "Segment                                                              \n", "Head            VERT-MIDG  0.0668   0.5894   0.330     0.359    0.318\n", "Trunk           SUPR-MIDH  0.4257   0.4151   0.357     0.339    0.171\n", "Upper trunk     SUPR-XYPH  0.1545   0.2077   0.746     0.502    0.718\n", "Middle trunk    XYPH-OMPH  0.1465   0.4512   0.433     0.354    0.415\n", "Lower trunk    OMPH-MIDH   0.1247   0.4920   0.433     0.402    0.444\n", "Upper arm         SJC-EJC  0.0255   0.5754   0.278     0.260    0.148\n", "Forearm           EJC-WJC  0.0138   0.4559   0.261     0.257    0.094\n", "Hand             WJC-MET3  0.0056   0.7474   0.531     0.454    0.335\n", "Thigh             HJC-KJC  0.1478   0.3612   0.369     0.364    0.162\n", "Shank             KJC-AJC  0.0481   0.4352   0.267     0.263    0.092\n", "Foot            HEEL-TTIP  0.0129   0.4014   0.299     0.279    0.139\n", "Head 2          VERT-CERV  0.0668   0.4841   0.271     0.295    0.261\n", "Trunk 2         CERV-MIDH  0.4257   0.4964   0.307     0.292    0.147\n", "Trunk 3         MIDS-MIDH  0.4257   0.3782   0.379     0.361    0.182\n", "Upper trunk 2   CERV-XYPH  0.1545   0.5050   0.466     0.314    0.449\n", "Forearm 2       EJC-STYL   0.0138   0.4592   0.263     0.259    0.095\n", "Hand 2           WJC-DAC3  0.0056   0.3427   0.244     0.208    0.154\n", "Hand 3          STYL-DAC3  0.0056   0.3502   0.241     0.206    0.152\n", "Hand 4          STYL-MET3  0.0056   0.7534   0.519     0.443    0.327\n", "Shank 2          KJC-LMAL  0.0481   0.4416   0.271     0.267    0.093\n", "Shank 3          KJC-SPHY  0.0481   0.4481   0.275     0.271    0.094"], "text/html": ["\n", "  <div id=\"df-71c599c8-745b-456f-8d5b-6a54fd7a0b8e\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Endpoints</th>\n", "      <th>Mass</th>\n", "      <th><PERSON><PERSON> Long</th>\n", "      <th>Rg Sag</th>\n", "      <th>Rg Trans</th>\n", "      <th>Rg Long</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Segment</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Head</th>\n", "      <td>VERT-MIDG</td>\n", "      <td>0.0668</td>\n", "      <td>0.5894</td>\n", "      <td>0.330</td>\n", "      <td>0.359</td>\n", "      <td>0.318</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk</th>\n", "      <td>SUPR-MIDH</td>\n", "      <td>0.4257</td>\n", "      <td>0.4151</td>\n", "      <td>0.357</td>\n", "      <td>0.339</td>\n", "      <td>0.171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper trunk</th>\n", "      <td>SUPR-XYPH</td>\n", "      <td>0.1545</td>\n", "      <td>0.2077</td>\n", "      <td>0.746</td>\n", "      <td>0.502</td>\n", "      <td>0.718</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Middle trunk</th>\n", "      <td>XYPH-OMPH</td>\n", "      <td>0.1465</td>\n", "      <td>0.4512</td>\n", "      <td>0.433</td>\n", "      <td>0.354</td>\n", "      <td>0.415</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lower trunk</th>\n", "      <td>OMPH-MIDH</td>\n", "      <td>0.1247</td>\n", "      <td>0.4920</td>\n", "      <td>0.433</td>\n", "      <td>0.402</td>\n", "      <td>0.444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper arm</th>\n", "      <td>SJC-EJC</td>\n", "      <td>0.0255</td>\n", "      <td>0.5754</td>\n", "      <td>0.278</td>\n", "      <td>0.260</td>\n", "      <td>0.148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm</th>\n", "      <td>EJC-WJC</td>\n", "      <td>0.0138</td>\n", "      <td>0.4559</td>\n", "      <td>0.261</td>\n", "      <td>0.257</td>\n", "      <td>0.094</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand</th>\n", "      <td>WJC-MET3</td>\n", "      <td>0.0056</td>\n", "      <td>0.7474</td>\n", "      <td>0.531</td>\n", "      <td>0.454</td>\n", "      <td>0.335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Thigh</th>\n", "      <td>HJC-KJC</td>\n", "      <td>0.1478</td>\n", "      <td>0.3612</td>\n", "      <td>0.369</td>\n", "      <td>0.364</td>\n", "      <td>0.162</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>KJC-AJC</td>\n", "      <td>0.0481</td>\n", "      <td>0.4352</td>\n", "      <td>0.267</td>\n", "      <td>0.263</td>\n", "      <td>0.092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Foot</th>\n", "      <td>HEEL-TTIP</td>\n", "      <td>0.0129</td>\n", "      <td>0.4014</td>\n", "      <td>0.299</td>\n", "      <td>0.279</td>\n", "      <td>0.139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Head 2</th>\n", "      <td>VERT-CERV</td>\n", "      <td>0.0668</td>\n", "      <td>0.4841</td>\n", "      <td>0.271</td>\n", "      <td>0.295</td>\n", "      <td>0.261</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk 2</th>\n", "      <td>CERV-MIDH</td>\n", "      <td>0.4257</td>\n", "      <td>0.4964</td>\n", "      <td>0.307</td>\n", "      <td>0.292</td>\n", "      <td>0.147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk 3</th>\n", "      <td>MIDS-MIDH</td>\n", "      <td>0.4257</td>\n", "      <td>0.3782</td>\n", "      <td>0.379</td>\n", "      <td>0.361</td>\n", "      <td>0.182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper trunk 2</th>\n", "      <td>CERV-XYPH</td>\n", "      <td>0.1545</td>\n", "      <td>0.5050</td>\n", "      <td>0.466</td>\n", "      <td>0.314</td>\n", "      <td>0.449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm 2</th>\n", "      <td>EJC-STYL</td>\n", "      <td>0.0138</td>\n", "      <td>0.4592</td>\n", "      <td>0.263</td>\n", "      <td>0.259</td>\n", "      <td>0.095</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand 2</th>\n", "      <td>WJC-DAC3</td>\n", "      <td>0.0056</td>\n", "      <td>0.3427</td>\n", "      <td>0.244</td>\n", "      <td>0.208</td>\n", "      <td>0.154</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand 3</th>\n", "      <td>STYL-DAC3</td>\n", "      <td>0.0056</td>\n", "      <td>0.3502</td>\n", "      <td>0.241</td>\n", "      <td>0.206</td>\n", "      <td>0.152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand 4</th>\n", "      <td>STYL-MET3</td>\n", "      <td>0.0056</td>\n", "      <td>0.7534</td>\n", "      <td>0.519</td>\n", "      <td>0.443</td>\n", "      <td>0.327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shank 2</th>\n", "      <td>KJC-LMAL</td>\n", "      <td>0.0481</td>\n", "      <td>0.4416</td>\n", "      <td>0.271</td>\n", "      <td>0.267</td>\n", "      <td>0.093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shank 3</th>\n", "      <td>KJC-SPHY</td>\n", "      <td>0.0481</td>\n", "      <td>0.4481</td>\n", "      <td>0.275</td>\n", "      <td>0.271</td>\n", "      <td>0.094</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-71c599c8-745b-456f-8d5b-6a54fd7a0b8e')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-71c599c8-745b-456f-8d5b-6a54fd7a0b8e button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-71c599c8-745b-456f-8d5b-6a54fd7a0b8e');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-3e334e59-13d0-4d5f-80de-bf580001e4e6\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-3e334e59-13d0-4d5f-80de-bf580001e4e6')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-3e334e59-13d0-4d5f-80de-bf580001e4e6 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "bsp_Zf", "summary": "{\n  \"name\": \"bsp_Zf\",\n  \"rows\": 21,\n  \"fields\": [\n    {\n      \"column\": \"Segment\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 21,\n        \"samples\": [\n          \"Head\",\n          \"Hand 3\",\n          \"Forearm 2\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Endpoints\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 21,\n        \"samples\": [\n          \"VERT-MIDG\",\n          \"STYL-DAC3\",\n          \"EJC-STYL \"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Mass\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.14136122489835004,\n        \"min\": 0.0056,\n        \"max\": 0.4257,\n        \"num_unique_values\": 11,\n        \"samples\": [\n          0.0255,\n          0.0668,\n          0.0481\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"CM Long\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.12558807325083518,\n        \"min\": 0.2077,\n        \"max\": 0.7534,\n        \"num_unique_values\": 21,\n        \"samples\": [\n          0.5894,\n          0.3502,\n          0.4592\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg Sag\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.12620240734252108,\n        \"min\": 0.241,\n        \"max\": 0.746,\n        \"num_unique_values\": 19,\n        \"samples\": [\n          0.33,\n          0.261,\n          0.307\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg Trans\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.07998842178120294,\n        \"min\": 0.206,\n        \"max\": 0.502,\n        \"num_unique_values\": 21,\n        \"samples\": [\n          0.359,\n          0.206,\n          0.259\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg Long\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.16297836549385203,\n        \"min\": 0.092,\n        \"max\": 0.718,\n        \"num_unique_values\": 20,\n        \"samples\": [\n          0.318,\n          0.152,\n          0.095\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}], "source": ["bsp_Zf = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data//BSPfemale_ZdeLeva.txt', index_col=0, sep='\\t')\n", "display(Latex('\\\\text{BSP female values from <PERSON><PERSON><PERSON><PERSON>\\'s model adjusted by <PERSON> (1996):}'))\n", "display(bsp_Zf)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.891167Z", "start_time": "2020-04-17T14:59:00.878782Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 759}, "id": "V1K_dp87O6ag", "outputId": "572bef5a-8298-4196-ef5a-16c27de4cef5"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\text{BSP male values from <PERSON><PERSON><PERSON><PERSON>'s model adjusted by <PERSON> (1996):}"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["               Endpoints    Mass  CM Long  Rg Sag  Rg Trans  Rg Long\n", "Segment                                                             \n", "Head           VERT-MIDG  0.0694   0.5976   0.362     0.376    0.312\n", "Trunk          SUPR-MIDH  0.4346   0.4486   0.372     0.347    0.191\n", "Upper trunk    SUPR-XYPH  0.1596   0.2999   0.716     0.454    0.659\n", "Middle trunk   XYPH-OMPH  0.1633   0.4502   0.482     0.383    0.468\n", "Lower trunk    OMPH-MIDH  0.1117   0.6115   0.615     0.551    0.587\n", "Upper arm        SJC-EJC  0.0271   0.5772   0.285     0.269    0.158\n", "Forearm          EJC-WJC  0.0162   0.4574   0.276     0.265    0.121\n", "Hand            WJC-MET3  0.0061   0.7900   0.628     0.513    0.401\n", "Thigh            HJC-KJC  0.1416   0.4095   0.329     0.329    0.149\n", "Shank            KJC-AJC  0.0433   0.4395   0.251     0.246    0.102\n", "Foot           HEEL-TTIP  0.0137   0.4415   0.257     0.245    0.124\n", "Head 2         VERT-CERV  0.0694   0.5002   0.303     0.315    0.261\n", "Trunk 2        CERV-MIDH  0.4346   0.5138   0.328     0.306    0.169\n", "Trunk 3        MIDS-MIDH  0.4346   0.4310   0.384     0.358    0.197\n", "Upper trunk 2  CERV-XYPH  0.1596   0.5066   0.505     0.320    0.465\n", "Forearm 2       EJC-STYL  0.0162   0.4608   0.278     0.267    0.122\n", "Hand 2          WJC-DAC3  0.0061   0.3624   0.288     0.235    0.184\n", "Hand 3         STYL-DAC3  0.0061   0.3691   0.285     0.233    0.182\n", "Hand 4         STYL-MET3  0.0061   0.7948   0.614     0.502    0.392\n", "Shank 2         KJC-LMAL  0.0433   0.4459   0.255     0.249    0.103\n", "Shank 3         KJC-SPHY  0.0433   0.4524   0.258     0.253    0.105"], "text/html": ["\n", "  <div id=\"df-8611aa8e-4f0f-47a5-9c48-963061dfd7cc\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Endpoints</th>\n", "      <th>Mass</th>\n", "      <th><PERSON><PERSON> Long</th>\n", "      <th>Rg Sag</th>\n", "      <th>Rg Trans</th>\n", "      <th>Rg Long</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Segment</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Head</th>\n", "      <td>VERT-MIDG</td>\n", "      <td>0.0694</td>\n", "      <td>0.5976</td>\n", "      <td>0.362</td>\n", "      <td>0.376</td>\n", "      <td>0.312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk</th>\n", "      <td>SUPR-MIDH</td>\n", "      <td>0.4346</td>\n", "      <td>0.4486</td>\n", "      <td>0.372</td>\n", "      <td>0.347</td>\n", "      <td>0.191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper trunk</th>\n", "      <td>SUPR-XYPH</td>\n", "      <td>0.1596</td>\n", "      <td>0.2999</td>\n", "      <td>0.716</td>\n", "      <td>0.454</td>\n", "      <td>0.659</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Middle trunk</th>\n", "      <td>XYPH-OMPH</td>\n", "      <td>0.1633</td>\n", "      <td>0.4502</td>\n", "      <td>0.482</td>\n", "      <td>0.383</td>\n", "      <td>0.468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lower trunk</th>\n", "      <td>OMPH-MIDH</td>\n", "      <td>0.1117</td>\n", "      <td>0.6115</td>\n", "      <td>0.615</td>\n", "      <td>0.551</td>\n", "      <td>0.587</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper arm</th>\n", "      <td>SJC-EJC</td>\n", "      <td>0.0271</td>\n", "      <td>0.5772</td>\n", "      <td>0.285</td>\n", "      <td>0.269</td>\n", "      <td>0.158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm</th>\n", "      <td>EJC-WJC</td>\n", "      <td>0.0162</td>\n", "      <td>0.4574</td>\n", "      <td>0.276</td>\n", "      <td>0.265</td>\n", "      <td>0.121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand</th>\n", "      <td>WJC-MET3</td>\n", "      <td>0.0061</td>\n", "      <td>0.7900</td>\n", "      <td>0.628</td>\n", "      <td>0.513</td>\n", "      <td>0.401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Thigh</th>\n", "      <td>HJC-KJC</td>\n", "      <td>0.1416</td>\n", "      <td>0.4095</td>\n", "      <td>0.329</td>\n", "      <td>0.329</td>\n", "      <td>0.149</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>KJC-AJC</td>\n", "      <td>0.0433</td>\n", "      <td>0.4395</td>\n", "      <td>0.251</td>\n", "      <td>0.246</td>\n", "      <td>0.102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Foot</th>\n", "      <td>HEEL-TTIP</td>\n", "      <td>0.0137</td>\n", "      <td>0.4415</td>\n", "      <td>0.257</td>\n", "      <td>0.245</td>\n", "      <td>0.124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Head 2</th>\n", "      <td>VERT-CERV</td>\n", "      <td>0.0694</td>\n", "      <td>0.5002</td>\n", "      <td>0.303</td>\n", "      <td>0.315</td>\n", "      <td>0.261</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk 2</th>\n", "      <td>CERV-MIDH</td>\n", "      <td>0.4346</td>\n", "      <td>0.5138</td>\n", "      <td>0.328</td>\n", "      <td>0.306</td>\n", "      <td>0.169</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk 3</th>\n", "      <td>MIDS-MIDH</td>\n", "      <td>0.4346</td>\n", "      <td>0.4310</td>\n", "      <td>0.384</td>\n", "      <td>0.358</td>\n", "      <td>0.197</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper trunk 2</th>\n", "      <td>CERV-XYPH</td>\n", "      <td>0.1596</td>\n", "      <td>0.5066</td>\n", "      <td>0.505</td>\n", "      <td>0.320</td>\n", "      <td>0.465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm 2</th>\n", "      <td>EJC-STYL</td>\n", "      <td>0.0162</td>\n", "      <td>0.4608</td>\n", "      <td>0.278</td>\n", "      <td>0.267</td>\n", "      <td>0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand 2</th>\n", "      <td>WJC-DAC3</td>\n", "      <td>0.0061</td>\n", "      <td>0.3624</td>\n", "      <td>0.288</td>\n", "      <td>0.235</td>\n", "      <td>0.184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand 3</th>\n", "      <td>STYL-DAC3</td>\n", "      <td>0.0061</td>\n", "      <td>0.3691</td>\n", "      <td>0.285</td>\n", "      <td>0.233</td>\n", "      <td>0.182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand 4</th>\n", "      <td>STYL-MET3</td>\n", "      <td>0.0061</td>\n", "      <td>0.7948</td>\n", "      <td>0.614</td>\n", "      <td>0.502</td>\n", "      <td>0.392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shank 2</th>\n", "      <td>KJC-LMAL</td>\n", "      <td>0.0433</td>\n", "      <td>0.4459</td>\n", "      <td>0.255</td>\n", "      <td>0.249</td>\n", "      <td>0.103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shank 3</th>\n", "      <td>KJC-SPHY</td>\n", "      <td>0.0433</td>\n", "      <td>0.4524</td>\n", "      <td>0.258</td>\n", "      <td>0.253</td>\n", "      <td>0.105</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-8611aa8e-4f0f-47a5-9c48-963061dfd7cc')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-8611aa8e-4f0f-47a5-9c48-963061dfd7cc button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-8611aa8e-4f0f-47a5-9c48-963061dfd7cc');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-5314bcea-d364-4db1-8e2a-442acc7bfb7c\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-5314bcea-d364-4db1-8e2a-442acc7bfb7c')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-5314bcea-d364-4db1-8e2a-442acc7bfb7c button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "bsp_Zm", "summary": "{\n  \"name\": \"bsp_Zm\",\n  \"rows\": 21,\n  \"fields\": [\n    {\n      \"column\": \"Segment\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 21,\n        \"samples\": [\n          \"Head\",\n          \"Hand 3\",\n          \"Forearm 2\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Endpoints\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 21,\n        \"samples\": [\n          \"VERT-MIDG\",\n          \"STYL-DAC3\",\n          \"EJC-STYL\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Mass\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.14456614518390304,\n        \"min\": 0.0061,\n        \"max\": 0.4346,\n        \"num_unique_values\": 11,\n        \"samples\": [\n          0.0271,\n          0.0694,\n          0.0433\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"CM Long\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.12410329626104678,\n        \"min\": 0.2999,\n        \"max\": 0.7948,\n        \"num_unique_values\": 21,\n        \"samples\": [\n          0.5976,\n          0.3691,\n          0.4608\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg Sag\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.1469126724735934,\n        \"min\": 0.251,\n        \"max\": 0.716,\n        \"num_unique_values\": 20,\n        \"samples\": [\n          0.362,\n          0.614,\n          0.278\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg Trans\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.09771330756959605,\n        \"min\": 0.233,\n        \"max\": 0.551,\n        \"num_unique_values\": 21,\n        \"samples\": [\n          0.376,\n          0.233,\n          0.267\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Rg Long\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.16934003548791296,\n        \"min\": 0.102,\n        \"max\": 0.659,\n        \"num_unique_values\": 21,\n        \"samples\": [\n          0.312,\n          0.182,\n          0.122\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}], "source": ["bsp_Zm = pd.read_csv('https://raw.githubusercontent.com/BMClab/BMC/master/data/BSPmale_ZdeLeva.txt', index_col=0, sep='\\t')\n", "display(Latex('\\\\text{BSP male values from <PERSON><PERSON><PERSON><PERSON>\\'s model adjusted by <PERSON> (1996):}'))\n", "display(bsp_Zm)"]}, {"cell_type": "markdown", "metadata": {"id": "LTTOWhCLO6ah"}, "source": ["### Differences between the anthropometric models from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\n", "\n", "The anthropometric models from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are different in many aspects; regarding the subjetcs investigated in the studies, <PERSON><PERSON><PERSON>'s model is based on the data of 8 cadavers of older male individuals (but two of the individuals were of unknown age) analyzed in the United States. <PERSON><PERSON><PERSON><PERSON>'s model is based on image scanning of 100 young men and 15 young women, at the time all students of a military school in the former Soviet Union.\n", "\n", "The difference between models for some segments is large (see table below): the mass fraction of the thigh segment for <PERSON><PERSON><PERSON><PERSON>'s model is more than 40% larger than for the <PERSON><PERSON><PERSON>'s model, inversely, the trunk segment has about 15% lower mass fraction for <PERSON><PERSON><PERSON><PERSON>'s model. Also, note that some of the  segments don't have the same definition in the two models."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.905358Z", "start_time": "2020-04-17T14:59:00.892343Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 443}, "id": "16KGwsiGO6ah", "outputId": "f64ebcc3-8323-48e9-946d-e5ba3378c7f7"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Latex object>"], "text/latex": "\\text{Mass fraction difference (in %) of <PERSON><PERSON><PERSON><PERSON>'s model w.<PERSON>.<PERSON><PERSON>'s model}"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["              Females  Males\n", "Segment                     \n", "Foot            -11.0   -6.0\n", "Shank             3.0   -7.0\n", "Thigh            48.0   42.0\n", "Lower trunk     -12.0  -21.0\n", "Middle trunk      5.0   17.0\n", "Upper trunk     -28.0  -26.0\n", "Trunk           -14.0  -13.0\n", "Upper arm        -9.0   -3.0\n", "Forearm         -14.0    1.0\n", "Hand             -7.0    2.0\n", "Head            -18.0  -14.0"], "text/html": ["\n", "  <div id=\"df-89fe79d4-1335-446f-a545-b307dfb4b541\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Females</th>\n", "      <th>Males</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Segment</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Foot</th>\n", "      <td>-11.0</td>\n", "      <td>-6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>3.0</td>\n", "      <td>-7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Thigh</th>\n", "      <td>48.0</td>\n", "      <td>42.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lower trunk</th>\n", "      <td>-12.0</td>\n", "      <td>-21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Middle trunk</th>\n", "      <td>5.0</td>\n", "      <td>17.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper trunk</th>\n", "      <td>-28.0</td>\n", "      <td>-26.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Trunk</th>\n", "      <td>-14.0</td>\n", "      <td>-13.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Upper arm</th>\n", "      <td>-9.0</td>\n", "      <td>-3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Forearm</th>\n", "      <td>-14.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Hand</th>\n", "      <td>-7.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Head</th>\n", "      <td>-18.0</td>\n", "      <td>-14.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-89fe79d4-1335-446f-a545-b307dfb4b541')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-89fe79d4-1335-446f-a545-b307dfb4b541 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-89fe79d4-1335-446f-a545-b307dfb4b541');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-2c761e4c-0dbc-48a1-ae4e-2116355c5e6d\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-2c761e4c-0dbc-48a1-ae4e-2116355c5e6d')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-2c761e4c-0dbc-48a1-ae4e-2116355c5e6d button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "d", "summary": "{\n  \"name\": \"d\",\n  \"rows\": 11,\n  \"fields\": [\n    {\n      \"column\": \"Segment\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 11,\n        \"samples\": [\n          \"Upper trunk\",\n          \"Foot\",\n          \"Hand\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Females\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 19.843478434075926,\n        \"min\": -28.0,\n        \"max\": 48.0,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          -7.0,\n          3.0,\n          -28.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Males\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 18.875188138737247,\n        \"min\": -26.0,\n        \"max\": 42.0,\n        \"num_unique_values\": 11,\n        \"samples\": [\n          -26.0,\n          -6.0,\n          2.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}], "source": ["m_D = bsp_D.loc[['Foot', 'Leg', 'Thigh', '<PERSON><PERSON>vis', 'Abdomen', '<PERSON><PERSON>', 'Trunk',\n", "                   'Upper arm', '<PERSON>earm', 'Hand', 'Head neck'], 'Mass']\n", "m_Zf = bsp_Zf.loc[['Foot', 'Shank', 'Thigh', 'Lower trunk', 'Middle trunk', 'Upper trunk',\n", "                   'Trunk', 'Upper arm', 'Forearm', 'Hand', 'Head'], 'Mass']\n", "m_Zm = bsp_Zm.loc[['Foot', 'Shank', 'Thigh', 'Lower trunk', 'Middle trunk', 'Upper trunk',\n", "                   'Trunk', 'Upper arm', 'Forearm', 'Hand', 'Head'], 'Mass']\n", "m_D.index = m_Zf.index  # because of different names for some segments\n", "\n", "display(Latex(\"\\\\text{Mass fraction difference (in %) of <PERSON><PERSON><PERSON><PERSON>'s model w.<PERSON>.<PERSON><PERSON>'s model}\"))\n", "d = pd.DataFrame({'Females': np.around(100 * (m_Zf - m_D) / m_D), \\\n", "                  'Males': np.around(100 * (m_Zm - m_D) / m_D)})\n", "display(d)"]}, {"cell_type": "markdown", "metadata": {"id": "Qv0HiiOaO6ah"}, "source": ["## Center of mass\n", "\n", "See the notebook [Center of Mass and Moment of Inertia](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/CenterOfMassAndMomentOfInertia.ipynb) for a description of center of mass.\n", "\n", "Using the data of the body segment parameters table, the center of mass of a single segment $i$ is (see figure below):\n", "\n", "\\begin{equation}\n", "r_{i} = r_{i,p} + \\text{bsp[i,cmp]} \\cdot (r_{i,d}-r_{i,p})\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $r_{i,p}$ and $\\:r_{i,d}$ are the positions of the proximal and distal landmarks used to define the $i$ segment.   \n", "Note that $r$ is a vector and may have more than one dimension. The equation for the center of mass is valid in each direction and the calculations are performed independently in each direction. In addition, there is no need to include the mass of the segment in the equation above; the mass of the segment is used only when there is more than one segment.\n", "\n", "For example, given the following coordinates ($x, y$) for the MT2, ankle, knee and hip joints:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.910452Z", "start_time": "2020-04-17T14:59:00.907440Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 86}, "id": "2kmMP41ZO6ah", "outputId": "8899fb63-b0cc-4b58-88ea-b2adc98f8197"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["array([[1.011, 0.013],\n", "       [0.849, 0.11 ],\n", "       [0.864, 0.549],\n", "       [0.721, 0.928]])"]}, "metadata": {}}], "source": ["r = np.array([[101.1, 1.3], [84.9, 11.0], [86.4, 54.9], [72.1, 92.8]])/100\n", "display(np.around(r, 3))"]}, {"cell_type": "markdown", "metadata": {"id": "VBVuD6COO6ai"}, "source": ["The position of the center of mass of each segment and of the lower limb are:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:00.918168Z", "start_time": "2020-04-17T14:59:00.911546Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "id": "jRgLCrpxO6ai", "outputId": "33db25a0-ba25-4ef5-d76f-09c40229ddab"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Foot CM:  [0.93  0.062] m\n", "Leg CM:  [0.858 0.359] m\n", "Thigh CM:  [0.783 0.764] m\n", "Lower limb CM:  [0.818 0.584] m\n"]}], "source": ["M = bsp_D.loc[['Foot', 'Leg', 'Thigh'], 'Mass'].sum()\n", "rcm_foot  = r[1] + bsp_D.loc['Foot', 'CM prox']*(r[0]-r[1])\n", "rcm_leg   = r[2] + bsp_D.loc['Leg',  'CM prox']*(r[1]-r[2])\n", "rcm_thigh = r[3] + bsp_D.loc['Thigh','CM prox']*(r[2]-r[3])\n", "rcm = (bsp_<PERSON>.loc['Foot','Mass']*rcm_foot + bsp_D.loc['Leg','Mass']*rcm_leg + \\\n", "       bsp_D.loc['Thigh','Mass']*rcm_thigh)/M\n", "print('Foot CM: ', np.around(rcm_foot, 3), 'm')\n", "print('Leg CM: ', np.around(rcm_leg, 3), 'm')\n", "print('Thigh CM: ', np.around(rcm_thigh, 3), 'm')\n", "print('Lower limb CM: ', np.around(rcm, 3), 'm')"]}, {"cell_type": "markdown", "metadata": {"id": "PGuLMUOAO6ai"}, "source": ["And here is a geometric representation of part of these calculations:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:01.252974Z", "start_time": "2020-04-17T14:59:00.919088Z"}, "colab": {"base_uri": "https://localhost:8080/", "height": 416}, "id": "vl6tfK4aO6ai", "outputId": "9624be78-2f0b-422d-9adc-f073cf04ffe9"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["plt.rc('axes',  labelsize=14, linewidth=1.5)\n", "plt.rc('xtick', labelsize=14)\n", "plt.rc('ytick', labelsize=14)\n", "plt.rc('lines', markersize=8)\n", "hfig, hax = plt.subplots(1, 1, figsize=(10, 5))\n", "\n", "# bones and joints\n", "plt.plot(r[:,0], r[:,1], 'b-')\n", "plt.plot(r[:,0], r[:,1], 'ko', label='joint')\n", "# center of mass of each segment\n", "plt.plot(rcm_foot[0], rcm_foot[1], 'go', label='segment center of mass')\n", "plt.plot(rcm_leg[0], rcm_leg[1], 'go', rcm_thigh[0], rcm_thigh[1], 'go')\n", "# total center of mass\n", "plt.plot(rcm[0], rcm[1], 'ro', label='total center of mass')\n", "hax.legend(frameon=False, loc='upper left', fontsize=12, numpoints=1)\n", "plt.arrow(0, 0, r[3,0], r[3,1], color='b', head_width=0.02, overhang=.5, fc=\"k\", ec=\"k\",\n", "          lw=2, length_includes_head=True)\n", "plt.arrow(r[3,0], r[3,1], rcm_thigh[0] - r [3,0], rcm_thigh[1] - r[3,1], head_width=0.02,\n", "      overhang=.5, fc=\"b\", ec=\"b\", lw=2, length_includes_head=True)\n", "plt.arrow(0, 0, rcm_thigh[0], rcm_thigh[1], head_width=0.02, overhang=.5, fc=\"g\", ec=\"g\",\n", "          lw=2, length_includes_head=True)\n", "plt.text(0.30, .5, '$\\mathbf{r}_{thigh,p}$', rotation=38, fontsize=16)\n", "plt.text(0.77, .85, '$bsp_{thigh,cmp}*(\\mathbf{r}_{i,d}-\\mathbf{r}_{i,p})$',\n", "         fontsize=16, color='b')\n", "plt.text(0.15, .05,\n", "         '$\\mathbf{r}_{thigh,cm}=\\mathbf{r}_{i,p}+bsp_{i,cmp}*' +\n", "         '(\\mathbf{r}_{i,d}-\\mathbf{r}_{i,p})$',\n", "         rotation=25, fontsize=16, color='g')\n", "hax.set_xlim(0,1.1)\n", "hax.set_ylim(0,1.05)\n", "hax.set_xlabel('x [m]')\n", "hax.set_ylabel('y [m]')\n", "hax.set_title('Determination of center of mass', fontsize=16)\n", "hax.grid()"]}, {"cell_type": "markdown", "metadata": {"id": "yUHZnLD2O6aj"}, "source": ["## Moment of inertia\n", "\n", "### Radius of gyration\n", "\n", "See the notebook [Center of Mass and Moment of Inertia](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/CenterOfMassAndMomentOfInertia.ipynb) for a description of moment of inertia and radius of gyration.\n", "\n", "The radius of gyration (as a fraction of the segment length) is the quantity that is given in the table of body segment parameters. Because of that, we don't need to sum each element of mass of the segment to calculate its moment of inertia; we just need to take the mass of the segment times the radius or gyration squared.\n", "\n", "Using the body segment parameters, the moment of inertia of a single segment $i$ rotating around its own center of mass is (see figure below):\n", "\n", "\\begin{equation}\n", "I_{i,cm} = M \\cdot \\text{bsp[i,mass]} \\cdot \\left(\\text{bsp[i,rgcm]} \\cdot ||r_{i,d}-r_{i,p}||\\right)^2\n", "\\label{}\n", "\\end{equation}\n", "\n", "Where $M$ is the total body mass of the subject and $||r_{i,d}-r_{i,p}||$ is the length of the segment $i$.\n", "\n", "For example, the moment of inertia of each segment of the lower limb around each corresponding segment center of mass considering the coordinates (x, y) for the MT2, ankle, knee and hip joints given above are:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:01.259330Z", "start_time": "2020-04-17T14:59:01.254030Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "id": "PsiaeFkZO6aj", "outputId": "e4dc4233-52e4-4ffd-f2b6-0ebb71b17d31"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Icm foot:  0.012 kgm2\n", "Icm leg:  0.082 kgm2\n", "Icm thigh:  0.171 kgm2\n"]}], "source": ["norm = np.linalg.norm\n", "M = 100  # body mass\n", "Icm_foot = M*bsp_D.loc['Foot', 'Mass']*((bsp_D.loc['Foot', 'Rg CM']*norm(r[0]-r[1]))**2)\n", "Icm_leg = M*bsp_D.loc['Leg',  'Mass']*((bsp_D.loc['Leg',  'Rg CM']*norm(r[1]-r[2]))**2)\n", "Icm_thigh = M*bsp_D.loc['Thigh','Mass']*((bsp_D.loc['Thigh','Rg CM']*norm(r[2]-r[3]))**2)\n", "print('Icm foot: ', np.around(Icm_foot, 3), 'kgm2')\n", "print('Icm leg: ', np.around(Icm_leg, 3), 'kgm2')\n", "print('Icm thigh: ', np.around(Icm_thigh, 3), 'kgm2')"]}, {"cell_type": "markdown", "metadata": {"id": "dZDPrYYBO6aj"}, "source": ["### Parallel axis theorem\n", "\n", "See the notebook [Center of Mass and Moment of Inertia](https://nbviewer.jupyter.org/github/BMClab/bmc/blob/master/notebooks/CenterOfMassAndMomentOfInertia.ipynb) for a description of parallel axis theorem.\n", "\n", "For example, using the parallel axis theorem the moment of inertia of the lower limb around its center of mass is:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:01.263507Z", "start_time": "2020-04-17T14:59:01.260262Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "id": "rqxoERv8O6aj", "outputId": "b91e9e43-35db-461c-db01-2eb9242f8c96"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Icm lower limb:  1.257 kgm2\n"]}], "source": ["Icmll = (Icm_foot  + M*bsp_D.loc['Foot', 'Mass']*norm(rcm-rcm_foot )**2 + \\\n", "         Icm_leg   + M*bsp_D.loc['Leg',  'Mass']*norm(rcm-rcm_leg  )**2 + \\\n", "         Icm_thigh + M*bsp_D.loc['Thigh','Mass']*norm(rcm-rcm_thigh)**2)\n", "\n", "print('Icm lower limb: ', np.around(Icmll, 3), 'kgm2')"]}, {"cell_type": "markdown", "metadata": {"id": "fbTGwPR9O6ak"}, "source": ["To calculate the moment of inertia of the lower limb around the hip, we use again the parallel axis theorem:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:01.268640Z", "start_time": "2020-04-17T14:59:01.264370Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "id": "sIN7NKUiO6ak", "outputId": "b3de25d5-6c1a-4fc2-a07b-b3903fc2f9d1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Ihip lower limb:  3.317 kgm2\n"]}], "source": ["Ihipll = (Icm_foot  + M*bsp_D.loc['Foot', 'Mass']*norm(r[3]-rcm_foot )**2 + \\\n", "          Icm_leg   + M*bsp_D.loc['Leg',  'Mass']*norm(r[3]-rcm_leg  )**2 + \\\n", "          Icm_thigh + M*bsp_D.loc['Thigh','Mass']*norm(r[3]-rcm_thigh)**2)\n", "\n", "print('Ihip lower limb: ', np.around(<PERSON><PERSON><PERSON>, 3), 'kgm2')"]}, {"cell_type": "markdown", "metadata": {"id": "aou4S1PwO6ak"}, "source": ["Note that for the correct use of the parallel axis theorem we have to input the moment of inertia around the center of mass of each body. For example, we CAN NOT calculate the moment of inertia around the hip with the moment of inertia of the entire lower limb:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2020-04-17T14:59:01.272960Z", "start_time": "2020-04-17T14:59:01.269498Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "id": "butpDgj8O6ak", "outputId": "edf58c45-8ede-4947-cf46-ed31fb07382c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Icm lower limb:  2.324 kgm2. THIS IS WRONG!\n"]}], "source": ["# THIS IS WRONG:\n", "I = (Icm_foot  + M*bsp_D.loc['Foot', 'Mass']*norm(r[3]-rcm)**2 + \\\n", "     Icm_leg   + M*bsp_D.loc['Leg',  'Mass']*norm(r[3]-rcm)**2 + \\\n", "     Icm_thigh + M*bsp_D.loc['Thigh','Mass']*norm(r[3]-rcm)**2)\n", "\n", "print('Icm lower limb: ', np.around(I, 3), 'kgm2. THIS IS WRONG!')"]}, {"cell_type": "markdown", "metadata": {"id": "VxeLkpVwO6ak"}, "source": ["## Center of buoyancy   \n", "\n", "[Center of buoyancy](https://en.wikipedia.org/wiki/Buoyancy) is the center of the volume of water which the submerged part of an object displaces. Center of buoyancy is to center of volume as center of gravity is to center of mass.  \n", "\n", "For the human body submerged in water, because different parts of the body have different densities, the center of buoyancy is at a different place than the center of gravity, see for example [<PERSON><PERSON> and <PERSON> (2008)](https://onlinelibrary.wiley.com/doi/pdf/10.1002/jst.23).\n", "\n", "<figure><center><img src='https://github.com/BMClab/BMC/blob/master/images/buoyance.png?raw=1' width=400 alt='Center of gravity and center of buoyancy.'/></center><figcaption><center><i>Figure. Forces of gravity and buoyancy acting respectively on the center of gravity and center of buoyancy in a submerged human body (figure from <PERSON><PERSON> and <PERSON> (2008)).</i></center></figcaption></figure>  "]}, {"cell_type": "markdown", "metadata": {"id": "tUEgevq4O6ak"}, "source": ["## Further reading\n", "\n", "- [<PERSON><PERSON> et a. (1964)](http://www.oandplibrary.org/al/1964_01_044.asp) and [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1995)](http://citeseerx.ist.psu.edu/viewdoc/summary?doi=*********.5223) for a historical overview on the estimation of body segment parameters."]}, {"cell_type": "markdown", "metadata": {"id": "dPj94e6nO6al"}, "source": ["## Video lectures on the internet   \n", "\n", "- [Body segments parameters in Biomechanics](https://youtu.be/8hyPiha-lFU)  \n", "- [Estimating and Visualizing the Inertia of the Human Body with Python](https://youtu.be/H9AK65ZY-Vw)"]}, {"cell_type": "markdown", "metadata": {"id": "yYgsOU6SO6al"}, "source": ["## Problems\n", "\n", "1. Take a picture at the frontal plane of somebody standing on one foot on tiptoes with the arms and the other leg abducted at the horizontal.  \n", "  1. Estimate the body center of mass of this person. Hint: for simplicity, consider the center of mass of each segment to be located at the middle of the segment and measure these positions using a image digitizer, e.g., [WebPlotDigitizer](https://automeris.io/WebPlotDigitizer/).  \n", "  2. If the person is almost standing still, through which part of the body a vertical line through the center of mass should necessarily pass? Have you obtained this result? Comment on possible differences between the expected and obtained results.  \n", "\n", "2. Consider the kinematic data from table A.1 of the Winter's book (Winter, 2009) used in problem 2 of the notebook [Angular kinematics in a plane (2D)](http://nbviewer.jupyter.org/github/demotu/BMC/blob/master/notebooks/KinematicsAngular2D.ipynb).  \n", "  1. Calculate the center of mass position for each segment and for the whole body (beware that no data are given for the head and arms segments) using the De<PERSON><PERSON>'s and <PERSON><PERSON><PERSON><PERSON>'s models.  \n", "  2. Perform these calculations also for the moment of inertia (of each segment and of the whole body around the corresponding centers of mass).  \n", "  \n", "3. Consider the following positions of markers placed on a leg (described in the laboratory coordinate system with coordinates $x, y, z$ in cm, the $x$ axis points forward and the $y$ axes points upward): lateral malleolus (**lm** = [2.92, 10.10, 18.85]), medial malleolus (**mm** = [2.71, 10.22, 26.52]), fibular head (**fh** = [5.05, 41.90, 15.41]), and medial condyle (**mc** = [8.29, 41.88, 26.52]). Define the ankle joint center as the centroid between the **lm** and **mm** markers and the knee joint center as the centroid between the **fh** and **mc** markers (same data as in problem 1 of the notebook [Rigid-body transformations (3D)](http://nbviewer.ipython.org/github/demotu/BMC/blob/master/notebooks/Transformation3D.ipynb)). Consider that the principal axes of the leg are aligned with the axes of the respective anatomical coordinate system.  \n", "  1. Determine the center of mass position of the leg at the anatomical and laboratory coordinate systems.  \n", "  2. Determine the inertia tensor of the leg for a rotation around its proximal joint and around its center of mass."]}, {"cell_type": "markdown", "metadata": {"id": "Zf0lya8IO6al"}, "source": ["## References\n", "\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1995) [Estimation of Human Body Segment Parameters - Historical Background](http://citeseerx.ist.psu.edu/viewdoc/summary?doi=*********.5223). Technical Report.  \n", "- de <PERSON>a P (1996) [Adjustments to <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>'s segment inertia parameters](http://ebm.ufabc.edu.br/wp-content/uploads/2013/12/Leva-1996.pdf). Journal of Biomechanics, 29, 9, 1223-1230.  \n", "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> (2014) [An object oriented implementation of the Yeadon human inertia model](https://www.ncbi.nlm.nih.gov/pmc/articles/PMC4329601/). F1000Research. 2014;3:223. doi:10.12688/f1000research.5292.2.  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (1964) [Body segment parameters: a survey of measurement techniques](http://www.oandplibrary.org/al/1964_01_044.asp). Artificial Limbs, 8, 44-66.  \n", "- <PERSON><PERSON>-<PERSON> (1998) [BSP Estimation Methods](http://kwon3d.com/theory/bsp.html).  \n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (2013) [Introduction to Statics and Dynamics](http://ruina.tam.cornell.edu/Book/index.html). Oxford University Press.  \n", "- Winter DA (2009) [Biomechanics and motor control of human movement](http://books.google.com.br/books?id=_bFHL08IWfwC). 4 ed. Hoboken, EUA: Wiley.  \n", "- <PERSON><PERSON>, <PERSON> (2008) [How does buoyancy influence front-crawl performance?\n", "Exploring the assumptions](https://onlinelibrary.wiley.com/doi/pdf/10.1002/jst.23). Sports Technol., 1 2–3, 89–99.  \n", "- <PERSON><PERSON><PERSON><PERSON> VM (2002) [Kinetics of human motion](http://books.google.com.br/books?id=wp3zt7oF8a0C&lpg=PA571&ots=Kjc17DAl19&dq=ZATSIORSKY%2C%20Vladimir%20M.%20Kinetics%20of%20human%20motion&hl=pt-BR&pg=PP1#v=onepage&q&f=false). Champaign, IL: Human Kinetics.  \n", "- Some of the original works on body segment parameters:  \n", "  - <PERSON><PERSON><PERSON> (1972) [Body Segment Parameters, Part II](http://www.oandplibrary.org/al/1972_01_001.asp). Artificial Limbs, 16, 1-19.  \n", "  - <PERSON><PERSON><PERSON> WT (1955) [Space requirements of the seated operator: geometrical, kinematic, and mechanical aspects of the body, with special reference to the limbs](http://deepblue.lib.umich.edu/handle/2027.42/4540). WADC Technical Report 55-159, AD-087-892, Wright-Patterson Air Force Base, Ohio.  \n", "  - Hanavan <PERSON> (1964). [A mathematical model of the human body](http://www.dtic.mil/cgi-bin/GetTRDoc?AD=*********). AMRL-TR-64-102, AD-608-463. Aerospace Medical Research Laboratories, Wright-Patterson Air Force Base, Ohio.  "]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {"height": "244px", "width": "547px"}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": "block", "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}