{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# The minimum jerk hypothesis\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<center><div style=\"background-color:#f2f2f2;border:1px solid black;width:72%;padding:5px 10px 5px 10px;text-align:left;\">\n", "<i>\"Whatever its physiological underpinnings, the real strength of the minimum-jerk criterion function, or indeed any other criterion function, is its use as an organizing principle. The use of variational principles is common in physics and engineering. They are not presented as the cause of the behavior they describe but rather as a distillation of its essence.\"</i> &nbsp; <b>Hogan</b> (1984)</div></center>"]}, {"cell_type": "markdown", "metadata": {"toc": 1}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Python-setup\" data-toc-modified-id=\"Python-setup-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Python setup</a></span></li><li><span><a href=\"#Development\" data-toc-modified-id=\"Development-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Development</a></span></li><li><span><a href=\"#Finding-the-minimum-jerk-trajectory\" data-toc-modified-id=\"Finding-the-minimum-jerk-trajectory-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>Finding the minimum jerk trajectory</a></span></li><li><span><a href=\"#The-angular-trajectory-of-a-minimum-jerk-trajectory\" data-toc-modified-id=\"The-angular-trajectory-of-a-minimum-jerk-trajectory-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>The angular trajectory of a minimum jerk trajectory</a></span></li><li><span><a href=\"#Problems\" data-toc-modified-id=\"Problems-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Problems</a></span></li><li><span><a href=\"#References\" data-toc-modified-id=\"References-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>References</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Python setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:50:48.498170Z", "start_time": "2020-05-03T20:50:47.923775Z"}}, "outputs": [], "source": ["# import necessary libraries and configure environment\n", "import numpy as np\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from IPython.display import display, Math\n", "from sympy import symbols, Matrix, latex, Eq, collect, solve, diff, simplify, init_printing\n", "from sympy.core import S\n", "from sympy.utilities.lambdify import lambdify\n", "init_printing() \n", "sns.set_context('notebook', rc={\"lines.linewidth\": 2})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Development\n", "\n", "<PERSON> and <PERSON> (1984, 1985), based on observations of voluntary movements in primates, suggested that movements are performed (organized) with the smoothest trajectory possible. In this organizing principle, the endpoint trajectory is such that the mean squared-jerk across time of this movement is minimum.   \n", "\n", "Jerk is the derivative of acceleration and the observation of the minimum-jerk trajectory is for the endpoint in the extracorporal coordinates (not for joint angles) and according to <PERSON> <PERSON> (1985), the minimum-jerk trajectory of a planar movement is such that minimizes the following objective function:\n", "\n", "\\begin{equation}\n", "\\begin{array}{rcl}\n", "C = \\frac{1}{2} \\displaystyle\\int\\limits_{t_{i}}^{t_{f}}\\;\\left[\\left(\\dfrac{d^{3}x}{dt^{3}}\\right)^2+\\left(\\dfrac{d^{3}y}{dt^{3}}\\right)^2\\right]\\;\\mathrm{d}t\n", "\\label{}\n", "\\end{array}\n", "\\end{equation}\n", "\n", "<PERSON> (1984) found that the solution for this objective function is a fifth-order polynomial trajectory (see <PERSON><PERSON><PERSON><PERSON> and <PERSON> (2004) for a simpler proof): \n", "\n", "\\begin{equation}\n", "\\begin{array}{l l}\n", "x(t) = a_0+a_1t+a_2t^2+a_3t^3+a_4t^4+a_5t^5 \\\\\n", "y(t) = b_0+b_1t+b_2t^2+b_3t^3+b_4t^4+b_5t^5\n", "\\label{}\n", "\\end{array}\n", "\\end{equation}\n", "\n", "With the following boundary conditions for $ x(t) $ and $ y(t) $: initial and final positions are $ (x_i,y_i) $ and $ (x_f,y_f) $ and initial and final velocities and accelerations are zero."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Finding the minimum jerk trajectory\n", "\n", "Let's employ [Sympy](http://sympy.org/en/index.html) to find the solution for the minimum jerk trajectory using symbolic algebra.  \n", "The equation for minimum jerk trajectory for x is:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:50:50.618975Z", "start_time": "2020-05-03T20:50:50.407000Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle x{\\left(t \\right)} = a_{0} + a_{1} t + a_{2} t^{2} + a_{3} t^{3} + a_{4} t^{4} + a_{5} t^{5}$"], "text/plain": ["                       2       3       4       5\n", "x(t) = a₀ + a₁⋅t + a₂⋅t  + a₃⋅t  + a₄⋅t  + a₅⋅t "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# symbolic variables\n", "x, xi, xf, y, yi, yf, d, t = symbols('x, x_i, x_f, y, y_i, y_f, d, t')\n", "a0, a1, a2, a3, a4, a5 = symbols('a_0:6')\n", "x = a0 + a1*t + a2*t**2 + a3*t**3 + a4*t**4 + a5*t**5\n", "display(Eq(S('x(t)'), x))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Without loss of generality, consider $ t_i=0 $ and let's use $ d $ for movement duration ($ d=t_f $).  \n", "The system of equations with the boundary conditions for $ x $ is:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle a_{0} = x_{i}$"], "text/plain": ["a₀ = xᵢ"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle a_{1} = 0$"], "text/plain": ["a₁ = 0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle 2 a_{2} = 0$"], "text/plain": ["2⋅a₂ = 0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle a_{0} + a_{1} d + a_{2} d^{2} + a_{3} d^{3} + a_{4} d^{4} + a_{5} d^{5} = x_{f}$"], "text/plain": ["                2       3       4       5      \n", "a₀ + a₁⋅d + a₂⋅d  + a₃⋅d  + a₄⋅d  + a₅⋅d  = x_f"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle a_{1} + 2 a_{2} d + 3 a_{3} d^{2} + 4 a_{4} d^{3} + 5 a_{5} d^{4} = 0$"], "text/plain": ["                    2         3         4    \n", "a₁ + 2⋅a₂⋅d + 3⋅a₃⋅d  + 4⋅a₄⋅d  + 5⋅a₅⋅d  = 0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle 2 a_{2} + 6 a_{3} d + 12 a_{4} d^{2} + 20 a_{5} d^{3} = 0$"], "text/plain": ["                       2          3    \n", "2⋅a₂ + 6⋅a₃⋅d + 12⋅a₄⋅d  + 20⋅a₅⋅d  = 0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# define the system of equations\n", "s = [Eq(x.subs(t, 0), xi),\n", "     Eq(diff(x, t, 1).subs(t, 0),  0),\n", "     Eq(diff(x, t, 2).subs(t, 0),  0),\n", "     Eq(x.subs(t, d), xf),\n", "     Eq(diff(x, t, 1).subs(t, d),  0),\n", "     Eq(diff(x, t, 2).subs(t, d),  0)]\n", "[display(si) for si in s];"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Which gives the following solution:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:50:54.435370Z", "start_time": "2020-05-03T20:50:54.304927Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left\\{ a_{0} : x_{i}, \\  a_{1} : 0, \\  a_{2} : 0, \\  a_{3} : \\frac{10 x_{f} - 10 x_{i}}{d^{3}}, \\  a_{4} : \\frac{- 15 x_{f} + 15 x_{i}}{d^{4}}, \\  a_{5} : \\frac{6 x_{f} - 6 x_{i}}{d^{5}}\\right\\}$"], "text/plain": ["⎧                          10⋅x_f - 10⋅xᵢ      -15⋅x_f + 15⋅xᵢ      6⋅x_f - 6⋅xᵢ⎫\n", "⎪a₀: xᵢ, a₁: 0, a₂: 0, a₃: ──────────────, a₄: ───────────────, a₅: ────────────⎪\n", "⎨                                 3                    4                  5     ⎬\n", "⎪                                d                    d                  d      ⎪\n", "⎩                                                                               ⎭"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# algebraically solve the system of equations\n", "sol = solve(s, [a0, a1, a2, a3, a4, a5])\n", "display(sol)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Substituting this solution in the fifth order polynomial trajectory equation, we have the actual displacement trajectories:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:50:56.502720Z", "start_time": "2020-05-03T20:50:56.384170Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle x{\\left(t \\right)} = \\frac{d^{5} x_{i} + \\left(x_{f} - x_{i}\\right) \\left(10 d^{2} t^{3} - 15 d t^{4} + 6 t^{5}\\right)}{d^{5}}$"], "text/plain": ["        5                 ⎛    2  3         4      5⎞\n", "       d ⋅xᵢ + (x_f - xᵢ)⋅⎝10⋅d ⋅t  - 15⋅d⋅t  + 6⋅t ⎠\n", "x(t) = ──────────────────────────────────────────────\n", "                              5                      \n", "                             d                       "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle y{\\left(t \\right)} = \\frac{d^{5} y_{i} + \\left(y_{f} - y_{i}\\right) \\left(10 d^{2} t^{3} - 15 d t^{4} + 6 t^{5}\\right)}{d^{5}}$"], "text/plain": ["        5                 ⎛    2  3         4      5⎞\n", "       d ⋅yᵢ + (y_f - yᵢ)⋅⎝10⋅d ⋅t  - 15⋅d⋅t  + 6⋅t ⎠\n", "y(t) = ──────────────────────────────────────────────\n", "                              5                      \n", "                             d                       "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# substitute the equation parameters by the solution\n", "x2 = x.subs(sol)\n", "x2 = collect(simplify(x2, ratio=1), xf-xi)\n", "display(Eq(S('x(t)'), x2))\n", "y2 = x2.subs([(xi, yi), (xf, yf)])\n", "display(Eq(S('y(t)'), y2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And for the velocity, acceleration, and jerk trajectories in x:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:50:58.180004Z", "start_time": "2020-05-03T20:50:58.136431Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\operatorname{v_{x}}{\\left(t \\right)} = \\frac{\\left(x_{f} - x_{i}\\right) \\left(30 d^{2} t^{2} - 60 d t^{3} + 30 t^{4}\\right)}{d^{5}}$"], "text/plain": ["                   ⎛    2  2         3       4⎞\n", "        (x_f - xᵢ)⋅⎝30⋅d ⋅t  - 60⋅d⋅t  + 30⋅t ⎠\n", "vₓ(t) = ───────────────────────────────────────\n", "                            5                  \n", "                           d                   "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\operatorname{a_{x}}{\\left(t \\right)} = \\frac{60 t \\left(x_{f} - x_{i}\\right) \\left(d^{2} - 3 d t + 2 t^{2}\\right)}{d^{5}}$"], "text/plain": ["                        ⎛ 2              2⎞\n", "        60⋅t⋅(x_f - xᵢ)⋅⎝d  - 3⋅d⋅t + 2⋅t ⎠\n", "aₓ(t) = ───────────────────────────────────\n", "                          5                \n", "                         d                 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle \\operatorname{j_{x}}{\\left(t \\right)} = \\frac{60 \\left(x_{f} - x_{i}\\right) \\left(d^{2} - 6 d t + 6 t^{2}\\right)}{d^{5}}$"], "text/plain": ["                      ⎛ 2              2⎞\n", "        60⋅(x_f - xᵢ)⋅⎝d  - 6⋅d⋅t + 6⋅t ⎠\n", "jₓ(t) = ─────────────────────────────────\n", "                         5               \n", "                        d                "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# symbolic differentiation\n", "vx = x2.diff(t, 1)\n", "display(Eq(S('v_x(t)'), vx))\n", "ax = x2.diff(t, 2)\n", "display(Eq(S('a_x(t)'), ax))\n", "jx = x2.diff(t, 3)\n", "display(Eq(S('j_x(t)'), jx))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's plot the minimum jerk trajectory for x and its velocity, acceleration, and jerk considering $x_i=0,x_f=1,d=1$:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:51:44.741170Z", "start_time": "2020-05-03T20:51:44.166089Z"}, "code_folding": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1000x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# substitute by the numerical values\n", "x3  = x2.subs([(xi, 0), (xf, 1), (d, 1)])\n", "#create functions for calculation of numerical values\n", "xfu = lambdify(t, diff(x3, t, 0), 'numpy')\n", "vfu = lambdify(t, diff(x3, t, 1), 'numpy')\n", "afu = lambdify(t, diff(x3, t, 2), 'numpy')\n", "jfu = lambdify(t, diff(x3, t, 3), 'numpy')\n", "#plots using matplotlib\n", "ts = np.arange(0, 1.01, .01)\n", "fig, axs = plt.subplots(1, 4, figsize=(10, 4), sharex=True)\n", "axs[0].plot(ts, xfu(ts), linewidth=3)\n", "axs[0].set_title('Displacement [$\\mathrm{m}$]')\n", "axs[1].plot(ts, vfu(ts), linewidth=3)\n", "axs[1].set_title('Velocity [$\\mathrm{m/s}$]')\n", "axs[2].plot(ts, afu(ts), linewidth=3)\n", "axs[2].set_title('Acceleration [$\\mathrm{m/s^2}$]')\n", "axs[3].plot(ts, jfu(ts), linewidth=3)\n", "axs[3].set_title('Jerk [$\\mathrm{m/s^3}$]')\n", "\n", "for axi in axs:\n", "    axi.set_xlabel('Time [s]', fontsize=12)\n", "    axi.grid(True)\n", "\n", "fig.suptitle('Minimum jerk trajectory kinematics', fontsize=16)\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that for the minimum jerk trajectory, initial and final values of both velocity and acceleration are zero, but not for the jerk.  \n", "\n", "Read more about the minimum jerk trajectory hypothesis in the [<PERSON><PERSON><PERSON><PERSON> and <PERSON>'s book companion site](https://storage.googleapis.com/wzukusers/user-31382847/documents/5a7253343814f4Iv6Hnt/minimumjerk.pdf) and in [<PERSON>'s website](https://gribblelab.org/teaching/compneuro2012/4_Computational_Motor_Control_Kinematics.html#orgheadline12)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The angular trajectory of a minimum jerk trajectory \n", "\n", "Let's calculate the resulting angular trajectory given a minimum jerk linear trajectory, supposing it is from a circular motion of an elbow flexion. The length of the forearm is 0.5 m, the movement duration is 1 s, the elbow starts flexed at 90$^o$ and the flexes to 180$^o$.\n", "\n", "First, the linear trajectories for this circular motion:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:52:00.401046Z", "start_time": "2020-05-03T20:52:00.328067Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle y{\\left(t \\right)} = - 3.0 t^{5} + 7.5 t^{4} - 5.0 t^{3} + 0.5$"], "text/plain": ["              5        4        3      \n", "y(t) = - 3.0⋅t  + 7.5⋅t  - 5.0⋅t  + 0.5"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/latex": ["$\\displaystyle x{\\left(t \\right)} = 3.0 t^{5} - 7.5 t^{4} + 5.0 t^{3}$"], "text/plain": ["            5        4        3\n", "x(t) = 3.0⋅t  - 7.5⋅t  + 5.0⋅t "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# substitute by the numerical values\n", "x3 = x2.subs([(xi, 0.5), (xf, 0), (d, 1)])\n", "y3 = x2.subs([(xi, 0), (xf, 0.5), (d, 1)])\n", "display(Eq(S('y(t)'), x3))\n", "display(Eq(S('x(t)'), y3))\n", "#create functions for calculation of numerical values\n", "xfux = lambdify(t, diff(x3, t, 0), 'numpy')\n", "vfux = lambdify(t, diff(x3, t, 1), 'numpy')\n", "afux = lambdify(t, diff(x3, t, 2), 'numpy')\n", "jfux = lambdify(t, diff(x3, t, 3), 'numpy')\n", "xfuy = lambdify(t, diff(y3, t, 0), 'numpy')\n", "vfuy = lambdify(t, diff(y3, t, 1), 'numpy')\n", "afuy = lambdify(t, diff(y3, t, 2), 'numpy')\n", "jfuy = lambdify(t, diff(y3, t, 3), 'numpy')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:52:02.941489Z", "start_time": "2020-05-03T20:52:02.549260Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1000x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#plots using matplotlib\n", "ts = np.arange(0, 1.01, .01)\n", "fig, axs = plt.subplots(1, 4, figsize=(10, 4), sharex=True)\n", "axs[0].plot(ts, xfux(ts), 'b', linewidth=3)\n", "axs[0].plot(ts, xfuy(ts), 'r', linewidth=3)\n", "axs[0].set_title('Displacement [$\\mathrm{m}$]')\n", "axs[1].plot(ts, vfux(ts), 'b', linewidth=3)\n", "axs[1].plot(ts, vfuy(ts), 'r', linewidth=3)\n", "axs[1].set_title('Velocity [$\\mathrm{m/s}$]')\n", "axs[2].plot(ts, afux(ts), 'b', linewidth=3)\n", "axs[2].plot(ts, afuy(ts), 'r', linewidth=3)\n", "axs[2].set_title('Acceleration [$\\mathrm{m/s^2}$]')\n", "axs[3].plot(ts, jfux(ts), 'b', linewidth=3)\n", "axs[3].plot(ts, jfuy(ts), 'r', linewidth=3)\n", "axs[3].set_title('Jerk [$\\mathrm{m/s^3}$]')\n", "\n", "for axi in axs:\n", "    axi.set_xlabel('Time [s]', fontsize=12)\n", "    axi.grid(True)\n", "\n", "fig.suptitle('Minimum jerk trajectory kinematics', fontsize=16)\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, the angular trajectories for this circular motion:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:52:08.619875Z", "start_time": "2020-05-03T20:52:07.703615Z"}}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\operatorname{angle}{\\left(t \\right)} = \\frac{180 \\operatorname{atan_{2}}{\\left(3.0 t^{5} - 7.5 t^{4} + 5.0 t^{3},- 3.0 t^{5} + 7.5 t^{4} - 5.0 t^{3} + 0.5 \\right)}}{\\pi}$"], "text/plain": ["                    ⎛     5        4        3         5        4        3      ⎞\n", "           180⋅atan2⎝3.0⋅t  - 7.5⋅t  + 5.0⋅t , - 3.0⋅t  + 7.5⋅t  - 5.0⋅t  + 0.5⎠\n", "angle(t) = ─────────────────────────────────────────────────────────────────────\n", "                                             π                                  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sympy import atan2, pi\n", "ang = atan2(y3, x3)*180/pi\n", "display(Eq(S('angle(t)'), ang))\n", "xang = lambdify(t, diff(ang, t, 0), 'numpy')\n", "vang = lambdify(t, diff(ang, t, 1), 'numpy')\n", "aang = lambdify(t, diff(ang, t, 2), 'numpy')\n", "jang = lambdify(t, diff(ang, t, 3), 'numpy')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-05-03T20:52:09.997122Z", "start_time": "2020-05-03T20:52:09.625869Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1000x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ts = np.arange(0, 1.01, .01)\n", "fig, axs = plt.subplots(1, 4, figsize=(10, 4), sharex=True)\n", "axs[0].plot(ts, xang(ts), linewidth=3)\n", "axs[0].set_title('Displacement [$\\mathrm{^o}$]')\n", "axs[1].plot(ts, vang(ts), linewidth=3)\n", "axs[1].set_title('Velocity [$\\mathrm{^o/s}$]')\n", "axs[2].plot(ts, aang(ts), linewidth=3)\n", "axs[2].set_title('Acceleration [$\\mathrm{^o/s^2}$]')\n", "axs[3].plot(ts, jang(ts), linewidth=3)\n", "axs[3].set_title('Jerk [$\\mathrm{^o/s^3}$]')\n", "\n", "for axi in axs:\n", "    axi.set_xlabel('Time [s]', fontsize=14)\n", "    axi.grid(True)\n", "\n", "fig.suptitle('Minimum jerk trajectory angular kinematics', fontsize=16)\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Problems\n", "\n", "1. What is your opinion on the the minimum jerk hypothesis? Do you think humans control movement based on this principle? (Think about what biomechanical and neurophysiological properties are not considered on this hypothesis.)\n", "2. Calculate and plot the position, velocity, acceleration, and jerk trajectories for different movement speeds (for example, consider always a displacement of 1 m and movement durations of 0.5, 1, and 2 s).  \n", "3. For the data in the previous item, calculate the ratio peak speed to average speed. <PERSON><PERSON><PERSON><PERSON> and  <PERSON> (2004) argue that psychophysical experiments show that reaching movements with the hand have this ratio equals to 1.75. <PERSON><PERSON><PERSON> with the calculated values.  \n", "4. Can you propose alternative hypotheses for the control of movement?  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON>, <PERSON> (1985) [The coordination of arm movements: an experimentally confirmed mathematical model](http://www.jneurosci.org/cgi/reprint/5/7/1688.pdf). Journal of Neuroscience, 5, 1688-1703.   \n", "- <PERSON> (1984) [An organizing principle for a class of voluntary movements](http://www.jneurosci.org/content/4/11/2745.full.pdf). Journal of Neuroscience, 4, 2745-2754.\n", "- <PERSON><PERSON><PERSON><PERSON>, <PERSON> (2004) [The Computational Neurobiology of Reaching and Pointing: A Foundation for Motor Learning](https://books.google.com.br/books?id=fKeImql1s_sC&pg=PP1&ots=WuEHfPo6G4&dq=shadmehr&sig=UjsmHL92SidKEKzgvpe5Qu_9pIs&redir_esc=y#v=onepage&q=shadmehr&f=false). A Bradford Book. [Supplementary documents](https://www.shadmehrlab.org/publications).\n", "- <PERSON><PERSON><PERSON><PERSON> (1998) [Kinematics of Human Motion](http://books.google.com.br/books/about/Kinematics_of_Human_Motion.html?id=Pql_xXdbrMcC&redir_esc=y). Champaign, Human Kinetics."]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}