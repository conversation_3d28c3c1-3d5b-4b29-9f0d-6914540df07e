{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ordinary Differential Equation\n", "\n", "> <PERSON>  \n", "> Laboratory of Biomechanics and Motor Control ([http://demotu.org/](http://demotu.org/))  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["An ordinary differential equation (ODE) is an equation containing a function of one independent variable and its derivatives.  \n", "\n", "Solve an ODE is finding such a function whose derivatives satisfy the equation. The order of an ODE refers to the order of the derivatives; e.g., a first order ODE has only first derivatives. A linear ODE has only linear terms for the function of one independent variable and in general its solution can be obtained analytically. By contrast, a nonlinear ODE doesn't have an exact analytical solution and it has to be solved by numerical methods. The equation is referred as partial differential equation when contains a function of more than one independent variable and its derivatives.  \n", "\n", "A simple and well known example of ODE is <PERSON>'s second law of motion:\n", "\n", "$$ m\\frac{\\mathrm{d}^2 \\mathbf{x}}{\\mathrm{d}t^2}(t) = \\mathbf{F} $$\n", "\n", "$\\mathbf{x}$ is the function with a derivative and $t$ is the independent variable. Note that the force, $\\mathbf{F}$, can be constant (e.g., the gravitational force) or a function of position, $\\mathbf{F}(\\mathbf{x}(t))$, (e.g., the force of a spring) or a function of other quantity. If $\\mathbf{F}$ is constant or a linear function of $\\mathbf{x}$, this equation is a second-order linear ODE. "]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["## First-order ODE\n", "\n", "A first-order ODE has the general form:\n", "\n", "$$ \\frac{\\mathrm{d} y}{\\mathrm{d} x} = f(x, y) $$\n", "\n", "Where $f(x, y)$ is an expression for the derivative of $y$ that can be evaluated given $x$ and $y$. When $f(x, y)$ is linear w.r.t. $y$, the equation is a first-order linear ODE which can be written in the form:\n", "\n", "$$ \\frac{\\mathrm{d} y}{\\mathrm{d} x} + P(x)y = Q(x) $$\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Numerical methods for solving ODE\n", "\n", "When an ODE can't be solved analytically, usually because it's nonlinear, numerical methods are used, a procedure also referred as numerical integration (<PERSON><PERSON>, 2011; <PERSON><PERSON>, 2013; <PERSON><PERSON><PERSON><PERSON>, 2013; [Wikipedia](http://en.wikipedia.org/wiki/Numerical_methods_for_ordinary_differential_equations)). In numerical methods, a first-order differential equation can be solved as an Initial Value Problem (IVP) of the form:  \n", "\n", "$$ \\dot{y}(t) = f(t, y(t)), \\quad y(t_0) = y_0 $$\n", "\n", "In numerical methods, a higher-order ODE is usually transformed into a system of first-order ODE and then this system is solved using numerical integration. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Euler method\n", "\n", "The most simple method to solve an ODE is using the Euler method.  \n", "First, the derivative of $y$ is approximated by:\n", "\n", "$$ \\dot{y}(t) \\approx \\frac{y(t+h)-y(t)}{h} $$\n", "\n", "Where $h$ is the step size.  \n", "Rearranging the equation above:\n", "\n", "$$ y(t+h) \\approx y(t) +h\\dot{y}(t) $$\n", "\n", "And replacing $\\dot{y}(t)$:\n", "\n", "$$ y(t+h) \\approx y(t) +hf(t, y(t)) $$  \n", "\n", "The ODE then can be solved starting at $t_0$, which has a known value for $y_0$:\n", "\n", "$$ y(t+h) \\approx y_0 + hf(t_0, y_0) $$ \n", "\n", "And using the equation recursively for a sequence of values for $t$ $(t_0, t_0+h, t_0+2h, ...)$:\n", "\n", "$$ y_{n+1} = y_n + hf(t_n, y_n) $$ \n", "\n", "This is the Euler method to solve an ODE with a known initial value. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Other numerical methods for solving ODE\n", "\n", "There are other methods for solving an ODE. One family of methods, usually more accurate, uses more points in the interval $[t_n,t_{n+1}]$ and are known as [Runge–Kutta methods](http://en.wikipedia.org/wiki/Runge%E2%80%93Kutta_method). In the Python ecosystem, Runge–Kutta methods are available using the [`scipy.integrate.ode`](http://docs.scipy.org/doc/scipy-0.14.0/reference/generated/scipy.integrate.ode.html) library of numeric integrators. The library [`scipy.integrate.odeint`](http://docs.scipy.org/doc/scipy/reference/generated/scipy.integrate.odeint.html) has other popular integrator known as `lsoda`, from the FORTRAN library odepack."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Examples\n", "\n", "### Motion under constant force\n", "\n", "Consider a football ball kicked up from an initial height $y_0$ and with initial velocity $v_0$. Determine the equation of motion of the ball in the vertical direction.  \n", "\n", "Neglecting the air resistance, <PERSON>'s second law of motion applied to this problem for the instants the ball is in the air gives: \n", "\n", "$$ m\\frac{\\mathrm{d}^2 y}{\\mathrm{d}t^2} = -mg $$\n", "\n", "Consider $g=9.8m/s^2$, $y_0(t_0=0)=1m$, and $v_0(t_0=0)=20m/s$.\n", "\n", "We know the analytical solution for this problem:\n", "\n", "$$ y(t) = y_0 + v_0 t - \\frac{g}{2}t^2 $$\n", "\n", "Let's solve this problem numerically and compare the results.\n", "\n", "A second-order ODE can be transformed into two first-order ODE, introducing a new variable:\n", "\n", "$$ \\dot{y} = v $$\n", "$$ \\dot{v} = a $$\n", "\n", "And rewriting <PERSON>'s second law as a couple of equations:\n", "\n", "$$ \\left\\{\n", "\\begin{array}{r}\n", "\\frac{\\mathrm{d} y}{\\mathrm{d}t} = &v, \\quad y(t_0) = y_0\n", "\\\\\n", "\\frac{\\mathrm{d} v}{\\mathrm{d}t} = &-g, \\quad v(t_0) = v_0\n", "\\end{array}\n", "\\right.$$\n", "\n", "First, let's import the necessary Python libraries and customize the environment:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:41.038034Z", "start_time": "2017-12-30T07:50:40.798597Z"}}, "outputs": [], "source": ["import numpy as np\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "matplotlib.rcParams['lines.linewidth'] = 3\n", "matplotlib.rcParams['font.size'] = 13\n", "matplotlib.rcParams['lines.markersize'] = 5\n", "matplotlib.rc('axes', grid=False, labelsize=14, titlesize=16, ymargin=0.05)\n", "matplotlib.rc('legend', numpoints=1, fontsize=11)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is the equation for calculating the ball trajectory given the model and using the Euler method:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:42.981866Z", "start_time": "2017-12-30T07:50:42.962953Z"}}, "outputs": [], "source": ["def ball_euler(t0, tend, y0, v0, h):\n", "    \n", "    t, y, v, i = [t0], [y0], [v0], 0\n", "    a = -9.8  \n", "    \n", "    while t[-1] <= tend and y[-1] > 0:\n", "        y.append(y[-1] + h*v[-1])\n", "        v.append(v[-1] + h*a)\n", "        i += 1\n", "        t.append(i*h)\n", "        \n", "    return np.array(t), np.array(y), np.array(v)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Initial values:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:44.696459Z", "start_time": "2017-12-30T07:50:44.691141Z"}}, "outputs": [], "source": ["y0 = 1\n", "v0 = 20\n", "\n", "a = -9.8"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's call the function with different step sizes:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:46.251304Z", "start_time": "2017-12-30T07:50:46.244399Z"}}, "outputs": [], "source": ["t100, y100, v100 = ball_euler(0, 10, y0, v0, 0.1)\n", "t10, y10, v10    = ball_euler(0, 10, y0, v0, 0.01)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here are the plots for the results:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:48.614362Z", "start_time": "2017-12-30T07:50:48.513770Z"}}, "outputs": [], "source": ["def plots(t100, y100, v100, t10, y10, v10, title):\n", "    \"\"\"Plots of numerical integration results.\n", "    \"\"\"\n", "    a = -9.8\n", "    \n", "    fig, axs = plt.subplots(nrows=2, ncols=2, sharex=True, figsize=(10, 5))\n", "\n", "    axs[0, 0].plot(t10, y0 + v0*t10 + 0.5*a*t10**2, color=[0, 0, 1, .7], label='Analytical')\n", "    axs[0, 0].plot(t100, y100, '--', color=[0, 1, 0, .7], label='h = 100ms')\n", "    axs[0, 0].plot(t10, y10, ':', color=[1, 0, 0, .7], label='h =   10ms')\n", "\n", "    axs[0, 1].plot(t10, v0 + a*t10, color=[0, 0, 1, .5], label='Analytical')\n", "    axs[0, 1].plot(t100, v100, '--', color=[0, 1, 0, .7], label='h = 100ms')\n", "    axs[0, 1].plot(t10, v10, ':', color=[1, 0, 0, .7], label='h =   10ms')\n", "\n", "    axs[1, 0].plot(t10, y0 + v0*t10 + 0.5*a*t10**2 - (y0 + v0*t10 + 0.5*a*t10**2),\n", "                   color=[0, 0, 1, .7], label='Analytical')\n", "    axs[1, 0].plot(t100, y100 - (y0 + v0*t100 + 0.5*a*t100**2), '--',\n", "                   color=[0, 1, 0, .7], label='h = 100ms')\n", "    axs[1, 0].plot(t10, y10 - (y0 + v0*t10 + 0.5*a*t10**2), ':',\n", "                   color=[1, 0, 0, .7], label='h =   10ms')\n", "\n", "    axs[1, 1].plot(t10, v0 + a*t10 - (v0 + a*t10), color=[0, 0, 1, .7], label='Analytical')\n", "    axs[1, 1].plot(t100, v100 - (v0 + a*t100), '--', color=[0, 1, 0, .7], label='h = 100ms')\n", "    axs[1, 1].plot(t10, v10 - (v0 + a*t10), ':', color=[1, 0, 0, .7], label='h =   10ms')\n", "\n", "    ylabel = ['y [m]', 'v [m/s]', 'y error [m]', 'v error [m/s]']\n", "    axs[0, 0].set_xlim(t10[0], t10[-1])\n", "    axs[1, 0].set_xlabel('Time [s]')\n", "    axs[1, 1].set_xlabel('Time [s]')\n", "    axs[0, 1].legend()\n", "    axs = axs.flatten()\n", "    for i, ax in enumerate(axs):\n", "        ax.set_ylabel(ylabel[i])\n", "    plt.suptitle('Kinematics of a soccer ball - %s method'%title, y=1.02, fontsize=16)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:50.618762Z", "start_time": "2017-12-30T07:50:50.159643Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f11d28e1518>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plots(t100, y100, v100, t10, y10, v10, 'Euler')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use the integrator `lsoda` to solve the same problem:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:53.472878Z", "start_time": "2017-12-30T07:50:53.404576Z"}}, "outputs": [], "source": ["from scipy.integrate import odeint, ode\n", "\n", "def ball_eq(yv, t):\n", "    \n", "    y = yv[0]  # position \n", "    v = yv[1]  # velocity\n", "    a = -9.8   # acceleration\n", "    \n", "    return [v, a]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:54.263534Z", "start_time": "2017-12-30T07:50:54.248845Z"}}, "outputs": [], "source": ["yv0   = [1, 20]\n", "t10   = np.arange(0, 4, 0.1)\n", "yv10  = odeint(ball_eq, yv0, t10)\n", "y10, v10 = yv10[:, 0], yv10[:, 1]\n", "t100  = np.arange(0, 4, 0.01)\n", "yv100 = odeint(ball_eq, yv0, t100)\n", "y100, v100 = yv100[:, 0], yv100[:, 1]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:55.477772Z", "start_time": "2017-12-30T07:50:55.040634Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f11cb071ac8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plots(t100, y100, v100, t10, y10, v10, 'lsoda')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use an explicit runge-kutta method of order (4)5 due to <PERSON><PERSON><PERSON> and <PERSON> (a.k.a. ode45 in Matlab):"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:50:59.210647Z", "start_time": "2017-12-30T07:50:59.201419Z"}}, "outputs": [], "source": ["def ball_eq(t, yv):\n", "\n", "    y = yv[0]  # position \n", "    v = yv[1]  # velocity\n", "    a = -9.8   # acceleration\n", "    \n", "    return [v, a]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:00.182616Z", "start_time": "2017-12-30T07:51:00.161245Z"}}, "outputs": [], "source": ["def ball_sol(fun, t0, tend, yv0, h):\n", "    f = ode(fun).set_integrator('dopri5')\n", "    # or f = ode(fun).set_integrator('dopri5', nsteps=1, max_step=h/2)\n", "    f.set_initial_value(yv0, t0)\n", "    data = []\n", "    while f.successful() and f.t < tend:\n", "        f.integrate(f.t + h)\n", "        # or f.integrate(tend)\n", "        data.append([f.t, f.y[0], f.y[1]])\n", "\n", "    data = np.array(data)\n", "    \n", "    return data"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:01.039350Z", "start_time": "2017-12-30T07:51:00.962994Z"}}, "outputs": [], "source": ["data = ball_sol(ball_eq, 0, 4, [1, 20], .1)\n", "t100, y100, v100 = data[:, 0], data[:, 1], data[:, 2]\n", "data = ball_sol(ball_eq, 0, 4, [1, 20], .01)\n", "t10, y10, v10 = data[:, 0], data[:, 1], data[:, 2]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:02.732732Z", "start_time": "2017-12-30T07:51:02.304907Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f11cafe2048>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plots(t100, y100, v100, t10, y10, v10, 'dopri5 (ode45)')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Motion under varying force\n", "\n", "Let's consider the air resistance in the calculations for the vertical trajectory of the football ball.  \n", "According to the Laws of the Game from FIFA, the ball is spherical, has a circumference of $0.69m$, and a mass of $0.43kg$.  \n", "We will model the magnitude of the [drag force](http://en.wikipedia.org/wiki/Drag_%28physics%29) due to the air resistance by:\n", "\n", "$$ F_d(v) = \\frac{1}{2}\\rho C_d A v^2 $$\n", "\n", "Where $\\rho$ is the air density $(1.22kg/m^3)$, $A$ the ball cross sectional area $(0.0379m^2)$, and $C_d$ the drag coefficient, which for now we will consider constant and equal to $0.25$ (<PERSON> and <PERSON>, 2003).  \n", "Applying <PERSON>'s second law of motion to the new problem:\n", "\n", "$$ m\\frac{\\mathrm{d}^2 y}{\\mathrm{d}t^2} = -mg -\\frac{1}{2}\\rho C_d A v^2\\frac{v}{||v||} $$\n", "\n", "In the equation above, $-v/||v||$ takes into account that the drag force always acts opposite to the direction of motion.  \n", "Reformulating the second-order ODE above as a couple of first-order equations:\n", "\n", "$$ \\left\\{\n", "\\begin{array}{l l}\n", "\\frac{\\mathrm{d} y}{\\mathrm{d}t} = &v, \\quad &y(t_0) = y_0\n", "\\\\\n", "\\frac{\\mathrm{d} v}{\\mathrm{d}t} = &-g -\\frac{1}{2m}\\rho C_d A v^2\\frac{v}{||v||}, \\quad &v(t_0) = v_0\n", "\\end{array}\n", "\\right.$$\n", "\n", "Although (much) more complicated, it's still possible to find an analytical solution for this problem. But for now let's explore the power of numerical integration and use the `lsoda` method (the most simple method to call in terms of number of lines of code) to solve this problem:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:14.588822Z", "start_time": "2017-12-30T07:51:14.570278Z"}}, "outputs": [], "source": ["def ball_eq(yv, t):\n", "    \n", "    g   = 9.8     # m/s2\n", "    m   = 0.43    # kg\n", "    rho = 1.22    # kg/m3\n", "    cd  = 0.25    # dimensionless\n", "    A   = 0.0379  # m2\n", "    \n", "    y = yv[0]  # position \n", "    v = yv[1]  # velocity\n", "    a = -g - 1/(2*m)*rho*cd*A*v*np.abs(v)  # acceleration\n", "    \n", "    return [v, a]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:15.560345Z", "start_time": "2017-12-30T07:51:15.552409Z"}}, "outputs": [], "source": ["yv0   = [1, 20]\n", "t10   = np.arange(0, 4, 0.01)\n", "yv10  = odeint(ball_eq, yv0, t10)\n", "y10, v10 = yv10[:, 0], yv10[:, 1]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:16.600471Z", "start_time": "2017-12-30T07:51:16.507995Z"}}, "outputs": [], "source": ["def plots(t10, y10, v10):\n", "    \"\"\"Plots of numerical integration results.\n", "    \"\"\"\n", "    a = -9.8\n", "    \n", "    fig, axs = plt.subplots(nrows=2, ncols=2, sharex=True, figsize=(10, 5))\n", "\n", "    axs[0, 0].plot(t10, y0 + v0*t10 + 0.5*a*t10**2, color=[0, 0, 1, .7], label='No resistance')\n", "    axs[0, 0].plot(t10, y10, '-', color=[1, 0, 0, .7], label='With resistance')\n", "\n", "    axs[0, 1].plot(t10, v0 + a*t10, color=[0, 0, 1, .7], label='No resistance')\n", "    axs[0, 1].plot(t10, v10, '-', color=[1, 0, 0, .7], label='With resistance')\n", "\n", "    axs[1, 0].plot(t10, y0 + v0*t10 + 0.5*a*t10**2 - (y0 + v0*t10 + 0.5*a*t10**2),\n", "                   color=[0, 0, 1, .7], label='Real')\n", "    axs[1, 0].plot(t10, y10 - (y0 + v0*t10 + 0.5*a*t10**2), '-',\n", "                   color=[1, 0, 0, .7], label='h=10 ms')\n", "\n", "    axs[1, 1].plot(t10, v0 + a*t10 - (v0 + a*t10), color=[0, 0, 1, .7], label='No resistance')\n", "    axs[1, 1].plot(t10, v10 - (v0 + a*t10), '-', color=[1, 0, 0, .7], label='With resistance')\n", "\n", "    ylabel = ['y [m]', 'v [m/s]', 'y diff [m]', 'v diff [m/s]']\n", "    axs[1, 0].set_xlabel('Time [s]')\n", "    axs[1, 1].set_xlabel('Time [s]')\n", "    axs[0, 1].legend()\n", "    axs = axs.flatten()\n", "    for i, ax in enumerate(axs):\n", "        ax.set_ylabel(ylabel[i])\n", "    plt.suptitle('Kinematics of a soccer ball - effect of air resistance', y=1.02, fontsize=16)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:51:18.520910Z", "start_time": "2017-12-30T07:51:18.095283Z"}}, "outputs": [{"data": {"image/png": "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*****************************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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f11ce9b36d8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plots(t10, y10, v10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exercises\n", "\n", "1. Run the simulations above considering different values for the parameters.  \n", "2. Model and run simulations for the two-dimensional case of the ball trajectory and investigate the effect of air resistance. Hint: chapter 9 of <PERSON><PERSON> (2011) presents part of the solution."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References\n", "\n", "- <PERSON>, <PERSON><PERSON><PERSON> (2003) [Modelling the flight of a soccer ball in a direct free kick](http://people.stfx.ca/smackenz/Courses/HK474/Labs/Jump%20Float%20Lab/Bray%202002%20Modelling%20the%20flight%20of%20a%20soccer%20ball%20in%20a%20direct%20free%20kick.pdf). Journal of Sports Sciences, 21, 75–85.   \n", "- Downey AB (2011) [Physical Modeling in MATLAB](http://greenteapress.com/matlab/). Green Tea Press.  \n", "- FIFA (2015) [Laws of the Game 2014/2015](http://www.fifa.com/aboutfifa/footballdevelopment/technicalsupport/refereeing/laws-of-the-game/).\n", "- <PERSON><PERSON> (2013) [pycse - Python Computations in Science and Engineering](http://kitchingroup.cheme.cmu.edu/pycse/).  \n", "- <PERSON><PERSON><PERSON><PERSON> (2013) [Numerical methods in engineering with Python 3](http://books.google.com.br/books?id=aJkXoxxoCoUC). 3rd edition. Cambridge University Press.  "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 1}