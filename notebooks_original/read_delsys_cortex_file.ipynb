{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Read Delsys file from Cortex\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](http://demotu.org/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T17:59:02.171865Z", "start_time": "2019-11-07T17:59:01.668686Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "%matplotlib notebook\n", "# tk qt notebook inline ipympl\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "import sys, os\n", "sys.path.insert(1, r'./../functions')\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "from io_cortexmac import read_delsys"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T17:59:03.753813Z", "start_time": "2019-11-07T17:59:03.730133Z"}}, "outputs": [], "source": ["freq_trc = 150 # Cortex sampling rate\n", "muscles = ['TA', 'Sol', 'VL', 'BF', 'GMax', 'GL', 'RF', 'GMed', 'VM']\n", "path2 = '/mnt/A/BMClab/Projects/FapespRunAge/Data/Cadence/s20/'\n", "fname = 'run100s.csv'\n", "fname = os.path.join(path2, fname)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T17:59:06.736127Z", "start_time": "2019-11-07T17:59:04.909196Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening file \"/mnt/A/BMClab/Projects/FapespRunAge/Data/Cadence/s20/run100s.csv\" ... done.\n", "Saving file \"/mnt/A/BMClab/Projects/FapespRunAge/Data/Cadence/s20/run100s.emg\" ... \n", "Saving file \"/mnt/A/BMClab/Projects/FapespRunAge/Data/Cadence/s20/run100s.imu\" ... done.\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support. ' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>');\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option);\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width / mpl.ratio, fig.canvas.height / mpl.ratio);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>');\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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***************************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********************************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\" width=\"899.9999804930258\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_emg, df_imu = read_delsys(fname, fname2='=', sensors=muscles, freq_trc=150, emg=True, imu=True,\n", "                             resample=[150, 150], show_msg=True, show=True, suptitle=fname)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T17:59:50.088144Z", "start_time": "2019-11-07T17:59:50.037722Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TA</th>\n", "      <th>Sol</th>\n", "      <th>VL</th>\n", "      <th>BF</th>\n", "      <th>GMax</th>\n", "      <th>GL</th>\n", "      <th>RF</th>\n", "      <th>GMed</th>\n", "      <th>VM</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>0.007029</td>\n", "      <td>0.006594</td>\n", "      <td>0.004291</td>\n", "      <td>0.019979</td>\n", "      <td>0.001023</td>\n", "      <td>0.000115</td>\n", "      <td>0.001364</td>\n", "      <td>0.006995</td>\n", "      <td>0.012894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.006667</th>\n", "      <td>0.017886</td>\n", "      <td>0.015504</td>\n", "      <td>0.009065</td>\n", "      <td>0.039202</td>\n", "      <td>0.002080</td>\n", "      <td>0.000222</td>\n", "      <td>0.002835</td>\n", "      <td>0.014085</td>\n", "      <td>0.026085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013333</th>\n", "      <td>0.017084</td>\n", "      <td>0.015021</td>\n", "      <td>0.008657</td>\n", "      <td>0.037148</td>\n", "      <td>0.001975</td>\n", "      <td>0.000212</td>\n", "      <td>0.002892</td>\n", "      <td>0.013995</td>\n", "      <td>0.048973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020000</th>\n", "      <td>0.025477</td>\n", "      <td>0.016714</td>\n", "      <td>0.011098</td>\n", "      <td>0.042947</td>\n", "      <td>0.002208</td>\n", "      <td>0.000276</td>\n", "      <td>0.003207</td>\n", "      <td>0.015144</td>\n", "      <td>0.080798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.026667</th>\n", "      <td>0.027587</td>\n", "      <td>0.016324</td>\n", "      <td>0.011031</td>\n", "      <td>0.042059</td>\n", "      <td>0.002324</td>\n", "      <td>0.000256</td>\n", "      <td>0.003414</td>\n", "      <td>0.014969</td>\n", "      <td>0.087736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.946667</th>\n", "      <td>0.070643</td>\n", "      <td>0.337400</td>\n", "      <td>0.105687</td>\n", "      <td>0.025723</td>\n", "      <td>0.043704</td>\n", "      <td>0.001281</td>\n", "      <td>0.044262</td>\n", "      <td>0.150382</td>\n", "      <td>1.326730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.953333</th>\n", "      <td>0.074686</td>\n", "      <td>0.306654</td>\n", "      <td>0.106799</td>\n", "      <td>0.022041</td>\n", "      <td>0.044794</td>\n", "      <td>0.001114</td>\n", "      <td>0.044378</td>\n", "      <td>0.145094</td>\n", "      <td>1.234395</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.960000</th>\n", "      <td>0.060467</td>\n", "      <td>0.249718</td>\n", "      <td>0.093027</td>\n", "      <td>0.013716</td>\n", "      <td>0.042580</td>\n", "      <td>0.000937</td>\n", "      <td>0.038819</td>\n", "      <td>0.137253</td>\n", "      <td>1.125383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.966667</th>\n", "      <td>0.055182</td>\n", "      <td>0.250829</td>\n", "      <td>0.076784</td>\n", "      <td>0.010169</td>\n", "      <td>0.044326</td>\n", "      <td>0.000701</td>\n", "      <td>0.033299</td>\n", "      <td>0.089877</td>\n", "      <td>1.186043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.973333</th>\n", "      <td>0.025094</td>\n", "      <td>0.103625</td>\n", "      <td>0.031833</td>\n", "      <td>0.004877</td>\n", "      <td>0.013978</td>\n", "      <td>0.000241</td>\n", "      <td>0.013248</td>\n", "      <td>0.024222</td>\n", "      <td>0.495989</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4497 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                 TA       Sol        VL        BF      GMax        GL  \\\n", "Time                                                                    \n", "0.000000   0.007029  0.006594  0.004291  0.019979  0.001023  0.000115   \n", "0.006667   0.017886  0.015504  0.009065  0.039202  0.002080  0.000222   \n", "0.013333   0.017084  0.015021  0.008657  0.037148  0.001975  0.000212   \n", "0.020000   0.025477  0.016714  0.011098  0.042947  0.002208  0.000276   \n", "0.026667   0.027587  0.016324  0.011031  0.042059  0.002324  0.000256   \n", "...             ...       ...       ...       ...       ...       ...   \n", "29.946667  0.070643  0.337400  0.105687  0.025723  0.043704  0.001281   \n", "29.953333  0.074686  0.306654  0.106799  0.022041  0.044794  0.001114   \n", "29.960000  0.060467  0.249718  0.093027  0.013716  0.042580  0.000937   \n", "29.966667  0.055182  0.250829  0.076784  0.010169  0.044326  0.000701   \n", "29.973333  0.025094  0.103625  0.031833  0.004877  0.013978  0.000241   \n", "\n", "                 RF      GMed        VM  \n", "Time                                     \n", "0.000000   0.001364  0.006995  0.012894  \n", "0.006667   0.002835  0.014085  0.026085  \n", "0.013333   0.002892  0.013995  0.048973  \n", "0.020000   0.003207  0.015144  0.080798  \n", "0.026667   0.003414  0.014969  0.087736  \n", "...             ...       ...       ...  \n", "29.946667  0.044262  0.150382  1.326730  \n", "29.953333  0.044378  0.145094  1.234395  \n", "29.960000  0.038819  0.137253  1.125383  \n", "29.966667  0.033299  0.089877  1.186043  \n", "29.973333  0.013248  0.024222  0.495989  \n", "\n", "[4497 rows x 9 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_emg"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T17:59:51.380338Z", "start_time": "2019-11-07T17:59:51.336250Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TA IM ACC Pitch</th>\n", "      <th>TA IM ACC Roll</th>\n", "      <th>TA IM ACC Yaw</th>\n", "      <th>TA IM GYR Pitch</th>\n", "      <th>TA IM GYR Roll</th>\n", "      <th>TA IM GYR Yaw</th>\n", "      <th>TA IM MAG Pitch</th>\n", "      <th>TA IM MAG Roll</th>\n", "      <th>TA IM MAG Yaw</th>\n", "      <th>Sol IM ACC Pitch</th>\n", "      <th>...</th>\n", "      <th>GMed IM MAG Yaw</th>\n", "      <th>VM IM ACC Pitch</th>\n", "      <th>VM IM ACC Roll</th>\n", "      <th>VM IM ACC Yaw</th>\n", "      <th>VM IM GYR Pitch</th>\n", "      <th>VM IM GYR Roll</th>\n", "      <th>VM IM GYR Yaw</th>\n", "      <th>VM IM MAG Pitch</th>\n", "      <th>VM IM MAG Roll</th>\n", "      <th>VM IM MAG Yaw</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.000000</th>\n", "      <td>1.461924</td>\n", "      <td>0.054725</td>\n", "      <td>-1.220061</td>\n", "      <td>332.541443</td>\n", "      <td>-0.305084</td>\n", "      <td>393.802277</td>\n", "      <td>153.198425</td>\n", "      <td>-93.232719</td>\n", "      <td>-140.406525</td>\n", "      <td>-3.092418</td>\n", "      <td>...</td>\n", "      <td>-100.159142</td>\n", "      <td>-1.307523</td>\n", "      <td>-0.878033</td>\n", "      <td>-0.970382</td>\n", "      <td>-186.955399</td>\n", "      <td>109.036980</td>\n", "      <td>-38.867687</td>\n", "      <td>13.935328</td>\n", "      <td>-128.196564</td>\n", "      <td>-379.281097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.006667</th>\n", "      <td>1.206918</td>\n", "      <td>-0.580939</td>\n", "      <td>-1.055024</td>\n", "      <td>317.020447</td>\n", "      <td>6.356417</td>\n", "      <td>376.144806</td>\n", "      <td>154.889496</td>\n", "      <td>-94.512703</td>\n", "      <td>-142.536484</td>\n", "      <td>-3.524715</td>\n", "      <td>...</td>\n", "      <td>-100.225967</td>\n", "      <td>-0.786845</td>\n", "      <td>-0.687781</td>\n", "      <td>-0.411544</td>\n", "      <td>-261.242249</td>\n", "      <td>123.340370</td>\n", "      <td>-16.872202</td>\n", "      <td>14.170884</td>\n", "      <td>-128.877518</td>\n", "      <td>-379.898071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.013333</th>\n", "      <td>1.254572</td>\n", "      <td>-0.929420</td>\n", "      <td>-0.892858</td>\n", "      <td>293.616821</td>\n", "      <td>25.956482</td>\n", "      <td>338.785217</td>\n", "      <td>154.434433</td>\n", "      <td>-94.714867</td>\n", "      <td>-143.238785</td>\n", "      <td>-3.617497</td>\n", "      <td>...</td>\n", "      <td>-99.351959</td>\n", "      <td>0.015441</td>\n", "      <td>-0.560639</td>\n", "      <td>0.490481</td>\n", "      <td>-193.679840</td>\n", "      <td>80.191986</td>\n", "      <td>39.518909</td>\n", "      <td>14.068916</td>\n", "      <td>-128.381226</td>\n", "      <td>-376.153442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.020000</th>\n", "      <td>1.295480</td>\n", "      <td>-1.040251</td>\n", "      <td>-0.754853</td>\n", "      <td>276.118744</td>\n", "      <td>26.507492</td>\n", "      <td>306.563416</td>\n", "      <td>156.412994</td>\n", "      <td>-96.575020</td>\n", "      <td>-145.354950</td>\n", "      <td>-3.532538</td>\n", "      <td>...</td>\n", "      <td>-99.758972</td>\n", "      <td>0.813057</td>\n", "      <td>-0.650732</td>\n", "      <td>0.985687</td>\n", "      <td>-36.635735</td>\n", "      <td>17.316431</td>\n", "      <td>74.768578</td>\n", "      <td>14.443660</td>\n", "      <td>-129.880676</td>\n", "      <td>-378.628845</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.026667</th>\n", "      <td>0.720512</td>\n", "      <td>-1.038927</td>\n", "      <td>-0.815304</td>\n", "      <td>258.242981</td>\n", "      <td>11.812378</td>\n", "      <td>270.558746</td>\n", "      <td>155.565628</td>\n", "      <td>-96.788765</td>\n", "      <td>-143.896317</td>\n", "      <td>-2.934765</td>\n", "      <td>...</td>\n", "      <td>-97.856499</td>\n", "      <td>1.212349</td>\n", "      <td>-0.795189</td>\n", "      <td>0.612395</td>\n", "      <td>-6.388461</td>\n", "      <td>-62.259018</td>\n", "      <td>57.945923</td>\n", "      <td>14.935710</td>\n", "      <td>-128.734497</td>\n", "      <td>-374.054565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.946667</th>\n", "      <td>6.703976</td>\n", "      <td>0.445667</td>\n", "      <td>-4.139219</td>\n", "      <td>-270.413361</td>\n", "      <td>-47.437138</td>\n", "      <td>-268.662994</td>\n", "      <td>152.623108</td>\n", "      <td>-101.347496</td>\n", "      <td>-147.524078</td>\n", "      <td>-1.395813</td>\n", "      <td>...</td>\n", "      <td>-97.832497</td>\n", "      <td>-3.226587</td>\n", "      <td>4.142353</td>\n", "      <td>-5.151693</td>\n", "      <td>-125.047264</td>\n", "      <td>225.588974</td>\n", "      <td>-38.197094</td>\n", "      <td>13.122366</td>\n", "      <td>-127.026657</td>\n", "      <td>-372.528320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.953333</th>\n", "      <td>2.637491</td>\n", "      <td>0.850779</td>\n", "      <td>-1.344797</td>\n", "      <td>-261.812836</td>\n", "      <td>-165.894760</td>\n", "      <td>-224.713684</td>\n", "      <td>150.876724</td>\n", "      <td>-99.626503</td>\n", "      <td>-144.750290</td>\n", "      <td>-0.624715</td>\n", "      <td>...</td>\n", "      <td>-97.712196</td>\n", "      <td>-2.899570</td>\n", "      <td>2.789130</td>\n", "      <td>-4.677643</td>\n", "      <td>-147.121429</td>\n", "      <td>208.933853</td>\n", "      <td>-1.363779</td>\n", "      <td>13.491474</td>\n", "      <td>-127.160400</td>\n", "      <td>-368.826569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.960000</th>\n", "      <td>0.467987</td>\n", "      <td>1.139431</td>\n", "      <td>-2.419861</td>\n", "      <td>-199.883545</td>\n", "      <td>31.710869</td>\n", "      <td>-123.376144</td>\n", "      <td>151.648697</td>\n", "      <td>-99.329712</td>\n", "      <td>-144.617905</td>\n", "      <td>-2.538196</td>\n", "      <td>...</td>\n", "      <td>-100.098679</td>\n", "      <td>-1.761733</td>\n", "      <td>0.788060</td>\n", "      <td>-4.187121</td>\n", "      <td>-244.747864</td>\n", "      <td>68.450127</td>\n", "      <td>17.060461</td>\n", "      <td>13.958459</td>\n", "      <td>-130.325455</td>\n", "      <td>-370.974426</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.966667</th>\n", "      <td>1.178041</td>\n", "      <td>-0.060100</td>\n", "      <td>-2.848602</td>\n", "      <td>-224.175644</td>\n", "      <td>86.155693</td>\n", "      <td>-67.423538</td>\n", "      <td>150.504471</td>\n", "      <td>-97.854019</td>\n", "      <td>-143.243225</td>\n", "      <td>-1.446289</td>\n", "      <td>...</td>\n", "      <td>-99.830841</td>\n", "      <td>-0.881454</td>\n", "      <td>-2.445498</td>\n", "      <td>-2.791923</td>\n", "      <td>-197.206223</td>\n", "      <td>-104.399704</td>\n", "      <td>-14.399959</td>\n", "      <td>13.710456</td>\n", "      <td>-130.421295</td>\n", "      <td>-369.229919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.973333</th>\n", "      <td>0.014567</td>\n", "      <td>-0.003157</td>\n", "      <td>-0.019136</td>\n", "      <td>-2.363307</td>\n", "      <td>0.406399</td>\n", "      <td>-0.576367</td>\n", "      <td>1.448279</td>\n", "      <td>-0.939822</td>\n", "      <td>-1.378258</td>\n", "      <td>-0.009453</td>\n", "      <td>...</td>\n", "      <td>-0.959878</td>\n", "      <td>-0.005267</td>\n", "      <td>-0.032889</td>\n", "      <td>-0.022074</td>\n", "      <td>-1.444339</td>\n", "      <td>-1.538728</td>\n", "      <td>-0.105037</td>\n", "      <td>0.130882</td>\n", "      <td>-1.255806</td>\n", "      <td>-3.557163</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4497 rows × 81 columns</p>\n", "</div>"], "text/plain": ["           TA IM ACC Pitch  TA IM ACC Roll  TA IM ACC Yaw  TA IM GYR Pitch  \\\n", "Time                                                                         \n", "0.000000          1.461924        0.054725      -1.220061       332.541443   \n", "0.006667          1.206918       -0.580939      -1.055024       317.020447   \n", "0.013333          1.254572       -0.929420      -0.892858       293.616821   \n", "0.020000          1.295480       -1.040251      -0.754853       276.118744   \n", "0.026667          0.720512       -1.038927      -0.815304       258.242981   \n", "...                    ...             ...            ...              ...   \n", "29.946667         6.703976        0.445667      -4.139219      -270.413361   \n", "29.953333         2.637491        0.850779      -1.344797      -261.812836   \n", "29.960000         0.467987        1.139431      -2.419861      -199.883545   \n", "29.966667         1.178041       -0.060100      -2.848602      -224.175644   \n", "29.973333         0.014567       -0.003157      -0.019136        -2.363307   \n", "\n", "           TA IM GYR Roll  TA IM GYR Yaw  TA IM MAG Pitch  TA IM MAG Roll  \\\n", "Time                                                                        \n", "0.000000        -0.305084     393.802277       153.198425      -93.232719   \n", "0.006667         6.356417     376.144806       154.889496      -94.512703   \n", "0.013333        25.956482     338.785217       154.434433      -94.714867   \n", "0.020000        26.507492     306.563416       156.412994      -96.575020   \n", "0.026667        11.812378     270.558746       155.565628      -96.788765   \n", "...                   ...            ...              ...             ...   \n", "29.946667      -47.437138    -268.662994       152.623108     -101.347496   \n", "29.953333     -165.894760    -224.713684       150.876724      -99.626503   \n", "29.960000       31.710869    -123.376144       151.648697      -99.329712   \n", "29.966667       86.155693     -67.423538       150.504471      -97.854019   \n", "29.973333        0.406399      -0.576367         1.448279       -0.939822   \n", "\n", "           TA IM MAG Yaw  Sol IM ACC Pitch  ...  GMed IM MAG Yaw  \\\n", "Time                                        ...                    \n", "0.000000     -140.406525         -3.092418  ...      -100.159142   \n", "0.006667     -142.536484         -3.524715  ...      -100.225967   \n", "0.013333     -143.238785         -3.617497  ...       -99.351959   \n", "0.020000     -145.354950         -3.532538  ...       -99.758972   \n", "0.026667     -143.896317         -2.934765  ...       -97.856499   \n", "...                  ...               ...  ...              ...   \n", "29.946667    -147.524078         -1.395813  ...       -97.832497   \n", "29.953333    -144.750290         -0.624715  ...       -97.712196   \n", "29.960000    -144.617905         -2.538196  ...      -100.098679   \n", "29.966667    -143.243225         -1.446289  ...       -99.830841   \n", "29.973333      -1.378258         -0.009453  ...        -0.959878   \n", "\n", "           VM IM ACC Pitch  VM IM ACC Roll  VM IM ACC Yaw  VM IM GYR Pitch  \\\n", "Time                                                                         \n", "0.000000         -1.307523       -0.878033      -0.970382      -186.955399   \n", "0.006667         -0.786845       -0.687781      -0.411544      -261.242249   \n", "0.013333          0.015441       -0.560639       0.490481      -193.679840   \n", "0.020000          0.813057       -0.650732       0.985687       -36.635735   \n", "0.026667          1.212349       -0.795189       0.612395        -6.388461   \n", "...                    ...             ...            ...              ...   \n", "29.946667        -3.226587        4.142353      -5.151693      -125.047264   \n", "29.953333        -2.899570        2.789130      -4.677643      -147.121429   \n", "29.960000        -1.761733        0.788060      -4.187121      -244.747864   \n", "29.966667        -0.881454       -2.445498      -2.791923      -197.206223   \n", "29.973333        -0.005267       -0.032889      -0.022074        -1.444339   \n", "\n", "           VM IM GYR Roll  VM IM GYR Yaw  VM IM MAG Pitch  VM IM MAG Roll  \\\n", "Time                                                                        \n", "0.000000       109.036980     -38.867687        13.935328     -128.196564   \n", "0.006667       123.340370     -16.872202        14.170884     -128.877518   \n", "0.013333        80.191986      39.518909        14.068916     -128.381226   \n", "0.020000        17.316431      74.768578        14.443660     -129.880676   \n", "0.026667       -62.259018      57.945923        14.935710     -128.734497   \n", "...                   ...            ...              ...             ...   \n", "29.946667      225.588974     -38.197094        13.122366     -127.026657   \n", "29.953333      208.933853      -1.363779        13.491474     -127.160400   \n", "29.960000       68.450127      17.060461        13.958459     -130.325455   \n", "29.966667     -104.399704     -14.399959        13.710456     -130.421295   \n", "29.973333       -1.538728      -0.105037         0.130882       -1.255806   \n", "\n", "           VM IM MAG Yaw  \n", "Time                      \n", "0.000000     -379.281097  \n", "0.006667     -379.898071  \n", "0.013333     -376.153442  \n", "0.020000     -378.628845  \n", "0.026667     -374.054565  \n", "...                  ...  \n", "29.946667    -372.528320  \n", "29.953333    -368.826569  \n", "29.960000    -370.974426  \n", "29.966667    -369.229919  \n", "29.973333      -3.557163  \n", "\n", "[4497 rows x 81 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_imu"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2019-11-07T18:00:47.862753Z", "start_time": "2019-11-07T18:00:47.844202Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sampling rates for EMG and IMU data:\n", "150.00000000000006 150.00000000000006\n"]}], "source": ["print('Sampling rates for EMG and IMU data:')\n", "print(np.mean(1/np.diff(df_emg.index)), np.mean(1/np.diff(df_imu.index)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "position": {"height": "419px", "left": "841px", "right": "20px", "top": "227px", "width": "359px"}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}