{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Direct Linear Transformation (DLT)\n", "\n", "> <PERSON>   \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Camera calibration and point reconstruction based on DLT\n", "\n", "The fundamental problem here is to find a mathematical relationship between the coordinates of a 3D point and its projection onto the image plane. The DLT (a linear approximation to this problem) is derived from modeling the object and its projection on the image plane as a pinhole camera situation.   \n", "In simplistic terms, using a pinhole camera model, it can be found by similar triangles the following relation between the image coordinates (u,v) and the 3D point (X,Y,Z):\n", "\n", "$$ \\begin{bmatrix} u \\\\\\ v\\\\\\ l \\end{bmatrix} = \n", "\\begin{bmatrix} L1 &  L2 & L3 & L4 \\\\\\ L5 & L6 & L7 & L8 \\\\\\ L9 & L10 & L11 & L12 \\end{bmatrix}  \n", "\\begin{bmatrix} X \\\\\\ Y \\\\\\ Z \\\\\\ 1 \\end{bmatrix} $$\n", "\n", "The matrix L is kwnown as the camera matrix or camera projection matrix. For a 2D point (X,Y), this matrix is 3x3. In fact, the L12 term (or L9 for 2D DLT) is not independent from the other parameters and then there are only 11 (or 8 for 2D DLT) independent parameters in the DLT to be determined through the calibration procedure.   \n", "\n", "There are more accurate (but more complex) algorithms for camera calibration that also consider lens distortion. For example, OpenCV and Tsai softwares have been ported to Python. However, DLT is classic, simple, and effective (fast) for most applications.   \n", "\n", "DLT is typically used in two steps: 1. Camera calibration. 2. Object (point) reconstruction.\n", "\n", "The camera calibration step consists in digitizing points with known coordinates in the real space and find the camera parameters.   \n", "At least 4 points are necessary for the calibration of a plane (2D DLT) and at least 6 points for the calibration of a volume (3D DLT). For the 2D DLT, at least one view of the object (points) must be entered. For the 3D DLT, at least 2 different views of the object (points) must be entered.   \n", "\n", "These coordinates (from the object and image(s)) are inputed to a DLT algorithm which estimates the camera parameters (8 for 2D DLT and 11 for 3D DLT).   \n", "\n", "Usually it is used more points than the minimum necessary and the overdetermined linear system is solved by a least squares minimization algorithm or using singular value decomposition (SVD).\n", "\n", "With these camera parameters and with the camera(s) at the same position of the calibration step, we now can reconstruct the real position of any point inside the calibrated space (area for 2D DLT and volume for the 3D DLT) from the point position(s) viewed by the same fixed camera(s).    \n", "\n", "See more about DLT at [https://docs.opencv.org/3.4/d9/dab/tutorial_homography.html](https://docs.opencv.org/3.4/d9/dab/tutorial_homography.html)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Python code for camera calibration and point reconstruction based on DLT\n", "\n", "> [dltx](https://pypi.org/project/dltx/): This package implements camera calibration and point reconstruction by direct linear transformation (DLT)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: dltx in /home/<USER>/miniconda3/lib/python3.9/site-packages (0.1)\n", "Requirement already satisfied: numpy in /home/<USER>/miniconda3/lib/python3.9/site-packages (from dltx) (1.24.1)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install dltx"]}, {"cell_type": "markdown", "metadata": {"scrolled": false}, "source": ["### Example from [dltx](https://github.com/joonaojapalo/dltx)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.31704156e-01,  8.71539661e-01,  2.54975288e+03])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dltx import dlt_calibrate, dlt_reconstruct\n", "\n", "# Define locations of 6 or more points in real world.\n", "world_positions = [\n", "    [0,     0,      2550],  # point 1\n", "    [0,     0,      0],     # point 2\n", "    [0,     2632,   0   ],  # point 3\n", "    [4500,  0,      2550],  # point 4\n", "    [5000,  0,      0   ],  # point 5\n", "    [5660,  2620,   0   ]   # point 6\n", "]\n", "\n", "# Define pixel coordinates of respective points seen by two or more cameras.\n", "cameras = [\n", "    # Camera 1\n", "    [\n", "        [1810, 885],\n", "        [1353, 786],\n", "        [1362, 301],\n", "        [455, 1010],\n", "        [329, 832],\n", "        [183, 180]\n", "    ],\n", "    # Camera 2\n", "    [\n", "        [1734, 952],\n", "        [1528, 768],\n", "        [1546, 135],\n", "        [115, 834],\n", "        [459, 719],\n", "        [358, 202]\n", "    ]\n", "]\n", "\n", "# Calibrate cameras\n", "n_dims = 3\n", "L1, err = dlt_calibrate(n_dims, world_positions, cameras[0])\n", "L2, err = dlt_calibrate(n_dims, world_positions, cameras[1])\n", "camera_calibration = [L1, L2]\n", "\n", "# Find world coordinates for `query_point` visible in both cameras\n", "query_point = [\n", "    [1810, 885], # cam 1\n", "    [1734, 952]  # cam 2\n", "    ]\n", "dlt_reconstruct(n_dims, len(cameras), camera_calibration, query_point)\n", "# coordinates in real world: [-1.31704156e-01,  8.71539661e-01,  2.54975288e+03]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# %load https://raw.githubusercontent.com/joonaojapalo/dltx/main/dltx/dltx.py\n", "#!/usr/bin/env python\n", "\n", "__author__ = \"<PERSON> <<EMAIL>>\"\n", "__version__ = \"DLT.py v.0.1.0 2023/01/16\"\n", "\n", "\n", "import numpy as np\n", "\n", "\n", "def dlt_calibrate(n_dims, xyz, uv):\n", "    \"\"\"\n", "    Camera calibration by DLT with known object points and image points\n", "    This code performs 2D or 3D DLT camera calibration with any number of\n", "        views (cameras).\n", "    For 3D DLT, at least two views (cameras) are necessary.\n", "    Inputs:\n", "        n_dims is the number of dimensions of the object space: 3 for 3D DLT\n", "        and 2 for 2D DLT.\n", "        xyz are the coordinates in the object 3D or 2D space of the\n", "        calibration points.\n", "        uv are the coordinates in the image 2D space of these calibration\n", "        points.\n", "        The coordinates (x,y,z and u,v) are given as columns and the different\n", "        points as rows.\n", "        For the 2D DLT (object planar space), only the first 2 columns\n", "        (x and y) are used.\n", "        There must be at least 6 calibration points for the 3D DLT and 4\n", "        for the 2D DLT.\n", "    Outputs:\n", "        L: array of the 8 or 11 parameters of the calibration matrix.\n", "        err: error of the DLT (mean residual of the DLT transformation in units \n", "        of camera coordinates).\n", "    \"\"\"\n", "\n", "    # Convert all variables to numpy array:\n", "    xyz = np.asarray(xyz)\n", "    uv = np.asarray(uv)\n", "    # Number of points:\n", "    n_points = xyz.shape[0]\n", "    # Check the parameters:\n", "    if uv.shape[0] != n_points:\n", "        raise ValueError('xyz (%d points) and uv (%d points) have different number of points.'\n", "                            % (n_points, uv.shape[0]))\n", "    if (n_dims == 2 and xyz.shape[1] != 2) or (n_dims == 3 and xyz.shape[1] != 3):\n", "        raise ValueError('Incorrect number of coordinates (%d) for %dD DLT (it should be %d).'\n", "                            % (xyz.shape[1], n_dims, n_dims))\n", "    if n_dims == 3 and n_points < 6 or n_dims == 2 and n_points < 4:\n", "        raise ValueError('%dD DLT requires at least %d calibration points. Only %d points were entered.'\n", "                            % (n_dims, 2*n_dims, n_points))\n", "\n", "    # Normalize the data to improve the DLT quality (DLT is dependent on the\n", "    #  system of coordinates).\n", "    # This is relevant when there is a considerable perspective distortion.\n", "    # Normalization: mean position at origin and mean distance equals to 1\n", "    #  at each direction.\n", "    Txyz, xyzn = normalize(n_dims, xyz)\n", "    Tuv, uvn = normalize(2, uv)\n", "    # Formulating the problem as a set of homogeneous linear equations, M*p=0:\n", "    A = []\n", "    if n_dims == 2:  # 2D DLT\n", "        for i in range(n_points):\n", "            x, y = xyzn[i, 0], xyzn[i, 1]\n", "            u, v = uvn[i, 0], uvn[i, 1]\n", "            A.append([x, y, 1, 0, 0, 0, -u*x, -u*y, -u])\n", "            A.append([0, 0, 0, x, y, 1, -v*x, -v*y, -v])\n", "    elif n_dims == 3:  # 3D DLT\n", "        for i in range(n_points):\n", "            x, y, z = xyzn[i, 0], xyzn[i, 1], xyzn[i, 2]\n", "            u, v = uvn[i, 0], uvn[i, 1]\n", "            A.append([x, y, z, 1, 0, 0, 0, 0, -u*x, -u*y, -u*z, -u])\n", "            A.append([0, 0, 0, 0, x, y, z, 1, -v*x, -v*y, -v*z, -v])\n", "\n", "    # Convert A to array:\n", "    A = np.asarray(A)\n", "    # Find the 11 (or 8 for 2D DLT) parameters:\n", "    U, S, Vh = np.linalg.svd(A)\n", "    # The parameters are in the last line of Vh and normalize them:\n", "    L = Vh[-1, :] / Vh[-1, -1]\n", "    # Camera projection matrix:\n", "    H = L.reshape(3, n_dims+1)\n", "    # Denormalization:\n", "    H = np.dot(np.dot(np.linalg.pinv(Tuv), H), Txyz)\n", "    H = H / H[-1, -1]\n", "    L = H.flatten()\n", "    # Mean error of the DLT (mean residual of the DLT transformation in\n", "    #  units of camera coordinates):\n", "    uv2 = np.dot(H, np.concatenate((xyz.T, np.ones((1, xyz.shape[0])))))\n", "    uv2 = uv2 / uv2[2, :]\n", "    # Mean distance:\n", "    err = np.sqrt(np.mean(np.sum((uv2[0:2, :].T - uv)**2, 1)))\n", "\n", "    return L, err\n", "\n", "def dlt_reconstruct(n_dims, n_cams, Ls, uvs):\n", "    \"\"\"\n", "    Reconstruction of object point from image point(s) based on the DLT parameters.\n", "    This code performs 2D or 3D DLT point reconstruction with any number of\n", "        views (cameras).\n", "    For 3D DLT, at least two views (cameras) are necessary.\n", "    Inputs:\n", "        n_dims is the number of dimensions of the object space: 3 for 3D DLT\n", "        and 2 for 2D DLT.\n", "        n_cams is the number of cameras (views) used.\n", "        Ls (array type) are the camera calibration parameters of each camera \n", "        (is the output of DLTcalib function). The Ls parameters are given\n", "        as columns and the Ls for different cameras as rows.\n", "        uvs are the coordinates of the point in the image 2D space of each camera.\n", "        The coordinates of the point are given as columns and the different\n", "        views as rows.\n", "    Outputs:\n", "        xyz: point coordinates in space.\n", "    \"\"\"\n", "\n", "    # Convert Ls to array:\n", "    Ls = np.asarray(Ls)\n", "    # Check the parameters:\n", "    if Ls.ndim == 1 and n_cams != 1:\n", "        raise ValueError('Number of views (%d) and number of sets of camera calibration parameters (1) are different.'\n", "                            % (n_cams))\n", "    if Ls.ndim > 1 and n_cams != Ls.shape[0]:\n", "        raise ValueError('Number of views (%d) and number of sets of camera calibration parameters (%d) are different.'\n", "                            % (n_cams, Ls.shape[0]))\n", "    if n_dims == 3 and Ls.ndim == 1:\n", "        raise ValueError(\n", "            'At least two sets of camera calibration parametersd are neede for 3D point reconstruction.')\n", "\n", "    # 2D and 1 camera (view), the simplest (and fastest) case.\n", "    if n_cams == 1:\n", "        # One could calculate inv(H) and input that to the code to speed up\n", "        #  things if needed.\n", "        Hinv = np.linalg.inv(<PERSON>s.reshape(3, 3))\n", "        # Point coordinates in space:\n", "        xyz = np.dot(Hinv, [uvs[0], uvs[1], 1])\n", "        xyz = xyz[0:2] / xyz[2]\n", "    else:\n", "        # Formulate problem as a set of homogeneous linear equations, A*p=0:\n", "        M = []\n", "        for i in range(n_cams):\n", "            L = Ls[i, :]\n", "            # indexing works for both list and numpy array\n", "            u, v = uvs[i][0], uvs[i][1]\n", "            if n_dims == 2:\n", "                <PERSON><PERSON>append([L[0]-u*L[6], L[1]-u*L[7], L[2]-u*L[8]])\n", "                <PERSON><PERSON>append([L[3]-v*L[6], L[4]-v*L[7], L[5]-v*L[8]])\n", "            elif n_dims == 3:\n", "                <PERSON><PERSON>append([L[0]-u*L[8], L[1]-u*L[9],\n", "                            L[2]-u*L[10], L[3]-u*L[11]])\n", "                <PERSON><PERSON>append([L[4]-v*L[8], L[5]-v*L[9],\n", "                            L[6]-v*L[10], L[7]-v*L[11]])\n", "\n", "        # Find the xyz coordinates:\n", "        U, S, Vh = np.linalg.svd(np.asarray(M))\n", "        # Point coordinates in space:\n", "        xyz = Vh[-1, 0:-1] / Vh[-1, -1]\n", "\n", "    return xyz\n", "\n", "def normalize(n_dims, x):\n", "    \"\"\"Normalization of coordinates (centroid to the origin and mean distance of sqrt(2 or 3)).\n", "    Inputs:\n", "        n_dims: number of dimensions (2 for 2D; 3 for 3D)\n", "        x: the data to be normalized (directions at different columns and points at rows)\n", "    Outputs:\n", "        Tr: the transformation matrix (translation plus scaling)\n", "        x: the transformed data\n", "    \"\"\"\n", "\n", "    x = np.asarray(x)\n", "    m, s = np.mean(x, 0), np.std(x)\n", "    if n_dims == 2:\n", "        Tr = np.array([[s, 0, m[0]],\n", "                        [0, s, m[1]],\n", "                        [0, 0,   1]])\n", "    else:\n", "        Tr = np.array([[s, 0, 0, m[0]],\n", "                        [0, s, 0, m[1]],\n", "                        [0, 0, s, m[2]],\n", "                        [0, 0, 0,   1]])\n", "\n", "    Tr = np.linalg.inv(Tr)\n", "    x = np.dot(Tr, np.concatenate((x.T, np.ones((1, x.shape[0])))))\n", "    x = x[0:n_dims, :].T\n", "\n", "    return Tr, x\n", "\n"]}], "metadata": {"hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "vscode": {"interpreter": {"hash": "1d2a593bd81c41ea841d7e55fa713b60dcb5304d3f00607b05ce5d23dd67b6bd"}}}, "nbformat": 4, "nbformat_minor": 1}