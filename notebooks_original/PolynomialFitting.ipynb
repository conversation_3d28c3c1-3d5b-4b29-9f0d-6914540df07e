{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial fitting with confidence/prediction intervals\n", "\n", "> <PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br/)  \n", "> Federal University of ABC, Brazil"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:20:57.580664Z", "start_time": "2017-12-30T07:20:57.571928Z"}, "editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import sys\n", "sys.path.insert(1, r'./../functions')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's implement the polynomial fit by least squares and calculate the confidence and prediction intervals assuming normal distribution of the residuals.  \n", "The code of the `polyfit.py` function is at the end of this notebook (or download the function from the GitHub repo)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:21:04.658840Z", "start_time": "2017-12-30T07:21:04.461819Z"}, "editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["from polyfit import polyfit"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:22:09.356316Z", "start_time": "2017-12-30T07:22:09.350357Z"}, "editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function polyfit in module polyfit:\n", "\n", "polyfit(x, y, degree, yerr=None, plot=True, xlabel='x', ylabel='y', title=True, legend=True, plotCI=True, plotPI=True, axis=None)\n", "    Least squares polynomial regression of order degree for x vs. y [1]_\n", "    \n", "    Parameters\n", "    ----------\n", "    x : numpy array_like, shape (N,)\n", "        Independent variable, x-coordinates of the N points (x[i], y[i]).\n", "    y : numpy array_like, shape (N,)\n", "        Dependent variable, y-coordinates of the N points (x[i], y[i]).\n", "    degree : integer\n", "        Degree of the polynomial to be fitted to the data.\n", "    yerr : numpy array_like, shape (N,), optional (default = None)\n", "        Error (uncertainty) in y. If no error is entered, unitary equal errors\n", "        for all y values are assumed.\n", "    plot : bool, optional (default = True)\n", "        Show plot (True) of not (False). \n", "    xlabel : string, optional (default = 'x')\n", "        Label for the x (horizontal) axis.\n", "    ylabel : string, optional (default = 'y')\n", "        Label for the y (vertical) axis.\n", "    title : bool, optional (default = True)\n", "        Show title (True) of not (False) in the plot.\n", "    legend : bool, optional (default = True)\n", "        Show legend (True) of not (False) in the plot.\n", "    plotCI : bool, optional (default = True)\n", "        Plot the shaded area for the confidence interval (True) of not (False).\n", "    plotPI : bool, optional (default = True)\n", "        Plot the shaded area for the prediction interval (True) of not (False).\n", "    axis : matplotlib object, optional (default = None)\n", "        Mat<PERSON>lot<PERSON>b axis object where to plot.\n", "    \n", "    Returns\n", "    -------\n", "    p : numpy array, shape (deg + 1,)\n", "        Coefficients of the least squares polynomial fit.\n", "    perr : numpy array, shape (deg + 1,)\n", "        Standard-deviation of the coefficients.\n", "    R2 : float\n", "        Coefficient of determination.\n", "    chi2red : float\n", "        Reduced chi-squared\n", "    yfit : numpy array, shape (N + 1,)\n", "        Values of the fitted polynomial evaluated at x.\n", "    ci : numpy array, shape (N + 1,)\n", "        Values of the 95% confidence interval evaluated at x.\n", "    pi : numpy array, shape (N + 1,)\n", "        Values of the 68% prediction interval evaluated at x.\n", "    MSE : float\n", "        Mean squared error of the regression.\n", "    \n", "    References\n", "    ----------\n", "    .. [1] https://docs.scipy.org/doc/numpy/reference/generated/numpy.polyfit.html\n", "    \n", "    \n", "    Examples\n", "    --------\n", "    >>> import numpy as np\n", "    >>> import matplotlib.pyplot as plt\n", "    >>> N = 50\n", "    >>> x = np.linspace(-2, 4, N)\n", "    >>> y = np.polyval([3, 1, 2], x) + np.random.randn(N)*8\n", "    >>> # simple use:\n", "    >>> polyfit(x, y, 2);\n", "    >>> # compare two models:\n", "    >>> fig, ax = plt.subplots(1, 2, figsize=(14, 5))\n", "    >>> p1, perr1, R21, chi2red1, yfit1, ci1, pi1, MSE = polyfit(x, y, 1, axis=ax[0])\n", "    >>> p2, perr2, R22, chi2red2, yfit2, ci2, pi2, MSE = polyfit(x, y, 2, axis=ax[1])\n", "    >>> plt.tight_layout()\n", "    >>> Enter error (uncertainty) in y:\n", "    >>> yerr = np.ones(N)*8\n", "    >>> polyfit(x, y, 2, yerr);\n", "    \n", "    \n", "    Version history\n", "    ---------------\n", "    '1.0.3':\n", "        Correct the order of the terms in title and account for zeros in yerr.\n", "    '1.0.2':\n", "        Included output 'MSE', mean squared error of the regression.\n", "\n"]}], "source": ["help(polyfit)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Some data to play:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:24:23.464833Z", "start_time": "2017-12-30T07:24:23.456844Z"}}, "outputs": [], "source": ["N = 50\n", "x = np.linspace(-2, 4, N)\n", "y = np.polyval([3, 1, 2], x) + np.random.randn(N)*8"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:25:24.810638Z", "start_time": "2017-12-30T07:25:24.535715Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["p, perr, R2, chi2red, yfit, ci, pi, MSE = polyfit(x, y, 2)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:25:33.853800Z", "start_time": "2017-12-30T07:25:33.848380Z"}}, "outputs": [{"data": {"text/plain": ["array([2.96260407, 0.85157922, 1.16748645])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["p"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["64.**************"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["MSE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The $\\chi^2_{red}$ value is much higher than one, suggesting a poor fitting (although the $R^2$ value is good). We can see that the data variability not accounted by the model is very high. One way of considering this variability is to treat it as error (uncertainty) in the measurement of the y variable. The square root of the $\\chi^2_{red}$ value is a good average estimate of this error in y (see for example page 79 of [https://www.astro.rug.nl/software/kapteyn/_downloads/statmain.pdf](https://www.astro.rug.nl/software/kapteyn/_downloads/statmain.pdf)).  \n", "Let's generate an array for the error in y:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:26:03.775357Z", "start_time": "2017-12-30T07:26:03.769490Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average uncertainty in y: 8.***************\n"]}], "source": ["yerr = np.ones(N)*np.sqrt(chi2red)\n", "print('Average uncertainty in y:', yerr[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And let's run the fitting again:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:26:10.638163Z", "start_time": "2017-12-30T07:26:10.351175Z"}}, "outputs": [{"data": {"image/png": "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***************************+1/zTXXsHLlSltCjfPnz7Nq1SpATyOflJTEiRMnOHv2LOPGjUPTNJo1a1aBZyiuRNKyJYQQQgghqpXnnnuO7OzsYvOY/ec//+Gtt94iNTWVmJgY/vGPf/DII49gsVgYP348X331FfXr16d37978/PPPxbonCuFtEmwJIYQQQgghRDmQboRCCCGEEEIIUQ4k2BJCCCGEEEKIcuBb2RWoDiwWC8nJyYSGhqJpWmVXRwghhBBCCFFJlFJcvHiRuLg4DIaS264k2HJBcnIyDRo0qOxqCCGEEEIIIaqIEydOUL9+/RK3kWDLBaGhoYB+QcPCwiq5NkIIIYQQQojKkpGRQYMGDWwxQkkk2HKBtetgWFiYBFtCCCGEEEIIl4YXSYIMIYQQQgghhCgHEmwJIYQQQgghRDmQYEsIIYQQQgghyoGM2fISpRT5+fkUFBRUdlWEqNF8fHzw9fWVaRiEEEIIUeVJsOUFubm5nDp1iuzs7MquihBXhKCgIGJjY/H396/sqgghhBBCOCXBlocsFgtHjx7Fx8eHuLg4/P395Rt3IcqJUorc3FzOnj3L0aNHad68eamTCQohhBBCVBYJtjyUm5uLxWKhQYMGBAUFVXZ1hKjxAgMD8fPz488//yQ3Nxej0VjZVRJCCCGEcEi+EvYS+XZdiIojf29CCCGEqA7kE4sQQgghhBBClAMJtoQQQgghhBCiHEiwJa5ICQkJDBs2zPZzz549mTp1qkdleqOM0iQlJaFpGmlpaeV6nPKmaRrLli2r7GoIIYQQQpQrCbbKU3Jyxb7K4OTJk4wdO5bo6GgCAwNp164dW7duta3PzMzkwQcfpH79+gQGBtK6dWvee+89uzIefvhhoqKiaNCgAQsWLLBbt3jxYgYPHlymulWkr776ihdffNGlbZ0FPO6UUVZdu3bl1KlThIeHu7zP5YGlEEIIIYSoGJKN8AqWmppKt27d6NWrFytWrKB27docOnSIyMhI2zYPP/wwP/74I59++imNGzfm+++/5x//+AdxcXEMGTKEb775hoULF/L9999z6NAhxo8fT//+/alVqxbp6ek8/fTTrF69ulzqn5ub67V5lqKioqpEGaXx9/enbt265X4cR7x5vYUQQgghrgTSsnUF+89//kODBg2YO3cu1113HU2aNKFfv340bdrUts3PP//MuHHj6NmzJ40bN+a+++7j6quv5tdffwVg//799OzZk06dOnHXXXcRFhbG0aNHAXjsscd44IEHaNiwYal1mT59Otdccw2zZ8+2pdEfNWoU6enptm2sLTQzZswgLi6Oli1bAnDixAlGjRpFREQEUVFRDB06lGPHjtn2Kygo4OGHHyYiIoLo6Ggee+wxlFJ2x7+8C6DZbObxxx+nQYMGBAQE0KxZMz788EOOHTtGr169AIiMjETTNBISEhyWkZqayj333ENkZCRBQUHccsstHDp0yLb+448/JiIiglWrVhEfH09ISAgDBgzg1KlTTq/T5a1qpZUxffp05s2bx9dff42maWiaRlJSkkvXzdH1fuqpp7j++uuL1evqq6/mhRdeAGDLli3cfPPN1KpVi/DwcHr06MH27dudnpMQQgghRE0lwdYVLDExkU6dOjFy5Ejq1KlDhw4deP/99+226dq1K4mJiZw8eRKlFD/99BMHDx6kX79+gP4he+vWraSmprJt2zZycnJo1qwZGzZsYPv27UyePNnl+hw+fJgvvviCb775hpUrV7Jjxw7+8Y9/2G2zZs0aDhw4wA8//MDy5cvJy8ujf//+hIaGsn79ejZu3GgLOHJzcwH473//y8cff8xHH33Ehg0buHDhAkuXLi2xLvfccw+LFi3izTffZP/+/cyePZuQkBAaNGjAl19+CcCBAwc4deoUs2bNclhGQkICW7duJTExkV9++QWlFAMHDiQvL8+2TXZ2NjNnzmT+/PmsW7eO48eP8+ijj7p8zUor49FHH2XUqFG2AOzUqVN07drVpevm6HqPGTOGX3/9lSNHjti2+e2339i9ezejR48G4OLFi4wbN44NGzawadMmmjdvzsCBA7l48aJb5yWEEEKIqicr24JW7xRavVNkZVsquzpVnxKlSk9PV4BKT08vti4nJ0ft27dP5eTkFN/x5MmKfbkpICBABQQEqCeffFJt375dzZ49WxmNRvXxxx/btjGZTOqee+5RgPL19VX+/v5q3rx5duU899xzqmnTpqpt27bqq6++UmazWbVt21Zt3bpV/e9//1MtWrRQXbt2VXv37nVal+eee075+Piov/76y7ZsxYoVymAwqFOnTimllBo3bpyKiYlRZrPZts38+fNVy5YtlcVisS0zm80qMDBQrVq1SimlVGxsrHrllVds6/Py8lT9+vXV0KFDbct69OihpkyZopRS6sCBAwpQP/zwg8O6/vTTTwpQqampdsuLlnHw4EEFqI0bN9rWnzt3TgUGBqovvvhCKaXU3LlzFaAOHz5s2+btt99WMTExTq/T5cd2pYxx48bZnatSrl03R9dbKaWuvvpq9cILL9h+fvLJJ9X111/vtM4FBQUqNDRUffPNN7ZlgFq6dKnTfUpT4t+dEEIIIcpNZlaBIi5ZEZesMrMKKrs6laKk2OBy0rJ1BbNYLHTs2JF//etfdOjQgfvuu4+JEyfaJcD43//+x6ZNm0hMTGTbtm3897//ZdKkSXbjsKZPn87hw4fZs2cPw4cP5+WXX6Zv3774+fnx0ksvsWHDBiZMmMA999xTYn0aNmxIvXr1bD936dIFi8XCgQMHbMvatWtnN25o165dHD58mNDQUEJCQggJCSEqKgqTycSRI0dIT0/n1KlTdl3ffH196dSpk9N67Ny5Ex8fH3r06OHahXRg//79+Pr62h03Ojqali1bsn//ftuyoKAgu26bsbGxnDlzxq1jlaWM0q6b1eXXG2DMmDEsXLgQAKUUixYtYsyYMbb1KSkpTJw4kebNmxMeHk5YWBiZmZkcP37crfMSQgghhKjuJEHGFSw2NpbWrVvbLYuPj7d1k8vJyeGpp55i6dKl3HrrrQC0b9+enTt3MnPmTPr27VuszN9//51PP/2UHTt28NFHH9G9e3dq167NqFGjGD9+PBcvXiQ0NLTMdQ4ODrb7OTMzk2uvvbZYFkSA2rVrl+kYgYGBZdqvLPz8/Ox+1jSt2Hiy8ijD1et2+fUGuOuuu3j88cfZvn07OTk5nDhxgjvuuMO2fty4cZw/f55Zs2bRqFEjAgIC6NKli133RCGEEEKIK4EEW1ewbt262bUaARw8eJBGjRoBkJeXR15eHgaDfQOoj48PFkvxPrpKKe6//35ee+01QkJCKCgosI1Psv5bUFDgtD7Hjx8nOTmZuLg4ADZt2oTBYLAlwnCkY8eOfP7559SpU4ewsDCH28TGxrJ582a6d+8OQH5+Ptu2baNjx44Ot2/Xrh0Wi4W1a9c6DCitLT0lnUt8fDz5+fls3ryZrl27AnD+/HkOHDhQLMAtb/7+/sXq6sp1c6Z+/fr06NGDBQsWkJOTw80330ydOnVs6zdu3Mg777zDwIEDAT0Rx7lz5zw/ESGEEEKIaka6EV7B/u///o9Nmzbxr3/9i8OHD7Nw4ULmzJnDpEmTAAgLC6NHjx5MmzaNpKQkjh49yscff8wnn3zC8OHDi5X3wQcfULt2bdu8Wt26dePHH39k06ZNvP7667Ru3ZqIiAin9TEajYwbN45du3axfv16Jk+ezKhRo0pMdT5mzBhq1arF0KFDWb9+PUePHiUpKYnJkyfz119/ATBlyhT+/e9/s2zZMn7//Xf+8Y9/lDgpcOPGjRk3bhzjx49n2bJltjK/+OILABo1aoSmaSxfvpyzZ8+SmZlZrIzmzZszdOhQJk6cyIYNG9i1axdjx46lXr16DB061Omxy0Pjxo3ZvXs3Bw4c4Ny5c+Tl5bl03UoyZswYPvvsMxYvXmzXhRD0c58/fz779+9n8+bNjBkzpkJbC4UQQgghqgoJtspTXFzFvtzUuXNnli5dyqJFi2jbti0vvvgib7zxht2H588++4zOnTszZswYWrduzb///W9mzJjB3//+d7uyUlJSmDFjBm+++aZt2XXXXccjjzzCrbfeyhdffMHcuXNLrE+zZs247bbbGDhwIP369aN9+/a88847Je4TFBTEunXraNiwIbfddhvx8fH87W9/w2Qy2VpsHnnkEe6++27GjRtHly5dCA0NdRgsFvXuu+8yYsQI/vGPf9CqVSsmTpxIVlYWAPXq1eP555/niSeeICYmhgcffNBhGXPnzuXaa69l0KBBdOnSBaUU3333XbFuf+Vt4sSJtGzZkk6dOlG7dm02btzo0nUryYgRIzh//jzZ2dnFJkz+8MMPSU1NpWPHjtx9991MnjzZruVLCCGEEOJKoSl3B4hcgTIyMggPDyc9Pb3YB1GTycTRo0dp0qQJRqOxkmpY/U2fPp1ly5axc+fOyq6KqAbk704IIYSoHFnZFkKapwCQeSiG4KArr+2mpNjgclfe1RFCCCGEEEKICiDBlhBCCCGEEEKUAwm2RJUwffp06UIohBBCCCFqFAm2hBBCCCGEEKIcSLAlhBBCCCGEEOVAgi0hhBBCCCGEKAcSbAkhhBBCCCFEOZBgSwghhBBCCCHKgQRbQgghhBBCCFEOfCu7AjXaN99U7PEGD67Y41UzSinuv/9+lixZQmpqKjt27GDq1Klcc801vPHGG073a9y4MVOnTmXq1KkVVld3JSUl0atXL1JTU4mIiKjs6pSZpmksXbqUYcOGVXZVhBBCCCE8Ji1bV7CLFy8ydepUGjVqRGBgIF27dmXLli122yQkJKBpmt1rwIABtvVms5m7776bsLAwWrRowerVq+32f/XVV3nooYcq5HxKs3LlSj7++GOWL1/OqVOnaNu2LV999RUvvvhiZVfNY127duXUqVOEh4e7vE9CQoIENUIIIYQQ5Uhatq5gEyZMYO/evcyfP5+4uDg+/fRT+vbty759+6hXr55tuwEDBjB37lzbzwEBAbb/z5kzh23btvHLL7+wYsUKRo8eTUpKCpqmcfToUd5//322bt1aoeflzJEjR4iNjaVr1662ZVFRUZVYI+/x9/enbt26lXLs3Nxc/P39K+XYQgghhBBVmbRsXaFycnL48ssveeWVV+jevTvNmjVj+vTpNGvWjHfffddu24CAAOrWrWt7RUZG2tbt37+fIUOG0KZNGyZNmsTZs2c5d+4cAA888AD/+c9/CAsLc6lOH330EW3atCEgIIDY2FgefPBB27rjx48zdOhQQkJCCAsLY9SoUaSkpNjWT58+nWuuuYb58+fTuHFjwsPDufPOO7l48SKgt+I89NBDHD9+HE3TaNy4MQA9e/a06x545swZBg8eTGBgIE2aNGHBggXF6pmWlsaECROoXbs2YWFh9O7dm127drlcFwCLxcIrr7xCs2bNCAgIoGHDhsyYMcO2/sSJE4waNYqIiAiioqIYOnQox44dc3rtkpKS0DSNtLQ0AD7++GMiIiJYtWoV8fHxhISEMGDAAE6dOmWr47x58/j6669tLZZJSUkuHdvaIjZjxgzi4uJo2bIlTz31FNdff32xel199dW88MILAGzZsoWbb76ZWrVqER4eTo8ePdi+fbvTcxJCCCGEqO4k2LpC5efnU1BQgNFotFseGBjIhg0b7JYlJSVRp04dWrZsyQMPPMD58+dt666++mo2bNhATk4Oq1atIjY2llq1arFgwQKMRiPDhw93qT7vvvsukyZN4r777mPPnj0kJibSrFkzQA9Mhg4dyoULF1i7di0//PADf/zxB3fccYddGUeOHGHZsmUsX76c5cuXs3btWv79738DMGvWLF544QXq16/PqVOninWXtEpISODEiRP89NNPLFmyhHfeeYczZ87YbTNy5EjOnDnDihUr2LZtGx07dqRPnz5cuHDBpboAPPnkk/z73//mn//8J/v27WPhwoXExMQAkJeXR//+/QkNDWX9+vVs3LjRFizl5ua6dD0BsrOzmTlzJvPnz2fdunUcP36cRx99FIBHH32UUaNG2QKwU6dO0bVrV5ePvWbNGg4cOMAPP/zA8uXLGTNmDL/++itHjhyxbfPbb7+xe/duRo8eDejdVseNG8eGDRvYtGkTzZs3Z+DAgXZBqBBCCCFETSLdCK9QoaGhdOnShRdffJH4+HhiYmJYtGgRv/zyiy3IAb0L4W233UaTJk04cuQITz31FLfccgu//PILPj4+jB8/nt27d9O6dWtq1arFF198QWpqKs8++yxJSUk888wzfPbZZzRt2pSPPvrIrntiUS+99BKPPPIIU6ZMsS3r3LkzoH+w37NnD0ePHqVBgwYAfPLJJ7Rp04YtW7bYtrNYLHz88ceEhoYCcPfdd7NmzRpmzJhBeHg4oaGh+Pj4OO1ud/DgQVasWMGvv/5qK/PDDz8kPj7ets2GDRv49ddfOXPmjK075cyZM1m2bBlLlizhvvvuK7UuFy9eZNasWbz11luMGzcOgKZNm3LjjTcC8Pnnn2OxWPjggw/QNA2AuXPnEhERQVJSEv369XPpd5yXl8d7771H06ZNAXjwwQdtrUwhISEEBgZiNpvtrsenn37q0rGDg4P54IMP7LoPXn311SxcuJB//vOfACxYsIDrr7/edj/17t3brn5z5swhIiKCtWvXMmjQIJfOSQghhBCiOpGWrSvY/PnzUUpRr149AgICePPNN7nrrrswGApvizvvvJMhQ4bQrl07hg0bxvLly9myZYuty5mfnx9vv/02R48eZcuWLdx444088sgjTJ48mR07drBs2TJ27drFDTfcwOTJkx3W48yZMyQnJ9OnTx+H6/fv30+DBg1sgRZA69atiYiIYP/+/bZljRs3tgU3ALGxscVapUqyf/9+fH19ufbaa23LWrVqZZfdb9euXWRmZhIdHU1ISIjtdfToUbtWnZLqsn//fsxms9Pz3bVrF4cPHyY0NNRWflRUFCaTye4YpQkKCrIFWpfXwRlXj92uXbti47TGjBnDwoULAT3z46JFixgzZoxtfUpKChMnTqR58+aEh4cTFhZGZmYmx48fd/mchBBCCCGqE2nZuoI1bdqUtWvXkpWVRUZGBrGxsdxxxx1cddVVTve56qqrqFWrFocPH3YYLPz000/89ttvfPDBB0ybNo2BAwcSHBzMqFGjeOuttxyWGRgY6JXz8fPzs/tZ0zQsFotXyrbKzMwkNjbWFmwWVTQoK6kupZ1vZmYm1157rcPxYrVr13a5ro7qoJTyyrGDg4OLrb/rrrt4/PHH2b59Ozk5OZw4ccKuq+e4ceM4f/48s2bNolGjRgQEBNClSxe3ukYKIYQQQlQnEmwJgoODCQ4OJjU1lVWrVvHKK6843favv/7i/PnzxMbGFltnMpmYNGkSCxYswMfHh4KCAtuH+7y8PAoKChyWGRoaSuPGjVmzZg29evUqtj4+Pp4TJ05w4sQJW+vWvn37SEtLo3Xr1mU5ZYdatWpFfn4+27Zts3UjPHDggC3pBEDHjh05ffo0vr6+tiQb7mrevDmBgYGsWbOGCRMmFFvfsWNHPv/8c+rUqeNycpGy8Pf3L/Y78eTY9evXp0ePHixYsICcnBxuvvlm6tSpY1u/ceNG3nnnHQYOHAjoiTisyVSEEEIIIWoi6UZ4BVu1ahUrV67k6NGj/PDDD/Tq1YtWrVpx7733Anorx7Rp09i0aRPHjh1jzZo1DB06lGbNmtG/f/9i5b344osMHDiQDh06ANCtWze++uordu/ezVtvvUW3bt2c1mX69On897//5c033+TQoUNs376d//3vfwD07duXdu3aMWbMGLZv386vv/7KPffcQ48ePejUqZPXrkfLli0ZMGAA999/P5s3b2bbtm1MmDDBriWqb9++dOnShWHDhvH9999z7Ngxfv75Z55++mmXU9wbjUYef/xxHnvsMT755BOOHDnCpk2b+PDDDwG9O16tWrUYOnQo69ev5+jRoyQlJTF58mT++usvr51v48aN2b17NwcOHODcuXPk5eV5fOwxY8bw2WefsXjxYrsuhKAHmfPnz2f//v1s3ryZMWPGeK1VUwghhBDlz2RSLFqWY/t59KQ05i/JxmQquefMlUxatsrT4MGVXYMSpaen8+STT/LXX38RFRXF7bffzowZM2zdz3x8fNi9ezfz5s0jLS2NuLg4+vXrx4svvmg31xbA3r17+eKLL9i5c6dt2YgRI0hKSuKmm26iZcuWtvE8jowbNw6TycTrr7/Oo48+Sq1atRgxYgSgd3/7+uuveeihh+jevTsGg4EBAwbYgjFvmjt3LhMmTKBHjx7ExMTw0ksv2RI+WOvy3Xff8fTTT3Pvvfdy9uxZ6tatS/fu3W3ZBF3xz3/+E19fX5599lmSk5OJjY3l73//O6CPtVq3bh2PP/44t912GxcvXqRevXr06dPHqy1dEydOJCkpiU6dOpGZmclPP/1Ez549PTr2iBEjePDBB/Hx8Sk2YfKHH37IfffdR8eOHWnQoAH/+te/bNkRhRBCCFG1JX5vImFqGqnphYHV8tVmEr83M+XZDOa9EcHgfsYSSrgyaaq0QRyCjIwMwsPDSU9PL/aB02QycfToUZo0aVIsjboQonzI350QQghRcRK/NzFsfCoAjiKHSwmMWfZRJEOugICrpNjgctKNUAghhBBCCOGQyaRImJoGOA60ii5PmJomXQovI8GWEEIIIYQQwqHFy3NITVdOAy0rpSA1XbHk25ySN7zCSLAlhBBCCCGEcGjZKjMGFyMGgwGWrjSXb4WqGQm2hBBCCCGEEA6dT7Xg6rSlFgtcSPXuHKfVnQRbQgghhBBCCIeiIw1utWxFRUp4UZRcDSGEEEIIIYRDw/oHuNWyNXxAQOkbXkEk2BJCCCGEEEI4NHJQIJHhmi29uzOaBpHhGiNuDayYilUTNSLYOnnyJGPHjiU6OprAwEDatWvH1q1bbeuVUjz77LPExsYSGBhI3759OXToUCXWWAghhBBCiKrPaNSY90YEgNOAy7p83hsRGI2lRGVXmGofbKWmptKtWzf8/PxYsWIF+/bt47///S+RkZG2bV555RXefPNN3nvvPTZv3kxwcDD9+/fHZDJVYs2FEEIIIYSo+gb3M7Lso0giwuwDKetYrogwja8/imRweU5obDZDQUH5lV9Oqn2w9Z///IcGDRowd+5crrvuOpo0aUK/fv1o2rQpoLdqvfHGGzzzzDMMHTqU9u3b88knn5CcnMyyZcsqt/LV3Mcff0xERERlV8Ml06dP55prrnFrH03TSr1Hzp8/T506dTh27JjTbZKSktA0jbS0NLeOX52sXLmSa665BosLnbqnT59OTEyM7fomJCQwbNiw8q+kEEIIIcpsSD8jydtj+GBmmG3ZoL4BzH8znOTtMeUbaGVmwvnzuDx4rAqp9sFWYmIinTp1YuTIkdSpU4cOHTrw/vvv29YfPXqU06dP07dvX9uy8PBwrr/+en755ReHZZrNZjIyMuxeNVFCQgKapqFpGv7+/jRr1owXXniB/Pz8yq6a1z366KOsWbPG6+XOmDGDoUOH0rhxY6+XXVUcO3aMv/3tbzRp0oTAwECaNm3Kc889R25urm2bAQMG4Ofnx4IFC0osa//+/Tz//PPMnj2bU6dOccsttzBr1iw+/vhj2zY9e/Zk6tSp5XQ2QgghhCgro1HjzqGFY7IWvh3B2NuDyq/roFKQmgrV+LO4b2VXwFN//PEH7777Lg8//DBPPfUUW7ZsYfLkyfj7+zNu3DhOnz4NQExMjN1+MTExtnWXe/nll3n++efLViGLBdLTy7avt4SH42qOzgEDBjB37lzMZjPfffcdkyZNws/PjyeffLKcK1mxQkJCCAkJ8WqZ2dnZfPjhh6xatcqr5ZZVXl4efn5+Xi/3999/x2KxMHv2bJo1a8bevXuZOHEiWVlZzJw507ZdQkICb775JnfffbfTso4cOQLA0KFD0S518A4IkKxFQgghhLhMQQFcuAB5eZVdE49U+5Yti8VCx44d+de//kWHDh247777mDhxIu+9916Zy3zyySdJT0+3vU6cOOH6zunpcPPNlftyI9gLCAigbt26NGrUiAceeIC+ffuSmJgI6OPh7rnnHiIjIwkKCuKWW25xmljk2LFjGAwGu8QkAG+88QaNGjXCYrHYutOtWbOGTp06ERQURNeuXTlw4IDdPu+++y5NmzbF39+fli1bMn/+fLv1mqYxe/ZsBg0aRFBQEPHx8fzyyy8cPnyYnj17EhwcTNeuXW0f7KF4N8ItW7Zw8803U6tWLcLDw+nRowfbt293+boBfPfddwQEBHDDDTcUW96iRQsCAwPp1auXwy6GGzZs4KabbiIwMJAGDRowefJksrKybOtPnTrFrbfeSmBgIE2aNGHhwoU0btyYN954w+46vPvuuwwZMoTg4GBmzJgBwNdff03Hjh0xGo1cddVVPP/883atlWlpaUyYMIHatWsTFhZG79692bVrl9PztAbk/fr146qrrmLIkCE8+uijfPXVV3bbDR48mK1bt9pd96KmT5/O4MGDATAYDLZgq2g3woSEBNauXcusWbNsra4lddEUQgghRA1kNsPZs9U+0IIaEGzFxsbSunVru2Xx8fEcP34cgLp16wKQkpJit01KSopt3eUCAgIICwuze10pAgMDbd3DEhIS2Lp1K4mJifzyyy8opRg4cCB5Dm78xo0b07dvX+bOnWu3fO7cuSQkJGAo0tL29NNP89///petW7fi6+vL+PHjbeuWLl3KlClTeOSRR9i7dy/3338/9957Lz/99JNduS+++CL33HMPO3fupFWrVowePZr777+fJ598kq1bt6KU4sEHH3R6nhcvXmTcuHFs2LCBTZs20bx5cwYOHMjFixddvlbr16/n2muvtVt24sQJbrvtNgYPHszOnTuZMGECTzzxhN02R44cYcCAAdx+++3s3r2bzz//nA0bNtjV95577iE5OZmkpCS+/PJL5syZw5kzZ4rVYfr06QwfPpw9e/Ywfvx41q9fzz333MOUKVPYt28fs2fP5uOPP7YFYgAjR47kzJkzrFixgm3bttGxY0f69OnDhQsXXD739PR0oqKi7JY1bNiQmJgY1q9f73CfRx991HZ/nDp1ilOnThXbZtasWXTp0oWJEyfatmnQoIHL9RJCCCFENVeNx2c5Uu2DrW7duhVrGTl48CCNGjUCoEmTJtStW9duvE5GRgabN2+mS5cuFVrXqkwpxerVq1m1ahW9e/fm0KFDJCYm8sEHH3DTTTdx9dVXs2DBAk6ePOk0acSECRNYtGgRZrMZgO3bt7Nnzx7uvfdeu+1mzJhBjx49aN26NU888QQ///yzLTPkzJkzSUhI4B//+ActWrTg4Ycf5rbbbrPrrgZw7733MmrUKFq0aMHjjz/OsWPHGDNmDP379yc+Pp4pU6aQlJTk9Hx79+7N2LFjadWqFfHx8cyZM4fs7GzWrl3r8jX7888/iYuLs1tmbZX773//S8uWLRkzZgwJCQl227z88suMGTOGqVOn0rx5c7p27cqbb77JJ598gslk4vfff2f16tW8//77XH/99XTs2JEPPviAnJycYnUYPXo09957L1dddRUNGzbk+eef54knnmDcuHFcddVV3Hzzzbz44ovMnj0b0FvUfv31VxYvXkynTp1o3rw5M2fOJCIigiVLlrh03ocPH+Z///sf999/f7F1cXFx/Pnnnw73CwkJsSVUqVu3rsMvO8LDw/H39ycoKMi2jY+Pj0v1EkIIIUQ1VgPGZzlS7YOt//u//2PTpk3861//4vDhwyxcuJA5c+YwadIkQO9qNXXqVF566SUSExPZs2cP99xzD3FxcZIBDVi+fDkhISEYjUZuueUW7rjjDqZPn87+/fvx9fXl+uuvt20bHR1Ny5Yt2b9/v8Oyhg0bho+PD0uXLgX0bIW9evUqljyiffv2tv/HxsYC2Fpt9u/fT7du3ey279atW7FjFi3DOh6vXbt2dstMJpPT5CYpKSlMnDiR5s2bEx4eTlhYGJmZmbYWUVfk5ORgNNpn3tm/f7/dNQOKBfW7du3i448/to0jCwkJoX///lgsFo4ePcqBAwfw9fWlY8eOtn2aNWtmN52BVadOnYqV/cILL9iVbW0lys7OZteuXWRmZhIdHW23zdGjR512/yvq5MmTDBgwgJEjRzJx4sRi6wMDA8nOzi61HCGEEEIIm4ICOHcOHHyxXN1V+wQZnTt3ZunSpTz55JO88MILNGnShDfeeIMxY8bYtnnsscfIysrivvvuIy0tjRtvvJGVK1cW+6DsFeHh8MMP3i/X3Tq4qFevXrz77rv4+/sTFxeHr2/Zbwl/f3/uuece5s6dy2233cbChQuZNWtWse2KJnGwjttxJWV4aWW4U+64ceM4f/48s2bNolGjRgQEBNClSxe7DHulqVWrFqmpqW7VGyAzM5P777+fyZMnF1vXsGFDDh486HJZwcHBxcp+/vnnue2224ptazQayczMJDY21mGrX2lp/JOTk+nVqxddu3Zlzpw5Dre5cOECtWvXdrn+QgghhLjCmc16i1YN6TZ4uWofbAEMGjSIQYMGOV2vaRovvPACL7zwQvlXxmAABy0QVVVwcDDNmjUrtjw+Pp78/Hw2b95M165dAX1OqQMHDhQbI1fUhAkTaNu2Le+88w75+fkOP/SXJD4+no0bNzJu3Djbso0bN5Z4zLLYuHEj77zzDgMHDgT0sVbnzp1zq4wOHTrw6aef2i2Lj4+3JRix2rRpk93PHTt2ZN++fQ6vO0DLli3Jz89nx44dtjFhhw8fdimw69ixIwcOHHBadseOHTl9+jS+vr5upas/efIkvXr14tprr2Xu3Ll2Y/CsTCYTR44coUOHDi6X64i/vz8F1XDSQiGEEEK4KTOzxnUbvFy170Yoykfz5s0ZOnQoEydOZMOGDezatYuxY8dSr149hg4d6nS/+Ph4brjhBh5//HHuuusuAgMDnW7ryLRp0/j444959913OXToEK+99hpfffUVjz76qKenZKd58+bMnz+f/fv3s3nzZsaMGeN2Xfv3789vv/1mFwT9/e9/59ChQ0ybNo0DBw6wcOFCuzmkAB5//HF+/vlnHnzwQXbu3MmhQ4f4+uuvbQkyWrVqRd++fbnvvvv49ddf2bFjB/fddx+BgYG2Fjtnnn32WT755BOef/55fvvtN/bv389nn33GM888A0Dfvn3p0qULw4YN4/vvv+fYsWP8/PPPPP3008UySVqdPHmSnj170rBhQ2bOnMnZs2c5ffp0sakTNm3aZGsh9ETjxo3ZvHkzx44d49y5c263egohhBCiiquh47MckWBLODV37lyuvfZaBg0aRJcuXVBK8d1335U6l9Pf/vY3cnNz7bIMumrYsGHMmjWLmTNn0qZNG2bPns3cuXPp2bNnGc/CsQ8//JDU1FQ6duzI3XffzeTJk6lTp45bZbRr146OHTvyxRdf2JY1bNiQL7/8kmXLlnH11Vfz3nvv8a9//ctuv/bt27N27VoOHjzITTfdRIcOHXj22Wftkm188sknxMTE0L17d4YPH87EiRMJDQ0ttetr//79Wb58Od9//z2dO3fmhhtu4PXXX7cljNE0je+++47u3btz77330qJFC+68807+/PPPYnPRWf3www8cPnyYNWvWUL9+fWJjY22vohYtWsSYMWMICgpy6zpe7tFHH8XHx4fWrVtTu3Ztt8bRCSGEEKKKy8/X07rXwPFZjmhKKVXZlajqMjIyCA8PJz09vVgaeJPJxNGjR2nSpEn5jAGrhl588UUWL17M7t27K7sq5e7bb79l2rRp7N2712HXOm/566+/aNCgAatXr6ZPnz7ldpyyOnfuHC1btmTr1q00adKk3I8nf3dCCCFE5cjKthDSXJ9SKfNQDMFBbnz+MZn0Fq2yhh+1a0MpX/pXhJJig8vViDFbomrIzMzk2LFjvPXWW7z00kuVXZ0Kceutt3Lo0CFOnjzp1fmgfvzxRzIzM2nXrh2nTp3iscceo3HjxnTv3t1rx/CmY8eO8c4771RIoCWEEEKIaigjQx+jdYWRYEt4zYMPPsiiRYsYNmxYmboQVldTp071epl5eXk89dRT/PHHH4SGhtK1a1cWLFhQahfOytKpU6diaeiFEEIIIbBY9NasS/OwXmkk2BJe8/HHHxdLBiHKpn///vTv37+yqyGEEEIIUXa5uXqg5a0sw9UwaZYkyBBCCCGEEEJ4V1aWPlGxNwKtggLYvbtadkOUli0vkTwjQlQc+XsTQgghqiilIC3Ne9kGTSbYubPapomXYMtD1jE02dnZbs/TJIQom+zsbIAqO4ZNCCGEuCLl58OFC/q/3pCeDrt26QFXNSXBlod8fHyIiIjgzJkzAAQFBZU68awQomyUUmRnZ3PmzBkiIiLw8fGp7CoJIYQQAvSWrLS0sqd1v9ypU/Dbb9VynFZREmx5Qd26dQFsAZcQonxFRETY/u6EEEIIUYmU0rv4ZWV5r7wjR+CPP7xTXiWTYMsLNE0jNjaWOnXqkJeXV9nVEaJG8/PzkxYtIYQQoioosMD5ND3roFfKK4C9eyElxTvlVQESbHmRj4+PfAgUQgghhBBXhnNnIcBL3QZzcvREGBcveqe8KkKCLSGEEEIIIYT7LF4KtNLS9EQYNXDiY5lnSwghhBBCCFE6iwXOX/BumcnJsHVriYFWVq4P2tP90drnkpVdvRJmSMuWEEIIIYQQomRmM6SmQq4F8ELmbaXg8GE4etTzsqowCbaEEEIIIYQQzl286N2xVPn5sGcPnD3rvTKrKAm2hBBCCCGEEMVZLHprVpEufsGBoLZ4MFarhibCcEaCLSGEEEIIIYQ9a7dBb04qfOEC7N7tvVTx1YAEW0IIIYQQQohC3u42CPDnn3DwoD5W6woiwZYQQgghhBDCYbdBjxUUwP79etZBD9UuOAfEeF6nCiSp34UQQgghhLjSmc1w5ox3Ay2TSU/r7mmgZbEwKesjll24F+33/d6pWwWRYEsIIYQQQogr2cWLcP68d8dnpabC5s2Qnu5ZOdnZ+H/+KQnZX+BPHv5PTdPLriYk2BJCCCGEEOJKZLHoQZa3x2f99Rds2+Z5K9np0/D++xj+OGxbpJ05A08+6d3AsBzJmC0hhBBCCCGuNOWRbdBigd9/14MtT+3eDd98o8/JVZSPD/TtC5oXJlauABJsCSGEEEIIcaVQSm/Jysz0brkmkz5RcQld/LJyfQh5vi8Amc+tJti/oPhGBQXwww96F8TLnDdEkvu/1wm8oYPXql3eJNgSQgghhBDiSpCfrwdDeXneLTc9HXbt0gMuT2RmwpIlepr4y+z2jefxsGc41r61Z8eoYBJsCSGEEEIIUdNlZ+tB0aV5rrJyIKS73hUvc50iOLCM5Z48qad297Q74smT8PnnDsePFVzbmfs3vUK+5ufZMSqBBFtCCCGEEEIUkZVtIaR5CgCZh2IIDqrGOeUsFj3IysnxfrkHD8Lx456XtX07fPed3oWwKB8fuPVW8tp0In9z9Qu0QIItIYQQQgghaqbcXL3b4OVBjKfMZj2Bhacp2PPzYeVKPXPh5cLCYNQoqFcPcj07TGWSYEsIIYQQQoiaJjMTMjK8X25amh5oeTo+KyMDvv7McebCRo1g5EgIDvbsGFWABFtCCCGEEELUFAUFekDk6RxXjpw4AQcOeDw+6+q8vRg/eg9yHGREvOEGuPlmMFTjrptFSLAlhBBCCCFETWAy6YGWtyf8LSjQk2AkJ3tWjlKMzEnkkczZEJgJReMpX18YMgTatfPsGFWMBFtCCCGEEEJUZ0rp3fKysrxfdna2ntbdQZZAt+Tl4ffNdzyW+UfxdRERcMcdULeuZ8eogiTYEkIIIYQQorrKy9Nbs7w9dxbA2bOwd6/nZZ8/D198gc/pM0CY/bpmzeC22yCwrLnnqzYJtoQQQgghhKiOMjP1FqdLc2d5jVJw5Aj84aAVyl379sHXX+uZES93003Qs2eNGZ/liARbQgghhBBCVCflmQQjLw/27IFz5zwrp6AAVq+GTZuKrcrWAskdMZDAdi09O0Y1IMGWEEIIIYQQ1UVOjj5JsbeTYIA+7mvXLs8nQM7IgCVL9OyFlzns05jHwp5hf8vfAS/P/1UFSbAlhBBCCCGEF2RlWwhpngJA5qEYgoO82D3OYtGDLE8DIWdOntQzDnoaxP3xB3z1lcNkHQXtriFh+2uYNSPwu2fHqSYk2BJCCCGEEKIqy82F1FS9a155+H0/nC3eCuUWiwU2bICkpOJjyHx84JZbyGvbGfMOo2fHqWYk2BJCCCGEEKIqUkpPgJHpYPJfbzqZDP4e7J+TA0uXwqFDxddFRMDIkRAXB7ma20Wb8gws2lWYEn70pDRG3Gpk5KBAjEb3y6toEmwJIYQQQghR1eTn661Z5ZHSHeD8OaC25+WcPKmPz0pLK76uRQsYNqzMad0T99cmYUk7Uk1+tmXLV5tJ/N7MlGczmPdGBIP7Ve2WspqbZ1EIIYQQQojqKCtLn+OqPAItpfQWqJ27PC9n61aYO7d4oKVp0Lu3PlGxB4HWsAUdSDPZtw1Zh5SlZSiGjk8l8XtTmcqvKNKyJYQQQgghRFVQhpTuWTkQ0l3vTpe5ThFcUmxjMulp3VNTAZ+y1zM3F779FnbvLr4uOFifpPiqq8pcvCnPQMKSdqBA4biroFJ6TJcwNY3k7TFVtkuhBFtCCCGEEEJUtvJM6Q5w/rweaDmaXNgd587BF1/oLW+Xa9AARoyAsDCPDrF4b4xd10FnlILUdMWSb3MYe3uQR8csLxJsCSGEEEIIUVnKO6W7Uno69iNHPC9r1y69RctR98YbboC+ffXMgx5adqQBBoPCYim9tcpggKUrzRJsCSGEEEII4a5ynbuqsplMerfB8mrNMpth7169VcsTubmwYgXs3Fl8nb8/DB0KrVt7dgzQ+wU2a8Z5LcKlQAv0S3chtZyunxdIsCWEEEIIIURFslggIwOys8vvGBcu6IGWycMEEmfO6NkGHXUbrFMHRo2C6GjPjgFgNELbthAVRXQ4brVsRUVW3QBcgi0hhBBCCCEqitmst2aV1wTFSsGxY3D4cPHJhd0tZ+dO+O47PQ395Tp2hAEDwK/0sVWlio7WA62AAACG9YSvfnK9ZWv4gADP61BOJNgSQgghhBCivCmlt2ZlZZXfMXJzYf9ePYmFJ8xmPchylG3Q3x8GDYJ27Tw7hlXTpnrmQq0wuBrZB6bMVKRlglLOgy5Ng4gwjRG3li29fEWQYEsIIYQQQojylJurp1svr9Ysq19/BeVh18TTp/Vug47GecXEwMiR3uk26O8PndpAVFSxVcYAmDcdhj4KmqYcBlzW2GzeGxFVNu07yKTGQgghhBBClA9ra9a5c+UfaIFb83MVY52k+IMPHAdanTrBhAllDrRMeQYW7apr+3n0im7M3xSFyUmVB3eHZa9CRIj9csOl6CUiTOPrjyIZ3M9YpvpUFGnZEkIIIYQQwtty8yAz3fF4J68dIxd27Qeu8awcsxkSl8FvvxVfFxAAgwdDmzZlLj5xf20SlrSzmztr+c8GEjdoTJmpmDddD64uN6QHJK+ABSsVE17SW68G9Q1g5CAjI24NrNItWlbSsiWEEEIIIYS3nT9fvoHWhQuwaVOZxmcVbWUKtGRz9+uRzN/dAJPyt98wNhbuu8/jQGvYgg6kmezbeKyZBtMy9e6CiWsd728MgDv7Ff688O0Ixt4eVC0CLZBgSwghhBBCCBuTSbFoWeEEw6MnpTF/STYmkwuZ/fLKMbiyUkrPNLh1a5nSuifur03cv3sycVlbUIocQxBfm6/nnoJnictL5BtLN33D666D8eMdjqlylSnPQMKSdqBA4Tg4so7HSngep10KqzMJtoQQQgghhAASvzcR1zGFidMybMuWrzZzz5R04jqm8M33ToKbomOzylNOjh5k/fFHmXYv1sp0KcuEBR8A0ghhaP5/SOzyBNxyC/h6NuJo8W+xpJr8nAZaVkpppF7UWLLGo8NVSRJsCSGEEEKIK17i9yaGjU8lLcO+Bcti0f9Ny1AMHZ9K4uUBV26uPuFvZmb5VvDMGb3bYGpqmXZ3qZUJA6CRsPkWTHkehglBQSw73RKDwbW5vgwGxdIkzw5ZFUmwJYQQQgghapysbAtavVNo9U6RlW0pcVuTSZEwNQ1wPg+wdXnC1DS9S6FSkJ6ut2aV59gsiwV+/12fYDgvr8zFLN5Tx7VWJjRSTX4s+S2mzMciLg5uuIHzWb62sVmlsVg0LqSX/ZBVlQRbQgghhBDiirZ4eQ6p6cppoGWlFKSmK5Z8fVFvaSrPCYpBL3/zZjh+3LNyUlNZtsKEAdfSzxs0xdJ9ZQi2fHygbVv95etLdDhutWxFhTtYoWkQGel+XaoICbaEEEIIIcQVbdkqs23+ptIYDLD0m8zynzfr5Em92+DFi56Vs2cPzJ7N+Rw/29is0liUxoVsN8drhYVBly56q9Ylw3riVsvW8J6XLfT1hdq19ZSE1ZQEW0IIIYQQ4op2PtViG5tVGouF8u3ulp+vB0i//eZZQGc2w7Jl8NVXYDYTTbpbLVtRQW50jWzUSM9eGBRkt3hkH4gMVWhaya1bmqaIDFWM6FNkYWCgHmh5mKSjskmwJYQQQgghrmjRkQY3WracdHfzhowMvTXr1CnPyjl5EubMgV27bIuGGda71bI1vHVK6Rv6+0OHDtCyJY4uoDEA5k3X/+8s4LIunze9SANWWJjedVCrHnNplUSCLSGEEEIIcUUb1j/AjZYtB93dPKUUHD0Kv/4K2dllL8digY0b4aOP9EmPixhp+JFInyw0SmllQhFpzGNEm1KCrchIuOEGvfWpBIO7w7JXISLEfrl1LFdECHw9U98OgwGioyEkpHhB1ZQEW0IIIYQQ4oo2clAgkeFaqQ0pDru7XWIyw6JVhT+Pfgbmf+fCRL3WubMOHcLliM+B6ILz+H/2CaxeXbwcf3+Mtw9i3l0HQMNpwKWhQIN5I/Zg9HNSF02D5s2hUycwGl2q25AekLwCPnim8LiDboT5zyuSV1wKtPz89MAtoPqOz3JEgi0hhBBCCHFFMxo15r0RATjvueawu9sliWsh7haYOKNw5+Ub4J7nNOJugW/WOTnw6dMezZ1ldZN5E5+lPoDhqIPJjuvVg7//Hdq1Y3D8WZaN2UGE0X48luHSuUUY8/l6zA4Gx591fKCgIH1sVpMmbnfxMwbAnf0Kf174EowdeOlaBgVBrVp6NsMapnqPOBNCCCGEEMILBvfyYdnrBhL+WUDqxcJAwmBQWCwaESF6oDW4u/1+iWth2LTi5Vmz8KVlwtBH9a50Q3pcWpmfr8+dlZzsWaXz8vBbtZLXMvY7Xn/TTdCjh10QMyT+LMlPJLFgVywTlrYFYFCrs4xse5oRbVKct2jVr6+PzfJmQKRpEB5eLLFGTVLjWrb+/e9/o2kaU6dOtS0zmUxMmjSJ6OhoQkJCuP3220lJcWHQnxBCCCGEqNmU0hNTnD3LkG4FpXd3K8JkhoTnrcU4bumxLk94/lKXwrQ0+OUXzwOt5GSYMwefrZuLrwsNhXHjoHdvh8GR0c/Cne1P235eOGo3Y6855TjQ8veHa66B1q29G2j5XBqfVYMDLahhwdaWLVuYPXs27du3t1v+f//3f3zzzTcsXryYtWvXkpyczG233VZJtRRCCCGEEFWC2axPTpyZaVtUYne3yyxeA6kXNaeBlpVSGqkXNZYsTIEtW/RxWmVlscC6dfDhh3DuXPH1rVrBAw9A48ZlP4ZVdLSeBKNOHc/Lulyt2nogV8PVmG6EmZmZjBkzhvfff5+XXnrJtjw9PZ0PP/yQhQsX0rt3bwDmzp1LfHw8mzZt4oYbbqisKgshhBBCiMpgsUB6umdBD7AsqbCbYWkMmmLpWo2xo0vOBliiCxdg6VL46y8ATMqfzy29mRfWmzQtnIa1/Li9eS4jfVMwUvZkGxgM0KIFNGhQfunXDdU/rbsrakzL1qRJk7j11lvp27ev3fJt27aRl5dnt7xVq1Y0bNiQX375xWFZZrOZjIwMu5cQQgghhKgBsrP11iwPAy2A8+m4FGiBPnfVhewytnMoBdu3w+zZtkAr0XIjcXmJJFieJcm/Kzv82/FNSkvu+bI9cf/uyTf7S07J7lRoKFx/PTRs6N1Ay2DQ08VfYWpEy9Znn33G9u3b2bJlS7F1p0+fxt/fn4iICLvlMTExnD59utj2AC+//DLPP/98eVRVCCGEEEJUhvx8fbxUbq7XiowOd69lKyoov9TtisnKgm++gQMHbIsSLTcyLP/ftp+VprefWC51Z0wz+TJ0QQeWjdnBEGeZBR1p1EhP6+7qDM+u8vPTA63cGtPO47Jqf8YnTpxgypQpLFiwAKOLuf5L8+STT5Kenm57nThxwivlCiGEEEKISnAxE86e9WqgBTCsp3stW8Nbu5mg7eBBePddu0DLpPxJyH8GAOXko7xCAwUJS9phynPh476/P1x7rZ5t0NuBljWtu2+NaONxW7UPtrZt28aZM2fo2LEjvr6++Pr6snbtWt588018fX2JiYkhNzeXtLQ0u/1SUlKoW7euwzIDAgIICwuzewkhhBBCiIplMikWLSvs7jd6Uhrzl2RjMrk57ikzU++K52Uj+0BkqLLNweWMhiLSmMeINi4GW7m5sHw5LFqkt2wVsdjSm1TCnAZaVgqNVJMfS36LKf1411+vJ8PwJk2DiAj9VV7jvqqBah9s9enThz179rBz507bq1OnTowZM8b2fz8/P9asWWPb58CBAxw/fpwuXbpUYs2FEEIIIYQzid+biOuYwsRphWPnl682c8+UdOI6pvDN9ybnOxcUQGpaudfRGKDPvQU4Dbg0FGgwb8Qe53NYFfXXX/rYrG3biq8LCmJZvdG2SYhLY9AUS/c5CbZ8i6Rx93ZWQF9fqF27xqd1d0W1b88LDQ2lbdu2dsuCg4OJjo62Lf/b3/7Gww8/TFRUFGFhYTz00EN06dJFMhEKIYQQQlSArGwLIc31Vp3MQzEEB5X8fX/i9yaGjU8tttxyKVZJy1AMHZ/Kso8iGdLvsmEkWVn6vFkmBZR/i8rgGy0seySFhLdqk2rysy03aAqL0ogw5jNvxB4GlzZ2qqAA1q/X07o7aoVr0QIGD+b8Z5G2sVmlcZqUIzISmrYtvtwbAgOv+Nasoqp9sOWK119/HYPBwO23347ZbKZ///688847lV0tIYQQQghxGZNJkTA1DXDe808p/bN8wtQ0krfHYDRqete79HTIy6u4ymZlwd69DIlMJ/kJAwt2xTJhqR7EDGp1lpFtTzOiTUrpLVrnzukp3R1NdOznB/37Q8eOoGlEB+XbArnSFEvKYTDoCTAaNgSTl4MhTYOwMAgO9m651VyNDLaSkpLsfjYajbz99tu8/fbblVMhIYQQQgjhksXLc0hNL72bnFKQmq5YsjybsX3z9JTuFUUpOH4cDh2yNbcZ/Szc2f60LdhaOGo3wf4FJZdjscDmzbBmjd6ydbl69WD4cLvxVMPiU/jKlXFYXJaUIywM2raFkBCX9nWLj4/eWnYFTFLsrhoZbAkhhBBCiOpp2SozBkNhl8GSGAyw9OsMxnb1fvILp3JyYO9eSC3ezdEt58/D11+Do6zXmgY9esBNNxXLDjiybQpTlueRZvLVsw46oaGIMOYzou0ZaNoUmjTxfqZBgIAAPdAqj7JrAAm2hBBCCCFElXE+1eJSoAV6QHYhrQIDrZMn9TTs+WWYL8vKYoGtW2H1asddHqOi4Lbb9FYtB4x+FuaN2MPQBR3QlHIYcNmScow9hLFbJwgPL3t9SxIaqr/KWXCQAXUyttyPUx4k2BJCCCGEEFVGdKTBjZYtRVQ5xRF2TCbYv1+fq8sTqamQmAjHjjlef8MN0Lu3Pk6rBIPjz7JszA4SlrRznpTjHykMHtVC7+LnbQaD3poVEOD9smsYCbaEEEIIIUSVMax/AF99V0Ja9yIsFo3hPcu5ZSslBY7u8yzxhlJ6KvcffnA8sXJEBAwdCo0bu1zkkPizJD+RVDwpR4fzjLirDsa6jlvGPCbdBt0iV0kIIYQQQlQZIwcFEhmulZo5XNMUkaGKEX3KuUJ793oWaKWnw6efwrffOg60OnWCv//drUDLypqUw2rh1LOMfagZxrpRZa9vScLC9GQdEmi5TFq2hBBCCCFElWE0asx7I4Kh41PRNMfp360TCM+brk8s7HVnzwCuZfxzSinYsQNWrQKzufj68HAYMgSuusqz4xQV37p8Pt1LtsEyk7BUCCGEEEJUHUoxuGs+y2ZCRIh9pGUw6D9HhMDXM2Fwd8dFmMywaFXhz6Ofgfnf6ctLlJsLe/bA7j0enADUKjiP/+KF+vgsR4FWhw7wwAPeCbTqehgUlsZohNq1JdAqI2nZEkIIIYQQVYPZrHe7y89nSHdIXgELViomvKT3KRx0I4zso3cddNailbgWEp6H1IuF/RCXb4DEdRpTZirmTXcSpKWkwO+/XwqOyphUQikGmH5kWua7GA6dLN6sERoKgwfrEwt7KiAA4uMhtI7nZTkikxR7hQRbQgghhBCicuXnQ0aGnvWvCGMA3NkPJryk/7zwJQgOdF5M4loYNq34cotFD7zSMmHoo7DsVRjS49JKs1kPslJSPDuHjAz8v1nBixeTHa+/+mro3x8CSzgBV8XFQcuWetbCHM+LK8bXV+82WEpWRFE6CbaEEEIIIUTlUAouXoSsLMeDs9xgMustWnqxjrNrKKWhaYqE5/VWM+OFU/q8WY4SV7jKOjbr++8x5JiBMPv1wcF6a1bLlmU/hpXRqLdm1a7teVnOBAbq2RFLy1AiXCLBlhBCCCGEqHjZ2XprlqszGJdi8Rr7roPOKKWRehGWzD3G2KsOeHbQ1FT45hs4etTx+rZt4ZZbICjIs+OAfWtWedA0PWmHN+oqbCTYEkIIIYQQLsnKthDSXO9ul3kohuCgMuRay82D7IuetSY5sCxJT6Bh7TJYEoOmWLopkLFlzU9hscCvv8KPPzpMC5+qhZN72y0EXt2qjAcowmiE1q2hVi3Py3LG1wdqRUm3wXIgwZYQQgghhKg458+DF4YtFSs2HZcCLQCL0riQXcaPwWfP6lkG//rL4ervAnrz35C/kxz/K1BQtmNY1asHLVqUfxBUqxb4lTEpiBuCgwyok7HlfpyqRIItIYQQQohqyCutTBVBKbiYWe6HiQ53r2UrKijfvQMUFMDGjbBunf7/y4WGkjtgKM8t+Id75TpiNEKbNvoEwuWl6JgsGZ9VbiTYEkIIIYQQ5cM6LivLApTvB/phPeGrn1xv2Rre2o3sg8nJemuWs4yFnTpB375YNC+Md6pfX2/N8i3Hj+n+/gTHRKJOln9r1pVOgi0hhBBCCOFdZrMeZDkYz1ReRvaBKTMVaZnOsxECaCgijPmMaOM82Ar2L0DNWKXXf+1a+Plnx9kSo6L0TIONG+s/ezIMLTBQb82KivKgEBeEhuovUSEk2BJCCCGEEN6Rn69PSmw2V/ihjQEwb7o+j5aGQjloSdNQoMG8EXsw+pWSBfHPP/XWrAsXiq/TNOjSBXr29Hw8laZBo0bQtCn4lGNLk4+PPneWv3/5HUMUI8GWEEIIIYTwjMWit2RlZ1deHQoKGFz3CMvGZpOwuC2ppsIgyKApLEojwpjPvBF7GBx/1nk5JhOsWQNbtzpeX6cODBmiJ6/wVFiYnmkwLKz0bT0RGKindTdU0XF9NZgEW0IIIYQQomyUgsxM/eXhpMQeOX8e9u2DnByGtILkJ5JYsCuWCUvbAjCo1VlGtj3NiDYpzlu0lIL9+2HlSn2i5csZDNC9O3Tr5vl4KoMBmjXTW7TKMzmFzJ1V6STYEkIIIYQQ7svO0bMMOsrMV1Fyc+HgQT2BRRFGPwt3tj9tC7YWjtpNsH8J9UxPh+++08typF49vTWrTh3P6xwVpbdmlXcA5O8PERHlm2hDlEquvhBCCCGEcF96ernMl+Wy5GQ9OPJkcmSLBTZvhp9+cpzMw9cXeveG66/3vAuen5+eZdAb3Q9LExoKISGS0r0KkGBLCCGEEEKULi9Pnzm4smVn6939zp/3rJyTJ2H5cjh92vH6Zs1g4EA9qYSnYmP1QCsgwPOySuJjgFpRkgSjCpFgSwghhBBCOJefr49hysm5lNq8EltLjh2D5COedV00m+HHH2HLFsfjzIKDYcAAPQ27py1DRiPEx0Pt2p6V46patcFf5s6qSiTYEkIIIYQQxRUU6IkvsrI8KsZkhkWrCn8e/QyM6KPPi2V0t6HnyBEoaexVaX7/XR+b5SgBBsC110KfPnr2Pk/Vrw9tm5b/mKmiAaFBug1WNRJsCSGEEEKIQhZLYZDlYYbBxLWQ8DykXiwMApZvgMR1GlNmKuZNh8HdSyggLw8OHAHiPaoHGemwejkcOOB4fe3a+uTEDRp4dJhg/wLUrI16AoyIlh6V5RJ/fwiNAEpIZS8qlQRbQgghhBBCD6yysvRAy1LKhL8uSFwLw6YVX26x6IFXWqY+AfGyV2FIDwcFWBNgZBZQ1mBLUxbuyPka4+y3IN/BRMs+PtCjhz5BsactUD4++sTEDRuW/3xWmlaYBCPb89+VKD8SbAkhhBBCXOmys/WudV5K424y6y1aAEo57tqmlIamKRKeh+QVRboUZmbqCTBSUy8tKNsYJC35JPPSphCffwjCzHB5/HPVVXDrrXoqdk/VqQMtW5a5+2FWDoR0169T5jpFcEnF+PrqSTv8/ErYSFQVEmwJIYQQQlypTCbIyNCTYHjR4jX2XQedUUoj9SIsWaMY2y8f/vgD/vzTs+6LOTmwZg0BW7cTnx9afH1wMPTvD23beicBRqtW3pl/yxUhIXqLlqR0rzYk2BJCCCGEuNKYzXpLlidzVJVgWRIYDMrWZbAkBoNi6SozY0M268FfWVkssHMnrF6tB1yO4rUOHeDmmz1PgKFp0KiR3jpWEZMG+/joExSXd+p44XUSbAkhhBBCXCnKOciyOp+OS4EW6NtdOJXlWaB16pSeZfCvvxyuVrVqw5Bb9QDJU5GRemtWqINWs/IQFARhYU7HgQUHGVAnYyumLsJtEmwJIYQQQtR0ubl6kGV2kCSiHESHu9GypSmigsrYjTEnB376CbZuddj10KQF8H7QGN6ZEIZvoIdd7/z89ImJ4+IqphufwaC3ZhmN5X8sUW4k2BJCCCGEqKkqOMiyGtYTvvrJxZYtpTG8dYp7B7BYYPdu+OEHPbmHAwWtWjPC/C9SfOrwjs9qwIPkH3FxeqDl71/2MtxhNEJ4uN59UFRrEmwJIYQQQtQ0eXl6kOVJ1zwPjOwDU2Yq0jKdZyME0FBEGPMZ0caNYOv0ab3L4IkTjtdHRcEtt5DXsCUpez1MXBEcDPHx3slY6KqwMIgKqbjjiXIlwZYQQgghRE1RyUGWlTEA5k3X59HSNOUw4NJQoMG8EXsw+jmfKyrYvwA1Y5V+Tj8mwa+/Os5W6OsLN90EXbvq//dkWJqPDzRpAo0bl/+cWZcLDqrY44lyJcGWEEIIIUR1l18Aqen6GKYqYnB3WPZMOgkzg0nNKZwTyqApLEojwpjPvBF7GBx/tuSClCrsMpiV5Xibli31dO6RkZ5XvG5daN7c84yFrtI0CA0BMivmeKJCSbAlhBBCVJKsbAshzfXuU5mHYggOquBv0EXNcfYsVFBs4JKsLPj9d4YYz5P8uIEFu2KZsLQtAINanWVk29OMaJNSYosWoGcZXLkSjh93vD4yEm65RQ+OPBUSomcZrMgug/7+ehKMXAMSbNVMEmwJIYQQQlQ3eXmQerHMu2flQEh3vWtf5jpFsLcCtbw8fWLi48dtXf2MfhbubH/aFmwtHLWbYP9SklVkZupZBrdvd7zex0fvMtitm+fzXPn6QtOm0KBBxXUZ1DQ9dXzIpbFZuaUEnaLakmBLCCGEEKK6yM3VAxGTCUwAFZCC/BKTGRatKvx59DMwoo+eDMPoryA5GQ4f9izzYX4+bNkCa9c6L6dFCxgwwDtdBuPi9Faxipws2NqaVRGTIYtKJ79lIYQQQoiqrpJSuFslroWE5yH1YmFwt3wDJK7TmPKqhXljDjK44Z+eHeTQIb3L4IULjtdHROhBVsuWnh0H9Ix/rVrpZVaUy1uzxBVBgi0hhBBCiKrKbNaDrFz71HoltjJ5uZEmcS0Mm1Z8uXXC4rRMjaFzWrJsTDZDSkt24ci5c7Bqld4q5oifn95lsEsXz1uD/Pz0lqx69SpmYuKix42MlNasK5D8xoUQQgghqhqTSe8umFs8f3mJrUwzFfOm65kAvVINs34scD5flkJDU4qEJe1IfiKp9KQXVjk5sG6dnsrd4mSf9u2hTx+9JcpT9etDs2YVNzExSGuWkGBLCCGEEKLKMJn0lqy8PIerS29l0ue2WvYqDOnheXUWr7EP6pxRaKSa/FjyWwxjrzlV8sYWC2zbBj/+CNnZjreJi9O7DDZoUIZaO9C5M8R4IWBzh7RmCSTYEkIIIYSoXEoVBln5+U43c6mVSWlomiLheUhe4XmXwmVJYDAoWzBXEoOmWLqv5GDrmrw9BMydDWecbBMcDDffDO3aeZ4ZsGjSC2+0jLlK0/SWrJCQiu2qKKokCbaEEEIIISqDUnrLTmYmFJSSCh03WpmURupFWLJGMXagZ1U8n2rBYvFxaVuL0riQ7fijpZaWyoyMl+lnXot2OgMuj6N8fOCGG/SxWZ5mBvTxgcaNCW7cGLVFeVaWuyTToLiM3AlCCCGEEBXJYtEn/c3Kcj5WyQG3WpkMiqVJlD3Yys+HY8eIzgvDoNXB4qQlze6YmiIq6LKWuZwcWL+egE2/0s8c7HjHli2hXz/vTCYcF6ePyzIaPS/LHZqmt54FOzlHccWSYEsIIYS4JCvbQkjzFAAyD8UQHFRBE5yKK0N+vh5gZWfbJvx1x/l0XAq0QN/uQnoZWnWUgpMn4cgRMJsZFh/LV7/FuHZMpTG8tf73Q34+bN2qz5dlMoGjmLJ2bX1c1lVXuV/Py0VE6PNvVWQqd6uAAP24Pq61AIorS5mDrZUrVzJgwABv1kUIIYQQoubJy9O7CubkeFRMdLh7LVtR4W4e4Px5OHBAr+slI9umMGV5HmkmX1QJEyhrKCKM+YxofRr27YM1a5zPlxUYCL17QqdOno/LMhr1VO6xsZ6VUxYGg96aFRRU8ccW1UaZ7/CBAwfSsmVLZs2aRUZGhjfrJIQQQghR/ZnNegBz9qzHgRbAsJ7utWwN7+liwZmZsH27niGwSKAFYPSzMG/EHtD0gMoRDQUazOv1E8ZPP4TFix0GWvn4sjBwOKYHpsB113kWaPn4QNOm0K1b5QRagYFQp44EWqJUZb7L4+PjOXToEA8//DD16tXjgQceYO/evd6smxBCCCFE9ZOTowdY58/rAZeXjOwDkaEKTSu5e6CmKSJDFSP6uFjwr7/qEws7MTj+LMvG7CDCaD8ey3CpHhEBeXzd5C0Gr5kOJ044LKOgVRtGRs3m9ZD7IdDDACUuTg+ymjat+K57BoM+tiwy0vNWOXFFKPNd8ttvv/Hjjz8yfPhwzGYzs2fP5uqrr6ZXr158+eWXWNwY8CmEEEIIUa1ZLHqrUEoKpKY6nSfLE8YAmDdd/7+zgMu6fN70EtK+5+frY7KsXBg/NiT+LMlPJPHB8MIv1gc1P838VgtI1m5h8F+LHO9Yvz6MH0/e7Xfwl0+9Uo9ToshIuP56aNu24hNggN6KVadO5RxbVFseheQ9e/ZkyZIlHD16lKeffpo6deqwdu1aRo0aRaNGjZgxYwZnzpzxVl2FEEIIIaqW/HxIT9eDrIwMl1K4e2Jwd33C4ogQ++UGw6VWphD4eqa+XTEWC/z5J2zYAMeOuX1so5+FO9ufxlflcWf2Ur46NZaxf7yN0eKgi2REBIwYAePHez4xcVAQtG+vT0wc7u5ANC/w8YHoaP2cpDVLuMkrd0y9evV48cUXOX78OAsWLOCGG27g5MmTPPvsszRs2JC7776bzZs3e+NQQgghhBCVz2zWxyWdOaNnGCxDdsGyGtJDn7D4g2cKjznoRpj/vCJ5hYNASylIToaNG/UEGLm5ZTuwxYJh316+SL2fR7JmOx6HZjTqadwnTYI2bTyb1NffH1q1gq5doW7dspfjiZAQvTXL07m/xBXLq+G5n58fd911F2vXruWJJ55AKUVubi4LFiyga9eu3HTTTRJ0CSGEEKJ6sk5CbB2PZTJVWlWMAXBnv8KfF76kz6lVrOvguXOwaRPs3Vv2JB1K6d0O338f/6Vf0KAgufg2BoM+KfHkydCli2eT+hoMejr4bt2gYcPKaU3y89NT04eFeRYwusBkUixaVvi7GT0pjflLsjGZKnhCZlEuvDrPVkpKCnPmzGHOnDkkJ+t/iB06dKBfv34sWrSIjRs3cuONN/Lll18yZMgQbx5aCCGEEKJ8lHES4kqVlgaHDunjxzzx1196GveSuh22bg19+lTvSYmtNA1CQ/UWrQqQ+L2JhKlppBaZE235ajOJ35uZ8mwG896IYHA/GSNWnXkl2Pr555956623+Oqrr8jLy8NgMHDbbbcxZcoUbrzxRgBmzJjB7NmzmTx5MtOnT5dgSwghhBBVW16eHmDl5FRoN0GPZGXpQZanY+bPnIGffoLff3e6iaV+A3wG3Oz5mCyAWrX0+bJCQz0vq6yMRn1MWAVlOEz83sSw8cWDYWs8n5ahGDo+lWUfRTJEAq5qq8zBlslkYsGCBbz99tvs2rULpRSRkZFMnDiRSZMm0eCyPzyDwcADDzzAd999x+rVqz2uuKgYWdkWQprrs8FnHoohOEgGhgohhKjBlNK7B2ZllX1sU2XIyYE//tDHZnkSGKanQ1IS7NrltJxjPg14J3gc39yTg1+Ahy19YWF6kBUd7Vk5nvDx0YOsCmxNM5kUCVPTAOe/LqX0hraEqWkkb4/BaCzf7oyifJQ52KpXrx5paWkopWjTpg2TJ09m7NixBAYGlrhfTEwMudXp4SWEEEKImq+gQB+PlZ1d7hkFy8WmX8A3v/TtnMnK0rMUbtni/PxDQ8m7qQ93LH0Ei+YDmgdfnhuNepBVt265j4kqUUiI3ppWwXVYvDzHruugM0pBarpiybc5jL1dJlCujsocbKWlpTFo0CAmT55Mnz6uzpoHjz32GHfffXdZDyuEEEII4T1msx5oVGKyizLJy4Mjx4Fm+s+WsrVmBVqy8V3/E/y60XlLXmAg3HQTdO5MgSUAyzIPu9k1awYtGlRuGnU/Pz2Vu59fpRx+2SozBoNrQwANBli60izBVjVV5mDr0KFDXHXVVW7v16JFC1q0aFHWwwohhBBClKrEbvAWi97tLitLnyerOsnLg+PH9fmyshW2YMtd+fnckbOMv2UtwnfdCcf5qf389MyCXboUdrEra+cknyIHaNTIy/mw3aBpetfF4OBKqoDufKrF5VwrFgtcSK0miVlEMWUOtsoSaAkhhBBCVJrqmPDCKj+/MMjKy7u0sAwtTAUFsHMnAWvX82imk2tgMECnTnprlqdZ+QwGqFcP6jbxrBxvqOAEGCWJjjS41bIVFSlj5qsrr6Z+F0IIIYSocnJM+lis6jhmvKAATpzQU697Un+LRU96sW4dpKWhWQDCim/Xvj307AmRkWU/llVcHDRtqndDLOMUX15RCQkwSjOsfwBffeda11WLBYYPkEmVqysJtoQQQohK4Ggi0xG3Ghk5KFCyjnlDfpEkD2lpUHL+rqrHYikMssxmz8rZswfWri15zq0WLaB3b4iJKfuxrGJi9CCrSKtYcCCoLRXcmqhpenfBSkiAUZqRgwKZ8mwGaRmqxEZWTYOIMI0Rt1a3G1hYSbAlhBCiRqkOU1bIRKblRCm9i2B2NqTnAlXrA7ZLLBY4eRKOHvUsaYfFAr/9pqdxv3DBbpVJ+fO5pTfzwnpz0lCX+Hq+3N4qg5FRKRjxYGxQdLSe/CI8vOxleEtAgF4P36r5Uddo1Jj3RgRDx6eiaY57tVrjw3lvRMgXMNVY1bwDhRBCiBrqSpzItNwD4Ly8wrTt1W0slpXFAqdOwZEjngdZ+/frQda5c8VWJ1puJCH/GVIJA399IqfDxxVf/6kxZXke80bsYXD8WfeOGRGhB1lRUWWvt7dUwS6DzgzuZ2TZR5HFvnixjuWKCNPki5caQIItIYQQooLIRKZeZM0omJ1dJGFENWSx6BMR//GH50HWvn16kHXWcbCUaLmRYfn/xtbid6npxKL0f9NMvgxd0IFlY3YwxJWAKyQE2lwFtWuXvd7eUoW7DJZkSD8jydtjWLA0mwmPZgAwqG8AIwcZGXGrdCmuCape3wohhBCihrJOZFpa40vRiUzFZXJz9bFHKSmQnl59A62CAj2z4IYNepBU1kBLKW4ybyLgo/dg8WKngZYppgEJhhcADeWke6VCAwUJS9phynPhI+J111WNQCsgQK9HWFi1CrSsjEaNO4cWjsla+HYEY28PkkCrhqj2wdbLL79M586dCQ0NpU6dOgwbNowDBw7YbWMymZg0aRLR0dGEhIRw++23k5KSUkk1FlVRVrYFrd4ptHqnyMqWuSyEEOXDOpGpK6wTmQr0VpvMTDhzRu8aVx1Tt1vl5+vjsdavhwMHyh5kWSwYDv7OJ2mTeS1jOlrKacfbxcTAHXew+LqnSc01Og20rBQaqSY/lvzmIFFGUBC0bl34c2UHNj4+etfF6OgqOzZLiGp/Z65du5ZJkybRuXNn8vPzeeqpp+jXrx/79u0j+NKEdf/3f//Ht99+y+LFiwkPD+fBBx/ktttuY+PGjZVceyGEEFcSmcjUDUrpgUhOjmfd66oK62TEx4971hpn7S64fj3+p88Qn+8gfTvoLT09e0KrVmAwsGxhDAZN2boMlsSgKZbui2HsNaf0BUFB0KSJnsrdVAVaW6ppl0FxZar2wdbKlSvtfv7444+pU6cO27Zto3v37qSnp/Phhx+ycOFCevfuDcDcuXOJj49n06ZN3HDDDZVRbVFDVIesZ0KIqkMmMnVBbq4+Dqs6t14VlZurdxc8cUJv1XIgK9eHkOf7ApD53GqC/QuKb1RQoKdwX7++WHZBO7VqQY8eegtUkWbU89m+LgVaoI/hupDtq8+PddVVepBVVYKaKp5lUIjL1bg7NT09HYCoSxlxtm3bRl5eHn379rVt06pVKxo2bMgvv/ziMNgym82Yi8xpkZGRUc61dpPFoj/0qsqDTwghhEtkIlMn8vMLk10UOAg0qiOTSQ+y/vrLs3PKz4edO2HjRn2+MCdUVDT06g5t2+Kor2p0UL5bLVtRMYHQrZvDsipFNcoyKERRVeQvyDssFgtTp06lW7dutG3bFoDTp0/j7+9PRESE3bYxMTGcPu24f/PLL79MeHi47dWgQYPyrrp7TKbCgcGezCYvhBAVoLqMiXQ0yfD8JdmYTN5rXRk5KJDIcK3U78o0DSLDa/hEphYLZGXpY7DOnIGLF2tGoJWTo6de37BBD7bKek55efDLL/Dmm/Dtt04DraM+Dfln6GOY738I2rd3GhwNi09xq2Vr+C2BVSPQ0jS9u2CdOhJoiWqpCvwVec+kSZPYu3cvn332mUflPPnkk6Snp9teJ06c8FINvajom1RKCmRkVN+MTEIIUckSvzcR1zGFidMKezIsX23mninpxHVM4ZvvvTNmyDqRKTjvnFDjJzI1mfVucDXtS8OLF/Vufhs26F0GXR2cdzmzWS/jjTfg++/1ch2pW5fc2+/kjsj3WGnsXWpgNLJtCpHGPDRK/vJA0xSRoYoRfcpWfa8KDNSDLBmbJaqxGtON8MEHH2T58uWsW7eO+vXr25bXrVuX3Nxc0tLS7Fq3UlJSqFu3rsOyAgICCAioRl03Cgr0LE2ZmeDnpz+cAgP1JnchhBAlquhJhq/IiUzNRQKq1FSoSQ12Fy7o2QXPn/eomFDLRe7M+RrjW3Mht4SU//XrQ/fu0KwZljxflOba9+ZGPwvzRuxh6IIOaEo5zEqoafr9OG86GCvzY5C/v57G3d+/EishhHdU+2BLKcVDDz3E0qVLSUpKokmTJnbrr732Wvz8/FizZg233347AAcOHOD48eN06dKlMqpcvvLy9FdGhv6QsgZeVaErgBBCVDGVNcnwFTGRaW5uYSbBzAIoJeV4taIUnD6tdxP0dFx3Rga+G39l+YW3CVI5YMpx3O+oSRO46SZo3LjMrTyD48+ybMwOEr5sR2qOn225waCwWDQiQvRAa3D3MhXvOR8fvRUrKKiSKiCE91X7YGvSpEksXLiQr7/+mtDQUNs4rPDwcAIDAwkPD+dvf/sbDz/8MFFRUYSFhfHQQw/RpUuXmp+JMDdXf6Wn6/2cAwP1f6UpXgghgMJJhktTdJLhsbd754OgdSJTa7C18O2I6p/RNC9PD7BycmrG+KvLFRTAyZN6kJXj4YTTZ87Azz/Dnj345lsIUk5SuDdrprdkeWP8eGgoQ+6IJfl+HxasUkx4Sf88MOhGGNlH7zpYWotWcCCoLV7OEqlpEBKiv+Qziqhhqn2w9e677wLQs2dPu+Vz584lISEBgNdffx2DwcDtt9+O2Wymf//+vPPOOxVc00pmMukvTdPTpkrgJWooSccv3GGdZNjVVOxLV5q9FmzVGNZMgjk5TlObV3tmsz4O68QJz8ZHK6UHaj//DIcOlbxtfLzekhUbW/bjWUVG6i1jtWoBYATu7AcTXtJXL3xJD6IqRWCg3mVQhj6IGqraB1vKhTk4jEYjb7/9Nm+//XYF1KiKs04SKYGXEELIJMNldSUEWKCnoj92DJKTy57wAvR9f/9dT9+enOx8MwwUtGmPT89uemIIT0VH60HWpelwqhQ/Pz2Vu4zLEjVctQ+2hAccBV5Go/6SMV5CiCuATDLsBmuAZTLV/Oy3qalw/LieMdETeXmwa5eewr2kiYj9/FhsHMSCoNs4MGwf/o4mNXYg2L8ANWNV8RV16uhBVnh4GStejgwGvSVLxmWJK4QEW9WM3kUqHdDIXKe81+zvLPCyVKOsjEII4SaZZLgUubmF7w01uQUL9F/w6dN6V8H0dM/KysmBLVtg82a9dcyZwEC47jpMV9/AKzOHXlq4r2zH1DSoW1dPoBEaWrYyypOmQXCwPi5LvtAVVxAJtkRxRQOvHLBlkLJ4eUCsqFAylkmI4kYOCmTKsxmkZSin2QhB/5wYEVbDJxm2MpsL3wNqYpKLy5nN8Ndf+sts9qys1FQ9wNq+veTWv4gI6NIFrrlG70aX68F4JYNBH9fVpEnVbS0KCtIDQBmXJa5AEmwJ16WkgMm/sKuhr9w+QojqzTrJ8NDxqWia4/TvNX6SYaXsAyxPxiZ5WVYOhHTXr7lXe3OAnrL9+HG9NcuTc1ZKbw3btEkfl1VS1F63LnTrBq1be9664+enz7nVoIH+nlwVBQToXQb9/ErfVogaSj4tC6dMZlhUpCv46GdgRJ9cRvbJxRiQoQdbRqP+MK1Ok0ALIUQRV+QkwxaLfYDlQrKpGkEpPeX68eN6K5Qn8vPht9/0IOvStDNOXXWVHmQ1aeJ5MiqjERo1gnr1qu6Xnr6++ngx+WwghARbwrHEtZDwPKReLHxTWL4BEtdpTJmpLk16mA+ZmfrLYLBPsCGZDYUQ1cgVMclwfn5hcJWbW9m1qVh5eZByUg+yTK6N0XMqMxO2bdPHZGVlOd9O06BtW+jaVW/R8lRoqD4eKyam6o55kuQXQhQjwZYoJnEtDJtWfLnFon/YSMuEoY/CsldhSA/bysI0wJqm90G3Bl7SR1sIUQ3UuEmGlbJPcHEljL9yZuMG8PEwg+Lp03or1t69JV9Lf3/o0AFuuEEfm+Wp6Gg9yIqO9rys8iKTEgvhlARbwo7JrLdoASjl+IGplIamKRKeh+QVDmabt/b/N5v1jE5+foVdDf395UEsxCWStER4XUFBYfdAs/nK6R5YlMWC6UQKi74yAVcBMHpRW0a0Oc3ItikY/dwYn2WxwMGDepD1558lbxsRAdddpwdano6hsmYWbNRIbymqyiT5hRAlkmCrOisoALz7cFu8xr7roDNKaaRehCVrFGMHlrJxXp7+yswsTCtv7XIoD2chhPBMbm5hgOXC/FflmnSiMuXkwF9/kfi9iYTP4kk1FSZlWP57bRL312HK8jzmjdjD4PizJZdlNusZBX/9FdLSSt62USO9FatFC+9072vQAFo00NPCV2VGox4IVtVxY0JUEfIXUp1t3AAN6+iDZL30zdeyJDAYlK3LYEkMBsXSJEoPtooqmlY+PV1/SFsDL2n1EkKI0llbr6yvKpQ9sMIpBWfP6tkAz58ncX9thi3oAJc16Fku9dRIM/kydEEHlo3ZwRBHAVdKCmzdCrt3lzyuzccH2rWD66/3znisookkWrSAqhxn+fvrnzn8/Su7JkJUCxJsVWd5+fobzIkTehN+vXr6XBsepFg9n45LgRbo211I97CLSn6+/srKKhzrZQ2+5NsyIYTQgylr65XZXPMnF3aFyQQnT+qvSwkvTHkGEpa0AwUKJ93g0dCUImFJO5KfSNK7FObnw/79esKLEydKPm5wMHTuDNdeq49P8lREBDRsCKF1PC+rvPn66kFWVU0zL0QVJZ9ma4qLF/X5PQ4ehDqXWruiotxuKYoOd69lKyq8rBV2oOhYr4wM/ZtD6zivgADpciiEKJXJpFi0LMf28+hJaYy41cjIQdUoo6BSendA6/OwmmcOdDyNCIzs42DMb0mUgvMX9IDo7Nli49EW742x6zrotBg0Uk1+LPk1lLGmr/TugtnZJe9Ut67eVbBNG8+/CLSOx2rYUE+PDpBT8i6VysdHD7KqerdGIaooCbZqGotFz5h0+rT+7VO9ehAX5/JDclhP+Oon11u2hvcsx8HXBQX6G6D1TdDXtzDwCgiouqlvhRCVIvF7U7G5spavNpP4vZkpz2ZU/bmysrLBlFujElu4No2Ii4X98gsUZDpdvWx/DAZN2boMlsSAhaUrchjrt8H5RpoGrVrpXQUbNvS8m7u/vz4Jcf361aN1yGDQe80EBUkX/woQHGRAnYyt7GqIciCfVmsykwmOHIH16/U5QU6fLrVv/8g+EBmq0LSS3+g1TREZqhjRx5sVLkV+vh54pabq53LmjD7uy2S6sscsCCFI/N7EsPGppGXYP7usj4a0DMXQ8akkfu/hHEveYu0+nZpWuCwjo0ZNMGydRiTtsvjo8mlEEtc62Nli0Z/xu3cXLsspufnnfLavS4EWgAUDF3A81rkgMIQHUu8nJnsZWcPu0hNgeBJshIbqLWI33QTNmlX9QMs6V1ZMjN5tUgItITwiLVtXivPn9Zefn97NMDYWIiOLPUSNATBvuv4GqGnKYfp3ayA2b7qbXUBc5HKmrKLjvUBSzAtxhTKZFAlT0wDncYpS+iMhYWoaydtjKr5LYV5e4bir3NzCKNAEOBlfVJ2VeRqRzExIToZTpy5dK9e7j0cH5bvRslVAFBn2Cxs1gs6dMV3VmvdeGuDycZ2qU0dvEYuK8rysiqBpenAVEiI9R4TwIgm2rjR5eYWDio1GvYthbKz+gL1kcHd9wmK960fhrtaxXBEhuNT1o8LTCxdNMQ968OXvX/hyMuarRozxEOIKtnh5jl3XQWeUgtR0xZJvcxh7e1D5Vso65io31z64ukK4PY3I4vOMbXG49DTrJRgWn8JXv8W4tK0FH4Yb1unvDVdfDZ066cERuBXgFePnp7+vNmigd7+rLoKCoI7MlSVEeZBg60pmMsEff+iv8HB9wG7duhAQwJAe+jeNC1YqJrykv2EOuhFG9tG7DpZHi5bXWYMva8uXj4998OXnV/3HeAghWLbKjMHgWjxjMMDSlWbvBltK6QFV0QCrhnQFLCu3phHRFEtX5zO2TlrZD2ixMDJ4E1N8mpJWEIgqYZSEhoUILZsRtwbANQ/bp10vq7AwPcCqW7d6BizhYeAjrVlClAcJtoQuPV1/HTwI0dEQG4uxTh3u7OfDhJf0TRa+RPWe/LKgQO/zf6nff+J6jWGPFN/s8jEeyz6KZIiDgMvTFrGsbAshzVMAyDwUQ3CQvNEJURbnUy0uNxxZLHAh1cNWpvz8wuDK+q+w49Y0IkrjQnYZP46kp8POnbBzJ8a0NOZphxjKf9CwOAy4NCyAxrwxBzDGdyjbMa0MBj24ql9fT+FeRl7L1uiOwMBLqevPldMBhBBWEmwJe0rBuXP6y9cXwmOAtpVdK68zmSHhOf2bZ+fjCZyP8ZAWMSFcUxHddKMjDW61bEVFuvHFRtFWqyu0S2BZuDWNiKaICnJj7rD8fP2LwR074PBhu1WDDRtZ5vsECfnPkEoYBlWARfPBgAULBiKMBcwbsYfB8R4EGYGBeitWXJzHE/t6NVujKwID9YQdvr6QLfexEBVBvkoXzuXnw6nThT8fPKhnAqwB3WOs4wmcBVpWtjEeS1L1bpcFBdUv65kQlSTxexNxHVOYOK0wEcHy1WbumZJOXMcUvvHS38iw/gFutWwNH1BCc0F+QeH/z53TEzWcP1+YKVACLZcM6+5aoAV6y9bw1imlbqedOwvffw+vvw6LFxcLtKyGGDaQbBzO3AYfEa4uglIMij/H/JG7SX4iicHxZ906F/3gmj6m69pr4cYboXFjrwRaZc7W6C6jEWrX1hNjeTpPmBDCLfIXJ1x34gSkHNMf2jExevcJ64SM1Yxb4wkMiqUrTYztbdJbxCbr+1TZrGdCVAHWLyUu52o3XXeMHBTIlGczSMtQJX4XpGkQEaYx4tZA/Q81P79wbKf1la2wZQfMy5d3SXcopX8hd+oUI/3PMcXYjTSTL6qEbIsaighjPiPaOAm2zGYGm1Yx1LSKgNmbSv+KuE4d6NgRY7t2jPQN5d7nIwBYOGo3wf4FJe/riL8/XBWrz1npxUl9y5yt0V1Go96S5Vf6ZM9CiPIhbyPCfSYT/Pmn/jIaCxNrhDmes6QiuZoB0a3xBBaNC5e6C+otYqXvU6FZz4SoYio6FbvRqDHvjQiGjk9F0xwf0zoTxLx/+WHMPA+peVWilT44ENSWyq+HRzIy9LkPT5/W3x8AIzBvxB6GLuiAppTDgEtDgaZvZ/Qr0mJosehzRO7ahXH/AZ69WMoz1N8f2rWDDh30rn3WX3auF86tWzcI9n4nILezNa5RjB3oxgEkyBKiypBgS3jGZIJjx/RXUJAedMXE6A/5Ksyt8QQGRdSlBjz3WsRg6dcZjL05X++24een/yvzf4karjJSsQ/uZ2TZR5HFxlJax3JFhFwa/3Kd2Tsfwq90OTl6F8tTpwozvl5mcPxZlo3ZQcKSdqSaCj/0W+fCijDmXxo/dVa/GU6f1icx3rOnsMySem02aAAdO0Lr1h536QP0rIRxcRBVr3BZOc035XbviiRcC7YkyBKiypFgq5oxrP2J2IIoTvnUreyqFJedXZhKPigIImKBZpVdK4eG9YSvfnK9ZWt4T/3Dm3stYpeynmUW6ZCvaYWBlydzuQhRhVVIKvai3QAv/Tvk6nySv7WwYCVFpqxQjOxD9ZmyoirLyYEzZyAlxeX5sIbEnyX5iSQW7IplwlI92dKgVmcZ2fY0I9qkYMxJgw279SDrrAtjqYKD9XmxOnSAWrXKfi5WmqaXU6+e/q/BADml7+apsvaucCogQA+y3Ag6g4MMqJOxLm8vhCgbCbaqk+xs/J//J4kXstjtG4/P1lrQvtWl9K1VTHY2pB3DFmwdOgQNaunpcatAy87IPjBlpiIt03l/eQBNU0SE6B/UoOwtYjZKFY4NyQHb2JCUFAi91PJVtBWsOs7XUokqa4JqSeNvz+up2C8LqsjP118OGAPgzn7UnCkrKltOjv58OnOmzBMOG/0s3Nn+tC3YWjhsC8GH98Ki3XD0aKn7F+DDRv/OtBkRRWDrZt55LgYG6gFWXJzeGlTBPH4vsZKWLCGqPAm2qpOkJD3tMNA+fz9+qzJg9XfQpAm0bQvx8ZXypuGS48fh9FH927datfSuhlFR5dZFozTGAJg3Xc/0pGnKYcClafo3ifOmF34jXtYWsdI3VoVppe0rUTwAs76qgYoMQiQdf9VRplTsFkthEFX0VVBQJcZWXVGys/UAKyVFH4/lDRYLN+RuZaDpR4xvfAsFLsxNFhdHXpsO9P/mQdIN4dzfcjX4lCHJhZXBoCfQqFdPf/+pxC/+PH4vKZrCXQhRpclfaXWyalXxZUoVdt379lto3lwPvFq0qJrfdJnNcPKk/vL11QOv2rX1fyu4voO7w7JXrXOcFC63ftsYEUKxOU7K2iJWZnYtYUX6tmia/u2utfXLGoD5+OivKtB6WJEqMvOdKN2w/gF89Z1rad0tFhjexQSnK6DvlnAuO1sfM5WSAhddyALkCotF/6Jt716M+37nf+mXWqTy8pxnFQwPh/bt9VetWhTk+pD+rRey3jZvDk3qemdslxeU6b1E0wonI5YgS4hqQ/5aqwulMMU04HM1kHlh7UjXQmlUcIHhaj0jDT9i1HL1b4B//11/+ftDq1Z64HXVVVWzO1p+fmEGK4NBn/8jJgaCawEV0+9nSA89pe6ClarIGA8Y2Uc5HONR1hYxr7OOV3HSlcphIJZXM7u2VXTmO1ECpcBiYWQ/A1PCIO1iyY1Stg+SvSquiuIyf/wBGV4MsJSCv/6CvXth377CMasWACcZawMC9CQX7dtDw4be6fHg5wd1ioxHatgQqkacBbj5XvK8hjEqSA+yquJ7uRCiRBJsVROJP5hJ+HSs3kXKX//kuFMVsLSgF1MKpjLP90UGGzYW7pCbqw843r1bT1bRurUeeDVoUGld90pksegTh54/fylxRF99+cWLEFi+mQ3dHeNRlhaxCldQoL8uMZlh0QqwjhEbPTGFEf18GXmLH8agIi1i1lcV4GoXxMrIfHfFuhRM2br3WV9Ff+ZS2u/nrB8kHX9zXyFfSgh71nmwTpwDWurLjh6Fssw/dXm5p04VBljp6aXvo2nQrJkeYLVs6Z2eDdZkF3Fxeo8JcxV8ryui9PcSjXn/CWDw4Iiq+b4thHCJBFvVQLEuUpe6iFnQPxSnEcLQ/P+wzPcJhhg2FC8gOxu2btVfoaF64BUfX3UDr6J+/RXC/CA6Wn8TjY6uEt0n3G0Rq0yJa61v5oUfeJevUyQm5TPl5TzHgeHlwZf1ZTAU/ltFuip6K/PdFZ/kwmIpDJhK+r+LqsWXEleCvDw4d67wlZd36Qutlp6Vq5Te5XDfPj3ISi3ejdeR/b7NaXtzHXyuaV1qcidTnoFFuwoz747+oj0j2pxmZNsU+3m5wsIgNlafeiSgCj18XeDwveQmjZG3BjBieDjGoKrx5ZcQouwq/1OrKJFLXaQwoKFI0J4nudEDGI8fcr7xxYuwebP+Cg7Wg674eGjcuOoGXiZT4TgvTdO7G9aqpb8qMRNjdch6lrgWhk0rvtyaASstU299WPaq/qZvc1nLmEOaZh98Wf8t+v/88g/IvJ75rqawtkI5e10eTJWD6vSlRI1iTdF+9qweBHkrwYhSepn79sFvv+nBmytiYshv2Zbhq/7OXz71uO261aW2piXur11sfq7lv9cmcX8dpizPY95dvzO4r78eZFXxeR1LU+y95P06BAdLkCVETSHBVhXnchcpNFLzAllyzcOMvf2g/ka4d68eoDiTlVXY4hUYqHflaN1az25YBVqPHFIKLlzQXwcP6tkXrUk2oqIAeYOyMpn1VgVwPgBbKQ1NUyQ8r38oduvDr1KlB2WXp7cPLhKQWYO1y/9v/bnAtUCtTJnvqguLRb/O1sDJ2b+OXlUkg191+FKiqOBAUFuqxrVzmVJ6WvZz5/RgqOjcfp6yWCA5WR8LvH+//ux1RXS03nW9TRuoXZv8XB/+Wl2v9P3QA61hCzrAZb8Gy6XnWJrZl6Eft2VZGxjSwp2TqaJ8fSE8GLiU+bGK9BoQQnhHFf1ELazc6iKlKZbui2HsNafghhv014ULetC1d2/JE0bm5MDOnforIEAPvOLjoUEVfyczmfTB2H/9pX+SDooGOlZ2raqExWvsuw46o5RG6kVYskYxdmA5VsiinCf0cKRooHY6BYI0/UPIZa9hN1r46jsXq2CB4b0N+n0DhR9qNA1yi3yyy8uHPK3kgKXoOnOR/+eYrAOV7F/WfYq+sovsd+6cPtipaCAlhDNms/58P39ev3cunzbCEwUF8OefhQmXiiTPKLDAjlN6oosOsRn4FP3uIiKiMMCKiSlT0GDKM5CwpB0o/UtERzz6kqgq8ffXe2cYjZBtwRZsCSFqFAm2qji3ukgpjQvZl/1Ko6Kge3f9deaM3v1j/379/86YzbbkGkZff/6VsYMf/btBrhn8q/AtY7HYf+u6fj3ERujXICpKb727gixLcm/SzKVJlG+w5Qml9GDNgZHdYUoorqdQviELLmQV36BocHfunHsJMYvum5YG5tJ3KZa05PF8RvTRU0JX2w+PovxYLPq9ZU0k5K35r6zy8uDIET24OnCg8AuJ0oSGFgZYcXEet8os3htj13XQmQr7ksgNWTkQ0l0//8x1ynkLrtGoB1lVJA29EKJ8VeFPzgLc7CKlKaKCSmg5qFNHf/Xsqb9ZWwOvU6ec75Oby83mddxsXofxtSxo0khv9WrRQv8WsyrLzdXPzXp+QUF615boaH3cV1Wch8yLzqfjUqAF+nYXXOiuWhVVmXT8bnCYtGQDJK7TmDJTSeIIocvO1gP/8+f1L5K8MLauaNIJg8pnzPsNud1nHSPPf44x38GXEA6cMdQiyb8Lbe4JJLBJrPfG+4aEsOz4VTXnS6KiZI4sIa5Y8hdfxbk1OajSGN46xbWCo6Phppv0V2qqHnTt3693x3OmoKBwAuUVK/TAzRp4xcVV3QQbVtnZ+uvECf2NLyxMvw5RURAQQU0b7xUd7l7LVpQX5g2tLNUp812Zk5aImi8vr7Br4Pnz9hOZO5CV60PI8/o0GZnPrSbYlaQTi9uSavZHUxYsmi/fJDfla1owhVHFpxApKioK4uMxN2vDoDn3oDQDzzVYDYb/b+/Ow9sq73yBf8+RLB1bsiVvsRPihIQwLKGQhcIECjeBNKTNZqb2QCkFh6V9OoGbPKELdAnDnfbCLbk0HZoCnbmBPJ2wZohxQxuWDCGUUhqSpoQlgQxkwcaOHcuWN1m2zrl/vD46ki1rl7V9P89zHkuybL+2LOn9nvd9f2+CAVBRjEqCxcU4vTXHThLJsihGZbNl/nskEaUEw1aGq19eiLUb3Ohya+E3B4UGpzKMutlRhq1ApaXAZZeJw+0W00g++AA4cQJjVigHOnVKHG+8Id5Izj5bhK+ZMzN/eoSmib1gurtFeBw2A7hafM7VKcJXlr8x1i4Enn8t+k7LtQszvNMSQTZUvkt50RLKLj6fmBrocomQ1d2d/LV6w8PAiRNo+rMFte9/2X+zJonXt7BbiFRXi7W7554rihBJEjSvyf+1cbNYgGmVYl1XaWnQp3LmJJFJBhzFYkYFC14Q5TWGrQynKBK2bnJi1S0u/5r70SRogARsrTsUvPdIPEpKgEsuEUdvL4be+wh//m0PLh56N/zX9fUZBTZMJmDGDJhmnotK3xy0myoSa9NECFwPdOCvgKIBDofoCDid4siyqR/1VwNrN2rRr2W6egIblyKZXvku44qW0MQaHhaBKpXhChDVCI8eFRVbP/lEbCEy1ARAbBUSithCREWDugEtSzZBmX12cqeKWwKmbX/pS6LgTQg5c5Jo0iQg3/bqI6KQsqv3mKdWLFHQuKUUDeu6gsrAy5IGVZPgVIaxte4QVpwXptpgPOx2+OZ9EXf+bjGK1H50/MNvUPjJYeDjj8NPb/H5gKNHUfDRUfy+cy8+Ms+E+b/swN/NAKZNy47QoqqiQ6Rv1ClJYiF4YPjK8M0zs3EtU67L5qIlUS/+J4Merjo7xWtJisKVpKmQPm8GPh15fW5pCfr8c+pSuFAS8ftokOFS7dhuW4kbnWHW8kbLahWho6oKUAJGsMKM9GTtSaLCQjGKhShL4xNR3siCXi8BwMolCloOVGHbjn7c9l1RhWr5ue2ov6AVdbPbEh/RiqBfLoJ63gXAReeJIPLZZ6Ji1UcfRdzY8u+GP4H5LTfw9h9FUYozzwTOOguYNUusA8iGKRaaJqZYut2iJDIgpk4WlgKYndamhZNNa5nyQb4ULclbw8PGtMAUhisAQH8/TB8dw0963sXl3n2wbjmOcQat0KheARk+/5TBcIK2EImHohjFmEpLjdf38MvPjC9P80kizyDw1EvG9Rt+jPGrhMqyCFg2m5jR0Z8nG6YTUUwYtrKIoki4flWhP2w9+Y/vRlwQnRKyLEaopk0Dvvxlcdb2yBFxRvX48fClE4eGxP0+/lhcdzpF8DrrLGDGDHhMRf5KWQBww7MXom52K+ovSH2gjFlfH+DywB+23ngDqLSL6YcOh5iSmQEVD7NhLVO+yJn1KDSyT1q/CFddXSJYJXMz4dFG1l7hk09EifbWVhSowEpP5BGr03BEFbSAcbYQiURRxOhVVZV47UvwBFq6ThJFXSW0oGDkZFthdpwsJKK0YtiisGwWH7SfvRT+TmVlwIIF4hgYAP77v+E5/Ame+XAqtpZcgW6pGNN9nbhWewP18n9BkQI23+zqAvbvB/bvR5N6BRrUn8Cl2kVHRpKw83Almj6chLU7h1IzVTKZvF6xcXTg5tE2m+h8OJ3io92eljfnTF/LlC9yZj1KPhoaEiPberDq7ha3pYqmAW1tRrg6cSK2TcEtFnES6+yzUf7uJMgfi2nnkUTcQiTQ9Omi0IUj+WcFJvokUXRVQiU0PmbHymXFyf3hRJTTGLYouQoL0WRahIYP/6fYmNIiQtNBTcUO3yKs9a0LWV64Sf0SaofvN24YCSR656DLY8aqbXPR+I2/YmUmB67R+vrEoa+hMJmAkhJ4Cp146kAVANFJCTtVhXJG1q5HyTeaJp633d0TM2qlc7uN7TU++US0IRZlZWIrjrPPDlofW4tOPP/R1Ki+RdgtRBwOwF5pXJ81K7bNv2M0USeJoq8SCjR8rw8tV9uhKBzRIqLoMGxRUjV9WInabXONivF6aBpZTNCFYlFe2P7PWOl9FQDg0SxoGP4xgHCVsiRImoqGp85By7KXoZxVI/bIyrYpHD4fmv5kRsP2GSKMjtj5xshUlZ+r2PqjQaxYbM2+340iSvd6FApBD1ZuN9DTY3yMZRQpXr29YsTq2DFxtMd4IqmgAH+yXIw/F8zDv3/Hi6Lq0pB3q7+gDWt3DqHLY4aGMCF/9BYisiwC3KRJQEWFmC4Y5dqrbBJ9lVDA1a1h+4sDuPFrRRPQMiLKBQxb2WzaNMDdJtYNZADPkIyG7V8ANIz7hq5BggSgYfgnaLnt76Ac/xjP7a+GqzXKSlk+G7a/OIgbTZtFdcDp04EZM0TRjdLQHY1MMiaMjvCP4PVJWPUjBY2H/4aVf+8V0w5LSsTvardn/d5fxKIlaaWqwcFKD1e+CVr72tMj1rUeOyY+RiguFNKUKWJ64MyZ8EyajrU/WwoA+LeyVwGE/j2UAhVb6w5h1ba5kDQt5OuzfwuRr38IZXq12FervDw7qscmKLYqocCOXYMMW0QUtdx/Fc1lZ58NFJ4tOg9tbeKsaHd3yLv2eU2w37cYANB776spKazx3HtVQaM149EgweUpwPb2C3HjZZVoPDYHcluU6wngww71Stxoekl0XN57TxwAUFKCgpqZWO5RcaDgC6mrAhanqMOopqHh2dlombkHil56HhAjXXa7CF76YbNlfAl6GivR9Sgswx6D5mbA222Eq3AFfJKtu9sIVsePi2JCsdKLCM2cKU4sFQY82N7oil4AwIrz2tH4jb+iYfsXgl6n/VuI2FRs/X4/VnzlgpwZVbcVAtq+yO8Dp91S1P8Wqgp0ujKsWBMRZTSGrVxgs4k34pkzAY9HnC1taxOlhyewY9H4YZX/jTuSwPLCp/vNUX0NAKgwoXO8/WLcbpgOHcS9PZ8AAKz/qgLTa4CakaO6Oq1naWMOo++PKr+saUaHMZDFIv4HbDYRxux2hrAswKIlSeLziRNOvb3Gx9P9AL4kPn/4MDARVVs1Tbzm6sHq+HGx3itWVqsIVfprehK3x1h5Xjta7t6DbX+bgtt2iCqqyy/zoX6JCXVXy1Cs9qT8nKxhtQI2G8qrBiDLnqjeLmUZKCvlDAMiih7DVq5RFGDqVHEMDQGnT4sRr+bTKf/RMYWmgPLC5UXD0Yc0qCgzh5426dEseEa9CltLrhIVELs7ce17b6D+g5EKiCaT+LvUBASwwonr3cYbRiPyesUROAoGiPLEevCy2wGzHUB5fI0nSjd9CmBgqOrtFRVQR49ixzDiEzevVxS++ewz4ORJMYIWa0ELQDxPa2qMKdFnnJGa6cKKApSXQ6mowPVXlOG2HeLmJ+835VfI1/fGKioyCohco+L533ui+nJVBa5dyhNZRBQ9hq1cVlAgRnOqq4GZAafsFAVQ4+gURBBTaAooL1x7Xhuef78qqp+hQsa1q0qAmjVies6nnwLHj6OpZy4ahn8MF0ogW3xQJRMOar7gCoh40zjjrKuoAGpqYJp8JmqGz8VJ0xnx/OpRiTeMxm1oyNhcFRjpgIqppNi3DyhVRNgsKjI+cjSM0knTxOj8wIBYi6offX3iY7qmBuujVnqoOnlSzB6Ipz1Wq1hvO326OCZPFieCkk2SxBTEigpxFAeUK8/BIhcRWSzGa92okcL65YVYu8GNLrcW9iGVJMBZIqFuWT6lUyJKFMNWvgg8U3r55SJsdXSII0nTDWMKTQHlhWOulHXBKaBgpANx8cVo+qACtdvmGd9bEh0XfRPPLthFBUTz3Vgp/zH4m478DQr2/xXPu3ajSyqB5elCoGayWIg+ZUpwJyUB8YbRlHC7AY9r7O1ms3HWNzCEFRaKkE6UKE0DBjzBYUo/BgaS8loU1f6A4SRr1AoQzxs9WJ15ptj4N1WFbkZGr1BZKaYfZlBxi2jXTyWVJBmvZWE2mFcUCVs3ObHqFhckKXSG1vPZ1k1Oln0nophkzisxTSx9jc/06aLEscslpht2dIgzy3GIt7xwTJWy6g5BKTA6Y54hGQ3/eSGAcEUnZEhQ0TD8Y7QUrAzeVHkUp+aG/N+fAZ9+bNxYXGwErzPOEB/jmH4YbxidUMPDIoi53WM/ZzKJzpx+6AEs8MjDaomeQeCpgH59pu+ZlvLiGqoKDA6K4KR/dHsBnCc+v+c1wDwBZdWjNTQEtLYCn38uAtbnn4vXwnhH0YqKjHA1fboom56q54Uc8Jp36aVAZZ6tuRpPmFGs8axYoqBxSyka1nXB1W089rIs/qWdJSKQrVjCk05EFBuGLRJnPysrxQGIdRCBo15RdjoSCU0RK2Upw9hadwgrRm1oHH3RCRkulGD72d/GjUNN4ox1tOWee3qAI0fEoXM6gTPOgGnSVMz1TsJh86yI3ybuvW4yhV6IINwZfqvVH7w8ciGe+ks5gAoAwA33+FC3WEL9l+WMDCHxaHpdL+FuPJ47/ziyZ9pGLXdLuHd3A26PODHj8YhQ5Qm4PprXBH/YUtNYJXRwUEz/00PV55+L17l4g5UkiZEqfZ1sTY3YgiJV1fwkSWwFUV4uRq6sTuNz9jwPWiHWYsVq5RIFLQeqsG1HP277rjjhtHyxFfXLFdQtK+SIFhHFhWGLxtIr2p15phjp6OwUZ3o7O8VZ6jDiDU1AYKWsybhtxwUAgOXntqP+glbUzW4LCme6mItOSFfhxoYy8Xu1tYkNRU+ehHb8ZMSvD9LVBXR1oeDQ+/hN91vQIMH6iBmYPEl0vqqrxRltp9Pf8UokjGaNwUFgcBBNf7agYfvM4I2b/2RC05sS1v58CFtv+Agr5veJM9AWiwhpVqtx3WIBVCuACSh0EKem14Ha7429Xd+rp6tXbF7c+KAo9Z6xVNUosjI0JB5D/brXa1zvGQYwkhzfeWdiKvwlYnDQCFStrSJgxbOvVaCiIiNUTZ0qRrktluS0dzw2mxGuSkuDp8Pl49qr0RRFPC5JmuasKBKuX1XoD1tPbnbCVpR/I/ZElDwMWxSe2SxCw6RJ4vrAgKhw2NkpDu/YKXnxhCadUqDi+gtb/V/35D++G3ZPsLiLTpjNYkrgGWcACxbArGm4uKtLdMiam40z3yF+v1AkaJA6TwNdp4EPPzQ+YbWK8DVyrKiuRuN1PjQ0zok5jGaLiBs3D5ixasv5aBz8K1aeF2b0LrCgx1/+Athk0dE0m8d+tFjENEeLBRgyA0jt0JlnUIxoAYA2zv+fpkmQJA0N94k9tVI6mqdp4gSCfgwNjf3o84mPfSqAi8TXvbEXQJTThieiwl88hodFiDp1KvgYZ8/BqE30qJVOUUSw0g+ulRxLX1taWJia4iJEREnEsEWxKSw0Oh/6vk+dnSKAdXX5p+bFGprilbSiE5IkOlKlpcBssf8MVFX8XnoA08+SRzv9EBBn10+cEMeIlQBaSqvwdMUy/POxa9All2DhLDfq5p5G3Rfas3NEa0RMGzdv/wJa7t4T8vf1DMl46m/V/us3/L8ZqJvdivoLwod1AMEh7Y29gALRIdMPWQ6+bDaLy14TgJni61paAEUz1trIsvgfGTme263A1RO5cIqmSXD1ANubenDjVYPiOaMfwMjIRJXxM81D4v/O5zM++nzi/vplVQX6NQCXiq/b+zogDUZsS/DfR788BKR4YCZpVFUEqFOnxKi0HqpOn068qIYkiYI7k0cK40yeLEanUz1qBYgTMqWlYhS8vFyMZFFohYVAhW1iHhcioiRh2KL46esHSkrElENVFYFLH/Vq7015E1JadEKWjbVsF42MBPh8ooM3Er5Mzc242HQq5jUfiqsN31S34ALXdgDAXIsbphYZeKtMdPoqK0XHSy/bnCUl2RPeuBliZGz0NNSdhyvR9OEkrN05FNvIn3cIQORw7BmS8dTByf7rN9xXEDbcNf5hDmTJHv301Rf7caPjYIj2meAPWx9+GP3UvMDANDScPYEpGsPD4nWko0MEqY4OMY25vT3qkeawJEk8vwKDVVXVxHXg9ZEr/eROUdHE/NxsZbEAGBKXnQ7AkptT+mxFMrTmyZHvSERZh2GLkkeWjakvAOAeMj5XXAx4u5O+T86EF50wmUTnbHLAm+LQkHG2PfCItaqjqhqFSQ4fDv5ccbHoIOrhq6JChLHi4tRPa4pBohs3R5yC6DFj1ba5aPzGX7EySVMt4wl3E75nWpYbU4pdHxU/fdo4OjrESZoYivJEpJ8w0Z+z+ohVmDLgSVdUZASr0tIJ3Ug9awVOExyUAGRYsSAiohjkdw+AUiuwQ3PJJUDBsL+wBFwuMSUowek/GVF0oqDAWP+l0zRRPr211Qhfra2iMxmPnh5xfPJJ8O1ms9GJKysTU5H0s+ZO54Tvs5NICEnWFMRYxBvuMmrPtEylquJ/NvA5HxiqkjFKFcjhMNaX6kdFxcTvNWW3G89Jp5NrrqIly8a+fkFhOHunVRMRAQxbNJHMZmNUBhBT8rq7RSdM75DFsh5qRCIVEFNGkkTnz+EAzjnHuF0fBWttha+lHQd2mXGm7ySAEPtaRWN42JhiFUpxsT98mUvKscRjRrOpGujtAZzJ3xcrkRCSjCmIsUgk3GXFnmmppmlim4jAMKVf7upKysmUkBRFTPsbHazSEWoKCsQ0aodDhKuSkokdNZtgSd9TTpKMaoJZMlV6PB6PhqcajfKQN6zpQt0yBfXLWTKeKN8xbFH6mEzB0w71M+Gfd8X8rRKpgDihAkbBhr5gwrf/LAo59K7/HWzuNmNj6cB9zhKhj4gdPw6zCvysZx8AQPmlGzDLIozp6+4CD4dDfLTZYgpkiYSQRKcgxiqRcJf1e6ZFoqqi8qj+/+N2w+zqww973keV2g7ro+8CbldcJ0eippc819cu6qEqnVNn7Xbx3HA6xUebLaOm8aZSUveUs1rFKFYMmw5nsqaXPWM2Q9756iCaXh7E2g1uboZMlOcYtrJZZSUgeUSnaDgHpinJsujAWBzGbQsWAINd4ix5d7fo+I2znmOiKiCmRGEh4KgR5aUD6WWtT58eG8QS7ejq1d3ClciWRwWy4mLRwQx1FBQkFEImeh1UIuEuI6avxkNVgX4Pzhw+gQq1E6ZDfwMGuoxQFXiMGpUyq8C1nhIAgHTaDSRjUNRsNgJVYLAqK0v/2qY8G7UKJyl7yhUUGAErh8q1N73sQe0tY0+K6U+fLreGVbe40LilFCsZuIjyEsNWNjObgKJi0QEeGhKha2AgtWebJ1pREVBeJKqGAeJ3c7uNkNDdHXshimxiNosF/dXVwberqpjCpRcUGH309yfn50cTyHQWCxSbDVsdB7DK8z1I0KCF6JFLIwuktv7DwaAQMtHroBINd2mfvqppKNQGIHW5gKEe8ZhHOgYGoPg0POcSoamgKUmhKRyzWYwE6esJ9UClF3hJ8lTWuJhMwScUHA7x2pPBoy5Jn9IX5ufEvaecHrAUZeLXzk0Aj0dDw7ouAOPXdNE08W/UsK4LLQeqOKWQKA/l3qtfviooMM7Eer0idHk8uRW8ANEp0hef6zweIxCc6klf2yaSLAeX3R9tcFCEroAwpnZ04bNTZkz2nUpNm7xewOvFCryARvNpNAz/GC6UQNZ8UCUTZPigwgQnerDV/C9Ysf1NYIfJ3xmrHfoyntfujOpHqZqEa8/4SPx++v9+QQFgMo2tfDeOZIS7qKevBu6TpW8wPDwMqd+H2UOHUah5IB/5APANiMdu9OHxiI9eL+DxQPF48XbHQ5Chwrp5AgJTOCaTMbWutNQYCdI/ZtpUOz1YBY7YZlobI0jqlL4Intsd/HPG499Tbo+EG+ts4nmdgwEr0HM7B4KmDo5H0wBXt4btLw7gxq+x1D9RvsntV8J8ZbGIw+EI6KglaaQjEymKsWg+cBbeeecBQ27/mpOcC57hWK1jRsS8XhOu/XgxJE1Fz9oXYBtwib9L4KFP1XS7EypusFL+I1oKVuJZ3yI8MXA1uqViTLd24h/kvaiTX4MijVSi8/nECF1vL+q1/8Ra3Iwu2EOOiOkkqHCiF3Wv/QTYM6qinSwbwctsNjYjluUxR23vlXheWxPV76NqEq517QC2/Un8XfTT2KoKBcBNw4DkqoYFQ7i+6zOYX/cC/xUcrEKd+raqwBNd2wAAlu0xhCYVkDFBU9psNqCkBGpRCf7TNQ/tchl+tbIVpsoSEbDs9swYnQpFPykRGKzs9qwKVqMlZUpfDBr3ALKs+b9/OLIM7PiTFTeujrzhdy5ofGkQshzdS6UsAzt2DTJsEeUhhq0sE/PGh1arOMx2ACMjGrKEMbWuc9GUKUDhyPRDTRPTqNwB4aunR3SG84wmyYC9GCgrCi5XH0hVxd9LD1/d3eJv1tcnjt5e8bG/f9wQq0hefEN+Cee73wIAzJ3shilMn1yRvNhq/hesGv4/kKCOMwVR9Gq2mv/FCGyj262PBkVQrz2Htfhm9OGu/WmgI3S5clkFLhwW6zak9jSPNEWpR7JDq1QAh90II3a7EU6Ki/1ByjMk4z8OTsYDH4rRu/YPT6FObkX95DYocoasR1MU0V693cXFYipgpgbBOCQ0pS9Op7sRVdACxNOv05Uh/w8T4LRLjfqcVL79bYjIwLCVLwLP5FZVAaYhMeKVi1MNQ5Eko5BD4IbEAwPBAay3N+PXgPV5TbDfN1LF8N5XU1MERJZFh9VuD38/TRN/r/7+4BA2cvjc/fhrhxNO1Y059uOAN3wxlxXym2g03x1mCmKvmIIov5nwr5iUcJcpZFkEi6Ii8T+u71dksxm3jxyegmJc9n+XY0iyoPdbr8Ic4f8nnk2fU0ov2hIYrOx2MZqf42Ke0rdbw41fTexnljtiG9kqK82dcBtJeakc08hWPv1tiMiQV2Fr8+bNePDBB9Ha2oqLLroIDz/8MC655JJ0Nys99BEvh8O/DgQeT25UNYyFXh2rKqBc+dCQf2pb0JHlo2DRrmWKiSQZf8Py8jGfHvKa8K33RoLh2pGO/fCwsQZJ/7/T1xh6PFjp8aCl/1483fIF/OKTS+GBFeeXdOBryp9QZ9oDZbgfGCpIyuMxkeEuJFnGgKRgEBZopWag0GI8NxXFuKwfFgugKPCaivCNf1uIPqkIR+46AFuxOfqpcV4ThqTogkm8mz4nhSQZgVEPVPpoVRZPA0xEbFP6NOzYg4TDVu3VJjz/WnQjMqoKXLs0u/fLikXtNVY8//voTs7l29+GiAx5E7aeeeYZrF+/Ho8++iguvfRSbNq0Cddccw2OHDmCSZMmpbt56aWv8SopER1hveOb5eEibgUFY4twAOJvoo/eBB75MDKYTGZzxFEzBUC914TV980GABz4/jHYLLMBzDbupBedGBoyDq/XuOzziR5OhGOlqqJl6Bd4qnk2/tffLsGgZMGl1R34WtUHqKt+H4rJAshXiw7+OMeQz4SfPTsHXqkA2699D4WFsvg9TSZj/djoo6AAnuECXKmPUv5T5FEmneo14SPzrJE/1geAlPz/wUQ2fY6JyRR6K4EcmwKYDLFN6ZPQGUXxhpD0sK8oqP+6jLUPtqHLrY1bcQ8QTwVniYS6Zckt2R/z1PkJVL+8EGs3uNP2tyGi7JA3Yeuhhx7C7bffjtWrVwMAHn30Ubz44ovYsmUL7r777jS3LoOYzcZ6DX0EwuMRndh8pxfiCBzB0afR9fYaU+n0MtsZPh0x60mSEVwS3JNJAfCPXhNueX8aAOA//uk92CwOAJdF9fU+rwmNvxOhST0fQLbs7xZGIps+h2SxhA5VipK3I1Wxim1Kn4YyR8S7CZIUFLACQ65iArZucmLVLS5IUugS5/rDt3WTM69KmyuKxL8NEUWUF2HL6/Vi//79uOeee/y3ybKMxYsX46233hpz/8HBQQwGLLB3u90T0s6MEzgC4fMFl6AOdxovnwROoxtteNgIXn19YsRQD2P5OmpIWSOuTZ//3mWsF9M/6pfzdEPgZKpdCDz/WvQjW9cujOJ12ukEygrDBt4VSxQ0bilFw7quoFLn+nolZ4kIHSvycNNe/m2IKJK8CFsdHR3w+XyoClyXA6CqqgqHDx8ec//7778f991330Q1LzuYTEbHSdOC13lxGl1oZrNRbno0rxfo6DOuT64G1H5jCiflhZSso0uSmDd9tlYCV+b5lOwUq78aWLtRQ1fv+NUIAUCSNDjtYpPjIGazGLkqsgLoFLcVRjeyuHKJgpYDVdi2ox+3fVecgFy+2Ir65QrqlhXm9agN/zZEFE5ehK1Y3XPPPVi/fr3/utvtRk1NTZivyGwej4anGgf8129Y04W6ZQrql8f5JqBPOdELbHC6YewsFqA0oEjB+bMBfXBMVYOLRgwMGIfHA3g5KpbPPEMynvqbsX/aDc9eiLrZrai/oC2+NVOBRgpwQFFQXl4A+ViUI1uyhjIHO5SppliBrf8s9tGSJC1k4JIkMbqy9Z/FNLeggismk7hTf3z/J4oi4fpVhf5A8eRmJ2xFXFcH8G9DROPLi7BVUVEBk8mEtra2oNvb2tpQHbDpq85qtcJqzY2qQU0ve8ZMb9j56iCaXh7E2g3u5ExvCJxuqO9z5PEAgxyhiUtgGe9Q+lRAH3g9/3wAA8ZIo/6RUz1zUkJl2PVqhgiY8nreeYAzYK2O3hkHULsCeP6dJE9Zo4StuFJsWNxwH+DqMW7X13I57RK2PmDBiqU28ZiHGLXK5KITRES5Ji/ClsViwfz587F7927U1tYCAFRVxe7du3HHHXekt3Ep1PSyB7W3uMbcru8J0uXWsOoWFxq3lGJlsuaTy7KxhsmqAhgJuAVmAHlWVj5VAiu0TZ4c1Hf206d66hv8Bq63GxwMrtyXb+X+s1TUZdi/24qVl/tEsNIDVmCneyDgi6dMCf3/gyRMWUuCvgHAfqX42b17NdhYzA0AsPJ/iA2Lt+3ScNtPxd9n+UIz6lcUoW6ljdPWiIgySF6ELQBYv349br75Zlx88cW45JJLsGnTJvT19fmrE+Yaj0dDw7ouAOMPcGia6H81rOtCy4Gq1L5BV1SIkm+BnX+u9UqdwKmekejl0/Xw5fUGXw68Tb8vH7vk00vEWyzicsDhgQUN/3sqgAhl2KGh4dFqtKwSU84SEfOUtdyYDJAdzGYoNiuuv86C237aBQB48rEKTlsjIspAeRO2rrvuOrS3t2PDhg1obW3FnDlzsGvXrjFFM3LFczsHgqYOjkfTAFe3hu0vDuDGr40zbS1ZAke9ADGiEhi+OO0tPUwmcSgxjG6qqnj8fCObFOsjZIGHzwf0BoyalZYCBcPG/lejP2bT4y/L4m8myyMl6AMq7ZWXA7aRz4/eayvwNr1svX45zJ5Sz/0ecPVFPhmiaRJcPcD23VrCm9kC0UxZE0FrxZWJ/ywKQ5aDN7dOcO0VERFNnLwJWwBwxx135PS0wUCNLw36S89GIsvAjl2DqQ9bo+mdTZsteNrbsAcAR04ymiyPrP+JIHDK2rx5405ZAxAcvFQV6A34H5g3D1BGwpimRT4C6ZsPR7o8GBBm5s8H7CNhKvAwmYK/LtTvOWdO+N8zDo17YttfaccehAxbnkHgqYDihzf8WEz/q796/JGpkFPWvgTUX62hLszXUQIkyZj+abWybD4RURbLq7CVT0671KiCFiD6tZ2uNJ8hDZz2ZrbDv9arqAgwebmuKB/ogSaU0tKkB5gxAgOT05n6nxeD092IKmgB4n6dIUa1m17XR6iM77Pzj0DTXglrN2phR6gUK3D9EuC2n4rrT/4UXD+VbAUFxmugxcKNntOIBUSIKJkYtnJUeakc08hWWWnmzPUP+UanVzn0j34xfFH+KHfENrJV5gi+rel1oPZ7Y++rf7+uXrE2q/FBMZJFE0APVxaLOMJMIyUiouzFV/ccVXuNNaaRrWuXJn8ukB6atObJiS/c1td7ORzApElAdbUY7bDZxFREohxWuzC2ka1rFxrXPYNiRAsYv6qgfnvDfeL+lAIWi9geo7xcVBGtrBQbnisKgxYRUQ7jK3yOql9eiFKHFHEmiiQBpQ4JdcuybE7QeOFrvL2pMpCtEND2adD2saQ1hVd/NVBarPmr/41HkjSUFmtBZdif2y2mDoYr3w7oxTUkbN+djBbnOX3NVXGxEa4qKkS4GmfvKyIiyk0MWzlKUSRs3eQEMP77un771k3O7N+XxR++SozbyspEZ4edG8pyehl2AOMGrvHKsOvFNaKhF9egGMmyGKHSw1V1tQhXfP0hIsp7DFs5bMUSBY1bSuEsCX6j12esOEskvLClFCuStaFxprGOOrNcWSlGwgoLOfWQso5eht1pD75dD1JOO/DCxrFFLmIvrpGM1ua4ggIxhdnpNEbWeXKHiIhCYI8zx61coqDlQBW27ejHbd91AwCWL7aifrmCumWF2T+iFQt9k1ibTVxXVWOzXn3z3mza64nyTjxl2BMtrpH39G0OCgqMYhYMU0REFCWGrTygKBKuX1XoD1tPbnYmXrAiQ8VUslef+qNv5qtposqhHrz0gyiDxFqGvXYh8PxrsRTXyOMTDpIkRr0DwxVHwYmIKAF8FyHSSZIx+qXTtODgxQBGWab+amDtRg1dveNXIwTEmi+nHUHFNXJa4PNdP8zmuEet+vpV2M8W+wP2flyVsye0iIgoNgxbROHoVcUsFuO20QHMm/ubLvcNAPYrRSe0dy+rJ2YTvbjGqu+KQBUqcI1XXCNnhApWgSdViIiIUoRhiyhW4wWw4WEjgOmXo93sjCiF9OIaDfcBrh7jdn0tl9Mugtbo4hpZyWQSI1T6SBWnAhIRURrxHYgoGUJNQQQAn29sCBsezptCHPpeYrkuG37PeIprZDT9OacHK/0yNwgmIqIMwrBFlEomkzisAT1ZTRMhbHQAy/GpiJR+sRbXyCiKApRYjHBlMqW7RURERBExbBFNNL3imdks9vzS6SEsMHzpx8h0RM8g8NRLxpfc8GNR0KA+G0cmiALp0//MZuOy1wSgQ3y+1Amw6AQREWUZhi2iTBEYwkZTVTT9oR8N3+2By23cvPOPEpr2impzObPmhnJX4P/46CNUFUCueSQioizHsEWUBZpe9aL22z1jbtf7ol29ElZ9F2jcrGDllRAjZPqRpbJhHRSFoI9K6VNoAy9z6h8REeUZhi2iDOfxaGhY1wVg/LoamiYGBhruGUTLgSooimR8Qp+aqIcv/bKqZnUYSzaGuxgUFACF5tChioiIiPwYtogy3HM7B+DqjhwCNA1wdWvY/uIAbvxakbgx3NRE/Yv00BUYwAIPVQXAEJLzJMkITrIcPBoly8CgDKBd3LeinOuniIiIosCwRZThGl8ahCxHt3xFloEduwaNsBVJYAc7nF4fgFPissMBKCMhTT/0UKaqeVPWPitIkhGcZHnkkAD0i8+XlQHFIyNSodZMBRrm+ikiIqJYMWwRZbjTLjXqOgGqCnS6UtAplgM64kWF4Uc1AkfLAgOZHsQCP46+jUKTZSM4SRKgSgC84nN2O2APDFMBR6gA1a/CH7asFsDMESqKnq1IhtY8Od3NyEj82xBRKAxbRBmuvFSOaWSrrDTNnedoR8tCCRXINC2+AwgOcNFcjuZ3G++6flmSQh/hPqcfo0NV2MDUJi4X2zmlj4iIKEMxbBFluNprrHj+956o7quqwLVLs3jDLXkkNKSr0MLo4BVpah2ljMej4anGAf/1G9Z0oW6ZgvrlhUYBGCIiogzH06FEGa5+eSFKHVLEfr8kAaUOCXXLCsPfkcYXajSKJlzTyx5MmdeG279nbCq389VB3LS2G1PmteF3L0d38iHX6dPWtObJsHF0k4goI/HVmSjDKYqErZucAMbv/+u3b93k5Fl/ympNL3tQe4sLXe7gUUb/nnJuDatucaGJgYuIiLIAwxZRFlixREHjllI4S4KDlD7rzlki4YUtpVixRElD64iSI9o95QCgYV0XPB4WVSEioszGsEWUJVYuUdByoAr/vrHEf9vyxVb89l8daDlQxaBFKTURU9b0PeUi1SwJ3FOOiIgokzFsEWURRZFw/SpjTdaTm5248WtFnDpIOUHfUy4a+p5yREREmYxhi4iIMkJG7ClHRESURAxbRESUEfQ95aKREXvKERERRcB3KiIiygi111hjGtnK6j3liIgoLzBsERFRRuCeckRElGsYtvIEN78kokzHPeWIiCjXsNdNRJRlcvnkCfeUIyKiXJJb79JERJT1uKccERHlCoYtIgrL49HwVKOxeewNa7rw2+398Hgi7DxLlIBs2lOOzxEiIhoPwxYRjavpZQ+mzGvD7d9z+2/b+eogblrbjSnz2vC7lz1pbB1R+vE5QkRE4TBsEVFITS97UHuLC13u4LPzemnuLreGVbe40MTOJOUpPkeIiCgShi0iGsPj0dCwrgsAoI0zE0q/vWFdF6dLZZFcLq4xkfgcISKiaPCdlojGeG7nAFzd2ridSJ2mAa5uDdtfHAh/R6Icw+cIERFFg2GLiMZofGnQX2o7ElkGduwaTG2DiDIMnyNERBQNhi0iGuO0S/WvO4lEVYFOV5R3JsoRfI4QEVE0GLaIaIzyUjmms/ZlpXwpofzC5wgREUWDr/5ENEbtNdaYztpfu9Sa2gYRZRg+R4iIKBoMW0Q0Rv3yQpQ6JEgR9o+VJKDUIaFuWWH4OxLlGD5HiIgoGgxbRDSGokjYuskJAON2JvXbt25yQlEi9DiJcgyfI0REFA2GLSIKacUSBY1bSuEsCe4k6utUnCUSXthSihVLlDS0jij9+BwhIqJIGLaIaFwrlyhoOVCFf99Y4r9t+WIrfvuvDrQcqGInkvIenyNERBSOpGmRtmQkt9sNh8OB7u5ulJSURP4CohTq61dhP7sNAND7cRVsRak/Z5KOn0mUTf932dRWIiJKTCzZgO8GREREREREKcCwRURERERElAIMW0RERERERClgTncDiCg2tiIZWvPkdDeDiIiIiCLgyBYREREREVEKMGwRERERERGlAMMWERERERFRCjBsERERERERpQDDFhERERERUQowbBEREREREaUAwxYREREREVEKcJ8tIiLKSNxTjoiIsh1HtoiIiIiIiFKAYYuIiIiIiCgFGLaIiIiIiIhSgGGLiIiIiIgoBRi2iIiIiIiIUoBhi4iIiIiIKAWyOmwdO3YMt956K2bMmIHCwkKcddZZuPfee+H1eoPu9+677+KKK66AoiioqanBz3/+8zS1mIiIiIiI8kVW77N1+PBhqKqKxx57DLNmzcJ7772H22+/HX19fdi4cSMAwO12Y8mSJVi8eDEeffRRHDp0CLfccgucTie+9a1vpfk3ICIiIiKiXJXVYWvp0qVYunSp//rMmTNx5MgRPPLII/6wtW3bNni9XmzZsgUWiwWzZ8/GwYMH8dBDDzFsERERERFRymT1NMJQuru7UVZW5r/+1ltv4corr4TFYvHfds011+DIkSNwuVwhv8fg4CDcbnfQQUREREREFIucCltHjx7Fww8/jG9/+9v+21pbW1FVVRV0P/16a2tryO9z//33w+Fw+I+amprUNZqIiIiIiHJSRoatu+++G5IkhT0OHz4c9DXNzc1YunQp6uvrcfvttyf08++55x50d3f7j5MnTyb0/YiIiIiIKP9k5Jqtu+66Cw0NDWHvM3PmTP/llpYWLFq0CJdddhl+85vfBN2vuroabW1tQbfp16urq0N+b6vVCqvVGkfLiXKTrUiG1jw53c0gylh8jhARUSgZGbYqKytRWVkZ1X2bm5uxaNEizJ8/H48//jhkOXiwbsGCBfjRj36EoaEhFBQUAABeeeUVnHPOOSgtLU1624mIiIiIiIAMnUYYrebmZixcuBDTpk3Dxo0b0d7ejtbW1qC1WDfccAMsFgtuvfVWvP/++3jmmWfwy1/+EuvXr09jy4mIiIiIKNdl5MhWtF555RUcPXoUR48exdSpU4M+p2kaAMDhcODll1/GmjVrMH/+fFRUVGDDhg0s+05ERERERCklaXoqoXG53W44HA50d3ejpKQk3c0hIiIiIqI0iSUbZPU0QiIiIiIiokzFsEVERERERJQCDFtEREREREQpwLBFRERERESUAgxbREREREREKcCwRURERERElAIMW0RERERERCnAsEVERERERJQCDFtEREREREQpwLBFRERERESUAgxbREREREREKcCwRURERERElALmdDcgG2iaBgBwu91pbgkREREREaWTngn0jBAOw1YUenp6AAA1NTVpbgkREREREWWCnp4eOByOsPeRtGgiWZ5TVRUtLS0oLi6GJEnpbg7cbjdqampw8uRJlJSUpLs5lAR8THMPH9PcxMc19/AxzU18XHNPJj2mmqahp6cHU6ZMgSyHX5XFka0oyLKMqVOnprsZY5SUlKT9n42Si49p7uFjmpv4uOYePqa5iY9r7smUxzTSiJaOBTKIiIiIiIhSgGGLiIiIiIgoBRi2spDVasW9994Lq9Wa7qZQkvAxzT18THMTH9fcw8c0N/FxzT3Z+piyQAYREREREVEKcGSLiIiIiIgoBRi2iIiIiIiIUoBhi4iIiIiIKAUYtoiIiIiIiFKAYSuLHTt2DLfeeitmzJiBwsJCnHXWWbj33nvh9XrT3TRKwM9+9jNcdtllKCoqgtPpTHdzKE6bN2/GmWeeCUVRcOmll+Ivf/lLuptECdi7dy9WrFiBKVOmQJIkNDY2prtJlKD7778fX/ziF1FcXIxJkyahtrYWR44cSXezKAGPPPIILrzwQv+mtwsWLMAf/vCHdDeLkuiBBx6AJElYt25dupsSNYatLHb48GGoqorHHnsM77//Pn7xi1/g0UcfxQ9/+MN0N40S4PV6UV9fj+985zvpbgrF6ZlnnsH69etx77334sCBA7joootwzTXX4NSpU+luGsWpr68PF110ETZv3pzuplCSvP7661izZg3+/Oc/45VXXsHQ0BCWLFmCvr6+dDeN4jR16lQ88MAD2L9/P9555x1cddVVWLVqFd5///10N42SYN++fXjsscdw4YUXprspMWHp9xzz4IMP4pFHHsEnn3yS7qZQgp544gmsW7cOXV1d6W4KxejSSy/FF7/4RfzqV78CAKiqipqaGtx55524++6709w6SpQkSdixYwdqa2vT3RRKovb2dkyaNAmvv/46rrzyynQ3h5KkrKwMDz74IG699dZ0N4US0Nvbi3nz5uHXv/41fvrTn2LOnDnYtGlTupsVFY5s5Zju7m6UlZWluxlEecvr9WL//v1YvHix/zZZlrF48WK89dZbaWwZEYXT3d0NAHwPzRE+nw9PP/00+vr6sGDBgnQ3hxK0Zs0aLFu2LOi9NVuY090ASp6jR4/i4YcfxsaNG9PdFKK81dHRAZ/Ph6qqqqDbq6qqcPjw4TS1iojCUVUV69atw+WXX44LLrgg3c2hBBw6dAgLFiyAx+OB3W7Hjh07cP7556e7WZSAp59+GgcOHMC+ffvS3ZS4cGQrA919992QJCnsMbrT1tzcjKVLl6K+vh633357mlpO44nnMSUioomxZs0avPfee3j66afT3RRK0DnnnIODBw/i7bffxne+8x3cfPPN+OCDD9LdLIrTyZMnsXbtWmzbtg2KoqS7OXHhyFYGuuuuu9DQ0BD2PjNnzvRfbmlpwaJFi3DZZZfhN7/5TYpbR/GI9TGl7FVRUQGTyYS2trag29va2lBdXZ2mVhHReO644w7s3LkTe/fuxdSpU9PdHEqQxWLBrFmzAADz58/Hvn378Mtf/hKPPfZYmltG8di/fz9OnTqFefPm+W/z+XzYu3cvfvWrX2FwcBAmkymNLYyMYSsDVVZWorKyMqr7Njc3Y9GiRZg/fz4ef/xxyDIHKzNRLI8pZTeLxYL58+dj9+7d/gIKqqpi9+7duOOOO9LbOCLy0zQNd955J3bs2IE9e/ZgxowZ6W4SpYCqqhgcHEx3MyhOV199NQ4dOhR02+rVq3HuuefiBz/4QcYHLYBhK6s1Nzdj4cKFmD59OjZu3Ij29nb/53gGPXudOHECnZ2dOHHiBHw+Hw4ePAgAmDVrFux2e3obR1FZv349br75Zlx88cW45JJLsGnTJvT19WH16tXpbhrFqbe3F0ePHvVf//TTT3Hw4EGUlZVh2rRpaWwZxWvNmjV48skn8cILL6C4uBitra0AAIfDgcLCwjS3juJxzz334Ctf+QqmTZuGnp4ePPnkk9izZw9eeumldDeN4lRcXDxmHaXNZkN5eXnWrK9k2Mpir7zyCo4ePYqjR4+OmfrAiv7Za8OGDdi6dav/+ty5cwEAr732GhYuXJimVlEsrrvuOrS3t2PDhg1obW3FnDlzsGvXrjFFMyh7vPPOO1i0aJH/+vr16wEAN998M5544ok0tYoS8cgjjwDAmNfVxx9/POK0b8pMp06dwk033YTPP/8cDocDF154IV566SV8+ctfTnfTKI9xny0iIiIiIqIU4AIfIiIiIiKiFGDYIiIiIiIiSgGGLSIiIiIiohRg2CIiIiIiIkoBhi0iIiIiIqIUYNgiIiIiIiJKAYYtIiIiIiKiFGDYIiIiIiIiSgGGLSIiIiIiohRg2CIiIiIiIkoBhi0iIiIiIqIUYNgiIiIiIiJKAYYtIiKicdx5552QJAlXXHEFhoeHx3z+Rz/6ESRJwrx58+DxeNLQQiIiymSSpmlauhtBRESUibxeLy6//HK88847+MEPfoAHHnjA/7ldu3bhq1/9KoqLi7F//37MmjUrjS0lIqJMxLBFREQUxqeffop58+ahu7sbL774Ir7yla/gs88+w9y5c9HR0YFnn30W9fX16W4mERFlIE4jJCIiCmPGjBl44oknoGkavvnNb+LTTz/F9ddfj46ODtxxxx0MWkRENC6ObBEREUXhrrvuwkMPPQSHw4Hu7m5cfPHFePPNN2GxWNLdNCIiylAMW0RERFEYHh7GRRddhA8++AA2mw2HDh3CjBkz0t0sIiLKYJxGSEREFIW3334bH330EQCgr68Phw4dSnOLiIgo0zFsERERRdDR0YHrr78ew8PDWL16NSRJQkNDA44fP57uphERUQZj2CIiIgpDL4zx2Wef4aabbsKWLVtw1113weVy4brrrsPQ0FC6m0hERBmKYYuIiCiM+++/H7t27cL555+PX//61/7bFixYgLfffhvf//7309xCIiLKVCyQQURENI69e/fiqquugtVqxb59+3D++ef7P3fixAnMnTsXnZ2daGxsxKpVq9LYUiIiykQc2SIiIgqhvb0dX//61+Hz+bB58+agoAUA06ZNwxNPPAFJkrB69WocO3YsPQ0lIqKMxZEtIiIiIiKiFODIFhERERERUQowbBEREREREaUAwxYREREREVEKMGwRERERERGlAMMWERERERFRCjBsERERERERpQDDFhERERERUQowbBEREREREaUAwxYREREREVEKMGwRERERERGlAMMWERERERFRCjBsERERERERpcD/B/5LxjaqJX9UAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["p, perr, R2, chi2red, yfit, ci, pi, MSE = polyfit(x, y, 2, yerr=yerr)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see the results of linear and quadratic fits:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:26:32.289627Z", "start_time": "2017-12-30T07:26:31.800910Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 2, figsize=(12, 5))\n", "p, perr, R2, chi2red, yfit, ci, pi, MSE = polyfit(x, y, degree=1, axis=ax[0])\n", "p, perr, R2, chi2red, yfit, ci, pi, MSE = polyfit(x, y, degree=2, axis=ax[1], ylabel='')\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## P.S.: Calculation of moving standard-deviation\n", "\n", "Let's calculate the moving standard-deviation just to compare with the 68% (1 SD) prediction interval for the fitted polynomial given in the plot above."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:27:06.460551Z", "start_time": "2017-12-30T07:27:06.281680Z"}}, "outputs": [], "source": ["import pandas as pd\n", "\n", "ys = pd.Series(y)\n", "# use a moving window of one-tenth of the size of the data.\n", "ys_std = ys.rolling(window=int(np.round(N/10)), min_periods=4, center=True).std()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:27:08.724429Z", "start_time": "2017-12-30T07:27:08.480267Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 5))\n", "plt.fill_between(x, yfit+ys_std, yfit-ys_std, color=[1, 0, 0, 0.1], edgecolor=None,\n", "                 label='Moving standard-deviation')\n", "plt.plot(x, y, 'o', color=[0, 0.1, .9, 1], markersize=8)\n", "plt.plot(x, yfit, linewidth=3, color=[1, 0, 0, .8], label='Polynomial (degree {}) fit'.format(2))\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function `polyfit.py`"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2017-12-30T07:27:28.430893Z", "start_time": "2017-12-30T07:27:28.425306Z"}, "editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# %load ./../functions/polyfit.py\n", "\"\"\"Least squares polynomial regression with confidence/prediction intervals.\n", "\"\"\"\n", "\n", "__author__ = '<PERSON>, https://github.com/demotu/BMC'\n", "__version__ = 'polyfit.py v.1.0.3 20123/07/07'\n", "__license__ = \"MIT\"\n", "\n", "import numpy as np\n", "import scipy.stats as stats\n", "import matplotlib.pyplot as plt\n", "\n", "def polyfit(x, y, degree, yerr=None, plot=True, xlabel='x', ylabel='y',\n", "            title=True, legend=True, plotCI=True, plotPI=True, axis=None):\n", "    \"\"\"Least squares polynomial regression of order degree for x vs. y [1]_\n", "\n", "    Parameters\n", "    ----------\n", "    x : numpy array_like, shape (N,)\n", "        Independent variable, x-coordinates of the N points (x[i], y[i]).\n", "    y : numpy array_like, shape (N,)\n", "        Dependent variable, y-coordinates of the N points (x[i], y[i]).\n", "    degree : integer\n", "        Degree of the polynomial to be fitted to the data.\n", "    yerr : numpy array_like, shape (N,), optional (default = None)\n", "        Error (uncertainty) in y. If no error is entered, unitary equal errors\n", "        for all y values are assumed.\n", "    plot : bool, optional (default = True)\n", "        Show plot (True) of not (False). \n", "    xlabel : string, optional (default = 'x')\n", "        Label for the x (horizontal) axis.\n", "    ylabel : string, optional (default = 'y')\n", "        Label for the y (vertical) axis.\n", "    title : bool, optional (default = True)\n", "        Show title (True) of not (False) in the plot.\n", "    legend : bool, optional (default = True)\n", "        Show legend (True) of not (False) in the plot.\n", "    plotCI : bool, optional (default = True)\n", "        Plot the shaded area for the confidence interval (True) of not (False).\n", "    plotPI : bool, optional (default = True)\n", "        Plot the shaded area for the prediction interval (True) of not (False).\n", "    axis : matplotlib object, optional (default = None)\n", "        Mat<PERSON>lot<PERSON>b axis object where to plot.\n", "\n", "    Returns\n", "    -------\n", "    p : numpy array, shape (deg + 1,)\n", "        Coefficients of the least squares polynomial fit.\n", "    perr : numpy array, shape (deg + 1,)\n", "        Standard-deviation of the coefficients.\n", "    R2 : float\n", "        Coefficient of determination.\n", "    chi2red : float\n", "        Reduced chi-squared\n", "    yfit : numpy array, shape (N + 1,)\n", "        Values of the fitted polynomial evaluated at x.\n", "    ci : numpy array, shape (N + 1,)\n", "        Values of the 95% confidence interval evaluated at x.\n", "    pi : numpy array, shape (N + 1,)\n", "        Values of the 68% prediction interval evaluated at x.\n", "    MSE : float\n", "        Mean squared error of the regression.\n", "\n", "    References\n", "    ----------\n", "    .. [1] https://docs.scipy.org/doc/numpy/reference/generated/numpy.polyfit.html\n", "\n", "\n", "    Examples\n", "    --------\n", "    >>> import numpy as np\n", "    >>> import matplotlib.pyplot as plt\n", "    >>> N = 50\n", "    >>> x = np.linspace(-2, 4, N)\n", "    >>> y = np.polyval([3, 1, 2], x) + np.random.randn(N)*8\n", "    >>> # simple use:\n", "    >>> polyfit(x, y, 2);\n", "    >>> # compare two models:\n", "    >>> fig, ax = plt.subplots(1, 2, figsize=(14, 5))\n", "    >>> p1, perr1, R21, chi2red1, yfit1, ci1, pi1, MSE = polyfit(x, y, 1, axis=ax[0])\n", "    >>> p2, perr2, R22, chi2red2, yfit2, ci2, pi2, MSE = polyfit(x, y, 2, axis=ax[1])\n", "    >>> plt.tight_layout()\n", "    >>> Enter error (uncertainty) in y:\n", "    >>> yerr = np.ones(N)*8\n", "    >>> polyfit(x, y, 2, yerr);\n", "\n", "\n", "    Version history\n", "    ---------------\n", "    '1.0.3':\n", "        Correct the order of the terms in title and account for zeros in yerr.\n", "    '1.0.2':\n", "        Included output 'MSE', mean squared error of the regression.\n", "\n", "    \"\"\"\n", "\n", "    x, y = np.asarray(x), np.asarray(y)\n", "    N = y.size\n", "    if yerr is None:\n", "        yerr = np.ones(N)\n", "        errorbar = False\n", "    else:\n", "       # replace zeros in yerr (because in the fit, weigths = 1/yerr)\n", "       yerr[np.where(yerr == 0)] = 10 * np.finfo(np.float32).eps\n", "       errorbar = True\n", "    # coefficients and covariance matrix of the least squares polynomial fit\n", "    p, cov = np.polyfit(x, y, degree, w=1/yerr, cov=True)\n", "    # evaluate the polynomial at x\n", "    yfit = np.polyval(p, x)\n", "    # standard-deviation of the coefficients\n", "    perr = np.sqrt(np.diag(cov))\n", "    # residuals\n", "    res = y - yfit\n", "    # reduced chi-squared, see for example page 79 of\n", "    # https://www.astro.rug.nl/software/kapteyn/_downloads/statmain.pdf\n", "    chi2red = np.sum(res**2/yerr**2)/(N - degree - 1)\n", "    # standard deviation of the error (residuals)\n", "    s_err = np.sqrt(np.sum(res**2)/(N - degree - 1))\n", "    # sum of squared residuals\n", "    SSres = np.sum(res**2)\n", "    # mean squared error\n", "    MSE = SSres/N\n", "    # sum of squared totals\n", "    SStot = np.sum((y - np.mean(y))**2)\n", "    # coefficient of determination\n", "    R2 = 1 - SSres/SStot\n", "    # adjusted coefficient of determination\n", "    R2adj = 1 - (SSres/(N - degree - 1)) / (SStot/(N - 1))\n", "    # 95% (2 SD) confidence interval for the fit\n", "    t95 = stats.t.ppf(0.95 + (1-0.95)/2, N - degree - 1)\n", "    ci = t95 * s_err * np.sqrt(    1/N + (x - np.mean(x))**2/np.sum((x-np.mean(x))**2))\n", "    # 68% (1 SD) prediction interval for the fit\n", "    t68 = stats.t.ppf(0.683 + (1-0.683)/2, N - degree - 1)\n", "    pi = t68 * s_err * np.sqrt(1 + 1/N + (x - np.mean(x))**2/np.sum((x-np.mean(x))**2))\n", "    # plot\n", "    if plot:\n", "        # generate values if number of input values is too small or too large\n", "        if N < 50 or N > 500:\n", "            x2 = np.linspace(np.min(x), np.max(x), 100)\n", "            yfit2 = np.polyval(p, x2)\n", "            ci2 = np.interp(x2, x, ci)\n", "            pi2 = np.interp(x2, x, pi)\n", "        else:\n", "            x2, yfit2, ci2, pi2 = x, yfit, ci, pi\n", "\n", "        if axis is None:\n", "            fig, axis = plt.subplots(1, 1, figsize=(10, 5))\n", "        else:\n", "            fig = 0\n", "        if plotPI:\n", "            axis.fill_between(x2, yfit2+pi2, yfit2-pi2, color=[1, 0, 0, 0.1],\n", "                              edgecolor=None, label='68% prediction interval')\n", "        if plotCI:\n", "            axis.fill_between(x2, yfit2+ci2, yfit2-ci2, color=[1, 0, 0, 0.3],\n", "                              edgecolor=None, label='95% confidence interval')\n", "        if errorbar:\n", "            axis.errorbar(x, y, yerr=yerr, fmt='o', capsize=0,\n", "                          color=[0, 0.1, .9, 1], markersize=8)\n", "        else:\n", "            axis.plot(x, y, 'o', color=[0, 0.1, .9, 1], markersize=8)\n", "        axis.plot(x2, yfit2, linewidth=3, color=[1, 0, 0, .8],\n", "                 label='Polynomial (degree {}) fit'.format(degree))\n", "        axis.set_xlabel(xlabel, fontsize=16)\n", "        axis.set_ylabel(ylabel, fontsize=16)\n", "        if legend:\n", "            axis.legend()\n", "        if title:\n", "            xs = ['x^{:d}'.format(ideg) for ideg in range(2, degree+1)] + ['x', '']\n", "            title = ['({:.2f} \\pm {:.2f}) {}'.format(i, j, k) for i, j, k in zip(p, perr, xs)]\n", "            R2str = '\\, (R^2 = ' + '{:.2f}'.format(R2) + \\\n", "                    ', \\chi^2_{red} = ' + '{:.1f}'.format(chi2red) + ')'\n", "            title = '$ y = ' + '+'.join(title) + R2str + '$'\n", "            axis.set_title(title, fontsize=12, color=[0, 0, 0])\n", "        if fig:\n", "            plt.show()\n", "\n", "    return p, perr, R2, chi2red, yfit, ci, pi, MSE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}