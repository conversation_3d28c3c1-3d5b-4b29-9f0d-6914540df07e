{"cells": [{"cell_type": "markdown", "metadata": {"internals": {"slide_type": "subslide"}, "id": "Zzz5LAscJ-Ze"}, "source": ["# Python for scientific computing\n", "\n", "> <PERSON>, <PERSON><PERSON>  \n", "> [Laboratory of Biomechanics and Motor Control](https://bmclab.pesquisa.ufabc.edu.br/)  \n", "> Federal University of ABC, Brazil\n", "\n", "<p style=\"text-align: right;\">A <a href=\"https://jupyter.org/\">Jupyter Notebook</a></p>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "uBKID32FJ-Zh", "outputId": "9fbd8361-4624-4ab0-ff6f-9c8602e88e2d", "colab": {"base_uri": "https://localhost:8080/", "height": 605}}, "outputs": [{"output_type": "execute_result", "data": {"image/png": "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***********************************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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "execution_count": 1}], "source": ["from IPython.display import Image\n", "Image(data='http://imgs.xkcd.com/comics/python.png')"]}, {"cell_type": "markdown", "metadata": {"toc": true, "id": "9L7yoMmqJ-Zj"}, "source": ["<h1>Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Computing-as-a-third-kind-of-Science\" data-toc-modified-id=\"Computing-as-a-third-kind-of-Science-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Computing as a third kind of Science</a></span></li><li><span><a href=\"#About-Python-[Python-documentation]\" data-toc-modified-id=\"About-Python-[Python-documentation]-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>About Python [<a href=\"http://www.python.org/doc/essays/blurb/\" rel=\"nofollow\" target=\"_blank\">Python documentation</a>]</a></span></li><li><span><a href=\"#About-Python-[Python-documentation]\" data-toc-modified-id=\"About-Python-[Python-documentation]-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>About Python [<a href=\"http://www.python.org/doc/essays/blurb/\" rel=\"nofollow\" target=\"_blank\">Python documentation</a>]</a></span></li><li><span><a href=\"#Glossary-for-the-Python-technical-characteristics-I\" data-toc-modified-id=\"Glossary-for-the-Python-technical-characteristics-I-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>Glossary for the Python technical characteristics I</a></span></li><li><span><a href=\"#Glossary-for-the-Python-technical-characteristics-II\" data-toc-modified-id=\"Glossary-for-the-Python-technical-characteristics-II-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>Glossary for the Python technical characteristics II</a></span></li><li><span><a href=\"#About-Python\" data-toc-modified-id=\"About-Python-6\"><span class=\"toc-item-num\">6&nbsp;&nbsp;</span>About Python</a></span></li><li><span><a href=\"#Python\" data-toc-modified-id=\"Python-7\"><span class=\"toc-item-num\">7&nbsp;&nbsp;</span>Python</a></span></li><li><span><a href=\"#Why-Python-and-not-'X'-(put-any-other-language-here)\" data-toc-modified-id=\"Why-Python-and-not-'X'-(put-any-other-language-here)-8\"><span class=\"toc-item-num\">8&nbsp;&nbsp;</span>Why Python and not 'X' (put any other language here)</a></span></li><li><span><a href=\"#Popularity-of-Python-for-teaching\" data-toc-modified-id=\"Popularity-of-Python-for-teaching-9\"><span class=\"toc-item-num\">9&nbsp;&nbsp;</span>Popularity of Python for teaching</a></span></li><li><span><a href=\"#Python-ecosystem-for-scientific-computing-(main-libraries)\" data-toc-modified-id=\"Python-ecosystem-for-scientific-computing-(main-libraries)-10\"><span class=\"toc-item-num\">10&nbsp;&nbsp;</span>Python ecosystem for scientific computing (main libraries)</a></span></li><li><span><a href=\"#The-Jupyter-Notebook\" data-toc-modified-id=\"The-Jupyter-Notebook-11\"><span class=\"toc-item-num\">11&nbsp;&nbsp;</span>The Jupyter Notebook</a></span></li><li><span><a href=\"#Jupyter-Notebook-and-IPython-kernel-architectures\" data-toc-modified-id=\"Jupyter-Notebook-and-IPython-kernel-architectures-12\"><span class=\"toc-item-num\">12&nbsp;&nbsp;</span>Jupyter Notebook and IPython kernel architectures</a></span></li><li><span><a href=\"#Installing-the-Python-ecosystem\" data-toc-modified-id=\"Installing-the-Python-ecosystem-13\"><span class=\"toc-item-num\">13&nbsp;&nbsp;</span>Installing the Python ecosystem</a></span><ul class=\"toc-item\"><li><span><a href=\"#Anaconda\" data-toc-modified-id=\"Anaconda-13.1\"><span class=\"toc-item-num\">13.1&nbsp;&nbsp;</span>Anaconda</a></span></li><li><span><a href=\"#Miniconda\" data-toc-modified-id=\"Miniconda-13.2\"><span class=\"toc-item-num\">13.2&nbsp;&nbsp;</span>Miniconda</a></span></li></ul></li><li><span><a href=\"#IDE-for-Python\" data-toc-modified-id=\"IDE-for-Python-14\"><span class=\"toc-item-num\">14&nbsp;&nbsp;</span>IDE for Python</a></span></li><li><span><a href=\"#To-learn-about-Python\" data-toc-modified-id=\"To-learn-about-Python-15\"><span class=\"toc-item-num\">15&nbsp;&nbsp;</span>To learn about Python</a></span></li><li><span><a href=\"#More-examples-of-Jupyter-Notebooks\" data-toc-modified-id=\"More-examples-of-Jupyter-Notebooks-16\"><span class=\"toc-item-num\">16&nbsp;&nbsp;</span>More examples of Jupyter Notebooks</a></span></li><li><span><a href=\"#Questions?\" data-toc-modified-id=\"Questions?-17\"><span class=\"toc-item-num\">17&nbsp;&nbsp;</span>Questions?</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 2, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "l9kf05UyJ-Zk"}, "source": ["The [Python programming language](https://www.python.org/) with [its ecosystem for scientific programming](https://scipy.org/) has features, maturity, and a community of developers and users that makes it the ideal environment for the scientific community.   \n", "\n", "This talk will show some of these features and usage examples.  "]}, {"cell_type": "markdown", "metadata": {"id": "TF2XU9VlJ-Zk"}, "source": ["## Computing as a third kind of Science\n", "\n", "Traditionally, science has been divided into experimental and theoretical disciplines, but nowadays computing plays an important role in science. Scientific computation is sometimes related to theory, and at other times to experimental work. Hence, it is often seen as a new third branch of science.\n", "\n", "<figure><img src=\"https://raw.githubusercontent.com/j<PERSON><PERSON><PERSON><PERSON>/scientific-python-lectures/master/images/theory-experiment-computation.png\" width=300 alt=\"theory-experiment-computation\"/></figure>  \n", "\n", "Figure from [<PERSON><PERSON><PERSON><PERSON>](http://nbviewer.jupyter.org/github/jr<PERSON><PERSON><PERSON>/scientific-python-lectures/blob/master/Lecture-0-Scientific-Computing-with-Python.ipynb)."]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 6}, "id": "Xv79GJcJJ-Zl"}, "source": ["## About Python [[Python documentation](http://www.python.org/doc/essays/blurb/)]\n", "\n", "*Python is a programming language that lets you work more quickly and integrate your systems more effectively. You can learn to use Python and see almost immediate gains in productivity and lower maintenance costs* [[python.org](http://python.org/)]."]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 7, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "DZFBn7bvJ-Zl"}, "source": ["- *Python is an interpreted, object-oriented, high-level programming language with dynamic semantics. Its high-level built in data structures, combined with dynamic typing and dynamic binding, make it very attractive for Rapid Application Development, as well as for use as a scripting or glue language to connect existing components together*.  \n", "- *Python's simple, easy to learn syntax emphasizes readability and therefore reduces the cost of program maintenance. Python supports modules and packages, which encourages program modularity and code reuse*.  \n", "- Python is free and open source."]}, {"cell_type": "markdown", "metadata": {"id": "hI6YFSiEJ-Zm"}, "source": ["## About Python [[Python documentation](http://www.python.org/doc/essays/blurb/)]\n", "\n", "- *Often, programmers fall in love with Python because of the increased productivity it provides. Since there is no compilation step, the edit-test-debug cycle is incredibly fast. Debugging Python programs is easy: a bug or bad input will never cause a segmentation fault. Instead, when the interpreter discovers an error, it raises an exception. When the program doesn't catch the exception, the interpreter prints a stack trace.*  \n", "- A source level debugger allows inspection of local and global variables, evaluation of arbitrary expressions, setting breakpoints, stepping through the code a line at a time, and so on. The debugger is written in Python itself, testifying to Python's introspective power. On the other hand, often the quickest way to debug a program is to add a few print statements to the source: the fast edit-test-debug cycle makes this simple approach very effective.*"]}, {"cell_type": "markdown", "metadata": {"id": "ZiN4RclIJ-Zm"}, "source": ["## Glossary for the Python technical characteristics I\n", "\n", " - Programming language: a formal language designed to communicate instructions to a computer. A sequence of instructions that specifies how to perform a computation is called a program.\n", " - Interpreted language: a program in an interpreted language is executed or interpreted by an interpreter program. This interpreter executes the program source code, statement by statement.\n", " - Compiled language: a program in a compiled language is first explicitly translated by the user into a lower-level machine language executable (with a compiler) and then this program can be executed.\n", " - Python interpreter: an interpreter is the computer program that executes the program. The most-widely used implementation of the Python programming language, referred as CPython or simply Python, is written in C (another programming language, which is lower-level and compiled).\n", " - High-level: a high-level programming language has a strong abstraction from the details of the computer and the language is independent of a particular type of computer. A high-level programming language is closer to human languages than to the programming language running inside the computer that communicate instructions to its hardware, the machine language. The machine language is a low-level programming language, in fact, the lowest one.\n", " - Object-oriented programming: a programming paradigm that represents concepts as \"objects\" that have data fields (attributes that describe the object) and associated procedures known as methods.\n", " - Semantics and syntax: the term semantics refers to the meaning of a language, as opposed to its form, the syntax.\n", " - Static and dynamic semantics: static and dynamic refer to the point in time at which some programming element is resolved. Static indicates that resolution takes place at the time a program is written. Dynamic indicates that resolution takes place at the time a program is executed.\n", " - Static and dynamic typing and binding: in dynamic typing, the type of the variable (e.g., if it is an integer or a string or a different type of element) is not explicitly declared, it can change, and in general is not known until execution time. In static typing, the type of the variable must be declared and it is known before the execution time.  \n", " - Rapid Application Development: a software development methodology that uses minimal planning in favor of rapid prototyping.  \n", " - Scripting: the writing of scripts, small pieces of simple instructions (programs) that can be rapidly executed.  "]}, {"cell_type": "markdown", "metadata": {"id": "qt-xYZ89J-Zn"}, "source": ["## Glossary for the Python technical characteristics II\n", "\n", " - Glue language: a programming language for writing programs to connect software components (including programs written in other programming languages).\n", " - Modules and packages: a module is a file containing Python definitions (e.g., functions) and statements. Packages are a way of structuring Python’s module namespace by using “dotted module names”. For example, the module name A.B designates a submodule named <PERSON> in a package named A. To be used, modules and packages have to be imported in Python with the import function. Namespace is a container for a set of identifiers (names), and allows the disambiguation of homonym identifiers residing in different namespaces. For example, with the command `import math`, we will have all the functions and statements defined in this module in the namespace '`math.`', for example, `math.pi` is the $\\pi$ constant and `math.cos()`, the cosine function.\n", " - Program modularity and code reuse: the degree that programs can be compartmentalized (divided in smaller programs) to facilitate program reuse.\n", " - Source or binary form: source refers to the original code of the program (typically in a text format) which would need to be compiled to a binary form (not anymore human readable) to be able to be executed.\n", " - Major platforms: typically refers to the main operating systems (OS) in the market: Windows (by Microsoft), Mac OSX (by Apple), and Linux distributions (such as Debian, Ubuntu, Mint, etc.). Mac OSX and Linux distros are derived from, or heavily inspired by, another operating system called Unix.\n", " - Edit-test-debug cycle: the typical cycle in the life of a programmer; write (edit) the code, run (test) it, and correct errors or improve it (debug). The read–eval–print loop (REPL) is another related term.\n", " - Segmentation fault: an error in a program that is generated by the hardware which notifies the operating system about a memory access violation.\n", " - Exception: an error in a program detected during execution is called an exception and the Python interpreter raises a message about this error (an exception is not necessarily fatal, i.e., does not necessarily terminate or break the program).\n", " - Stack trace: information related to what caused the exception describing the line of the program where it occurred with a possible history of related events.\n", " - Source level debugger: Python has a module (named pdb) for interactive source code debugging.\n", " - Local and global variables: refers to the scope of the variables. A local variable is defined inside a function and typically can be accessed (it exists) only inside that function unless declared as global."]}, {"cell_type": "markdown", "metadata": {"id": "VdQcr3_VJ-Zn"}, "source": ["## About Python\n", "\n", "Python is also the name of the software with the most-widely used implementation of the language (maintained by the [Python Software Foundation](http://www.python.org/psf/)).  \n", "This implementation is written mostly in the *C* programming language and it is nicknamed CPython.  \n", "So, the following phrase is correct: download Python *(the software)* to program in Python *(the language)* because Python *(both)* is great!   "]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 9}, "id": "JBCKNUuKJ-Zn"}, "source": ["## Python\n", "\n", "The origin of the name for the Python language in fact is not because of the big snake, the author of the Python language, <PERSON>, named the language after Monty Python, a famous British comedy group in the 70's.  \n", "By coincidence, the Monty Python group was also interested in human movement science:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2020-09-21T21:48:18.870047Z", "start_time": "2020-09-21T21:48:18.719024Z"}, "internals": {"frag_helper": "fragment_end", "frag_number": 10, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "n5KWnc-0J-Zo", "outputId": "8c984f07-18ff-4622-b9b4-01d84688106e", "colab": {"base_uri": "https://localhost:8080/", "height": 382}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<IPython.lib.display.YouTubeVideo at 0x7f8324571e70>"], "text/html": ["\n", "        <iframe\n", "            width=\"480\"\n", "            height=\"360\"\n", "            src=\"https://www.youtube.com/embed/eCLp7zodUiI?rel=0\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "image/jpeg": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEABALDA4MChAODQ4SERATGCgaGBYWGDEjJR0oOjM9PDkzODdASFxOQERXRTc4UG1RV19iZ2hnPk1xeXBkeFxlZ2MBERISGBUYLxoaL2NCOEJjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY//AABEIAWgB4AMBIgACEQEDEQH/xAAbAAEAAwEBAQEAAAAAAAAAAAAAAQIDBAUGB//EADYQAAIBAwIFAgUDAwQCAwAAAAABAgMEERIhBRMxQVFhkRQVIjJSQlOhBnGBI2Lh8LHxM0TR/8QAFwEBAQEBAAAAAAAAAAAAAAAAAAECA//EABoRAQEBAQEBAQAAAAAAAAAAAAARARICITH/2gAMAwEAAhEDEQA/APz8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGnIqeCfh6v4nW01PyXMdK4fhqngn4aq/0/wAnbuSslqOL4St+P8j4Ot+P8noxLIVY8z4Ot+P8j4Ov+P8AJ6hIpHl/B1/x/kfBV/x/k9TcZFI8v4Kv+P8AI+Cr/j/J6mQKseWrGu/0r3J+AuPw/k9aJff/AAKR4rsa66w/kj4St+P8nsVc49DFN5FI834Ot+P8k/B1/wAf5PTWSd/+RSPL+Cr/AI/yPgbj8P5PWXoWiKR5Py+4/Be4+X3P4fye2vT3CX/sl05eJ8uufwXuHw+5X6F7nt/2Ifp7inLxfl9z+C9x8uufwXue1HP/ACS/QUjxPl1z+C9x8uufwXue5nYlFpHh/LbrP2fyPlt1+C9z3dySXTl4Py26/Be4+WXX4L3Pf3G46Xl4Hyy6/Be4+WXX7a9z3xkdHLwPll1+3/JPyy6/Be57++RljrTl4Hyy6/bXuPll1+2vc9/LJyxdOXz/AMru/wBte4+V3X7a9z6Dcblpy+f+V3X7a9x8ruv217n0O/cCnL575Xd/tr3Hyu7/AG17n0RBLpy+e+V3f7a9x8ru/wBte59ENxTl878ru/217j5Xd/tr3PotwWpHz3yu7/bXuR8ru/217n0Q3FWPnfld3+2vcfK7r9te59DuQ8ipHz3y26/Be4+W3X4fye/3IeRSPB+W3P4L3Hy26/b/AJPdRKzkUjwvll1+C9x8suv217n0A3FI+e+WXX4L3Hy26/Be59AQ+gpHj1PvJwKq+skymiJQRKKLxLFUXQVAJBAAAAkAKmPU0RSJdAVqLbyYd/J0VOhz9wLEkFgJRaPUqvYvH+ANESF0AUIkSQwECSsSwAsvchEoCSRjbrkgASCSAMAFAf8AcAdgH/cE4IJAE/8AckEgACQI/wC4J/7gIAAAAAJAgkACCGWKsooRIsRIiIiSupCLIosRgsAKsh9CwA8at9+wMlmUsvc0IyksiuSUBpEsVRbJVSCAQSCCQAACrRNEZx6miIIn0OfudM/tOZ/cUXRJVEgWReJRGkUBdEhFJ1FHqBcrKSS6o5ql32iYpufWXUK6uek8Lc0jJyOenRj1yzpp6V3CNIQkzqo0MtLDKUWtS2bR6dGVOCzjL8FVe34fTlH602Vu7CzjH6ajhIs+ITWVGGDnnb1rqeVJ5ZWbHDUoqD2mpGfc9P5HdNbZZy1uE3NBvKZIua5gawtqkltF7FZQcHhoiqAnAwBCBOAAACIJABQJIAEgAgAAoEkAIEPoSQwKkSBDAIsiqLIC4BBQD6APoB4qROBlDKIyYLxiVTRdSj5CrKJbBCmvJOuPkqpwRga4jXHyRDBOBrj5GuPkKYGBrXkcxASkaRMuZElVF5A1ktjml9xo6qwZNpsCyJIXQkCyNI9DNCVRQiBerVUI9dzhq1dW7ZnUqubbKQi5ySKiYJylsddGjUbwondw/hzkk2j37Xh0Y4yiNZjxaFjKS3izvocLXeJ7cLenBbF9K7Fa5cFKxhFdMGqtY5OhvBnzMvcjUVVmm9md9rawj1OSMsbnTRuFHqi5rO+XqU4xivpwVnThN/Uk2cvxcF0M3d4e/Q1WOdXurSOlujFKR8vfy01XCrBJ+T6ZXSfR7Hn8YsY3dFzp41pZCzXzcljp0IMHOdCo4VEaxmnjcyJGCQQQCwIIBIAgkAAASBAJAAYAyVEYIaLEMDMhlykgES0Sq6FohFyCQFQGSRgD57LGZGvLY5bGsMk5EpvyactkqmUZ6pE6pGnLJVIistUicyNeUOUBjmROZGqpE8sDHMvJOZeTbljlhWOZDMjblk8sDFZNI5LqBbSAXQsQkSBKOW6qNbHU3iLbPNry1TGDPqz1+D2Mq01JrY5OHWTuayz9p9faUYUKahBIu6vnHRbUIQikkdkI4RhT6G0WZdcxqoZDpeoU8F9RVYuks7sh0omjfoUy/BBTSkRgs2yHkDOTaMZ1GayiclVPOEEdFOq8G6r4W558ItPc21bbmsZ15HG6UeYprueTCq4vS/8AB7fFkp0PVHzM5tSx4DL0o1tieecdOSlH17nRGOSDTnj4gpyxywLfED4gryxyvQIt8QPiSOURyvQKt8QyPiGRyieV6BB3LI+JZPK9ByvQCPiWPiGTyvQcr0Ar8SyPiJFuUTyvQIpz5Fo1ck8r0HKA0i8mkTKMGaxjgC4ACgACPKwMEgaIwMAkIDAJCgAAAEgwwAAAJAAEgKgnuMBAZXM9MDzm22dV690jkLia+g4NJJHu05Hy/Cqqg8Hv0aikia6eXpQllG0WcdKWTqiYrbVM1T2MkW14NCZN9iup9y6eVuZVJRXRkF+rLYWDHWkkRUrrBUJvCZx1JJMvVrwUXmRw1r6hB9cgrrjUb2waNrH1I8WXGYxl9Ecl6PGVUeJQLjOtuIU3peOjPlbmLjUZ9rGdK5pNHy3FaDo1pJ9Cs64KdV05I9WhJSUWujPHZ6HDtUoY8DWcejhDSSgRpGkaSQQRpJ0okkCukaSxBRGknSCQiNKIwiwArhDSWBBXSTgkFEEgAQASECCSGB5uCCSAAwCQgBgkKjAJBBBIBRIAChKIJAEgAACQPPvU9aOXPqeldU9UMrqedp3wXGddFpUcZo9+xbkup83TeJo+m4Ys0kya35elS2ZrO9p0erOWpPTF7nlV55k22R0ezPjVKPYwqf1BTXRHkwtncPZ7Gz4PiOp5/wAiM12Lj+t+EaU+IqrJbni1LKMOjIp0K0ZJxzgLX08ZznuuhyX1xUpo6+ES1UfrRnxejqi8LYivnK95UnsmzOnRqV33Oydjtqh1M1KvQ6IuMrx4YopObZWVGNN7ESv67WJRFKVatLpsUdNpXqUp4XRnTxC1V3b8xL6l1IpW2ycjvoR0rD6DDcfHfD/6ul56nuW0raFFU1Q+r8slL6jGFeWF16C3+nr3Gp5xpOOmXoVOiolKlnwc4XchgYAIyAAAAAgASUAAAABEAAUAAAAAAgkgDzQABBJAAsAAAAIAAKqQAQSCCQJAAEgAoNZ2OS5takfqVOWH6Ho0Iycm4rdLua0Lm7nLS6UZx8YDWea+eimpYxufTcJT5KyeffUoRqRkoODz0PS4Y/pCZk1116E6ixE4ZcNrPOx7lLodUFnsRuPmYutZL/4snPWvrqu9OlxR9fK2p1F9USny+it9BSPmKFpUqYckz1Kdm5QUcYR60bSC7bF5wjGOyJpmOOlFUsRii17S5lJ7diHL6jocXKkSNPn4LTLSzpjawqdhdUHCeo0tZdiozlw2lJ/aI2UafRHop7dClR7AjmUIp4ZMpxWyZlVmck5vOQMeJS+vJz21RSe5XiE21k24HYyvXKTqKnFd2i/rP46sf6bOfsdVZQi5QpyctOzb7nME3UAAiAAAAAACQEQSQSWgAAgAQBIBAEkAAAAwPNYJIYEEgDAJAAAAAASAABFCSABJJCJAAAquqxko1sPoz1qEVTlskeDF6Wn4Pas6kasItvdrAb864+MTUpL6Vt3MLCvpnhvY77myVbKTPIuIytamloL6fRUq2O+x2U6+x4VjXdWkj0ab2IZr1FXLxqZ7nnwlubRZFdnMRhXqbdSreF1M3TdXqVHJc3lO3jlyRyx49CawpG95wyFZ7sx+W29OH2pY9AOW84m6kcRyxY3NTVuWlawT+hEQgoSKy9inW1R6lasmc1Kaii85qXQjTKq8o5ZdTpqdDmmByV0pbNG9CpOnTUIvEU+iMqq3Cy1sEdVeopqMlhPvgxISwgVhIIBBIIAEgAIAAoEkAgAAAACoAAggBgoBgMI88ABUAEgASCCASQUSCCQoACCQAARIQKBJAQEm9tcOjLH6X1MCe4WvcoVXLDUk4+DPiNr8VT+nCaPPo1JQ+1m076rGDylgN9VNnR5ENOcs7abPFt+I6rhwmsHrQnkmq7YM3gctOWx0QeSDbbuOZpWxSUsI4ri5cPti2UrrlUz1Mak4OLi5Lc8qtcXlZ4p02jn+Dv5Sy3gqPY/0aNNuUkcE69KUm0zklZXsnictguFV31ngDsjVj5N6cs9DjocOcX9c2ztjSVNbBSb2OST3Omo9jjlLciMqrLU86cmUsyqJHR0WAzqAAEAAECSABIIJAAAAAAAACGAAUBkgkCoAAkMgBHCQAFQSQAiSSARUggFEgAAASFAgCCQAUCUQEBZBkIskBeAqfYyYolrYDwqjcazfR5Pa4beqpBRk/qRx3dtmLlFbmNpqpNScWtyma+qpzyjojUSPKt7jSlnudkKiluZdHbzFJF6dOOctI56ckbKT7CDZ6UtkZVJ46E5bQ0+Sjnbm2NDxub7eCGwOVxlkT+0vN4e6MatVYCues8I4pSW5rc10kcE6uSI6KDTqNm7e5hZQc5YS3Z1VLerS++Eo+vYrDMbAhyS6tIC2wMpXFKC3miIXNKbxGWREbkEJp9CQJBBJAAIKiQEABBIIIABQAARDBJAAAMDgAICpBBIQAAUAAEggkAAABJUkCcklSQJCICCrFosqgBvF7EspAuBGCHGPhFh2YCC1QfoQridF4fQm1nCU3HO5erTz22DS9O/Wdzto30H3PCqUdL2M1qT6gr6lXUcbNE/GR7tHzKq1F+ph1qj/AFMQr6R3cPJnK+pruj5t1Z4+5mbqS/Jhenu1uIR8o4K19nODznN+SFuEradVzeWxGOSsYeh0UoYIOm02qRwe9aVm5qhU+unPZpniWlNzqxjFNyb2R9VZWVGzkq91Na0to+DWXU9bmPkf6goT4ffuhTqPQ1qj6HiTqVH+tv8AyfZ/1T8Pd0FXjHFWG2V3R8bKDlLEU8m3Osct9W2ddrSlH63si1G1VP6qnU2lU7JbDMZrSlcaZb9DshNSWUebr9DWlW0yRNytZr0ECkJqSyi5jWgAEAAFAkgEAAFQIJBBAAAB9CSGUcBAIYVIIJQRJGQABJAAkEEhQAYAAAgkAZRQJRXUhqiBckpzI+SVUj5CtoMuZxkkitS4UFkQbNpLc47i8WHGJzV7mc3hPCMMs1Ga9Xgdu7viUablhYyz6S64bWtn9mun+SPmOAVXS4nTfnY/SraanBKS2Ncp1uPjqltGfb2PPubRwf0rJ9zdcGoV2505cuX8M8W84XXt01KGqPlGd8umbmvlZRcepnKWx69eylhtYPPqW0k2Q1yORRyOj4SbCtnF/UD6wjByN4U8I1hT7I9Cy4XXucYSjHywOCFN5PQtrGtWWVHTH8me3a8KoW2HJcyflnTJJehrPCb7cNpbxs/qp7z/ACZarKUt5SyLu5pW8cze/g8G84lKrnG0fCNyMbtehd31CFGVGMVUclu+yPFzFbJIwlVyyVLKIytPcppyy632JxgIrGnk0VLwEXTKEcwOmE1JHJOReEsIzuNZrryMmUankupJmI1VsggEVIIAEjJACJIyAABAAsQ+gI7AeeDnd1FFXdopXUDid2Vd0xErvGUee7plXcSEWvScl5I5kfJ5rrS8leZJ9xEr0nVj5IdeKPN1sOTYhXoO6iVd2jgLRpzn9sWywrrd4iru2UhZ1ZdVg3jYL9U3/gcp0xd1Ih3MjsjZ0V2yXVGnDpFL/BeTpwKdaf2pmsKNxPq9J2j/ACXkrGFs/wBc2zWNOMSk7iEHjO5lO5TWxZhW1WqoROOrVc/7FJzcnuQTQJIAHVw6fLvacj9J4bXVWlF98H5rw9armKP0LhcWqcMFwe3DPdF2tUcSSaZnGSSy+x4/Ef6gVGpyrSHNqd5dkXUdd7w21lCVRLlteOjPna9qk3mO3k65Vrm4gqlxKWV9sWcdb4upb1J1IRjp6YfUzuY31uOGdNfpZnC15k0qk3GPnBSE2upf4gkOte5Y8KtVHVTfMflnowpqmsJYPlKVW4lNSt41Na7wPZtOMVlFU7+hKL6czH/k3kZ2u6cjx+KcUp2qcItOo+3gpxrjtOknStZKUn1kuiPk6lWVSo5Sbbfku6OuvdzrScpybbOZyb6lHIJmEWyXiyiLFStM43RaM2zFSfRlZ1eWwOrWSpnH8SiPiIlHXrWvc1jVTW5xQkqr+k6IU3giOlTiWTXZmChgulgo3U/JbWjBEmd8tZ6ba4jXHyY4TKSpZ6PBnlenTqXknUvJ584VY9HkydSrHrkk1a9XK8jKPJ+In5LK5mQr1AeYrmXkn4qXkD0gzz/i5Fvim0B4uQAbQIAAAAokst9sERi5PCWTop28lhsiaU7VyWZSwjVWtLvkum1sWTZqJSFClDpE1TS6Iz1Maio11IasmWRqA01ByRm5FXIK21kazByZXXkC9WNN7uO5zyx2RacslDKgIJAEkG1rRdavCC7sDut7flQpVu+dz7XhteKt9TeElnJ5cLGnylTlukjpo2TVNU3N8vwaVvdXVfiP+lbt06H6pd5f2NKNpClFJLp3NacI04qMVhJFs7BKh04uO5yXmI28oLq0dFWooRPNrXUJyxkGPNXDqmUlGUs+DtteCVG1KdPC9WddncdsdDquOJRt6WuclFIZitKVkranmTUIrqfMcd4xTrOVG2jiK2dTyc3F+PV7yThGTjS8Lv8A3PEnJtjTCUssqiC8UZNSkCUi2xWRPYnBXBdLYIgpVWqJeTS3MuYm8JZAx5cvxZeFvOW2nB3U6X05eTZJIRawt7d01udKRGpIznWUSo1GUjjnd+DCVzJ9wPTc4ruQ6sfJ5XOm/wBTLKUvIo9LnLyOYcMHLyapsqOnWQ2pdTFMtlAqKlFS3j1OWeqm8NHapehWajJfUjG+Vz04OYOYa1LdZ+lowlT09TPLVX5iHMM8IaV5JCsgAaVAJIAEjBpGk2sgbUcRRtq9TlSwXyysa21DXkxyXTKNEyyM0yxRYgZAAqyxDCqMz1YZpLoYdyCzeSACKkAZAg9bgFNSutTXQ8lntcAaWt9+wwx9LR+ueGd8I4RzWdNxhqfVnUbEtmcpqKbYlI868uVGL3A5+KXrUWkzzraUnu8tvsY3NbnVcJ5NatzTsaKW0qrXTwZ1XoTuaVhbuVSWaj6R8nz19f1bueqcnpXSPZHPXrzrTc5yy2YtilHLJGSAREo1S2M4rJoiiySZZJFM469Cjq74ishGzcY9zKdZt4hv/YtC3nN5nsjpp04UlstwOaFtUqb1G0jqp0KdJbRTfkSqepm6hUbueO5lKrgxlUMZ1BRpUrvsznnUb7lZSKZJVi2phbkJF0iC0ImijkiKwizmkVKusJE6zLU5Mlt9IoI1z3k8FZXEY/as+pnyZy+6SRbk04/dPJRSVzN98GbrSfc2xQRVqkRWXMfklVM9RKnF/ayji0T6vxfTkjSyYPsy+xnRzA9OvwHiFD77d49DinaV4P6qUl/g3zrTAk05NTpoZdW0lHMtkSDKK7l1N9CzSUcIoWM1ZSGSoIi6ZZSSMwUa6yymYEphXQmWTMIyLqQRoCqYyVVZ9DDuazlsZdyCwCBFSCCQIPf/AKao8ybfZM8E+t/pSmuRJlwfQQjiJMti/RGNWelM0jmu6ypwbPm7y4lNvc7+I1221nY8G5r9VHqTVVlW5UsreRzVKkpycpPLZDeXkqyFGQyBkiICYZXPgDaOyyHUx03Yp0tf3ywdEIUqfTcowhQqVN3sjqp04Ulst/JWVVLoZSrNhHRKp4M3UMHNldRaNZTKOZRsrkgs5GcmS2UZFQwgSgqyReOxVBsIs5BepRbss5JFqNE/8Ih1sdDJybGlshEyqyfcplmipS8DlvygrLLGWaaP9yI5fqgXFNTJ1sty/wCxSUcML8Wi9zTJijXsZ0e/F11/9ur7l5OpozK9n/ZnBKtJPc57i8bWlG/waVbmUp6XU1Y7mNZ6qZya3nJfmZjhiprNsAYJoMIglASBgnARUsiAFXRZMoiwF8jJUFCTKFmVaICJIRbGwVAJwAI7n2H9NS0WyR8hjdH1fA3pppegwfRatupwXtXCwmdCqLTh9j5vjHEczdOm/wC7NI5OIXeZOMWeW3kmcm3lmbeCKkq/7lZVPBRtsiLOSRGp9iuCyiFMNl4rASLJACMhgIZIyTgAQMkkYCDIySVCobKssypFQi6KosgasUZZ9Cq6lRaJZRS6j+w0+QiylFdEQ5Sl0yRlLosj/UfRANE3/wAscv8A3R9xy5vx7h0muskFQ6f++JGiXlMODXdFcNEioaaGWTlkASsZNDJI0ILTvak+sYexi6jfXBQGlidTJ1MqAsW1sa36FQEi2tjWyoBFuY/QnmP0KAEX5j9COY/QqARfmy9CedLwjMAjTnS8Ic6XhGYCxpzpeEOdLwjMAi/Nl4RPOl4RmANOdLwhzpeEZgEac6XhHdb8bubdLRCl/lP/APTzQB7NT+pr6pBw0UY57qLz/wCTzZXdScm5YbZgAkac6XoVc2yoBE6idT8IqAq2t+ETzH4RQAac2XhDnS8IzAI050vCHNl4RmAkac2XhDmy8IzAI05svCHNl4RmARfmy8Icx+hQAi2t+hGpkALE6mTrfoVAFtb9ApteCoA0VVrsiefL8YmQCRrz5eF7Ec6fkzAJi/NkObL0KAEW1sjUyAFidTGWQALKbQ5kioAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//9k=\n"}, "metadata": {}, "execution_count": 2}], "source": ["from IPython.display import YouTubeVideo\n", "YouTubeVideo('eCLp7zodUiI', width=480, height=360, rel=0)"]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 14, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "GuwiKOIFJ-Zo"}, "source": ["## Why Python and not 'X' (put any other language here)\n", "\n", "Python is not the best programming language for all needs and for all people. There is no such language.   \n", "Now, if you are doing scientific computing, chances are that Python is perfect for you because (and might also be perfect for lots of other needs):\n", "\n", "- Python is free, open source, and cross-platform.  \n", "- Python is easy to learn, with readable code, well documented, and with a huge and friendly user community.\n", "- Python is a real programming language, able to handle a variety of problems, easy to scale from small to huge problems, and easy to integrate with other systems (including other programming languages).\n", "- Python code is not the fastest but Python is one the fastest languages for programming. It is not uncommon in science to care more about the time we spend programming than the time the program took to run. But if code speed is important, one can easily integrate in different ways a code written in other languages (such as C and Fortran) with Python.\n", "- The Jupyter Notebook is a versatile tool for programming, data visualization, plotting, simulation, numeric and symbolic mathematics, and writing for daily use."]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 14, "slide_type": "subslide"}, "id": "1qfK8wj0J-Zo"}, "source": ["## Popularity of Python for teaching"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2020-09-21T21:48:21.387118Z", "start_time": "2020-09-21T21:48:21.384472Z"}, "code_folding": [], "internals": {"frag_helper": "fragment_end", "frag_number": 16, "slide_helper": "subslide_end"}, "run_control": {"breakpoint": false}, "scrolled": true, "slide_helper": "slide_end", "id": "ZGa_Fz5EJ-Zo", "outputId": "14973e75-0a44-4fad-a955-6865cfb76bca", "colab": {"base_uri": "https://localhost:8080/", "height": 622}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<IPython.lib.display.IFrame at 0x7f8324571060>"], "text/html": ["\n", "        <iframe\n", "            width=\"800\"\n", "            height=\"600\"\n", "            src=\"https://cacm.acm.org/blogs/blog-cacm/176450-python-is-now-the-most-popular-introductory-teaching-language-at-top-us-universities/fulltext\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "]}, "metadata": {}, "execution_count": 3}], "source": ["from IPython.display import IFrame\n", "IFrame('https://cacm.acm.org/blogs/blog-cacm/176450-python-is-now-the-most-popular-introductory-teaching-language-at-top-us-universities/fulltext',\n", "       width=800, height=600)"]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 12, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "2rzVZp5gJ-Zp"}, "source": ["## Python ecosystem for scientific computing (main libraries)\n", "\n", "- [Python](https://www.python.org/) of course (the CPython distribution): a free, open source and cross-platform programming language that lets you work more quickly and integrate your systems more effectively.\n", "- [Numpy](https://numpy.org/): fundamental package for scientific computing with a N-dimensional array package.\n", "- [<PERSON><PERSON><PERSON>](https://scipy.org/): numerical routines for scientific computing.\n", "- [Matplotlib](https://matplotlib.org/): comprehensive 2D Plotting.\n", "- [Sympy](https://www.sympy.org/en/index.html): symbolic mathematics.\n", "- [Pandas](https://pandas.pydata.org/): data structures and data analysis tools.\n", "- [IPython](http://ipython.org): provides a rich architecture for interactive computing with powerful interactive shell, kernel for Jupyter, support for interactive data visualization and use of GUI toolkits, flexible embeddable interpreters, and high performance tools for parallel computing.  \n", "- [Jupyter Notebook](https://jupyter.org/): web application that allows you to create and share documents that contain live code, equations, visualizations and explanatory text.\n", "- [Statsmodels](https://www.statsmodels.org/stable/index.html#): to explore data, estimate statistical models, and perform statistical tests.\n", "- [Scikit-learn](https://scikit-learn.org/stable/): tools for data mining and data analysis (including machine learning).\n", "- [Pillow](https://python-pillow.org/): Python Imaging Library.\n", "- [Spyder](https://github.com/spyder-ide/spyder): interactive development environment with advanced editing, interactive testing, debugging and introspection features."]}, {"cell_type": "markdown", "metadata": {"id": "Js2Q1ZCRJ-Zp"}, "source": ["## The Jupyter Notebook\n", "\n", "The Jupyter Notebook App is a server-client application that allows editing and running notebook documents via a web browser. The Jupyter Notebook App can be executed on a local desktop requiring no Internet access (as described in this document) or installed on a remote server and accessed through the Internet.  \n", "\n", "Notebook documents (or “notebooks”, all lower case) are documents produced by the Jupyter Notebook App which contain both computer code (e.g. python) and rich text elements (paragraph, equations, figures, links, etc...). Notebook documents are both human-readable documents containing the analysis description and the results (figures, tables, etc..) as well as executable documents which can be run to perform data analysis.\n", "\n", "[Try Jupyter Notebook in your browser](https://try.jupyter.org/)."]}, {"cell_type": "markdown", "metadata": {"id": "t4K7JkHlJ-Zp"}, "source": ["## Jupyter Notebook and IPython kernel architectures\n", "\n", "<figure><img src=\"https://github.com/BMClab/BMC/blob/master/images/jupyternotebook.png?raw=1\" width=800 alt=\"Jupyter Notebook and IPython kernel architectures\"/></figure>"]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 24, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "n-3oe00qJ-Zp"}, "source": ["## Installing the Python ecosystem\n", "\n", "**The easy way**   \n", "The easiest way to get Python and the most popular packages for scientific programming is to install them with a Python distribution such as [Anaconda](https://www.anaconda.com/products/distribution) or [Miniconda](https://docs.conda.io/en/latest/miniconda.html).  \n", "\n", "In fact, you don't even need to install Python in your computer, you can run Python for scientific programming in the cloud using [python.org](https://www.python.org/shell/), [Google Colaboratory](https://colab.research.google.com/), or [repl.it](https://replit.com/languages/python3).\n", "\n", "**The hard way**   \n", "You can download Python and all individual packages you need and install them one by one. In general, it's not that difficult, but it can become challenging and painful for certain big packages heavily dependent on math, image visualization, and your operating system (i.e., Microsoft Windows)."]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 26, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "jzwHLfvGJ-Zp"}, "source": ["### <PERSON><PERSON><PERSON>\n", "\n", "Go to the [*Anaconda* website](https://www.anaconda.com/products/distribution) and download the appropriate version for your computer (but download Anaconda3! for Python 3.x). The file is big (about 500 MB).\n", "\n", "Follow the installation steps described in the [Anaconda documentatione](https://docs.anaconda.com/anaconda/install/) for your operational system.   "]}, {"cell_type": "markdown", "metadata": {"id": "Fl_kdkypJ-Zp"}, "source": ["### <PERSON>conda\n", "\n", "A variation of *Anaconda* is [*Miniconda*](https://docs.conda.io/en/latest/miniconda.html) (Miniconda3 for Python 3.x), which contains only the *Conda* package manager and Python.  \n", "\n", "Once *Miniconda* is installed, you can use the `conda` command to install any other packages and create environments, etc."]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 26, "slide_type": "subslide"}, "id": "eHL2tOMEJ-Zq"}, "source": ["# My current installation"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-09-21T21:48:28.037829Z", "start_time": "2020-09-21T21:48:28.025418Z"}, "id": "gt16Tc_kJ-Zq", "outputId": "daf83658-9e66-42d5-e780-e3e568c107e8", "colab": {"base_uri": "https://localhost:8080/", "height": 36}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'3.10.12 (main, Jun 11 2023, 05:26:28) [GCC 11.4.0]'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 4}], "source": ["import sys\n", "sys.version"]}, {"cell_type": "markdown", "metadata": {"id": "6Zmag3BSJ-Zq"}, "source": ["More information can be obtained using the [watermark extension](https://github.com/rasbt/watermark):"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "X8NQk34cJ-Zq"}, "outputs": [], "source": ["try:\n", "    from watermark import watermark\n", "except ImportError:\n", "    %pip install -q watermark\n", "%load_ext watermark"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "pkGbbAfeJ-Zq", "outputId": "640c81f3-edf2-4c37-af38-bc8cad9f3df8", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Last updated: 2023-09-19 16:06:59\n", "\n", "Python implementation: CPython\n", "Python version       : 3.10.12\n", "IPython version      : 7.34.0\n", "\n", "Compiler    : GCC 11.4.0\n", "OS          : Linux\n", "Release     : 5.15.109+\n", "Machine     : x86_64\n", "Processor   : x86_64\n", "CPU cores   : 2\n", "Architecture: 64bit\n", "\n", "sys: 3.10.12 (main, Jun 11 2023, 05:26:28) [GCC 11.4.0]\n", "\n"]}], "source": ["%watermark -u -t -d -m -v --iversions"]}, {"cell_type": "markdown", "metadata": {"id": "nCyfbaAxJ-Zq"}, "source": ["## IDE for Python\n", "\n", "You might want an Integrated Development Environment (IDE) for programming in Python.  \n", "See [10 Best Python IDE & Code Editors](https://hackr.io/blog/best-python-ide) for possible IDEs.  "]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 30, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "u4J8SQCbJ-Zr"}, "source": ["## To learn about Python\n", "\n", "There is a lot of good material in the Internet about Python for scientific computing, some of them are:  \n", "\n", " - [How To Think Like A Computer Scientist](http://openbookproject.net/thinkcs/python/english3e/) or [the interactive edition](https://runestone.academy/ns/books/published/thinkcspy/index.html) (book)\n", " - [Python Scientific Lecture Notes](http://scipy-lectures.org/) (lecture notes)  \n", " - [Python Data Science Handbook](https://jakevdp.github.io/PythonDataScienceHandbook/) (tutorial/book)    "]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 72, "slide_helper": "subslide_end"}, "slide_helper": "slide_end", "id": "u_UwxXluJ-Zr"}, "source": ["## More examples of Jupyter Notebooks\n", "\n", "Let's run stuff from:\n", "- [https://github.com/BMClab/BMC](https://github.com/BMClab/BMC)  \n", "- [A gallery of interesting Jupyter Notebooks](https://github.com/jupyter/jupyter/wiki)"]}, {"cell_type": "markdown", "metadata": {"internals": {"frag_helper": "fragment_end", "frag_number": 74, "slide_helper": "subslide_end"}, "slide_helper": "subslide_end", "id": "jz8q9xTiJ-Zr"}, "source": ["## Questions?\n", "\n", "- https://www.reddit.com/r/learnpython/\n", "- https://stackoverflow.com/questions/tagged/python\n", "- https://www.reddit.com/r/Python/  "]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2018-06-08T00:40:16.548261Z", "start_time": "2018-06-08T00:40:16.538254Z"}, "id": "BQp-IU_YJ-Zr", "outputId": "a30e9121-8fa5-4cfd-9cfc-f2c86a7009cb", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The Zen of Python, by <PERSON>\n", "\n", "Beautiful is better than ugly.\n", "Explicit is better than implicit.\n", "Simple is better than complex.\n", "Complex is better than complicated.\n", "Flat is better than nested.\n", "Sparse is better than dense.\n", "Readability counts.\n", "Special cases aren't special enough to break the rules.\n", "Although practicality beats purity.\n", "Errors should never pass silently.\n", "Unless explicitly silenced.\n", "In the face of ambiguity, refuse the temptation to guess.\n", "There should be one-- and preferably only one --obvious way to do it.\n", "Although that way may not be obvious at first unless you're Dutch.\n", "Now is better than never.\n", "Although never is often better than *right* now.\n", "If the implementation is hard to explain, it's a bad idea.\n", "If the implementation is easy to explain, it may be a good idea.\n", "Namespaces are one honking great idea -- let's do more of those!\n"]}], "source": ["import this"]}], "metadata": {"anaconda-cloud": {}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "nbTranslate": {"displayLangs": ["*"], "hotkey": "alt-t", "langInMainMenu": true, "sourceLang": "en", "targetLang": "fr", "useGoogleTranslate": true}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": true, "title_cell": "Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}